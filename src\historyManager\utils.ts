/**
 * 历史记录系统工具函数
 */

import { historyManager } from './index';
import type { BaseObjectModel } from '../type/baseObjectModel.svelte';

/**
 * 创建一个带历史记录的属性setter装饰器
 */
export function withHistory<T>(
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
) {
  const originalSetter = descriptor.set;
  
  if (!originalSetter) {
    throw new Error(`属性 ${propertyKey} 没有setter`);
  }

  descriptor.set = function(this: BaseObjectModel, value: T) {
    const oldValue = (this as any)[`#${propertyKey}`] || (this as any)[`_${propertyKey}`];
    
    // 记录历史（如果值发生变化）
    if (historyManager.isRecording() && oldValue !== value) {
      historyManager.recordChange(this, propertyKey, oldValue, value);
    }
    
    // 调用原始setter
    originalSetter.call(this, value);
  };

  return descriptor;
}

/**
 * 批量操作包装器
 */
export function batchOperation<T>(
  name: string,
  operation: () => T,
  description?: string
): T {
  historyManager.startGroup(name, description);
  
  try {
    const result = operation();
    historyManager.endGroup();
    return result;
  } catch (error) {
    // 如果操作失败，取消这个操作组
    historyManager.endGroup();
    throw error;
  }
}

/**
 * 异步批量操作包装器
 */
export async function batchOperationAsync<T>(
  name: string,
  operation: () => Promise<T>,
  description?: string
): Promise<T> {
  historyManager.startGroup(name, description);
  
  try {
    const result = await operation();
    historyManager.endGroup();
    return result;
  } catch (error) {
    // 如果操作失败，取消这个操作组
    historyManager.endGroup();
    throw error;
  }
}

/**
 * 暂时禁用历史记录的操作包装器
 */
export function withoutHistory<T>(operation: () => T): T {
  const wasRecording = historyManager.isRecording();
  
  if (wasRecording) {
    historyManager.pauseRecording();
  }
  
  try {
    return operation();
  } finally {
    if (wasRecording) {
      historyManager.resumeRecording();
    }
  }
}

/**
 * 异步暂时禁用历史记录的操作包装器
 */
export async function withoutHistoryAsync<T>(operation: () => Promise<T>): Promise<T> {
  const wasRecording = historyManager.isRecording();
  
  if (wasRecording) {
    historyManager.pauseRecording();
  }
  
  try {
    return await operation();
  } finally {
    if (wasRecording) {
      historyManager.resumeRecording();
    }
  }
}

/**
 * 常用的批量操作方法
 */
export const commonOperations = {
  /**
   * 移动对象
   */
  moveObject(obj: BaseObjectModel, newX: number, newY: number) {
    return batchOperation('移动对象', () => {
      obj.x = newX;
      obj.y = newY;
    }, `移动${obj.className}到(${newX}, ${newY})`);
  },

  /**
   * 调整对象大小
   */
  resizeObject(obj: BaseObjectModel, newWidth: number, newHeight: number) {
    return batchOperation('调整大小', () => {
      (obj as any).width = newWidth;
      (obj as any).height = newHeight;
    }, `调整${obj.className}大小为${newWidth}x${newHeight}`);
  },

  /**
   * 缩放对象
   */
  scaleObject(obj: BaseObjectModel, newScaleX: number, newScaleY: number) {
    return batchOperation('缩放对象', () => {
      obj.scaleX = newScaleX;
      obj.scaleY = newScaleY;
    }, `缩放${obj.className}为${newScaleX}x${newScaleY}`);
  },

  /**
   * 旋转对象
   */
  rotateObject(obj: BaseObjectModel, newRotation: number) {
    return batchOperation('旋转对象', () => {
      obj.rotation = newRotation;
    }, `旋转${obj.className}到${newRotation}度`);
  },

  /**
   * 设置对象透明度
   */
  setObjectAlpha(obj: BaseObjectModel, newAlpha: number) {
    return batchOperation('设置透明度', () => {
      obj.alpha = newAlpha;
    }, `设置${obj.className}透明度为${newAlpha}`);
  },

  /**
   * 批量更新对象属性
   */
  updateObjectProperties(obj: BaseObjectModel, properties: Record<string, any>) {
    const propertyNames = Object.keys(properties);
    return batchOperation('批量更新属性', () => {
      propertyNames.forEach(key => {
        if (key in obj) {
          (obj as any)[key] = properties[key];
        }
      });
    }, `更新${obj.className}的${propertyNames.join(', ')}`);
  }
};

/**
 * 创建历史记录状态的响应式对象（用于UI）
 */
export function createHistoryState() {
  let state = $state(historyManager.getState());
  
  // 监听历史记录变化
  const updateState = () => {
    const newState = historyManager.getState();
    Object.assign(state, newState);
  };
  
  historyManager.addListener(updateState);
  
  return {
    state,
    cleanup: () => {
      historyManager.removeListener(updateState);
    }
  };
}

/**
 * 调试工具：打印历史记录栈
 */
export function debugHistoryStack() {
  const manager = historyManager as any;
  console.group('🕰️ 历史记录栈状态');
  console.log('撤销栈:', manager.undoStack);
  console.log('重做栈:', manager.redoStack);
  console.log('当前操作组:', manager.currentGroup);
  console.log('状态:', historyManager.getState());
  console.groupEnd();
}

/**
 * 性能监控：记录操作耗时
 */
export function measureOperation<T>(
  name: string,
  operation: () => T
): T {
  const startTime = performance.now();
  
  try {
    const result = operation();
    const endTime = performance.now();
    console.log(`🕰️ 操作 "${name}" 耗时: ${(endTime - startTime).toFixed(2)}ms`);
    return result;
  } catch (error) {
    const endTime = performance.now();
    console.error(`🕰️ 操作 "${name}" 失败，耗时: ${(endTime - startTime).toFixed(2)}ms`, error);
    throw error;
  }
}
