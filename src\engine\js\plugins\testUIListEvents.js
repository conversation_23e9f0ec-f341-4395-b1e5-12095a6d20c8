/**
 * 测试UIList内部事件处理的简单脚本
 * 验证修复后的UIList是否能正确处理子元素事件
 */

(() => {
    'use strict';

    console.log('🧪 开始加载UIList事件测试脚本');

    // 等待所有插件加载完成
    setTimeout(() => {
        console.log('🧪 开始UIList事件测试');

        // 添加测试函数到全局
        window.testUIListEvents = function() {
            console.log('🧪 执行UIList事件测试');

            // 检查必要的类是否存在
            if (typeof UIList === 'undefined') {
                console.error('❌ UIList类未找到');
                return;
            }

            if (typeof UIButton === 'undefined') {
                console.error('❌ UIButton类未找到');
                return;
            }

            // 获取当前场景
            const scene = SceneManager._scene;
            if (!scene) {
                console.error('❌ 无法获取当前场景');
                return;
            }

            console.log('✅ 开始创建测试组件');

            // 创建测试UIList
            const testList = new UIList({
                x: 100,
                y: 100,
                width: 300,
                height: 200
            });
            testList.name = 'TestUIList';

            // 创建测试UIButton（在UIList内部）
            const testButton = new UIButton({
                x: 50,
                y: 50,
                width: 120,
                height: 40
            });
            testButton.name = 'TestButton';

            // 设置按钮事件
            testButton._eventCodes = {
                onClick: 'console.log("🎉 UIList内部按钮点击成功！测试通过！");'
            };

            // 将按钮添加到UIList
            testList.addChild(testButton);

            // 将UIList添加到场景
            scene.addChild(testList);

            console.log('✅ 测试组件创建完成');
            console.log('📍 测试按钮位置:', {
                global: testButton.getBounds(),
                local: { x: testButton.x, y: testButton.y, width: testButton.buttonWidth, height: testButton.buttonHeight }
            });

            console.log('💡 请点击测试按钮（位置大约在屏幕坐标 150,150 附近）');
            console.log('💡 如果看到"🎉 UIList内部按钮点击成功！测试通过！"消息，说明修复成功');

            // 返回测试对象供进一步调试
            return {
                testList: testList,
                testButton: testButton
            };
        };

        // 添加手动触发测试函数
        window.manualTriggerTest = function() {
            console.log('🧪 手动触发测试按钮事件');
            
            const testObjects = window.testUIListEvents();
            if (testObjects && testObjects.testButton) {
                // 直接调用按钮的事件处理
                if (typeof testObjects.testButton.executeEvent === 'function') {
                    testObjects.testButton.executeEvent('onClick');
                } else {
                    console.error('❌ 测试按钮没有executeEvent方法');
                }
            }
        };

        // 添加update调用测试函数
        window.testUpdateCalls = function() {
            console.log('🧪 测试update方法调用');
            
            const testObjects = window.testUIListEvents();
            if (testObjects) {
                const { testList, testButton } = testObjects;
                
                console.log('🔍 测试前状态:', {
                    listHasUpdate: typeof testList.update === 'function',
                    buttonHasUpdate: typeof testButton.update === 'function',
                    listHasOriginalUpdate: !!testList._originalUpdate,
                    buttonHasOriginalUpdate: !!testButton._originalUpdate
                });

                // 手动调用UIList的update方法
                if (typeof testList.update === 'function') {
                    console.log('🔄 手动调用UIList.update()');
                    testList.update();
                }

                // 手动调用UIButton的update方法
                if (typeof testButton.update === 'function') {
                    console.log('🔄 手动调用UIButton.update()');
                    testButton.update();
                }

                console.log('✅ update方法调用测试完成');
            }
        };

        console.log('✅ UIList事件测试脚本加载完成');
        console.log('💡 使用方法:');
        console.log('  - testUIListEvents() - 创建测试组件');
        console.log('  - manualTriggerTest() - 手动触发测试');
        console.log('  - testUpdateCalls() - 测试update方法调用');

    }, 3000); // 延迟3秒确保所有插件加载完成

})();
