// 导入项目中已定义的 bitmapProperties 接口
import type { bitmapProperties } from '../../generators/type';

// 重新导出以便在编辑器中使用
export type { bitmapProperties };

// 文本元素接口
export interface TextElement {
  type: 'text';
  id: string;
  text: string;
  x: number;
  y: number;
  maxWidth: number;
  lineHeight: number;
  align: 'left' | 'center' | 'right';
}

// 裁切区域接口
export interface CropRegion {
  sx: number;
  sy: number;
  sw: number;
  sh: number;
  label?: string;
}

// 图像元素接口
export interface ImageElement {
  type: 'image';
  id: string;
  source: any;
  dx: number;
  dy: number;
  dw: number;
  dh: number;
  sx: number;    // 源图像裁剪起始X（必需）
  sy: number;    // 源图像裁剪起始Y（必需）
  sw: number;    // 源图像裁剪宽度（必需）
  sh: number;    // 源图像裁剪高度（必需）
  useExternalResource?: boolean;
  regions?: CropRegion[];  // 保存的裁切区域列表
}

// 元素联合类型
export type Element = TextElement | ImageElement;

// 编辑器状态接口
export interface EditorState {
  selectedElement: Element | null;
  selectedElementIndex: number;
  isDragging: boolean;
  dragStartPosition: { x: number; y: number };
  elementOriginalPosition: { x: number; y: number };
  elements: Element[];
  elementsUpdateTimestamp: number;
  isSaving: boolean;
  lastSaveTime: string | null;
}

// 编辑器配置接口
export interface EditorConfig {
  canvasWidth: number;
  canvasHeight: number;
  backgroundColor: string;
}

// 编辑器回调函数类型
export interface EditorCallbacks {
  onSave?: (bitmapData: bitmapProperties) => void;
  onCancel?: () => void;
  onClose?: () => void;
}
