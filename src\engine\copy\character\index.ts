/**
 * 角色复制模块统一导出
 */

// 类型定义
export type {
  BaseCopyOptions,
  PlayerCopyOptions,
  EventCopyOptions,
  FollowerCopyOptions,
  VehicleCopyOptions,
  CharacterCopyOptions,
  CopyOptions,
  CopyResult,
  ObjectStructure
} from './types';

export { ObjectType, DEFAULT_COPY_OPTIONS } from './types';

// 工具类
export { CopyUtils } from './CopyUtils';

// 各类型复制器
export { PlayerCopier } from './PlayerCopier';
export { EventCopier } from './EventCopier';
export { CharacterCopier } from './CharacterCopier';
export { FollowerCopier } from './FollowerCopier';
export { VehicleCopier } from './VehicleCopier';

// 统一管理器
export { CharacterCopyManager } from './CharacterCopyManager';

// 便捷方法
export const copyCharacterObject = CharacterCopyManager.copyObject;
export const copyMultipleCharacterObjects = CharacterCopyManager.copyMultiple;
export const canCopyCharacterObject = CharacterCopyManager.canCopy;
export const getCharacterObjectInfo = CharacterCopyManager.getObjectInfo;
export const getSupportedCharacterTypes = CharacterCopyManager.getSupportedTypes;
