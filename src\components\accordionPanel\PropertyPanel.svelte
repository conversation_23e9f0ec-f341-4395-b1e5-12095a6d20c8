<script lang="ts">
  import AccordionPanel from './AccordionPanel.svelte';
  import PropertyContainer from './PropertyContainer.svelte';

  // 属性配置接口
  interface PropertyConfig {
    label: string;
    component: any;
    props?: Record<string, any>;
    required?: boolean;
    tooltip?: string;
    width?: 'auto' | 'full' | 'half';
    actions?: Array<{
      component: any;
      props?: Record<string, any>;
      content?: string;
    }>;
  }

  interface PanelConfig {
    title: string;
    icon?: string;
    badge?: string;
    badgeVariant?: 'active' | 'inactive' | 'info';
    expanded?: boolean;
    disabled?: boolean;
    properties: PropertyConfig[];
    columns?: number;
    gap?: string;
    labelWidth?: string;
  }

  // 组件属性
  export let config: PanelConfig;
</script>

<AccordionPanel
  title={config.title}
  icon={config.icon || ''}
  badge={config.badge || ''}
  badgeVariant={config.badgeVariant || 'active'}
  expanded={config.expanded !== false}
  disabled={config.disabled || false}
>
  <PropertyContainer
    columns={config.columns || 2}
    gap={config.gap || '8px'}
    labelWidth={config.labelWidth || '80px'}
  >
    {#each config.properties as property}
      <div class="property-item" class:full-width={property.width === 'full'} class:half-width={property.width === 'half'}>
        <label class="property-label" style="min-width: {config.labelWidth || '80px'}">
          {property.label}{#if property.required}<span class="required">*</span>{/if}:
          {#if property.tooltip}
            <span class="tooltip" title={property.tooltip}>?</span>
          {/if}
        </label>
        <div class="property-control">
          <svelte:component
            this={property.component}
            {...(property.props || {})}
          />

          {#if property.actions}
            {#each property.actions as action}
              <svelte:component
                this={action.component}
                {...(action.props || {})}
              >
                {action.content || ''}
              </svelte:component>
            {/each}
          {/if}
        </div>
      </div>
    {/each}
  </PropertyContainer>
</AccordionPanel>

<style>
  .property-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 8px;
    min-height: 28px;
  }

  .property-item.full-width {
    width: 100%;
  }

  .property-item.half-width {
    width: calc(50% - 4px);
  }

  .property-label {
    font-size: var(--component-font-size, 11px);
    font-family: var(--component-font-family, inherit);
    color: var(--component-text, #1a202c);
    font-weight: 500;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 4px;
    line-height: 1.2;
    padding-top: 4px;
  }

  .property-control {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 0;
  }

  .required {
    color: var(--theme-error, #ef4444);
    font-weight: bold;
  }

  .tooltip {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: var(--theme-primary, #3182ce);
    color: white;
    font-size: 10px;
    font-weight: bold;
    cursor: help;
    flex-shrink: 0;
  }

  /* 深色主题支持 */
  @media (prefers-color-scheme: dark) {
    .property-label {
      color: var(--theme-text-dark, #ffffff);
    }
  }
</style>
