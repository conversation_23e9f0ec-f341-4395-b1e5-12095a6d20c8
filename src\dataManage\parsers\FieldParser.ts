import type { FieldInfo, FieldType } from '../types/dataTypes';

export class FieldParser {
  /**
   * 解析对象的字段结构
   */
  static parseFields(obj: any, maxDepth: number = 3, currentDepth: number = 0): FieldInfo[] {
    if (!obj || currentDepth >= maxDepth) {
      return [];
    }

    const fields: FieldInfo[] = [];

    // 如果是数组，取第一个非null元素进行解析
    if (Array.isArray(obj)) {
      const sampleItem = obj.find(item => item !== null && item !== undefined);
      if (sampleItem) {
        return this.parseFields(sampleItem, maxDepth, currentDepth);
      }
      return [];
    }

    // 解析对象的每个属性
    for (const [key, value] of Object.entries(obj)) {
      if (key === 'meta' || key === 'note') continue; // 跳过一些特殊字段

      const fieldType = this.getFieldType(value);
      const fieldInfo: FieldInfo = {
        name: key,
        type: fieldType,
        displayName: this.getDisplayName(key),
        description: this.getFieldDescription(key)
      };

      // 处理数组类型
      if (Array.isArray(value)) {
        fieldInfo.isArray = true;
        if (value.length > 0) {
          const firstItem = value.find(item => item !== null && item !== undefined);
          if (firstItem) {
            fieldInfo.arrayItemType = this.getFieldType(firstItem);
            // 如果数组元素是对象，解析其子字段
            if (typeof firstItem === 'object' && firstItem !== null) {
              fieldInfo.children = this.parseFields(firstItem, maxDepth, currentDepth + 1);
            }
          }
        }
      }
      // 处理对象类型
      else if (fieldType === 'object' && value !== null) {
        fieldInfo.children = this.parseFields(value, maxDepth, currentDepth + 1);
      }

      fields.push(fieldInfo);
    }

    return fields.sort((a, b) => a.displayName.localeCompare(b.displayName));
  }

  /**
   * 获取值的类型
   */
  private static getFieldType(value: any): FieldType {
    if (value === null) return 'null' as FieldType;
    if (Array.isArray(value)) return 'array' as FieldType;

    const type = typeof value;
    switch (type) {
      case 'string': return 'string' as FieldType;
      case 'number': return 'number' as FieldType;
      case 'boolean': return 'boolean' as FieldType;
      case 'object': return 'object' as FieldType;
      default: return 'string' as FieldType;
    }
  }

  /**
   * 获取字段的显示名称
   */
  private static getDisplayName(fieldName: string): string {
    const displayNames: Record<string, string> = {
      // 通用字段
      'id': 'ID',
      'name': '名称',
      'note': '备注',
      'description': '描述',
      'iconIndex': '图标索引',
      'price': '价格',
      'params': '参数',
      'traits': '特性',
      'effects': '效果',

      // 角色相关
      'characterName': '角色图像',
      'characterIndex': '角色索引',
      'faceName': '头像图像',
      'faceIndex': '头像索引',
      'classId': '职业ID',
      'initialLevel': '初始等级',
      'maxLevel': '最大等级',
      'nickname': '昵称',
      'profile': '简介',
      'battlerName': '战斗图像',
      'equips': '装备',

      // 技能相关
      'animationId': '动画ID',
      'damage': '伤害',
      'hitType': '命中类型',
      'occasion': '使用场合',
      'scope': '范围',
      'speed': '速度',
      'successRate': '成功率',
      'tpCost': 'TP消耗',
      'mpCost': 'MP消耗',
      'requiredWtypeId1': '需要武器类型1',
      'requiredWtypeId2': '需要武器类型2',

      // 物品相关
      'consumable': '消耗品',
      'etypeId': '装备类型ID',
      'atypeId': '防具类型ID',
      'wtypeId': '武器类型ID',

      // 系统相关
      'gameTitle': '游戏标题',
      'versionId': '版本ID',
      'locale': '语言',
      'partyMembers': '队伍成员',
      'testBattlers': '测试战斗者',
      'currencyUnit': '货币单位',
      'elements': '属性',
      'skillTypes': '技能类型',
      'weaponTypes': '武器类型',
      'armorTypes': '防具类型',
      'equipTypes': '装备类型'
    };

    return displayNames[fieldName] || fieldName;
  }

  /**
   * 获取字段描述
   */
  private static getFieldDescription(fieldName: string): string {
    const descriptions: Record<string, string> = {
      'id': '数据的唯一标识符',
      'name': '显示的名称',
      'note': '备注信息，可包含插件标签',
      'iconIndex': '图标在图标集中的索引位置',
      'price': '购买/出售价格',
      'params': '基础参数数组',
      'traits': '特性列表',
      'effects': '效果列表',
      'characterName': '角色行走图文件名',
      'faceName': '头像图文件名',
      'classId': '所属职业的ID',
      'initialLevel': '角色的初始等级',
      'maxLevel': '角色可达到的最大等级',
      'equips': '初始装备列表',
      'animationId': '技能动画的ID',
      'mpCost': '使用技能消耗的MP',
      'tpCost': '使用技能消耗的TP',
      'scope': '技能的作用范围',
      'occasion': '技能的使用场合',
      'gameTitle': '游戏的标题',
      'partyMembers': '初始队伍成员ID列表',
      'currencyUnit': '游戏中的货币单位名称'
    };

    return descriptions[fieldName] || '';
  }

  /**
   * 构建字段路径
   */
  static buildFieldPath(parentPath: string, fieldName: string): string {
    return parentPath ? `${parentPath}.${fieldName}` : fieldName;
  }

  /**
   * 获取嵌套字段的显示文本
   */
  static getFieldDisplayText(fieldPath: string, fieldInfo: FieldInfo, dataTypeName: string): string {
    const pathParts = fieldPath.split('.');
    if (pathParts.length === 1) {
      return fieldInfo.displayName;
    } else {
      const parentPath = pathParts.slice(0, -1).join(' → ');
      return `${parentPath} → ${fieldInfo.displayName}`;
    }
  }

  /**
   * 获取完整的字段显示文本（包含数据类型）
   */
  static getFullFieldDisplayText(fieldPath: string, fieldInfo: FieldInfo, dataTypeName: string): string {
    const pathParts = fieldPath.split('.');
    if (pathParts.length === 1) {
      return `${dataTypeName} → ${fieldInfo.displayName}`;
    } else {
      const parentPath = pathParts.slice(0, -1).join(' → ');
      return `${dataTypeName} → ${parentPath} → ${fieldInfo.displayName}`;
    }
  }
}
