/**
 * Window_Base 创建器
 * 专门用于创建窗口基类
 */

import { BaseWindowCreator, type WindowCreationOptions } from './WindowCreator';

/**
 * 窗口基类创建选项
 */
export interface BaseWindowOptions extends WindowCreationOptions {
    /** 窗口内边距 */
    padding?: number;
    /** 窗口背景透明度 */
    backOpacity?: number;
    /** 窗口色调 */
    tone?: [number, number, number];
    /** 是否显示窗口 */
    visible?: boolean;
}

/**
 * 创建窗口基类
 * @param options 创建选项
 * @returns 创建的窗口基类实例
 */
export async function createWindowBase(options: BaseWindowOptions = {}): Promise<any> {
    console.log('=== 创建窗口基类 Window_Base ===');
    
    try {
        // 预加载资源
        BaseWindowCreator.preloadWindowResources('Window_Base');
        
        // 设置默认选项
        const defaultOptions: WindowCreationOptions = {
            autoOpen: true,
            addToStage: true,
            rect: options.rect || { x: 0, y: 0, width: 240, height: 120 },
            ...options
        };
        
        // 创建窗口实例
        const window = await BaseWindowCreator.createWindowInstance('Window_Base', defaultOptions);
        
        // Window_Base 特定的设置
        setupBaseWindow(window, options);
        
        console.log('Window_Base 创建完成，窗口属性:', {
            x: window.x,
            y: window.y,
            width: window.width,
            height: window.height,
            visible: window.visible,
            openness: window.openness
        });
        
        return window;
        
    } catch (error) {
        console.error('创建 Window_Base 失败:', error);
        throw error;
    }
}

/**
 * 设置窗口基类属性
 * @param window 窗口实例
 * @param options 窗口基类选项
 */
function setupBaseWindow(window: any, options: BaseWindowOptions): void {
    console.log('设置窗口基类属性...');
    
    try {
        // 设置内边距
        if (options.padding !== undefined && window.padding !== undefined) {
            window.padding = options.padding;
            console.log('设置窗口内边距:', options.padding);
        }
        
        // 设置背景透明度
        if (options.backOpacity !== undefined && window.backOpacity !== undefined) {
            window.backOpacity = options.backOpacity;
            console.log('设置窗口背景透明度:', options.backOpacity);
        }
        
        // 设置窗口色调
        if (options.tone && window.setTone && typeof window.setTone === 'function') {
            window.setTone(options.tone[0], options.tone[1], options.tone[2]);
            console.log('设置窗口色调:', options.tone);
        }
        
        // 设置可见性
        if (options.visible !== undefined) {
            window.visible = options.visible;
            console.log('设置窗口可见性:', options.visible);
        }
        
        console.log('窗口基类属性设置完成');
        
    } catch (error) {
        console.error('设置窗口基类属性失败:', error);
    }
}

/**
 * 创建并打开窗口基类
 * @param options 创建选项
 * @returns 创建的窗口基类实例
 */
export async function createAndOpenWindowBase(options: BaseWindowOptions = {}): Promise<any> {
    console.log('=== 创建并打开 Window_Base ===');
    
    const window = await createWindowBase({
        ...options,
        autoOpen: true
    });
    
    console.log('Window_Base 已创建并打开');
    return window;
}

/**
 * 创建简单的窗口基类（用于测试）
 * @returns 创建的窗口基类实例
 */
export async function createSimpleWindowBase(): Promise<any> {
    console.log('=== 创建简单窗口基类 ===');
    
    try {
        const window = await createWindowBase({
            rect: { x: 100, y: 100, width: 300, height: 150 },
            autoOpen: true,
            addToStage: true,
            visible: true,
            backOpacity: 192
        });
        
        console.log('简单窗口基类创建成功');
        return window;
        
    } catch (error) {
        console.error('创建简单窗口基类失败:', error);
        throw error;
    }
}
