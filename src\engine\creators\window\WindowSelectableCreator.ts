/**
 * Window_Selectable 创建器
 * 专门用于创建可选择窗口
 */

import { BaseWindowCreator, type WindowCreationOptions } from './WindowCreator';
import { type ScrollableWindowOptions } from './WindowScrollableCreator';

/**
 * 可选择窗口创建选项
 */
export interface SelectableWindowOptions extends ScrollableWindowOptions {
    /** 初始选中的索引 */
    initialIndex?: number;
    /** 是否激活窗口 */
    activate?: boolean;
    /** 是否启用光标 */
    cursorVisible?: boolean;
    /** 最大项目数 */
    maxItems?: number;
    /** 每行项目数 */
    maxCols?: number;
}

/**
 * 创建可选择窗口
 * @param options 创建选项
 * @returns 创建的可选择窗口实例
 */
export async function createWindowSelectable(options: SelectableWindowOptions = {}): Promise<any> {
    console.log('=== 创建可选择窗口 Window_Selectable ===');
    
    try {
        // 预加载资源
        BaseWindowCreator.preloadWindowResources('Window_Selectable');
        
        // 设置默认选项
        const defaultOptions: WindowCreationOptions = {
            autoOpen: true,
            addToStage: true,
            rect: options.rect || { x: 0, y: 0, width: 300, height: 200 },
            ...options
        };
        
        // 创建窗口实例
        const window = await BaseWindowCreator.createWindowInstance('Window_Selectable', defaultOptions);
        
        // Window_Selectable 特定的设置
        setupSelectableWindow(window, options);
        
        console.log('Window_Selectable 创建完成，窗口属性:', {
            x: window.x,
            y: window.y,
            width: window.width,
            height: window.height,
            index: window.index(),
            active: window.active,
            cursorVisible: window.cursorVisible,
            visible: window.visible
        });
        
        return window;
        
    } catch (error) {
        console.error('创建 Window_Selectable 失败:', error);
        throw error;
    }
}

/**
 * 设置可选择窗口属性
 * @param window 窗口实例
 * @param options 可选择窗口选项
 */
function setupSelectableWindow(window: any, options: SelectableWindowOptions): void {
    console.log('设置可选择窗口属性...');
    
    try {
        // 设置初始选中索引
        if (options.initialIndex !== undefined && window.select && typeof window.select === 'function') {
            window.select(options.initialIndex);
            console.log('设置初始选中索引:', options.initialIndex);
        }
        
        // 设置激活状态
        if (options.activate !== undefined) {
            if (options.activate && window.activate && typeof window.activate === 'function') {
                window.activate();
                console.log('窗口已激活');
            } else if (!options.activate && window.deactivate && typeof window.deactivate === 'function') {
                window.deactivate();
                console.log('窗口已取消激活');
            }
        }
        
        // 设置光标可见性
        if (options.cursorVisible !== undefined && window.setCursorRect && typeof window.setCursorRect === 'function') {
            window.cursorVisible = options.cursorVisible;
            console.log('设置光标可见性:', options.cursorVisible);
        }
        
        // 设置最大列数（如果窗口支持）
        if (options.maxCols !== undefined && window.maxCols && typeof window.maxCols === 'function') {
            // 这通常需要重写 maxCols 方法
            console.log('设置最大列数:', options.maxCols);
        }
        
        console.log('可选择窗口属性设置完成');
        
    } catch (error) {
        console.error('设置可选择窗口属性失败:', error);
    }
}

/**
 * 创建并激活可选择窗口
 * @param options 创建选项
 * @returns 创建的可选择窗口实例
 */
export async function createAndActivateWindowSelectable(options: SelectableWindowOptions = {}): Promise<any> {
    console.log('=== 创建并激活 Window_Selectable ===');
    
    const window = await createWindowSelectable({
        ...options,
        autoOpen: true,
        activate: true
    });
    
    console.log('Window_Selectable 已创建并激活');
    return window;
}

/**
 * 创建简单的可选择窗口（用于测试）
 * @returns 创建的可选择窗口实例
 */
export async function createSimpleWindowSelectable(): Promise<any> {
    console.log('=== 创建简单可选择窗口 ===');
    
    try {
        const window = await createWindowSelectable({
            rect: { x: 100, y: 100, width: 350, height: 250 },
            autoOpen: true,
            addToStage: true,
            visible: true,
            initialIndex: 0,
            activate: true,
            cursorVisible: true
        });
        
        console.log('简单可选择窗口创建成功');
        return window;
        
    } catch (error) {
        console.error('创建简单可选择窗口失败:', error);
        throw error;
    }
}
