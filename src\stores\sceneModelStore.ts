/**
 * 基于响应式模型的全局状态管理
 * 使用 SceneModel 作为主要的状态容器
 */

import { writable, derived } from 'svelte/store';
import { SceneModel } from '../type/senceModel.svelte';
import type { BaseObjectModel } from '../type/baseObjectModel.svelte';
// 导入模型注册中心，确保所有模型类都被注册
import '../type/modelRegistry';

/**
 * 全局场景模型状态接口
 */
export interface SceneModelState {
  /** 当前场景模型 */
  currentScene: SceneModel | null;

  /** 选中的对象模型数组（支持多选） */
  selectedObjects: BaseObjectModel[];

  /** 选中的对象路径数组（用于在对象树中定位） */
  selectedObjectPaths: string[];

  /** 主要选中的对象索引（用于属性面板显示） */
  primarySelectedIndex: number;

  /** 最后更新时间戳 */
  lastUpdateTime: number;

  /** 是否正在加载 */
  isLoading: boolean;

  /** 错误信息 */
  error: string | null;
}

/**
 * 初始状态
 */
const initialState: SceneModelState = {
  currentScene: null,
  selectedObjects: [],
  selectedObjectPaths: [],
  primarySelectedIndex: -1,
  lastUpdateTime: 0,
  isLoading: false,
  error: null
};

/**
 * 主要的场景模型状态存储
 */
export const sceneModelState = writable<SceneModelState>(initialState);

/**
 * 派生状态：当前场景的子对象列表
 */
export const sceneChildren = derived(
  sceneModelState,
  ($state) => $state.currentScene?.children || []
);

/**
 * 派生状态：是否有场景加载
 */
export const hasScene = derived(
  sceneModelState,
  ($state) => $state.currentScene !== null
);

/**
 * 派生状态：主要选中对象的信息
 */
export const primarySelectedObjectInfo = derived(
  sceneModelState,
  ($state) => {
    if ($state.selectedObjects.length === 0 || $state.primarySelectedIndex < 0) return null;

    const primaryObject = $state.selectedObjects[$state.primarySelectedIndex];
    if (!primaryObject) return null;

    return {
      className: primaryObject.className,
      name: primaryObject.name,
      x: primaryObject.x,
      y: primaryObject.y,
      width: primaryObject.width,
      height: primaryObject.height,
      alpha: primaryObject.alpha,
      visible: primaryObject.visible
    };
  }
);

/**
 * 派生状态：所有选中对象的信息
 */
export const selectedObjectsInfo = derived(
  sceneModelState,
  ($state) => {
    return $state.selectedObjects.map((obj, index) => ({
      index,
      isPrimary: index === $state.primarySelectedIndex,
      className: obj.className,
      name: obj.name,
      x: obj.x,
      y: obj.y,
      width: obj.width,
      height: obj.height,
      alpha: obj.alpha,
      visible: obj.visible
    }));
  }
);

/**
 * 派生状态：选中对象数量
 */
export const selectedObjectCount = derived(
  sceneModelState,
  ($state) => $state.selectedObjects.length
);

/**
 * 派生状态：是否有选中对象
 */
export const hasSelectedObjects = derived(
  sceneModelState,
  ($state) => $state.selectedObjects.length > 0
);

/**
 * 设置当前场景
 * @param scene RPG Maker 场景对象
 * @param sceneName 场景类名
 */
export function setCurrentScene(scene: any, sceneName: string) {
  console.log('=== 设置当前场景模型 ===');
  console.log('场景对象:', scene);
  console.log('场景类名:', sceneName);

  try {
    // 创建响应式场景模型（不使用 $effect，避免在非组件上下文中出错）
    const sceneModel = new SceneModel(scene);
    sceneModelState.update(state => ({
      ...state,
      currentScene: sceneModel,
      lastUpdateTime: Date.now(),
      isLoading: false,
      error: null
    }));

    console.log('场景模型已设置:', sceneModel);
    console.log('子对象数量:', sceneModel.children.length);

  } catch (error) {
    console.error('设置场景模型失败:', error);

    sceneModelState.update(state => ({
      ...state,
      currentScene: null,
      isLoading: false,
      error: error instanceof Error ? error.message : String(error)
    }));
  }
}

/**
 * 选择单个对象（清空之前的选择）
 * @param objectModel 要选择的对象模型
 * @param objectPath 对象在树中的路径
 */
export function selectObject(objectModel: BaseObjectModel | null, objectPath?: string) {
  console.log('=== 选择单个对象模型 ===');
  console.log('对象模型:', objectModel);
  console.log('对象路径:', objectPath);

  sceneModelState.update(state => ({
    ...state,
    selectedObjects: objectModel ? [objectModel] : [],
    selectedObjectPaths: objectPath ? [objectPath] : [],
    primarySelectedIndex: objectModel ? 0 : -1,
    lastUpdateTime: Date.now()
  }));
}

/**
 * 添加对象到选择列表（多选）
 * @param objectModel 要添加的对象模型
 * @param objectPath 对象在树中的路径
 * @param setPrimary 是否设置为主要选中对象
 */
export function addToSelection(objectModel: BaseObjectModel, objectPath: string, setPrimary = false) {
  console.log('=== 添加对象到选择列表 ===');
  console.log('对象模型:', objectModel);
  console.log('对象路径:', objectPath);
  console.log('设置为主要:', setPrimary);

  sceneModelState.update(state => {
    // 检查是否已经选中
    const existingIndex = state.selectedObjects.findIndex(obj => obj === objectModel);
    if (existingIndex >= 0) {
      // 如果已经选中，只更新主要选中索引
      return {
        ...state,
        primarySelectedIndex: setPrimary ? existingIndex : state.primarySelectedIndex,
        lastUpdateTime: Date.now()
      };
    }

    // 添加到选择列表
    const newSelectedObjects = [...state.selectedObjects, objectModel];
    const newSelectedPaths = [...state.selectedObjectPaths, objectPath];
    const newPrimaryIndex = setPrimary ? newSelectedObjects.length - 1 : state.primarySelectedIndex;

    return {
      ...state,
      selectedObjects: newSelectedObjects,
      selectedObjectPaths: newSelectedPaths,
      primarySelectedIndex: newPrimaryIndex >= 0 ? newPrimaryIndex : 0, // 如果没有主要选中，设置第一个为主要
      lastUpdateTime: Date.now()
    };
  });
}

/**
 * 从选择列表中移除对象
 * @param objectModel 要移除的对象模型
 */
export function removeFromSelection(objectModel: BaseObjectModel) {
  console.log('=== 从选择列表移除对象 ===');
  console.log('对象模型:', objectModel);

  sceneModelState.update(state => {
    const index = state.selectedObjects.findIndex(obj => obj === objectModel);
    if (index < 0) return state; // 对象不在选择列表中

    const newSelectedObjects = state.selectedObjects.filter((_, i) => i !== index);
    const newSelectedPaths = state.selectedObjectPaths.filter((_, i) => i !== index);

    // 调整主要选中索引
    let newPrimaryIndex = state.primarySelectedIndex;
    if (index === state.primarySelectedIndex) {
      // 如果移除的是主要选中对象，选择下一个或上一个
      newPrimaryIndex = newSelectedObjects.length > 0 ? Math.min(index, newSelectedObjects.length - 1) : -1;
    } else if (index < state.primarySelectedIndex) {
      // 如果移除的对象在主要选中对象之前，调整索引
      newPrimaryIndex = state.primarySelectedIndex - 1;
    }

    return {
      ...state,
      selectedObjects: newSelectedObjects,
      selectedObjectPaths: newSelectedPaths,
      primarySelectedIndex: newPrimaryIndex,
      lastUpdateTime: Date.now()
    };
  });
}

/**
 * 切换对象的选中状态
 * @param objectModel 要切换的对象模型
 * @param objectPath 对象在树中的路径
 * @param setPrimary 如果添加到选择，是否设置为主要选中对象
 */
export function toggleSelection(objectModel: BaseObjectModel, objectPath: string, setPrimary = false) {
  const currentState = getCurrentState();
  const isSelected = currentState.selectedObjects.includes(objectModel);

  if (isSelected) {
    removeFromSelection(objectModel);
  } else {
    addToSelection(objectModel, objectPath, setPrimary);
  }
}

/**
 * 设置主要选中对象
 * @param index 要设置为主要的对象索引
 */
export function setPrimarySelection(index: number) {
  console.log('=== 设置主要选中对象 ===');
  console.log('索引:', index);

  sceneModelState.update(state => {
    if (index < 0 || index >= state.selectedObjects.length) {
      console.warn('无效的主要选中索引:', index);
      return state;
    }

    return {
      ...state,
      primarySelectedIndex: index,
      lastUpdateTime: Date.now()
    };
  });
}

/**
 * 选择多个对象
 * @param objectModels 要选择的对象模型数组
 * @param objectPaths 对象路径数组
 * @param primaryIndex 主要选中对象的索引
 */
export function selectMultipleObjects(
  objectModels: BaseObjectModel[],
  objectPaths: string[],
  primaryIndex = 0
) {
  console.log('=== 选择多个对象 ===');
  console.log('对象数量:', objectModels.length);
  console.log('主要索引:', primaryIndex);

  sceneModelState.update(state => ({
    ...state,
    selectedObjects: [...objectModels],
    selectedObjectPaths: [...objectPaths],
    primarySelectedIndex: primaryIndex >= 0 && primaryIndex < objectModels.length ? primaryIndex : 0,
    lastUpdateTime: Date.now()
  }));
}

/**
 * 清空场景
 */
export function clearScene() {
  console.log('=== 清空场景模型 ===');

  sceneModelState.update(state => ({
    ...state,
    currentScene: null,
    selectedObjects: [],
    selectedObjectPaths: [],
    primarySelectedIndex: -1,
    lastUpdateTime: Date.now(),
    error: null
  }));
}

/**
 * 清空选择
 */
export function clearSelection() {
  console.log('=== 清空选择 ===');

  sceneModelState.update(state => ({
    ...state,
    selectedObjects: [],
    selectedObjectPaths: [],
    primarySelectedIndex: -1,
    lastUpdateTime: Date.now()
  }));
}

/**
 * 设置加载状态
 * @param loading 是否正在加载
 */
export function setLoading(loading: boolean) {
  sceneModelState.update(state => ({
    ...state,
    isLoading: loading
  }));
}

/**
 * 设置错误信息
 * @param error 错误信息
 */
export function setError(error: string | null) {
  sceneModelState.update(state => ({
    ...state,
    error,
    isLoading: false
  }));
}

/**
 * 触发更新（强制刷新）
 */
export function triggerUpdate() {
  sceneModelState.update(state => ({
    ...state,
    lastUpdateTime: Date.now()
  }));
}

/**
 * 获取当前状态的快照
 */
export function getCurrentState(): SceneModelState {
  let currentState: SceneModelState;
  sceneModelState.subscribe(state => {
    currentState = state;
  })();
  return currentState!;
}

/**
 * 在场景中查找对象模型
 * @param predicate 查找条件
 * @returns 找到的对象模型和路径
 */
export function findObjectInScene(
  predicate: (obj: BaseObjectModel) => boolean
): { object: BaseObjectModel; path: string } | null {
  const state = getCurrentState();
  if (!state.currentScene) return null;

  // 递归查找函数
  function searchInChildren(
    children: BaseObjectModel[],
    currentPath: string = ''
  ): { object: BaseObjectModel; path: string } | null {
    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      const childPath = currentPath ? `${currentPath}.children[${i}]` : `children[${i}]`;

      if (predicate(child)) {
        return { object: child, path: childPath };
      }

      // 如果子对象也有 children，递归查找
      if ('children' in child && Array.isArray((child as any).children)) {
        const found = searchInChildren((child as any).children, childPath);
        if (found) return found;
      }
    }
    return null;
  }

  // 首先检查根场景
  if (predicate(state.currentScene)) {
    return { object: state.currentScene, path: 'root' };
  }

  // 然后在子对象中查找
  return searchInChildren(state.currentScene.children);
}

/**
 * 根据类名查找对象
 * @param className 类名
 * @returns 找到的对象模型和路径
 */
export function findObjectByClassName(className: string) {
  return findObjectInScene(obj => obj.className === className);
}

/**
 * 根据名称查找对象
 * @param name 对象名称
 * @returns 找到的对象模型和路径
 */
export function findObjectByName(name: string) {
  return findObjectInScene(obj => obj.name === name);
}
