/**
 * 角色创建器
 * 专门用于创建RPG Maker MZ角色精灵
 */

import { objManage } from '../../objManage';

declare global {
    interface Window {
        Game_Character: any;
        Sprite_Character: any;
        Graphics: any;
        ImageManager: any;
        DataManager: any;
        $dataActors: any[];
        $dataClasses: any[];
        $dataSystem: any;
    }
}

/**
 * 创建角色精灵
 * @param characterName 角色图像文件名，默认'Actor1'
 * @param characterIndex 角色图像索引(0-7)，默认0
 * @param direction 角色朝向（2=下，4=左，6=右，8=上），默认2
 * @returns 创建的角色精灵对象
 */
export async function createCharacter(
    characterName: string = 'Actor1',
    characterIndex: number = 0,
    direction: number = 2
): Promise<any> {
    console.log('=== 创建角色精灵 ===');
    console.log(`角色图像: ${characterName}, 索引: ${characterIndex}, 朝向: ${direction}`);

    try {
        // 1. 检查资源是否准备就绪
        const resourceStatus = objManage.getResourceStatus();
        if (!resourceStatus.all) {
            console.log('等待资源加载完成...', resourceStatus);
            const loaded = await objManage.waitForResources();
            if (!loaded) {
                throw new Error('资源加载超时');
            }
        }

        // 2. 检查必要的类是否存在
        if (!window.Game_Character || !window.Sprite_Character) {
            throw new Error('RPG Maker MZ 角色类未加载');
        }

        // 3. 预加载角色图像
        if (window.ImageManager) {
            const bitmap = window.ImageManager.loadCharacter(characterName);
            console.log('预加载角色图像:', characterName, bitmap);

            // 检查 bitmap 对象是否有效
            if (bitmap && typeof bitmap === 'object') {
                // 等待图像加载完成
                if (typeof bitmap.isReady === 'function' && !bitmap.isReady()) {
                    console.log('等待角色图像加载完成...');
                    await new Promise<void>((resolve) => {
                        if (typeof bitmap.addLoadListener === 'function') {
                            bitmap.addLoadListener(() => {
                                console.log('角色图像加载完成');
                                resolve();
                            });
                        } else {
                            // 如果没有 addLoadListener 方法，使用轮询方式
                            const checkReady = () => {
                                if (bitmap.isReady && bitmap.isReady()) {
                                    console.log('角色图像加载完成（轮询检测）');
                                    resolve();
                                } else {
                                    setTimeout(checkReady, 100);
                                }
                            };
                            checkReady();
                        }
                    });
                } else if (typeof bitmap.isReady === 'function') {
                    console.log('角色图像已准备就绪');
                } else {
                    console.warn('Bitmap 对象缺少 isReady 方法，跳过等待');
                }
            } else {
                console.warn('无效的 bitmap 对象:', bitmap);
            }
        }

        // 4. 创建游戏角色对象
        const character = new window.Game_Character();

        // 5. 设置角色图像
        character.setImage(characterName, characterIndex);

        // 6. 设置角色位置（舞台中心对应的地图坐标）
        const centerMapX = Math.floor(window.Graphics.width / 2 / 48); // 48是瓦片大小
        const centerMapY = Math.floor(window.Graphics.height / 2 / 48);
        character.setPosition(centerMapX, centerMapY);

        // 7. 设置角色方向
        character.setDirection(direction);

        console.log('Game_Character 创建完成:', {
            characterName: character._characterName,
            characterIndex: character._characterIndex,
            direction: character._direction,
            x: character._x,
            y: character._y
        });

        // 8. 创建精灵对象
        const sprite = new window.Sprite_Character(character);

        console.log('Sprite_Character 创建完成:', sprite);
        console.log('Sprite 属性检查:', {
            visible: sprite.visible,
            alpha: sprite.alpha,
            x: sprite.x,
            y: sprite.y,
            width: sprite.width,
            height: sprite.height,
            bitmap: sprite.bitmap,
            character: sprite._character,
            // 检查锚点设置是否与源码一致
            anchorX: sprite.anchor ? sprite.anchor.x : 'no anchor',
            anchorY: sprite.anchor ? sprite.anchor.y : 'no anchor'
        });

        // 验证锚点是否符合 RPG Maker MZ 源码标准
        if (sprite.anchor) {
            const expectedAnchorX = 0.5;
            const expectedAnchorY = 1;
            console.log('锚点验证:', {
                actualAnchorX: sprite.anchor.x,
                expectedAnchorX: expectedAnchorX,
                anchorXCorrect: sprite.anchor.x === expectedAnchorX,
                actualAnchorY: sprite.anchor.y,
                expectedAnchorY: expectedAnchorY,
                anchorYCorrect: sprite.anchor.y === expectedAnchorY,
                bothAnchorsCorrect: sprite.anchor.x === expectedAnchorX && sprite.anchor.y === expectedAnchorY
            });
        } else {
            console.warn('Sprite 没有 anchor 属性！这不符合 RPG Maker MZ 标准');
        }

        // 强制更新精灵
        if (sprite.update) {
            sprite.update();
            console.log('精灵已强制更新');
        }

        // 检查 bitmap 状态
        if (sprite.bitmap) {
            console.log('Sprite bitmap 状态:', {
                url: sprite.bitmap.url,
                width: sprite.bitmap.width,
                height: sprite.bitmap.height,
                ready: sprite.bitmap.isReady ? sprite.bitmap.isReady() : 'unknown'
            });
        } else {
            console.warn('Sprite 没有 bitmap！');
        }

        // 9. 设置更明显的显示属性
        sprite.scale.x = 2; // 放大2倍
        sprite.scale.y = 2; // 放大2倍

        // 10. 通过objManage添加到舞台
        objManage.addToStage(sprite);

        // 11. 设置调试属性
        sprite.tint = 0xFFFFFF; // 确保没有色调变化
        console.log('已设置精灵属性以便调试，当前精灵尺寸:', sprite.width, 'x', sprite.height);

        console.log('角色精灵创建完成（已放大2倍并添加红色边框）');
        return sprite;

    } catch (error) {
        console.error('创建角色精灵失败:', error);
        throw error;
    }
}

/**
 * 基于数据库中的角色数据创建角色精灵
 * @param actorId 角色ID（来自$dataActors）
 * @returns 创建的角色精灵对象
 */
export async function createActorSprite(actorId: number): Promise<any> {
    console.log('=== 基于数据库创建角色精灵 ===');
    console.log(`角色ID: ${actorId}`);

    try {
        // 1. 检查资源是否准备就绪
        const resourceStatus = objManage.getResourceStatus();
        if (!resourceStatus.database) {
            throw new Error('数据库尚未加载完成');
        }

        // 2. 获取角色数据
        if (!window.$dataActors || !window.$dataActors[actorId]) {
            throw new Error(`角色ID ${actorId} 不存在于数据库中`);
        }

        const actorData = window.$dataActors[actorId];
        console.log('角色数据:', actorData);

        // 3. 使用角色数据中的图像信息
        const characterName = actorData.characterName || 'Actor1';
        const characterIndex = actorData.characterIndex || 0;

        console.log(`使用角色图像: ${characterName}, 索引: ${characterIndex}`);

        // 4. 创建角色精灵
        return await createCharacter(characterName, characterIndex, 2);

    } catch (error) {
        console.error('基于数据库创建角色精灵失败:', error);
        throw error;
    }
}

/**
 * 创建随机角色精灵
 * @returns 创建的角色精灵对象
 */
export async function createRandomCharacter(): Promise<any> {
    console.log('=== 创建随机角色精灵 ===');

    try {
        // 随机选择角色图像
        const characterNames = ['Actor1', 'Actor2', 'Actor3', 'People1', 'People2'];
        const randomName = characterNames[Math.floor(Math.random() * characterNames.length)];
        const randomIndex = Math.floor(Math.random() * 8); // 0-7
        const randomDirection = [2, 4, 6, 8][Math.floor(Math.random() * 4)]; // 下左右上

        console.log(`随机角色: ${randomName}, 索引: ${randomIndex}, 朝向: ${randomDirection}`);

        return await createCharacter(randomName, randomIndex, randomDirection);

    } catch (error) {
        console.error('创建随机角色精灵失败:', error);
        throw error;
    }
}

/**
 * 获取可用的角色列表
 * @returns 角色列表
 */
export function getAvailableActors(): Array<{ id: number, name: string, characterName: string }> {
    if (!window.$dataActors) {
        console.warn('角色数据库尚未加载');
        return [];
    }

    const actors = [];
    for (let i = 1; i < window.$dataActors.length; i++) {
        const actor = window.$dataActors[i];
        if (actor && actor.name) {
            actors.push({
                id: i,
                name: actor.name,
                characterName: actor.characterName || 'Actor1'
            });
        }
    }

    console.log('可用角色列表:', actors);
    return actors;
}

/**
 * 预加载所有角色图像
 */
export function preloadAllCharacterImages(): void {
    console.log('预加载所有角色图像...');

    if (!window.ImageManager) {
        console.warn('ImageManager 未加载');
        return;
    }

    // 预加载常用角色图像
    const commonCharacters = ['Actor1', 'Actor2', 'Actor3', 'People1', 'People2'];
    commonCharacters.forEach(name => {
        window.ImageManager.loadCharacter(name);
        console.log(`预加载角色图像: ${name}`);
    });

    // 如果数据库已加载，预加载数据库中的角色图像
    if (window.$dataActors) {
        const uniqueCharacters = new Set<string>();
        for (let i = 1; i < window.$dataActors.length; i++) {
            const actor = window.$dataActors[i];
            if (actor && actor.characterName) {
                uniqueCharacters.add(actor.characterName);
            }
        }

        uniqueCharacters.forEach(name => {
            window.ImageManager.loadCharacter(name);
            console.log(`预加载数据库角色图像: ${name}`);
        });
    }

    console.log('角色图像预加载请求已发送');
}

/**
 * 创建一个简单的测试角色（使用索引0，通常是最明显的）
 * @returns 创建的角色精灵对象
 */
export async function createTestCharacter(): Promise<any> {
    console.log('=== 创建测试角色精灵 ===');

    try {
        // 使用索引0（通常是角色图片的第一个，最明显）
        const sprite = await createCharacter('Actor1', 0, 2);

        console.log('测试角色创建成功');
        return sprite;

    } catch (error) {
        console.error('创建测试角色失败:', error);
        throw error;
    }
}

/**
 * 创建一个放大的角色精灵用于测试
 * @param characterName 角色图像文件名
 * @param characterIndex 角色索引
 * @returns 创建的角色精灵对象
 */
export async function createLargeCharacter(
    characterName: string = 'Actor1',
    characterIndex: number = 0
): Promise<any> {
    console.log('=== 创建放大角色精灵 ===');
    console.log(`角色: ${characterName}, 索引: ${characterIndex}`);

    try {
        const sprite = await createCharacter(characterName, characterIndex, 2);

        // 放大精灵使其更明显
        sprite.scale.x = 3;
        sprite.scale.y = 3;

        console.log('放大角色创建成功，缩放:', sprite.scale.x, 'x', sprite.scale.y);
        console.log('最终尺寸:', sprite.width, 'x', sprite.height);

        return sprite;

    } catch (error) {
        console.error('创建放大角色失败:', error);
        throw error;
    }
}
