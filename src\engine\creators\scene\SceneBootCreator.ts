/**
 * Scene_Boot 创建器
 * 专门用于创建启动场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';

/**
 * 创建启动场景
 * @param options 创建选项
 * @returns 创建的启动场景实例
 */
export async function createSceneBoot(options: SceneCreationOptions = {}): Promise<any> {
    console.log('=== 创建启动场景 Scene_Boot ===');
    
    try {
        // 预加载资源
        BaseSceneCreator.preloadSceneResources('Scene_Boot');
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false, // 启动场景通常不自动启动，由SceneManager控制
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Boot', defaultOptions);
        
        // Scene_Boot 特定的设置
        console.log('Scene_Boot 创建完成，场景属性:', {
            started: scene._started,
            active: scene._active,
            databaseLoaded: scene._databaseLoaded
        });
        
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Boot 失败:', error);
        throw error;
    }
}

/**
 * 创建并启动启动场景（用于测试）
 * @param options 创建选项
 * @returns 创建的启动场景实例
 */
export async function createAndStartSceneBoot(options: SceneCreationOptions = {}): Promise<any> {
    console.log('=== 创建并启动 Scene_Boot ===');
    
    const scene = await createSceneBoot({
        ...options,
        autoStart: true
    });
    
    console.log('Scene_Boot 已创建并启动');
    return scene;
}
