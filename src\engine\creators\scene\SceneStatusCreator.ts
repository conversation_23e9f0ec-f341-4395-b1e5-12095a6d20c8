/**
 * Scene_Status 创建器
 * 专门用于创建状态场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';
import { type MenuBaseSceneOptions } from './SceneMenuBaseCreator';

/**
 * 状态场景创建选项
 */
export interface StatusSceneOptions extends MenuBaseSceneOptions {
    /** 角色ID */
    actorId?: number;
}

/**
 * 创建状态场景
 * @param options 创建选项
 * @returns 创建的状态场景实例
 */
export async function createSceneStatus(options: StatusSceneOptions = {}): Promise<any> {
    console.log('=== 创建状态场景 Scene_Status ===');
    
    try {
        // 预加载状态场景资源
        BaseSceneCreator.preloadSceneResources('Scene_Status');
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Status', defaultOptions);
        
        // Scene_Status 特定的设置
        if (options.actorId !== undefined && scene._actor) {
            console.log('设置状态场景角色ID:', options.actorId);
        }
        
        console.log('Scene_Status 创建完成');
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Status 失败:', error);
        throw error;
    }
}

/**
 * 创建并启动状态场景
 * @param options 创建选项
 * @returns 创建的状态场景实例
 */
export async function createAndStartSceneStatus(options: StatusSceneOptions = {}): Promise<any> {
    return createSceneStatus({ ...options, autoStart: true });
}

/**
 * 创建简单的状态场景（用于测试）
 * @returns 创建的状态场景实例
 */
export async function createSimpleSceneStatus(): Promise<any> {
    return createSceneStatus({
        actorId: 1,
        backgroundType: 1,
        autoStart: false,
        addToStage: true
    });
}
