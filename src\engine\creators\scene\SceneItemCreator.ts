/**
 * Scene_Item 创建器
 * 专门用于创建物品场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';
import { type ItemBaseSceneOptions } from './SceneItemBaseCreator';

/**
 * 物品场景创建选项
 */
export interface ItemSceneOptions extends ItemBaseSceneOptions {
    /** 初始选中的物品索引 */
    initialItemIndex?: number;
}

/**
 * 创建物品场景
 * @param options 创建选项
 * @returns 创建的物品场景实例
 */
export async function createSceneItem(options: ItemSceneOptions = {}): Promise<any> {
    console.log('=== 创建物品场景 Scene_Item ===');
    
    try {
        // 预加载物品场景资源
        BaseSceneCreator.preloadSceneResources('Scene_Item');
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Item', defaultOptions);
        
        // Scene_Item 特定的设置
        if (options.initialItemIndex !== undefined && scene._itemWindow) {
            scene._itemWindow.select(options.initialItemIndex);
            console.log('设置初始物品索引:', options.initialItemIndex);
        }
        
        console.log('Scene_Item 创建完成');
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Item 失败:', error);
        throw error;
    }
}

/**
 * 创建并启动物品场景
 * @param options 创建选项
 * @returns 创建的物品场景实例
 */
export async function createAndStartSceneItem(options: ItemSceneOptions = {}): Promise<any> {
    return createSceneItem({ ...options, autoStart: true });
}

/**
 * 创建简单的物品场景（用于测试）
 * @returns 创建的物品场景实例
 */
export async function createSimpleSceneItem(): Promise<any> {
    return createSceneItem({
        showHelpWindow: true,
        showCategoryWindow: true,
        initialCategory: 'item',
        initialItemIndex: 0,
        backgroundType: 1,
        autoStart: false,
        addToStage: true
    });
}
