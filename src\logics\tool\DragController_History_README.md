# 🎯 DragController 历史记录功能

## 📋 **功能概述**

DragController 现在支持完整的历史记录功能，可以记录对象拖动操作的历史，支持撤销/重做。

## 🔧 **核心功能**

### **1. 拖动历史记录**
- **拖动开始时**：创建历史记录操作组
- **拖动过程中**：实时更新对象位置（不记录中间状态）
- **拖动结束时**：记录最终的位置变化
- **取消拖动时**：不记录历史，直接结束操作组

### **2. 智能记录策略**
- **只记录实际变化**：如果位置没有变化，不会创建历史记录
- **分别记录X和Y**：X坐标和Y坐标分别记录，支持单轴拖动
- **操作组管理**：一次拖动操作作为一个操作组，撤销时整体恢复

## 🎯 **使用方法**

### **基本使用**

```typescript
import { DragController } from './DragController';
import { historyManager } from '../../historyManager';

// 创建拖动控制器
const dragController = new DragController(config, callbacks);

// 开始拖动（自动开始历史记录）
dragController.startDrag(object, 'center', mouseX, mouseY);

// 更新拖动（实时更新位置）
dragController.updateDrag(object, mouseX, mouseY);

// 结束拖动（自动记录历史）
dragController.endDrag(object);

// 测试撤销
historyManager.undo(); // 恢复到拖动前的位置
```

### **配置历史记录**

```typescript
// 禁用历史记录
dragController.setHistoryEnabled(false);

// 启用历史记录
dragController.setHistoryEnabled(true);

// 检查历史记录状态
const isEnabled = dragController.isHistoryEnabled();
const isActive = dragController.isHistoryOperationActive();
```

### **取消拖动**

```typescript
// 取消拖动（恢复原位置，不记录历史）
dragController.cancelDrag(object);
```

## 📊 **历史记录结构**

### **操作组示例**
```
操作组: "拖动UILabel"
├── 变更1: object.x = 100 → 150
└── 变更2: object.y = 200 → 250
```

### **单轴拖动示例**
```
操作组: "拖动UIButton"
└── 变更1: object.x = 100 → 200  (只有X轴变化)
```

## 🔍 **工作流程**

### **1. 拖动开始 (startDrag)**
```typescript
// 1. 记录初始位置
this.dragState.startObjectX = object.x;
this.dragState.startObjectY = object.y;

// 2. 开始历史记录操作组
if (this.enableHistory && historyManager.isRecording()) {
  historyManager.startGroup(`拖动${object.className}`);
  this.historyOperationStarted = true;
}
```

### **2. 拖动过程 (updateDrag)**
```typescript
// 实时更新对象位置（不记录历史）
object.x = newModelX;
object.y = newModelY;
```

### **3. 拖动结束 (endDrag)**
```typescript
// 1. 检查位置变化
const hasPositionChanged = (startX !== finalX) || (startY !== finalY);

// 2. 记录变化
if (hasPositionChanged) {
  if (startX !== finalX) {
    historyManager.recordChange(object, 'x', startX, finalX);
  }
  if (startY !== finalY) {
    historyManager.recordChange(object, 'y', startY, finalY);
  }
}

// 3. 结束操作组
historyManager.endGroup();
```

## 🧪 **测试场景**

### **场景1：正常拖动**
1. 拖动对象从 (100, 200) 到 (150, 250)
2. 按 Ctrl+Z 撤销
3. 对象应该回到 (100, 200)

### **场景2：单轴拖动**
1. 使用右箭头拖动，只改变X坐标
2. 按 Ctrl+Z 撤销
3. 只有X坐标恢复，Y坐标不变

### **场景3：取消拖动**
1. 开始拖动对象
2. 调用 `cancelDrag()`
3. 对象回到原位置，不产生历史记录

### **场景4：无变化拖动**
1. 拖动对象但最终位置没有变化
2. 不应该产生历史记录
3. 按 Ctrl+Z 不应该有任何效果

## ⚡ **性能优化**

### **1. 延迟记录**
- 拖动过程中不记录历史，只在结束时记录
- 避免产生大量中间状态的历史记录

### **2. 智能检测**
- 只记录实际发生变化的坐标轴
- 如果位置没有变化，不创建历史记录

### **3. 操作组管理**
- 一次拖动操作作为一个操作组
- 撤销时整体恢复，提供更好的用户体验

## 🔧 **API 参考**

### **历史记录控制方法**

```typescript
// 启用/禁用历史记录
setHistoryEnabled(enabled: boolean): void

// 获取历史记录启用状态
isHistoryEnabled(): boolean

// 获取当前是否有进行中的历史记录操作
isHistoryOperationActive(): boolean
```

### **拖动控制方法**

```typescript
// 开始拖动（自动开始历史记录）
startDrag(object: BaseObjectModel, dragType: ArrowType, mouseX: number, mouseY: number): void

// 更新拖动（实时更新位置）
updateDrag(object: BaseObjectModel, mouseX: number, mouseY: number): boolean

// 结束拖动（自动记录历史）
endDrag(object: BaseObjectModel): void

// 取消拖动（恢复原位置，不记录历史）
cancelDrag(object: BaseObjectModel): void
```

## 📝 **注意事项**

1. **历史记录依赖**：需要确保 `historyManager` 已正确初始化
2. **坐标系转换**：支持复杂的坐标系转换，确保记录的是模型坐标
3. **性能考虑**：拖动过程中不记录历史，只在结束时记录
4. **错误处理**：包含完整的错误处理和验证逻辑
5. **调试支持**：提供详细的控制台日志用于调试

现在 DragController 具备了完整的历史记录功能，用户可以轻松撤销和重做拖动操作！🎉
