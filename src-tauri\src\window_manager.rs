use tauri::Window;

/// 最小化窗口
#[tauri::command]
pub async fn minimize_window(window: Window) -> Result<(), String> {
    window.minimize().map_err(|e| e.to_string())
}

/// 最大化窗口
#[tauri::command]
pub async fn maximize_window(window: Window) -> Result<(), String> {
    window.maximize().map_err(|e| e.to_string())
}

/// 取消最大化窗口
#[tauri::command]
pub async fn unmaximize_window(window: Window) -> Result<(), String> {
    window.unmaximize().map_err(|e| e.to_string())
}

/// 关闭窗口
#[tauri::command]
pub async fn close_window(window: Window) -> Result<(), String> {
    window.close().map_err(|e| e.to_string())
}

/// 检查窗口是否最大化
#[tauri::command]
pub async fn is_window_maximized(window: Window) -> Result<bool, String> {
    window.is_maximized().map_err(|e| e.to_string())
}

/// 切换窗口最大化状态
#[tauri::command]
pub async fn toggle_maximize_window(window: Window) -> Result<bool, String> {
    let is_maximized = window.is_maximized().map_err(|e| e.to_string())?;

    if is_maximized {
        window.unmaximize().map_err(|e| e.to_string())?;
        Ok(false)
    } else {
        window.maximize().map_err(|e| e.to_string())?;
        Ok(true)
    }
}

/// 设置窗口大小
#[tauri::command]
pub async fn set_window_size(window: Window, width: f64, height: f64) -> Result<(), String> {
    use tauri::Size;
    let size = Size::Physical(tauri::PhysicalSize {
        width: width as u32,
        height: height as u32,
    });
    window.set_size(size).map_err(|e| e.to_string())
}

/// 设置窗口位置
#[tauri::command]
pub async fn set_window_position(window: Window, x: f64, y: f64) -> Result<(), String> {
    use tauri::Position;
    let position = Position::Physical(tauri::PhysicalPosition {
        x: x as i32,
        y: y as i32,
    });
    window.set_position(position).map_err(|e| e.to_string())
}

/// 获取窗口信息
#[tauri::command]
pub async fn get_window_info(window: Window) -> Result<WindowInfo, String> {
    let is_maximized = window.is_maximized().map_err(|e| e.to_string())?;
    let is_minimized = window.is_minimized().map_err(|e| e.to_string())?;
    let is_visible = window.is_visible().map_err(|e| e.to_string())?;
    let is_focused = window.is_focused().map_err(|e| e.to_string())?;

    Ok(WindowInfo {
        is_maximized,
        is_minimized,
        is_visible,
        is_focused,
    })
}

#[derive(serde::Serialize)]
pub struct WindowInfo {
    pub is_maximized: bool,
    pub is_minimized: bool,
    pub is_visible: bool,
    pub is_focused: bool,
}
