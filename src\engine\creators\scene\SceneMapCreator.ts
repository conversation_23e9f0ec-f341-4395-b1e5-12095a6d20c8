/**
 * Scene_Map 创建器
 * 专门用于创建地图场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';

declare global {
    interface Window {
        $gameMap: any;
        $gamePlayer: any;
        $dataMap: any;
        Spriteset_Map: any;
    }
}

/**
 * 地图场景创建选项
 */
export interface MapSceneOptions extends SceneCreationOptions {
    /** 地图ID */
    mapId?: number;
    /** 玩家起始X坐标 */
    playerX?: number;
    /** 玩家起始Y坐标 */
    playerY?: number;
    /** 玩家起始方向 */
    playerDirection?: number;
    /** 是否启用菜单 */
    menuEnabled?: boolean;
}

/**
 * 创建地图场景
 * @param options 创建选项
 * @returns 创建的地图场景实例
 */
export async function createSceneMap(options: MapSceneOptions = {}): Promise<any> {
    console.log('=== 创建地图场景 Scene_Map ===');
    
    try {
        // 预加载地图场景资源
        await preloadMapResources(options);
        
        // 设置地图数据（如果提供了地图ID）
        if (options.mapId !== undefined) {
            await setupMapData(options);
        }
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Map', defaultOptions);
        
        // Scene_Map 特定的设置
        console.log('Scene_Map 创建完成，场景属性:', {
            started: scene._started,
            active: scene._active,
            mapLoaded: scene._mapLoaded,
            hasSpriteset: !!scene._spriteset,
            hasMapNameWindow: !!scene._mapNameWindow
        });
        
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Map 失败:', error);
        throw error;
    }
}

/**
 * 预加载地图场景资源
 * @param options 地图场景选项
 */
async function preloadMapResources(options: MapSceneOptions): Promise<void> {
    console.log('预加载地图场景资源...');
    
    if (!window.ImageManager) {
        console.warn('ImageManager 未加载，跳过地图资源预加载');
        return;
    }
    
    try {
        // 预加载地图系统图片
        window.ImageManager.loadSystem('Window');
        window.ImageManager.loadSystem('IconSet');
        window.ImageManager.loadSystem('Shadow1');
        window.ImageManager.loadSystem('Balloon');
        
        // 预加载常用角色图片
        window.ImageManager.loadCharacter('Actor1');
        window.ImageManager.loadCharacter('Actor2');
        
        // 预加载常用图块集（如果有地图ID）
        if (options.mapId && window.$dataMap) {
            // 这里可以根据地图数据预加载对应的图块集
            console.log('预加载地图', options.mapId, '的图块集');
        }
        
        console.log('地图场景资源预加载完成');
        
    } catch (error) {
        console.error('预加载地图场景资源失败:', error);
        // 不抛出错误，允许场景创建继续
    }
}

/**
 * 设置地图数据
 * @param options 地图场景选项
 */
async function setupMapData(options: MapSceneOptions): Promise<void> {
    console.log('设置地图数据...');
    
    try {
        if (!window.DataManager) {
            console.warn('DataManager 未加载，跳过地图数据设置');
            return;
        }
        
        // 加载地图数据
        if (options.mapId !== undefined) {
            console.log('加载地图数据，地图ID:', options.mapId);
            
            // 这里通常需要等待地图数据加载完成
            if (window.DataManager.loadMapData) {
                window.DataManager.loadMapData(options.mapId);
                
                // 等待地图数据加载完成
                let loadCheckCount = 0;
                const maxLoadChecks = 100; // 最多等待10秒
                
                while (!window.DataManager.isMapLoaded() && loadCheckCount < maxLoadChecks) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    loadCheckCount++;
                }
                
                if (loadCheckCount >= maxLoadChecks) {
                    console.warn('地图数据加载超时');
                } else {
                    console.log('地图数据加载完成');
                }
            }
        }
        
        // 设置玩家位置
        if (window.$gamePlayer && (options.playerX !== undefined || options.playerY !== undefined)) {
            const x = options.playerX || 0;
            const y = options.playerY || 0;
            const direction = options.playerDirection || 2; // 默认向下
            
            console.log('设置玩家位置:', { x, y, direction });
            window.$gamePlayer.setPosition(x, y);
            window.$gamePlayer.setDirection(direction);
        }
        
        console.log('地图数据设置完成');
        
    } catch (error) {
        console.error('设置地图数据失败:', error);
    }
}

/**
 * 创建并启动地图场景
 * @param options 创建选项
 * @returns 创建的地图场景实例
 */
export async function createAndStartSceneMap(options: MapSceneOptions = {}): Promise<any> {
    console.log('=== 创建并启动 Scene_Map ===');
    
    const scene = await createSceneMap({
        ...options,
        autoStart: true
    });
    
    console.log('Scene_Map 已创建并启动');
    return scene;
}

/**
 * 创建测试地图场景
 * @returns 创建的地图场景实例
 */
export async function createTestSceneMap(): Promise<any> {
    console.log('=== 创建测试地图场景 ===');
    
    try {
        const scene = await createSceneMap({
            mapId: 1, // 使用第一个地图
            playerX: 8,
            playerY: 6,
            playerDirection: 2,
            menuEnabled: true,
            autoStart: false,
            addToStage: true
        });
        
        console.log('测试地图场景创建成功');
        return scene;
        
    } catch (error) {
        console.error('创建测试地图场景失败:', error);
        throw error;
    }
}
