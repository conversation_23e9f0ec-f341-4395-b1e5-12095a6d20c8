/**
 * 模型监控器 - 监控模型属性变化并通知渲染层更新
 * 实现模型驱动的响应式更新架构
 */

import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';

/**
 * 属性变化信息
 */
export interface PropertyChanges {
  position?: { x: number; y: number };
  scale?: { x: number; y: number };
  rotation?: number;
  size?: { width: number; height: number };
  visibility?: { alpha: number; visible: boolean };
  anchor?: { x: number; y: number };
  pivot?: { x: number; y: number };
  skew?: { x: number; y: number };
  zIndex?: number;
}

/**
 * 渲染更新回调函数类型
 */
export type RenderUpdateCallback = (changes: PropertyChanges, model: BaseObjectModel) => void;

/**
 * 批量更新配置
 */
export interface BatchUpdateConfig {
  enabled: boolean;
  delay: number; // 延迟时间（毫秒）
  maxBatchSize: number; // 最大批量大小
}

/**
 * 模型监控器类
 * 负责监控BaseObjectModel的属性变化，并通知渲染层进行更新
 */
export class ModelWatcher {
  private model: BaseObjectModel;
  private renderCallback: RenderUpdateCallback;
  private cleanup: (() => void) | null = null;
  private batchConfig: BatchUpdateConfig;
  private pendingChanges: PropertyChanges = {};
  private batchTimer: number | null = null;
  private isDestroyed = false;

  constructor(
    model: BaseObjectModel,
    renderCallback: RenderUpdateCallback,
    batchConfig: Partial<BatchUpdateConfig> = {}
  ) {
    this.model = model;
    this.renderCallback = renderCallback;
    this.batchConfig = {
      enabled: true,
      delay: 16, // 约60fps
      maxBatchSize: 10,
      ...batchConfig
    };

    this.setupWatcher();
    console.log('🔧 ModelWatcher: 创建监控器', {
      model: model.className,
      batchEnabled: this.batchConfig.enabled
    });
  }

  /**
   * 设置属性监控
   */
  private setupWatcher(): void {
    if (this.cleanup) {
      this.cleanup();
    }

    this.cleanup = $effect.root(() => {
      $effect(() => {
        if (this.isDestroyed) return;

        // 收集所有属性变化
        const changes: PropertyChanges = {};

        // 监听位置变化
        const currentPosition = { x: this.model.x, y: this.model.y };
        if (this.hasPositionChanged(currentPosition)) {
          changes.position = currentPosition;
        }

        // 监听缩放变化
        const currentScale = { x: this.model.scaleX, y: this.model.scaleY };
        if (this.hasScaleChanged(currentScale)) {
          changes.scale = currentScale;
        }

        // 监听旋转变化
        if (this.hasRotationChanged(this.model.rotation)) {
          changes.rotation = this.model.rotation;
        }

        // 监听尺寸变化
        const currentSize = { width: this.model.width, height: this.model.height };
        if (this.hasSizeChanged(currentSize)) {
          changes.size = currentSize;
        }

        // 监听可见性变化
        const currentVisibility = { alpha: this.model.alpha, visible: this.model.visible };
        if (this.hasVisibilityChanged(currentVisibility)) {
          changes.visibility = currentVisibility;
        }

        // 监听锚点变化
        const currentAnchor = { x: this.model.anchorX, y: this.model.anchorY };
        if (this.hasAnchorChanged(currentAnchor)) {
          changes.anchor = currentAnchor;
        }

        // 监听轴心变化
        const currentPivot = { x: this.model.pivotX, y: this.model.pivotY };
        if (this.hasPivotChanged(currentPivot)) {
          changes.pivot = currentPivot;
        }

        // 监听倾斜变化
        const currentSkew = { x: this.model.skewX, y: this.model.skewY };
        if (this.hasSkewChanged(currentSkew)) {
          changes.skew = currentSkew;
        }

        // 监听层级变化
        if (this.hasZIndexChanged(this.model.zIndex)) {
          changes.zIndex = this.model.zIndex;
        }

        // 如果有变化，触发更新
        if (Object.keys(changes).length > 0) {
          this.handlePropertyChanges(changes);
        }
      });

      // 返回清理函数
      return () => {
        console.log('🔧 ModelWatcher: 清理响应式效果');
        this.clearBatchTimer();
      };
    });
  }

  /**
   * 处理属性变化
   */
  private handlePropertyChanges(changes: PropertyChanges): void {
    if (this.batchConfig.enabled) {
      // 批量更新模式
      this.addToBatch(changes);
    } else {
      // 立即更新模式（高性能）
      this.executeUpdateImmediate(changes);
    }
  }

  /**
   * 立即执行更新（高性能版本）
   */
  private executeUpdateImmediate(changes: PropertyChanges): void {
    if (this.isDestroyed) return;

    try {
      // 直接调用回调，跳过日志记录以提升性能
      this.renderCallback(changes, this.model);
    } catch (error) {
      console.error('🔧 ModelWatcher: 立即更新失败', error);
    }
  }

  /**
   * 添加到批量更新队列
   */
  private addToBatch(changes: PropertyChanges): void {
    // 合并变化
    Object.assign(this.pendingChanges, changes);

    // 清除之前的定时器
    this.clearBatchTimer();

    // 设置新的定时器
    this.batchTimer = window.setTimeout(() => {
      if (Object.keys(this.pendingChanges).length > 0) {
        this.executeUpdate(this.pendingChanges);
        this.pendingChanges = {};
      }
      this.batchTimer = null;
    }, this.batchConfig.delay);
  }

  /**
   * 执行更新
   */
  private executeUpdate(changes: PropertyChanges): void {
    if (this.isDestroyed) return;

    try {
      console.log('🔧 ModelWatcher: 执行属性更新', {
        model: this.model.className,
        changes: Object.keys(changes)
      });

      this.renderCallback(changes, this.model);
    } catch (error) {
      console.error('🔧 ModelWatcher: 更新回调执行失败', error);
    }
  }

  /**
   * 清除批量更新定时器
   */
  private clearBatchTimer(): void {
    if (this.batchTimer !== null) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }
  }

  // 以下是属性变化检测方法
  private lastPosition: { x: number; y: number } | null = null;
  private hasPositionChanged(current: { x: number; y: number }): boolean {
    if (!this.lastPosition ||
        this.lastPosition.x !== current.x ||
        this.lastPosition.y !== current.y) {
      this.lastPosition = { ...current };
      return true;
    }
    return false;
  }

  private lastScale: { x: number; y: number } | null = null;
  private hasScaleChanged(current: { x: number; y: number }): boolean {
    if (!this.lastScale ||
        this.lastScale.x !== current.x ||
        this.lastScale.y !== current.y) {
      this.lastScale = { ...current };
      return true;
    }
    return false;
  }

  private lastRotation: number | null = null;
  private hasRotationChanged(current: number): boolean {
    if (this.lastRotation !== current) {
      this.lastRotation = current;
      return true;
    }
    return false;
  }

  private lastSize: { width: number; height: number } | null = null;
  private hasSizeChanged(current: { width: number; height: number }): boolean {
    if (!this.lastSize ||
        this.lastSize.width !== current.width ||
        this.lastSize.height !== current.height) {
      this.lastSize = { ...current };
      return true;
    }
    return false;
  }

  private lastVisibility: { alpha: number; visible: boolean } | null = null;
  private hasVisibilityChanged(current: { alpha: number; visible: boolean }): boolean {
    if (!this.lastVisibility ||
        this.lastVisibility.alpha !== current.alpha ||
        this.lastVisibility.visible !== current.visible) {
      this.lastVisibility = { ...current };
      return true;
    }
    return false;
  }

  private lastAnchor: { x: number; y: number } | null = null;
  private hasAnchorChanged(current: { x: number; y: number }): boolean {
    if (!this.lastAnchor ||
        this.lastAnchor.x !== current.x ||
        this.lastAnchor.y !== current.y) {
      this.lastAnchor = { ...current };
      return true;
    }
    return false;
  }

  private lastPivot: { x: number; y: number } | null = null;
  private hasPivotChanged(current: { x: number; y: number }): boolean {
    if (!this.lastPivot ||
        this.lastPivot.x !== current.x ||
        this.lastPivot.y !== current.y) {
      this.lastPivot = { ...current };
      return true;
    }
    return false;
  }

  private lastSkew: { x: number; y: number } | null = null;
  private hasSkewChanged(current: { x: number; y: number }): boolean {
    if (!this.lastSkew ||
        this.lastSkew.x !== current.x ||
        this.lastSkew.y !== current.y) {
      this.lastSkew = { ...current };
      return true;
    }
    return false;
  }

  private lastZIndex: number | null = null;
  private hasZIndexChanged(current: number): boolean {
    if (this.lastZIndex !== current) {
      this.lastZIndex = current;
      return true;
    }
    return false;
  }

  /**
   * 强制刷新所有属性
   */
  public forceRefresh(): void {
    // 重置所有缓存的值，强制下次检测时认为所有属性都发生了变化
    this.lastPosition = null;
    this.lastScale = null;
    this.lastRotation = null;
    this.lastSize = null;
    this.lastVisibility = null;
    this.lastAnchor = null;
    this.lastPivot = null;
    this.lastSkew = null;
    this.lastZIndex = null;

    console.log('🔧 ModelWatcher: 强制刷新所有属性');
  }

  /**
   * 立即执行待处理的批量更新
   */
  public flushPendingUpdates(): void {
    if (this.batchTimer !== null) {
      this.clearBatchTimer();
      if (Object.keys(this.pendingChanges).length > 0) {
        this.executeUpdate(this.pendingChanges);
        this.pendingChanges = {};
      }
    }
  }

  /**
   * 更新批量配置
   */
  public updateBatchConfig(config: Partial<BatchUpdateConfig>): void {
    Object.assign(this.batchConfig, config);
    console.log('🔧 ModelWatcher: 更新批量配置', this.batchConfig);
  }

  /**
   * 销毁监控器
   */
  public destroy(): void {
    this.isDestroyed = true;

    // 清理响应式效果
    if (this.cleanup) {
      this.cleanup();
      this.cleanup = null;
    }

    // 清理定时器
    this.clearBatchTimer();

    // 执行最后的待处理更新
    if (Object.keys(this.pendingChanges).length > 0) {
      this.executeUpdate(this.pendingChanges);
      this.pendingChanges = {};
    }

    console.log('🔧 ModelWatcher: 监控器已销毁', this.model.className);
  }

  /**
   * 获取当前模型
   */
  public getModel(): BaseObjectModel {
    return this.model;
  }

  /**
   * 检查是否已销毁
   */
  public isDestroyed(): boolean {
    return this.isDestroyed;
  }
}
