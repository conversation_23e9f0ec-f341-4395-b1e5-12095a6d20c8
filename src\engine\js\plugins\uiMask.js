/**
 * UIMask - 通用遮罩组件
 * 采用对象绑定模式，类似UISlider
 */

(() => {
    'use strict';

    console.log('🎭 开始加载 UIMask 插件');

    /**
     * UIMask - 通用遮罩组件
     */
    class UIMask extends PIXI.Container {
        constructor(properties = {}) {
            super();

            console.log('🎭 UIMask: 创建遮罩组件', properties);

            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'UIMask';

            // 遮罩属性
            this.maskType = properties.maskType || 'rectangle'; // 'rectangle', 'circle', 'image'
            this.maskImage = properties.maskImage || '';        // 图片路径
            
            // 位置和尺寸
            this.offsetX = properties.offsetX || 0;
            this.offsetY = properties.offsetY || 0;
            this.maskWidth = properties.maskWidth || 200;
            this.maskHeight = properties.maskHeight || 200;

            // 绑定的目标对象
            this.boundTarget = null;
            this.maskObject = null; // 实际的遮罩对象

            // 初始化
            this.createMaskObject();
            this.setupEventListeners();

            console.log('✅ UIMask: 遮罩组件创建完成', {
                maskType: this.maskType,
                size: { width: this.maskWidth, height: this.maskHeight },
                offset: { x: this.offsetX, y: this.offsetY }
            });
        }

        /**
         * 创建遮罩对象
         */
        createMaskObject() {
            // 清除旧的遮罩对象
            if (this.maskObject) {
                this.removeChild(this.maskObject);
                this.maskObject = null;
            }

            switch (this.maskType) {
                case 'rectangle':
                    this.createRectangleMask();
                    break;
                case 'circle':
                    this.createCircleMask();
                    break;
                case 'image':
                    this.createImageMask();
                    break;
                default:
                    console.warn('UIMask: 未知的遮罩类型', this.maskType);
                    this.createRectangleMask();
            }

            console.log('🎭 UIMask: 遮罩对象创建完成', this.maskType);
        }

        /**
         * 创建矩形遮罩
         */
        createRectangleMask() {
            this.maskObject = new PIXI.Graphics();
            this.maskObject.beginFill(0xffffff);
            this.maskObject.drawRect(0, 0, this.maskWidth, this.maskHeight);
            this.maskObject.endFill();
            this.addChild(this.maskObject);
        }

        /**
         * 创建圆形遮罩
         */
        createCircleMask() {
            this.maskObject = new PIXI.Graphics();
            this.maskObject.beginFill(0xffffff);
            const radius = Math.min(this.maskWidth, this.maskHeight) / 2;
            this.maskObject.drawCircle(this.maskWidth / 2, this.maskHeight / 2, radius);
            this.maskObject.endFill();
            this.addChild(this.maskObject);
        }

        /**
         * 创建图片遮罩
         */
        createImageMask() {
            if (!this.maskImage) {
                console.warn('UIMask: 图片遮罩需要指定图片路径');
                this.createRectangleMask();
                return;
            }

            try {
                // 使用ImageManager加载图片
                const bitmap = ImageManager.loadBitmapFromUrl(this.maskImage);
                this.maskObject = new PIXI.Sprite(bitmap._canvas ? bitmap._canvas : bitmap);
                
                // 设置尺寸
                this.maskObject.width = this.maskWidth;
                this.maskObject.height = this.maskHeight;
                
                this.addChild(this.maskObject);
                
                console.log('🎭 UIMask: 图片遮罩加载完成', this.maskImage);
            } catch (error) {
                console.error('UIMask: 图片遮罩加载失败', error);
                this.createRectangleMask();
            }
        }

        /**
         * 绑定目标对象
         */
        bindTarget(targetObject) {
            console.log('🎭 UIMask: 绑定目标对象', targetObject?.constructor.name);

            // 解除之前的绑定
            this.unbindTarget();

            if (!targetObject) {
                console.warn('UIMask: 目标对象为空');
                return false;
            }

            this.boundTarget = targetObject;
            this.applyMask();
            
            console.log('✅ UIMask: 目标对象绑定成功');
            return true;
        }

        /**
         * 解除绑定
         */
        unbindTarget() {
            if (this.boundTarget && this.maskObject) {
                console.log('🎭 UIMask: 解除目标对象绑定');
                this.boundTarget.mask = null;
                this.boundTarget = null;
            }
        }

        /**
         * 应用遮罩到目标对象
         */
        applyMask() {
            if (!this.boundTarget || !this.maskObject) {
                console.warn('UIMask: 无法应用遮罩，缺少目标对象或遮罩对象');
                return;
            }

            // 设置遮罩位置
            this.updateMaskPosition();

            // 应用遮罩
            this.boundTarget.mask = this.maskObject;
            
            console.log('🎭 UIMask: 遮罩已应用到目标对象', {
                targetType: this.boundTarget.constructor.name,
                maskPosition: { x: this.maskObject.x, y: this.maskObject.y },
                maskSize: { width: this.maskWidth, height: this.maskHeight }
            });
        }

        /**
         * 更新遮罩位置
         */
        updateMaskPosition() {
            if (!this.maskObject || !this.boundTarget) return;

            // 计算遮罩的全局位置
            const targetGlobalPos = this.boundTarget.toGlobal(new PIXI.Point(0, 0));
            const maskGlobalPos = this.toGlobal(new PIXI.Point(this.offsetX, this.offsetY));

            // 设置遮罩位置
            this.maskObject.x = this.offsetX;
            this.maskObject.y = this.offsetY;

            console.log('🎭 UIMask: 遮罩位置已更新', {
                offset: { x: this.offsetX, y: this.offsetY },
                maskPos: { x: this.maskObject.x, y: this.maskObject.y }
            });
        }

        /**
         * 更新遮罩尺寸
         */
        updateMaskSize() {
            console.log('🎭 UIMask: 更新遮罩尺寸', { width: this.maskWidth, height: this.maskHeight });
            
            // 重新创建遮罩对象
            this.createMaskObject();
            
            // 如果有绑定的目标，重新应用遮罩
            if (this.boundTarget) {
                this.applyMask();
            }
        }

        /**
         * 更新遮罩类型
         */
        updateMaskType() {
            console.log('🎭 UIMask: 更新遮罩类型', this.maskType);
            
            // 重新创建遮罩对象
            this.createMaskObject();
            
            // 如果有绑定的目标，重新应用遮罩
            if (this.boundTarget) {
                this.applyMask();
            }
        }

        /**
         * 设置事件监听器
         */
        setupEventListeners() {
            // 监听位置变化
            this.on('positionChanged', () => {
                if (this.boundTarget) {
                    this.updateMaskPosition();
                }
            });

            console.log('✅ UIMask: 事件监听器设置完成');
        }

        /**
         * 获取遮罩信息
         */
        getMaskInfo() {
            return {
                maskType: this.maskType,
                maskImage: this.maskImage,
                offset: { x: this.offsetX, y: this.offsetY },
                size: { width: this.maskWidth, height: this.maskHeight },
                hasBoundTarget: !!this.boundTarget,
                targetType: this.boundTarget?.constructor.name
            };
        }

        /**
         * 克隆遮罩对象
         */
        clone(options = {}) {
            console.log('🔄 UIMask: 开始克隆遮罩对象');

            const clonedMask = new UIMask({
                maskType: this.maskType,
                maskImage: this.maskImage,
                offsetX: this.offsetX + (options.offsetX || 0),
                offsetY: this.offsetY + (options.offsetY || 0),
                maskWidth: this.maskWidth,
                maskHeight: this.maskHeight
            });

            // 设置基础属性
            clonedMask.name = this.name + '_Clone';
            clonedMask.x = this.x + (options.offsetPosition ? (options.offsetX || 20) : 0);
            clonedMask.y = this.y + (options.offsetPosition ? (options.offsetY || 20) : 0);
            clonedMask.visible = this.visible;
            clonedMask.alpha = this.alpha;

            console.log('✅ UIMask: 遮罩对象克隆完成');
            return clonedMask;
        }

        /**
         * 销毁遮罩
         */
        destroy() {
            console.log('💥 UIMask: 销毁遮罩对象');
            
            // 解除绑定
            this.unbindTarget();
            
            // 销毁遮罩对象
            if (this.maskObject) {
                this.maskObject.destroy();
                this.maskObject = null;
            }

            super.destroy();
        }
    }

    // 导出到全局
    window.UIMask = UIMask;
    console.log('✅ UIMask 插件加载完成');

})();
