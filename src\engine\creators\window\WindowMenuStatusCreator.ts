/**
 * Window_MenuStatus 创建器
 * 专门用于创建菜单状态窗口，显示队伍成员信息
 */

import { BaseWindowCreator, type WindowCreationOptions } from './WindowCreator';
import { type BaseWindowOptions } from './WindowBaseCreator';

declare global {
    interface Window {
        Window_MenuStatus: any;
        $gameParty: any;
        $gameActors: any;
        $dataActors: any;
    }
}

/**
 * 菜单状态窗口创建选项
 */
export interface MenuStatusWindowOptions extends BaseWindowOptions {
    /** 是否显示角色头像 */
    showFace?: boolean;
    /** 是否显示角色名称 */
    showName?: boolean;
    /** 是否显示等级 */
    showLevel?: boolean;
    /** 是否显示HP/MP */
    showParams?: boolean;
    /** 每行显示的角色数 */
    numVisibleRows?: number;
    /** 是否可选择角色 */
    selectable?: boolean;
}

/**
 * 创建菜单状态窗口
 * @param options 创建选项
 * @returns 创建的菜单状态窗口实例
 */
export async function createWindowMenuStatus(options: MenuStatusWindowOptions = {}): Promise<any> {
    console.log('=== 创建菜单状态窗口 Window_MenuStatus ===');
    
    try {
        // 预加载资源
        BaseWindowCreator.preloadWindowResources('Window_MenuStatus');
        
        // 设置默认选项
        const defaultOptions: WindowCreationOptions = {
            autoOpen: true,
            addToStage: true,
            rect: options.rect || { x: 0, y: 0, width: 480, height: 400 },
            ...options
        };
        
        // 创建窗口实例
        const window = await BaseWindowCreator.createWindowInstance('Window_MenuStatus', defaultOptions);
        
        // Window_MenuStatus 特定的设置
        setupMenuStatusWindow(window, options);
        
        // 确保窗口有角色数据显示
        await ensureMenuStatusContent(window, options);
        
        console.log('Window_MenuStatus 创建完成，窗口属性:', {
            x: window.x,
            y: window.y,
            width: window.width,
            height: window.height,
            maxItems: window.maxItems ? window.maxItems() : 0,
            visible: window.visible,
            openness: window.openness
        });
        
        return window;
        
    } catch (error) {
        console.error('创建 Window_MenuStatus 失败:', error);
        throw error;
    }
}

/**
 * 设置菜单状态窗口属性
 * @param window 窗口实例
 * @param options 菜单状态窗口选项
 */
function setupMenuStatusWindow(window: any, options: MenuStatusWindowOptions): void {
    console.log('设置菜单状态窗口属性...');
    
    try {
        // 设置可选择性
        if (options.selectable !== undefined) {
            if (options.selectable && window.activate && typeof window.activate === 'function') {
                window.activate();
                console.log('菜单状态窗口已激活');
            } else if (!options.selectable && window.deactivate && typeof window.deactivate === 'function') {
                window.deactivate();
                console.log('菜单状态窗口已取消激活');
            }
        }
        
        // 设置初始选中索引
        if (options.initialIndex !== undefined && window.select && typeof window.select === 'function') {
            window.select(options.initialIndex);
            console.log('设置初始选中角色索引:', options.initialIndex);
        }
        
        console.log('菜单状态窗口属性设置完成');
        
    } catch (error) {
        console.error('设置菜单状态窗口属性失败:', error);
    }
}

/**
 * 确保菜单状态窗口有角色数据显示
 * @param window 窗口实例
 * @param options 菜单状态窗口选项
 */
async function ensureMenuStatusContent(window: any, options: MenuStatusWindowOptions): Promise<void> {
    console.log('确保菜单状态窗口有角色数据显示...');
    
    try {
        // 检查游戏数据是否存在
        if (!window.$gameParty) {
            console.warn('$gameParty 不存在，无法显示角色数据');
            return;
        }
        
        // 获取队伍成员
        const members = window.$gameParty.allMembers ? window.$gameParty.allMembers() : [];
        console.log('队伍成员数量:', members.length);
        
        if (members.length === 0) {
            console.warn('队伍中没有成员，尝试创建测试角色数据');
            await createTestPartyMembers();
        }
        
        // 强制刷新窗口内容
        if (window.refresh && typeof window.refresh === 'function') {
            window.refresh();
            console.log('菜单状态窗口内容已刷新');
        }
        
        // 确保窗口可见且打开
        if (window.visible !== undefined) {
            window.visible = true;
        }
        
        if (window.openness !== undefined) {
            window.openness = 255; // 完全打开
        }
        
        // 检查窗口项目数量
        const itemCount = window.maxItems ? window.maxItems() : 0;
        console.log('状态窗口项目数量:', itemCount);
        
        // 如果窗口有 drawAllItems 方法，调用它来绘制所有项目
        if (window.drawAllItems && typeof window.drawAllItems === 'function') {
            window.drawAllItems();
            console.log('已绘制所有角色状态项目');
        }
        
    } catch (error) {
        console.error('确保菜单状态窗口内容失败:', error);
    }
}

/**
 * 创建测试队伍成员（如果不存在）
 */
async function createTestPartyMembers(): Promise<void> {
    console.log('创建测试队伍成员...');
    
    try {
        // 检查是否有 $gameActors 和 $dataActors
        if (!window.$gameActors || !window.$dataActors) {
            console.warn('游戏角色数据系统不存在，无法创建测试成员');
            return;
        }
        
        // 检查是否有角色数据
        if (!window.$dataActors[1]) {
            console.warn('没有角色数据，无法创建测试成员');
            return;
        }
        
        // 确保队伍中至少有一个成员
        if (window.$gameParty && window.$gameParty.addActor && typeof window.$gameParty.addActor === 'function') {
            // 添加第一个角色到队伍
            window.$gameParty.addActor(1);
            console.log('已添加角色1到队伍');
            
            // 如果有更多角色数据，也添加进来
            for (let i = 2; i <= 4; i++) {
                if (window.$dataActors[i]) {
                    window.$gameParty.addActor(i);
                    console.log(`已添加角色${i}到队伍`);
                }
            }
        }
        
    } catch (error) {
        console.error('创建测试队伍成员失败:', error);
    }
}

/**
 * 创建并激活菜单状态窗口
 * @param options 创建选项
 * @returns 创建的菜单状态窗口实例
 */
export async function createAndActivateWindowMenuStatus(options: MenuStatusWindowOptions = {}): Promise<any> {
    console.log('=== 创建并激活 Window_MenuStatus ===');
    
    const window = await createWindowMenuStatus({
        ...options,
        autoOpen: true,
        selectable: true
    });
    
    console.log('Window_MenuStatus 已创建并激活');
    return window;
}

/**
 * 创建简单的菜单状态窗口（用于测试）
 * @returns 创建的菜单状态窗口实例
 */
export async function createSimpleWindowMenuStatus(): Promise<any> {
    console.log('=== 创建简单菜单状态窗口 ===');
    
    try {
        const window = await createWindowMenuStatus({
            rect: { x: 300, y: 100, width: 480, height: 400 },
            autoOpen: true,
            addToStage: true,
            visible: true,
            selectable: true,
            showFace: true,
            showName: true,
            showLevel: true,
            showParams: true,
            numVisibleRows: 4
        });
        
        console.log('简单菜单状态窗口创建成功');
        return window;
        
    } catch (error) {
        console.error('创建简单菜单状态窗口失败:', error);
        throw error;
    }
}
