<script lang="ts">
    import { PropertyContainer } from '../../components/accordionPanel';
    import Checkbox from '../../components/Checkbox.svelte';
    import SafeInput from '../../components/SafeInput.svelte';
    import Select from '../../components/Select.svelte';
    import Label from '../../components/Label.svelte';
    import type { LayoutModel } from '../../type/ui/layoutModel.svelte';

    interface Props {
        model: LayoutModel;
    }

    let { model }: Props = $props();

    // 布局类型选项
    const layoutTypeOptions = [
        { value: 'vertical', label: '垂直布局' },
        { value: 'horizontal', label: '水平布局' },
        { value: 'grid', label: '网格布局' }
    ];

    // 是否显示网格特定选项
    let showGridOptions = $derived(model?.layoutType === 'grid');
</script>

<!-- 布局类型 -->
<PropertyContainer>
    <Label text="布局类型:" />
    <Select
        options={layoutTypeOptions}
        bind:value={model.layoutType}
    />
</PropertyContainer>

<!-- 间距设置 -->
<PropertyContainer>
    <Label text="间距:" />
    <SafeInput
        value={model.spacing.toString()}
        type="number"
        onchange={(e: any) => {
            const newValue = parseInt(e.target.value) || 0;
            console.log('📐 LayoutPropertyPanel: 间距变更', model.spacing, '→', newValue);
            model.spacing = newValue;
        }}
    />
</PropertyContainer>

<!-- 内边距 -->
<PropertyContainer>
    <Label text="内边距:" />
    <SafeInput
        value={model.padding.toString()}
        type="number"
        onchange={(e: any) => model.padding = parseInt(e.target.value) || 0}
    />
</PropertyContainer>

<!-- 网格布局特定设置 -->
{#if showGridOptions}
    <PropertyContainer>
        <Label text="网格列数:" />
        <SafeInput
            value={model.columns.toString()}
            type="number"
            onchange={(e: any) => model.columns = parseInt(e.target.value) || 1}
        />
    </PropertyContainer>
{/if}

<!-- 自动更新 -->
<PropertyContainer>
    <Label text="自动更新:" />
    <Checkbox bind:checked={model.autoUpdate} />
</PropertyContainer>


