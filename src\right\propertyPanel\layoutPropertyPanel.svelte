<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import PropertyContainer from './propertyContainer.svelte';
    import Select from './select.svelte';
    import SafeInput from './safeInput.svelte';
    import Checkbox from './checkbox.svelte';
    import type { LayoutModel } from '../../type/ui/layoutModel.svelte';

    export let model: LayoutModel | null = null;

    // 布局类型选项
    const layoutTypeOptions = [
        { value: 'vertical', label: '垂直布局' },
        { value: 'horizontal', label: '水平布局' },
        { value: 'grid', label: '网格布局' }
    ];

    // 对齐方式选项
    const alignmentOptions = [
        { value: 'start', label: '开始' },
        { value: 'center', label: '居中' },
        { value: 'end', label: '结束' },
        { value: 'space-between', label: '两端对齐' },
        { value: 'space-around', label: '环绕对齐' },
        { value: 'stretch', label: '拉伸' }
    ];

    // 是否显示网格特定选项
    $: showGridOptions = model?.layoutType === 'grid';
    
    // 是否显示高级间距选项
    let showAdvancedSpacing = false;

    function handleLayoutTypeChange(event: CustomEvent) {
        if (model) {
            model.layoutType = event.detail.value;
            console.log('📐 LayoutPropertyPanel: 布局类型变更为', event.detail.value);
        }
    }

    function handleSpacingChange(event: CustomEvent) {
        if (model) {
            const value = parseInt(event.detail.value) || 0;
            model.spacing = value;
            console.log('📐 LayoutPropertyPanel: 间距变更为', value);
        }
    }

    function handleHorizontalSpacingChange(event: CustomEvent) {
        if (model) {
            const value = parseInt(event.detail.value) || 0;
            model.horizontalSpacing = value;
            console.log('📐 LayoutPropertyPanel: 水平间距变更为', value);
        }
    }

    function handleVerticalSpacingChange(event: CustomEvent) {
        if (model) {
            const value = parseInt(event.detail.value) || 0;
            model.verticalSpacing = value;
            console.log('📐 LayoutPropertyPanel: 垂直间距变更为', value);
        }
    }

    function handlePaddingChange(event: CustomEvent) {
        if (model) {
            const value = parseInt(event.detail.value) || 0;
            model.padding = value;
            console.log('📐 LayoutPropertyPanel: 内边距变更为', value);
        }
    }

    function handleColumnsChange(event: CustomEvent) {
        if (model) {
            const value = parseInt(event.detail.value) || 1;
            model.columns = Math.max(1, value);
            console.log('📐 LayoutPropertyPanel: 网格列数变更为', value);
        }
    }

    function handleRowsChange(event: CustomEvent) {
        if (model) {
            const value = parseInt(event.detail.value) || 0;
            model.rows = Math.max(0, value);
            console.log('📐 LayoutPropertyPanel: 网格行数变更为', value);
        }
    }

    function handleMainAxisAlignmentChange(event: CustomEvent) {
        if (model) {
            model.mainAxisAlignment = event.detail.value;
            console.log('📐 LayoutPropertyPanel: 主轴对齐变更为', event.detail.value);
        }
    }

    function handleCrossAxisAlignmentChange(event: CustomEvent) {
        if (model) {
            model.crossAxisAlignment = event.detail.value;
            console.log('📐 LayoutPropertyPanel: 交叉轴对齐变更为', event.detail.value);
        }
    }

    function handleContainerWidthChange(event: CustomEvent) {
        if (model) {
            const value = parseInt(event.detail.value) || 0;
            model.containerWidth = Math.max(0, value);
            console.log('📐 LayoutPropertyPanel: 容器宽度变更为', value);
        }
    }

    function handleContainerHeightChange(event: CustomEvent) {
        if (model) {
            const value = parseInt(event.detail.value) || 0;
            model.containerHeight = Math.max(0, value);
            console.log('📐 LayoutPropertyPanel: 容器高度变更为', value);
        }
    }

    function handleAutoUpdateChange(event: CustomEvent) {
        if (model) {
            model.autoUpdate = event.detail.checked;
            console.log('📐 LayoutPropertyPanel: 自动更新变更为', event.detail.checked);
        }
    }

    function toggleAdvancedSpacing() {
        showAdvancedSpacing = !showAdvancedSpacing;
        console.log('📐 LayoutPropertyPanel: 切换高级间距选项', showAdvancedSpacing);
    }

    function resetLayout() {
        if (!model) return;
        
        console.log('📐 LayoutPropertyPanel: 重置布局设置');
        
        model.layoutType = 'vertical';
        model.spacing = 5;
        model.padding = 0;
        model.columns = 2;
        model.rows = 0;
        model.mainAxisAlignment = 'start';
        model.crossAxisAlignment = 'start';
        model.containerWidth = 0;
        model.containerHeight = 0;
        model.autoUpdate = true;
    }

    onMount(() => {
        console.log('📐 LayoutPropertyPanel: 组件挂载', model?.constructor.name);
    });

    onDestroy(() => {
        console.log('📐 LayoutPropertyPanel: 组件销毁');
    });
</script>

<div class="layout-property-panel">
    <h3>布局设置</h3>
    
    {#if model}
        <!-- 基础布局设置 -->
        <PropertyContainer label="布局类型">
            <Select 
                options={layoutTypeOptions}
                value={model.layoutType}
                on:change={handleLayoutTypeChange}
            />
        </PropertyContainer>

        <!-- 间距设置 -->
        <PropertyContainer label="间距">
            <SafeInput 
                type="number"
                value={model.spacing}
                min="0"
                max="100"
                on:change={handleSpacingChange}
            />
            <button 
                class="toggle-advanced" 
                on:click={toggleAdvancedSpacing}
                title="切换高级间距选项"
            >
                {showAdvancedSpacing ? '简单' : '高级'}
            </button>
        </PropertyContainer>

        {#if showAdvancedSpacing}
            <PropertyContainer label="水平间距">
                <SafeInput 
                    type="number"
                    value={model.horizontalSpacing}
                    min="0"
                    max="100"
                    on:change={handleHorizontalSpacingChange}
                />
            </PropertyContainer>

            <PropertyContainer label="垂直间距">
                <SafeInput 
                    type="number"
                    value={model.verticalSpacing}
                    min="0"
                    max="100"
                    on:change={handleVerticalSpacingChange}
                />
            </PropertyContainer>
        {/if}

        <PropertyContainer label="内边距">
            <SafeInput 
                type="number"
                value={model.padding}
                min="0"
                max="50"
                on:change={handlePaddingChange}
            />
        </PropertyContainer>

        <!-- 网格布局特定设置 -->
        {#if showGridOptions}
            <PropertyContainer label="网格列数">
                <SafeInput 
                    type="number"
                    value={model.columns}
                    min="1"
                    max="10"
                    on:change={handleColumnsChange}
                />
            </PropertyContainer>

            <PropertyContainer label="网格行数">
                <SafeInput 
                    type="number"
                    value={model.rows}
                    min="0"
                    max="10"
                    placeholder="0=自动"
                    on:change={handleRowsChange}
                />
            </PropertyContainer>
        {/if}

        <!-- 对齐设置 -->
        <PropertyContainer label="主轴对齐">
            <Select 
                options={alignmentOptions.filter(opt => 
                    model.layoutType === 'grid' ? 
                    ['start', 'center', 'end'].includes(opt.value) : 
                    true
                )}
                value={model.mainAxisAlignment}
                on:change={handleMainAxisAlignmentChange}
            />
        </PropertyContainer>

        <PropertyContainer label="交叉轴对齐">
            <Select 
                options={alignmentOptions.filter(opt => 
                    ['start', 'center', 'end', 'stretch'].includes(opt.value)
                )}
                value={model.crossAxisAlignment}
                on:change={handleCrossAxisAlignmentChange}
            />
        </PropertyContainer>

        <!-- 容器尺寸设置 -->
        <PropertyContainer label="容器宽度">
            <SafeInput 
                type="number"
                value={model.containerWidth}
                min="0"
                max="1000"
                placeholder="0=自动"
                on:change={handleContainerWidthChange}
            />
        </PropertyContainer>

        <PropertyContainer label="容器高度">
            <SafeInput 
                type="number"
                value={model.containerHeight}
                min="0"
                max="1000"
                placeholder="0=自动"
                on:change={handleContainerHeightChange}
            />
        </PropertyContainer>

        <!-- 高级设置 -->
        <PropertyContainer label="自动更新">
            <Checkbox 
                checked={model.autoUpdate}
                on:change={handleAutoUpdateChange}
            />
        </PropertyContainer>

        <!-- 操作按钮 -->
        <div class="layout-actions">
            <button class="reset-button" on:click={resetLayout}>
                重置布局
            </button>
        </div>
    {:else}
        <p class="no-model">请选择一个布局对象</p>
    {/if}
</div>

<style>
    .layout-property-panel {
        padding: 10px;
        background: #2a2a2a;
        border-radius: 4px;
        color: white;
        font-size: 12px;
    }

    h3 {
        margin: 0 0 15px 0;
        color: #ffffff;
        font-size: 14px;
        font-weight: bold;
        border-bottom: 1px solid #444;
        padding-bottom: 5px;
    }

    .toggle-advanced {
        background: #444;
        color: white;
        border: 1px solid #666;
        border-radius: 3px;
        padding: 2px 6px;
        font-size: 10px;
        cursor: pointer;
        margin-left: 5px;
    }

    .toggle-advanced:hover {
        background: #555;
    }

    .layout-actions {
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px solid #444;
    }

    .reset-button {
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 6px 12px;
        font-size: 11px;
        cursor: pointer;
        width: 100%;
    }

    .reset-button:hover {
        background: #c0392b;
    }

    .no-model {
        color: #888;
        font-style: italic;
        text-align: center;
        padding: 20px;
    }
</style>
