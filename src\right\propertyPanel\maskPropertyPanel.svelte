<script lang="ts">
    import { PropertyContainer } from '../../components/accordionPanel';
    import Checkbox from '../../components/Checkbox.svelte';
    import SafeInput from '../../components/SafeInput.svelte';
    import Select from '../../components/Select.svelte';
    import Label from '../../components/Label.svelte';
    import type { MaskModel } from '../../type/ui/maskModel.svelte';

    interface Props {
        model: MaskModel;
    }

    let { model }: Props = $props();

    // 遮罩类型选项
    const maskTypeOptions = [
        { value: 'rectangle', label: '矩形遮罩' },
        { value: 'circle', label: '圆形遮罩' },
        { value: 'image', label: '图片遮罩' }
    ];

    // 是否显示图片相关选项
    let showImageOptions = $derived(model?.maskType === 'image');

    // 可绑定的目标对象列表
    let availableTargets = $derived(() => {
        if (!model) return [];
        return model.getAvailableTargets();
    });

    // 当前选中的目标
    let selectedTargetId = $state('');

    // 绑定目标对象
    function bindTarget() {
        if (!selectedTargetId || !model) return;

        const targetInfo = availableTargets.find(t => t.name === selectedTargetId);
        if (targetInfo) {
            const success = model.bindTarget(targetInfo.model.getOriginalObject());
            if (success) {
                console.log('🎭 MaskPropertyPanel: 目标绑定成功', targetInfo.name);
            } else {
                console.error('🎭 MaskPropertyPanel: 目标绑定失败');
            }
        }
    }

    // 解除绑定
    function unbindTarget() {
        if (model) {
            model.unbindTarget();
            selectedTargetId = '';
            console.log('🎭 MaskPropertyPanel: 目标绑定已解除');
        }
    }

    // 应用遮罩
    function applyMask() {
        if (model) {
            model.applyMask();
            console.log('🎭 MaskPropertyPanel: 遮罩已应用');
        }
    }

    // 重置遮罩设置
    function resetMask() {
        if (!model) return;
        
        console.log('🎭 MaskPropertyPanel: 重置遮罩设置');
        
        model.maskType = 'rectangle';
        model.maskImage = '';
        model.offsetX = 0;
        model.offsetY = 0;
        model.maskWidth = 200;
        model.maskHeight = 200;
    }
</script>

<!-- 遮罩类型 -->
<PropertyContainer>
    <Label text="遮罩类型:" />
    <Select 
        options={maskTypeOptions}
        bind:value={model.maskType}
    />
</PropertyContainer>

<!-- 图片路径（仅当类型为image时显示） -->
{#if showImageOptions}
    <PropertyContainer>
        <Label text="图片路径:" />
        <SafeInput 
            value={model.maskImage}
            type="text"
            placeholder="输入图片路径"
            onchange={(e: any) => model.maskImage = e.target.value}
        />
    </PropertyContainer>
{/if}

<!-- 位置偏移 -->
<PropertyContainer>
    <Label text="X偏移:" />
    <SafeInput 
        value={model.offsetX.toString()}
        type="number"
        onchange={(e: any) => model.offsetX = parseInt(e.target.value) || 0}
    />
</PropertyContainer>

<PropertyContainer>
    <Label text="Y偏移:" />
    <SafeInput 
        value={model.offsetY.toString()}
        type="number"
        onchange={(e: any) => model.offsetY = parseInt(e.target.value) || 0}
    />
</PropertyContainer>

<!-- 遮罩尺寸 -->
<PropertyContainer>
    <Label text="遮罩宽度:" />
    <SafeInput 
        value={model.maskWidth.toString()}
        type="number"
        onchange={(e: any) => model.maskWidth = parseInt(e.target.value) || 1}
    />
</PropertyContainer>

<PropertyContainer>
    <Label text="遮罩高度:" />
    <SafeInput 
        value={model.maskHeight.toString()}
        type="number"
        onchange={(e: any) => model.maskHeight = parseInt(e.target.value) || 1}
    />
</PropertyContainer>

<!-- 目标对象绑定 -->
<PropertyContainer>
    <Label text="绑定目标:" />
    <div class="binding-controls">
        <Select 
            options={availableTargets.map(t => ({ value: t.name, label: `${t.name} (${t.type})` }))}
            value={selectedTargetId}
            onchange={(e: any) => selectedTargetId = e.target.value}
            placeholder="选择要遮罩的对象"
        />
        <div class="binding-buttons">
            <button class="bind-button" onclick={bindTarget} disabled={!selectedTargetId}>
                绑定
            </button>
            <button class="unbind-button" onclick={unbindTarget} disabled={!model.boundTarget}>
                解绑
            </button>
        </div>
    </div>
</PropertyContainer>

<!-- 当前绑定状态 -->
{#if model.boundTargetName}
    <PropertyContainer>
        <Label text="当前绑定:" />
        <div class="current-binding">
            <span class="target-name">{model.boundTargetName}</span>
            <button class="apply-button" onclick={applyMask}>
                应用遮罩
            </button>
        </div>
    </PropertyContainer>
{/if}

<!-- 操作按钮 -->
<div class="mask-actions">
    <button class="reset-button" onclick={resetMask}>
        重置设置
    </button>
</div>

<style>
    .binding-controls {
        display: flex;
        flex-direction: column;
        gap: 5px;
        width: 100%;
    }

    .binding-buttons {
        display: flex;
        gap: 5px;
    }

    .bind-button, .unbind-button {
        background: #3498db;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 4px 8px;
        font-size: 10px;
        cursor: pointer;
        flex: 1;
    }

    .bind-button:hover {
        background: #2980b9;
    }

    .bind-button:disabled {
        background: #7f8c8d;
        cursor: not-allowed;
    }

    .unbind-button {
        background: #e74c3c;
    }

    .unbind-button:hover {
        background: #c0392b;
    }

    .unbind-button:disabled {
        background: #7f8c8d;
        cursor: not-allowed;
    }

    .current-binding {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 5px;
    }

    .target-name {
        color: #2ecc71;
        font-size: 10px;
        font-weight: bold;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .apply-button {
        background: #27ae60;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 3px 6px;
        font-size: 9px;
        cursor: pointer;
    }

    .apply-button:hover {
        background: #229954;
    }

    .mask-actions {
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px solid #444;
    }

    .reset-button {
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 6px 12px;
        font-size: 11px;
        cursor: pointer;
        width: 100%;
    }

    .reset-button:hover {
        background: #c0392b;
    }
</style>
