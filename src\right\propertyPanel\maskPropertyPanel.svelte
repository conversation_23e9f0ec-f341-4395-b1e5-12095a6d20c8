<script lang="ts">
    import { PropertyContainer } from '../../components/accordionPanel';
    import SafeInput from '../../components/SafeInput.svelte';
    import Select from '../../components/Select.svelte';
    import Label from '../../components/Label.svelte';
    import DropTarget from '../../components/drop/DropTarget.svelte';
    import type { MaskModel } from '../../type/ui/maskModel.svelte';

    interface Props {
        model: MaskModel;
    }

    let { model }: Props = $props();

    // 遮罩类型选项
    const maskTypeOptions = [
        { value: 'rectangle', label: '矩形遮罩' },
        { value: 'circle', label: '圆形遮罩' },
        { value: 'image', label: '图片遮罩' }
    ];

    // 是否显示图片相关选项
    let showImageOptions = $derived(model?.maskType === 'image');

    // 获取绑定状态信息
    let bindingInfo = $derived(() => {
        if (!model) return null;
        return {
            hasBoundTarget: !!model.boundTarget,
            boundTargetName: model.boundTargetName,
            boundTarget: model.boundTarget
        };
    });

    // 处理目标对象拖拽
    function handleTargetDrop(droppedObject: any) {
        console.log('🎭 MaskPropertyPanel: 处理目标对象拖拽', droppedObject);

        if (!model) return;

        // 检查是否是有效的UI组件
        if (!droppedObject.isUIComponent || droppedObject.uiComponentType === 'UIMask') {
            console.warn('🎭 MaskPropertyPanel: 无效的绑定目标', droppedObject);
            return;
        }

        const success = model.bindTarget(droppedObject);
        if (success) {
            console.log('🎭 MaskPropertyPanel: 目标绑定成功', droppedObject.name);
        } else {
            console.error('🎭 MaskPropertyPanel: 目标绑定失败');
        }
    }

    // 解除绑定
    function unbindTarget() {
        if (model) {
            model.unbindTarget();
            console.log('🎭 MaskPropertyPanel: 目标绑定已解除');
        }
    }

    // 重置遮罩设置
    function resetMask() {
        if (!model) return;
        
        console.log('🎭 MaskPropertyPanel: 重置遮罩设置');
        
        model.maskType = 'rectangle';
        model.maskImage = '';
        model.offsetX = 0;
        model.offsetY = 0;
        model.maskWidth = 200;
        model.maskHeight = 200;
    }
</script>

<!-- 遮罩类型 -->
<PropertyContainer>
    <Label text="遮罩类型:" />
    <Select 
        options={maskTypeOptions}
        bind:value={model.maskType}
    />
</PropertyContainer>

<!-- 图片路径（仅当类型为image时显示） -->
{#if showImageOptions}
    <PropertyContainer>
        <Label text="图片路径:" />
        <SafeInput 
            value={model.maskImage}
            type="text"
            placeholder="输入图片路径"
            onchange={(e: any) => model.maskImage = e.target.value}
        />
    </PropertyContainer>
{/if}

<!-- 位置偏移 -->
<PropertyContainer>
    <Label text="X偏移:" />
    <SafeInput 
        value={model.offsetX.toString()}
        type="number"
        onchange={(e: any) => model.offsetX = parseInt(e.target.value) || 0}
    />
</PropertyContainer>

<PropertyContainer>
    <Label text="Y偏移:" />
    <SafeInput 
        value={model.offsetY.toString()}
        type="number"
        onchange={(e: any) => model.offsetY = parseInt(e.target.value) || 0}
    />
</PropertyContainer>

<!-- 遮罩尺寸 -->
<PropertyContainer>
    <Label text="遮罩宽度:" />
    <SafeInput 
        value={model.maskWidth.toString()}
        type="number"
        onchange={(e: any) => model.maskWidth = parseInt(e.target.value) || 1}
    />
</PropertyContainer>

<PropertyContainer>
    <Label text="遮罩高度:" />
    <SafeInput 
        value={model.maskHeight.toString()}
        type="number"
        onchange={(e: any) => model.maskHeight = parseInt(e.target.value) || 1}
    />
</PropertyContainer>

<!-- 目标对象绑定 -->
<div class="binding-section">
    <h4>🎯 目标对象绑定：</h4>

    <div class="binding-item">
        <div class="binding-header">
            <span class="binding-label">遮罩目标:</span>
            <span class="binding-status-badge {bindingInfo()?.hasBoundTarget ? 'bound' : 'unbound'}">
                {bindingInfo()?.hasBoundTarget ? '已绑定' : '未绑定'}
            </span>
            {#if bindingInfo()?.hasBoundTarget}
                <button class="unbind-btn" onclick={unbindTarget} title="解除绑定">✕</button>
            {/if}
        </div>

        {#if bindingInfo()?.hasBoundTarget}
            <div class="bound-object-info">
                已绑定: {bindingInfo()?.boundTarget?.constructor?.name || '未知对象'}
                <br>
                名称: {bindingInfo()?.boundTargetName || '未命名'}
            </div>
        {:else}
            <DropTarget
                onDrop={handleTargetDrop}
                placeholder="拖拽 UI 对象到此处绑定为遮罩目标"
                targetObject={model}
                fieldName="boundTarget"
                enableHistory={true}
                operationName="绑定遮罩目标对象"
            />
        {/if}
    </div>

    <div class="binding-info">
        <p class="binding-hint">
            💡 从左侧对象树拖拽 UI 对象到上方区域进行绑定
        </p>
    </div>
</div>

<!-- 操作按钮 -->
<div class="mask-actions">
    <button class="reset-button" onclick={resetMask}>
        重置设置
    </button>
</div>

<style>
    .binding-section {
        margin: 15px 0;
        padding: 10px;
        background: #333;
        border-radius: 4px;
    }

    .binding-section h4 {
        margin: 0 0 10px 0;
        color: #ffffff;
        font-size: 12px;
        font-weight: bold;
    }

    .binding-item {
        margin-bottom: 10px;
    }

    .binding-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 5px;
    }

    .binding-label {
        color: #ffffff;
        font-size: 11px;
        font-weight: bold;
    }

    .binding-status-badge {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 9px;
        font-weight: bold;
    }

    .binding-status-badge.bound {
        background: #27ae60;
        color: white;
    }

    .binding-status-badge.unbound {
        background: #7f8c8d;
        color: white;
    }

    .unbind-btn {
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 2px 6px;
        font-size: 10px;
        cursor: pointer;
        margin-left: 5px;
    }

    .unbind-btn:hover {
        background: #c0392b;
    }

    .bound-object-info {
        background: #2a2a2a;
        border: 1px solid #27ae60;
        border-radius: 3px;
        padding: 8px;
        color: #ffffff;
        font-size: 10px;
        line-height: 1.4;
    }

    .binding-info {
        margin-top: 10px;
    }

    .binding-hint {
        color: #bdc3c7;
        font-size: 10px;
        font-style: italic;
        margin: 0;
        text-align: center;
    }

    .mask-actions {
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px solid #444;
    }

    .reset-button {
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 6px 12px;
        font-size: 11px;
        cursor: pointer;
        width: 100%;
    }

    .reset-button:hover {
        background: #c0392b;
    }
</style>
