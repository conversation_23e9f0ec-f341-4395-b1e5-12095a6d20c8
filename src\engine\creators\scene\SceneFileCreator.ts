/**
 * Scene_File 创建器
 * 专门用于创建文件场景基类
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';
import { type MenuBaseSceneOptions } from './SceneMenuBaseCreator';

/**
 * 文件场景创建选项
 */
export interface FileSceneOptions extends MenuBaseSceneOptions {
    /** 初始选中的文件索引 */
    initialFileIndex?: number;
    /** 文件模式 ('save' | 'load') */
    mode?: 'save' | 'load';
}

/**
 * 创建文件场景
 * @param options 创建选项
 * @returns 创建的文件场景实例
 */
export async function createSceneFile(options: FileSceneOptions = {}): Promise<any> {
    console.log('=== 创建文件场景 Scene_File ===');
    
    try {
        // 预加载文件场景资源
        BaseSceneCreator.preloadSceneResources('Scene_File');
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_File', defaultOptions);
        
        // Scene_File 特定的设置
        if (options.initialFileIndex !== undefined && scene._listWindow) {
            scene._listWindow.select(options.initialFileIndex);
            console.log('设置初始文件索引:', options.initialFileIndex);
        }
        
        if (options.mode && scene._mode) {
            scene._mode = options.mode;
            console.log('设置文件模式:', options.mode);
        }
        
        console.log('Scene_File 创建完成');
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_File 失败:', error);
        throw error;
    }
}

/**
 * 创建并启动文件场景
 * @param options 创建选项
 * @returns 创建的文件场景实例
 */
export async function createAndStartSceneFile(options: FileSceneOptions = {}): Promise<any> {
    return createSceneFile({ ...options, autoStart: true });
}

/**
 * 创建简单的文件场景（用于测试）
 * @returns 创建的文件场景实例
 */
export async function createSimpleSceneFile(): Promise<any> {
    return createSceneFile({
        initialFileIndex: 0,
        mode: 'save',
        backgroundType: 1,
        autoStart: false,
        addToStage: true
    });
}
