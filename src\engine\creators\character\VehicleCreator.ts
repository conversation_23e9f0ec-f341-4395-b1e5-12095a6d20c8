/**
 * Game_Vehicle 创建器
 * 专门用于创建RPG Maker MZ载具
 */

import { objManage } from '../../objManage';

declare global {
    interface Window {
        Game_Vehicle: any;
        Sprite_Character: any;
        Graphics: any;
        ImageManager: any;
        $dataSystem: any;
    }
}

// 载具类型枚举
export enum VehicleType {
    BOAT = 'boat',      // 小船
    SHIP = 'ship',      // 大船
    AIRSHIP = 'airship' // 飞艇
}

/**
 * 创建载具精灵
 * @param options 创建选项
 * @returns 创建的载具精灵对象
 */
export async function createVehicle(options: {
    type?: VehicleType | string;
    characterName?: string;
    characterIndex?: number;
    direction?: number;
    x?: number;
    y?: number;
} = {}): Promise<any> {
    console.log('=== 创建 Game_Vehicle 精灵 ===');
    console.log('创建选项:', options);

    try {
        // 1. 检查资源是否准备就绪
        const resourceStatus = objManage.getResourceStatus();
        if (!resourceStatus.all) {
            console.log('等待资源加载完成...', resourceStatus);
            const loaded = await objManage.waitForResources();
            if (!loaded) {
                throw new Error('资源加载超时');
            }
        }

        // 2. 检查必要的类是否存在
        if (!window.Game_Vehicle || !window.Sprite_Character) {
            throw new Error('RPG Maker MZ Vehicle 类未加载');
        }

        // 3. 确定载具类型
        const vehicleType = options.type || VehicleType.BOAT;
        console.log(`创建载具类型: ${vehicleType}`);

        // 4. 创建 Game_Vehicle 实例
        const vehicle = new window.Game_Vehicle(vehicleType);
        console.log('Game_Vehicle 实例创建完成');

        // 5. 设置载具图像
        if (options.characterName && options.characterIndex !== undefined) {
            // 使用指定的角色图像
            vehicle.setImage(options.characterName, options.characterIndex);
            console.log(`设置载具图像: ${options.characterName}, 索引: ${options.characterIndex}`);
        } else {
            // 根据载具类型设置默认图像
            let defaultCharacterName = 'Vehicle';
            let defaultCharacterIndex = 0;

            // 尝试从系统数据获取载具图像设置
            if (window.$dataSystem && window.$dataSystem.vehicle) {
                const vehicleData = window.$dataSystem.vehicle;
                switch (vehicleType) {
                    case VehicleType.BOAT:
                        if (vehicleData.boat) {
                            defaultCharacterName = vehicleData.boat.characterName || 'Vehicle';
                            defaultCharacterIndex = vehicleData.boat.characterIndex || 0;
                        }
                        break;
                    case VehicleType.SHIP:
                        if (vehicleData.ship) {
                            defaultCharacterName = vehicleData.ship.characterName || 'Vehicle';
                            defaultCharacterIndex = vehicleData.ship.characterIndex || 1;
                        }
                        break;
                    case VehicleType.AIRSHIP:
                        if (vehicleData.airship) {
                            defaultCharacterName = vehicleData.airship.characterName || 'Vehicle';
                            defaultCharacterIndex = vehicleData.airship.characterIndex || 2;
                        }
                        break;
                    default:
                        defaultCharacterIndex = 0;
                        break;
                }
            } else {
                // 如果没有系统数据，使用基于类型的默认索引
                switch (vehicleType) {
                    case VehicleType.BOAT:
                        defaultCharacterIndex = 0;
                        break;
                    case VehicleType.SHIP:
                        defaultCharacterIndex = 1;
                        break;
                    case VehicleType.AIRSHIP:
                        defaultCharacterIndex = 2;
                        break;
                    default:
                        defaultCharacterIndex = 0;
                        break;
                }
            }

            vehicle.setImage(defaultCharacterName, defaultCharacterIndex);
            console.log(`使用默认载具图像: ${defaultCharacterName}, 索引: ${defaultCharacterIndex}`);
        }

        // 6. 设置载具位置
        const x = options.x !== undefined ? options.x : Math.floor(window.Graphics.width / 2 / 48);
        const y = options.y !== undefined ? options.y : Math.floor(window.Graphics.height / 2 / 48);
        vehicle.setPosition(x, y);

        // 7. 设置载具方向
        const direction = options.direction || 2; // 默认向下
        vehicle.setDirection(direction);

        // 8. 预加载载具图像
        if (window.ImageManager && vehicle._characterName) {
            const bitmap = window.ImageManager.loadCharacter(vehicle._characterName);
            console.log('预加载载具图像:', vehicle._characterName, bitmap);

            // 等待图像加载完成
            if (bitmap && typeof bitmap.isReady === 'function' && !bitmap.isReady()) {
                console.log('等待载具图像加载完成...');
                await new Promise<void>((resolve) => {
                    if (typeof bitmap.addLoadListener === 'function') {
                        bitmap.addLoadListener(() => {
                            console.log('载具图像加载完成');
                            resolve();
                        });
                    } else {
                        const checkReady = () => {
                            if (bitmap.isReady && bitmap.isReady()) {
                                console.log('载具图像加载完成（轮询检测）');
                                resolve();
                            } else {
                                setTimeout(checkReady, 100);
                            }
                        };
                        checkReady();
                    }
                });
            }
        }

        console.log('Game_Vehicle 创建完成:', {
            type: vehicle._type,
            characterName: vehicle._characterName,
            characterIndex: vehicle._characterIndex,
            direction: vehicle._direction,
            x: vehicle._x,
            y: vehicle._y
        });

        // 9. 创建精灵对象
        const sprite = new window.Sprite_Character(vehicle);

        console.log('Sprite_Character (Vehicle) 创建完成:', sprite);

        // 验证锚点是否符合 RPG Maker MZ 源码标准
        if (sprite.anchor) {
            const expectedAnchorX = 0.5;
            const expectedAnchorY = 1;
            console.log('Vehicle 锚点验证:', {
                actualAnchorX: sprite.anchor.x,
                expectedAnchorX: expectedAnchorX,
                anchorXCorrect: sprite.anchor.x === expectedAnchorX,
                actualAnchorY: sprite.anchor.y,
                expectedAnchorY: expectedAnchorY,
                anchorYCorrect: sprite.anchor.y === expectedAnchorY,
                bothAnchorsCorrect: sprite.anchor.x === expectedAnchorX && sprite.anchor.y === expectedAnchorY
            });
        } else {
            console.warn('Vehicle Sprite 没有 anchor 属性！这不符合 RPG Maker MZ 标准');
        }

        // 强制更新精灵
        if (sprite.update) {
            sprite.update();
            console.log('载具精灵已强制更新');
        }

        // 10. 设置显示属性
        sprite.scale.x = 2; // 放大2倍
        sprite.scale.y = 2; // 放大2倍

        // 11. 通过objManage添加到舞台
        objManage.addToStage(sprite);

        // 12. 设置调试属性
        sprite.tint = 0xFFFFFF; // 确保没有色调变化
        console.log(`${vehicleType} 载具精灵创建完成（已放大2倍）`);

        return sprite;

    } catch (error) {
        console.error('创建 Game_Vehicle 精灵失败:', error);
        throw error;
    }
}

/**
 * 创建小船精灵
 * @param options 创建选项
 * @returns 创建的小船精灵对象
 */
export async function createBoat(options: any = {}): Promise<any> {
    console.log('=== 创建小船精灵 ===');
    return await createVehicle({ ...options, type: VehicleType.BOAT });
}

/**
 * 创建大船精灵
 * @param options 创建选项
 * @returns 创建的大船精灵对象
 */
export async function createShip(options: any = {}): Promise<any> {
    console.log('=== 创建大船精灵 ===');
    return await createVehicle({ ...options, type: VehicleType.SHIP });
}

/**
 * 创建飞艇精灵
 * @param options 创建选项
 * @returns 创建的飞艇精灵对象
 */
export async function createAirship(options: any = {}): Promise<any> {
    console.log('=== 创建飞艇精灵 ===');
    return await createVehicle({ ...options, type: VehicleType.AIRSHIP });
}

/**
 * 创建测试载具精灵
 * @param type 载具类型
 * @returns 创建的载具精灵对象
 */
export async function createTestVehicle(type: VehicleType = VehicleType.BOAT): Promise<any> {
    console.log(`=== 创建测试载具精灵 (${type}) ===`);

    try {
        return await createVehicle({
            type: type,
            direction: 2
        });
    } catch (error) {
        console.error('创建测试载具失败:', error);
        throw error;
    }
}

/**
 * 创建所有类型的载具精灵
 * @returns 创建的载具精灵数组
 */
export async function createAllVehicles(): Promise<any[]> {
    console.log('=== 创建所有类型的载具精灵 ===');

    try {
        const vehicles = [];

        // 创建小船
        const boat = await createBoat({ x: 5, y: 5 });
        vehicles.push(boat);

        // 创建大船
        const ship = await createShip({ x: 7, y: 5 });
        vehicles.push(ship);

        // 创建飞艇
        const airship = await createAirship({ x: 9, y: 5 });
        vehicles.push(airship);

        console.log(`成功创建 ${vehicles.length} 个载具精灵`);
        return vehicles;
    } catch (error) {
        console.error('创建所有载具失败:', error);
        throw error;
    }
}
