/**
 * Generators 模块统一导出
 * 新的可扩展继承架构
 */

// 类型定义
export * from './types';

// 核心处理器
export { BaseProcessor } from './core/BaseProcessor';
export { SpriteProcessor } from './core/SpriteProcessor';
export { SpriteButtonProcessor } from './core/SpriteButtonProcessor';
export { ProcessorFactory } from './core/ProcessorFactory';

// 工具类
export { PathUtils } from './core/utils/PathUtils';
export { BitmapUtils } from './core/utils/BitmapUtils';

// 主要功能
export { OperationManager } from './operationManager';
export { generatePluginCode } from './pluginGenerator';

// 便捷方法：注册新的处理器
export const registerProcessor = ProcessorFactory.register.bind(ProcessorFactory);

// 便捷方法：检查是否支持某个类型
export const isTypeSupported = ProcessorFactory.isSupported.bind(ProcessorFactory);

// 便捷方法：获取所有支持的类型
export const getSupportedTypes = ProcessorFactory.getSupportedClassNames.bind(ProcessorFactory);

console.log('🚀 Generators 模块加载完成');
console.log(`📋 当前支持的对象类型: ${ProcessorFactory.getSupportedClassNames().join(', ')}`);
