/**
 * 坐标转换工具 - 处理不同坐标系之间的转换
 * 提供鼠标坐标、模型坐标、渲染坐标之间的转换功能
 */

import type { RenderContext } from './types';
import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';

/**
 * 坐标点接口
 */
export interface Point {
  x: number;
  y: number;
}

/**
 * 变换矩阵信息
 */
export interface TransformInfo {
  scale: Point;
  offset: Point;
  rotation: number;
}

/**
 * 坐标转换配置
 */
export interface CoordinateConfig {
  enableDebugLog: boolean;
  precision: number; // 坐标精度（小数位数）
}

/**
 * 坐标转换工具类
 */
export class CoordinateTransform {
  private static config: CoordinateConfig = {
    enableDebugLog: false,
    precision: 2
  };

  /**
   * 设置转换配置
   */
  static setConfig(config: Partial<CoordinateConfig>): void {
    Object.assign(this.config, config);
  }

  /**
   * 鼠标坐标转换为模型坐标
   * @param mouseX 鼠标X坐标
   * @param mouseY 鼠标Y坐标
   * @param context 渲染上下文
   * @returns 模型坐标
   */
  static mouseToModel(mouseX: number, mouseY: number, context?: RenderContext): Point {
    // 快速路径：如果没有上下文或者是默认变换，直接返回
    if (!context ||
        (!context.scale && !context.offset && !context.rotation) ||
        (context.scale?.x === 1 && context.scale?.y === 1 &&
         context.offset?.x === 0 && context.offset?.y === 0 &&
         (!context.rotation || context.rotation === 0))) {
      return {
        x: this.roundToPrecision(mouseX),
        y: this.roundToPrecision(mouseY)
      };
    }

    let modelX = mouseX;
    let modelY = mouseY;

    // 验证输入参数（仅在调试模式下）
    if (this.config.enableDebugLog && (isNaN(mouseX) || isNaN(mouseY))) {
      console.error('🔧 CoordinateTransform: 输入坐标包含 NaN', { mouseX, mouseY });
      return { x: 0, y: 0 };
    }

    // 应用变换
    if (context.scale && context.scale.x !== 0 && context.scale.y !== 0) {
      modelX = mouseX / context.scale.x;
      modelY = mouseY / context.scale.y;
    }

    if (context.offset) {
      modelX = modelX - context.offset.x;
      modelY = modelY - context.offset.y;
    }

    if (context.rotation && context.rotation !== 0) {
      const cos = Math.cos(-context.rotation);
      const sin = Math.sin(-context.rotation);
      const tempX = modelX;
      const tempY = modelY;
      modelX = tempX * cos - tempY * sin;
      modelY = tempX * sin + tempY * cos;
    }

    const result = {
      x: this.roundToPrecision(modelX),
      y: this.roundToPrecision(modelY)
    };

    // 验证结果（仅在调试模式下）
    if (this.config.enableDebugLog) {
      if (isNaN(result.x) || isNaN(result.y)) {
        console.error('🔧 CoordinateTransform: 转换结果包含 NaN', {
          input: { mouseX, mouseY },
          result: result,
          context: context
        });
        return { x: mouseX, y: mouseY }; // 回退到原始坐标
      }

      console.log('🔧 CoordinateTransform: 鼠标→模型', {
        mouse: { x: mouseX, y: mouseY },
        model: result,
        context: context ? {
          scale: context.scale,
          offset: context.offset,
          rotation: context.rotation
        } : 'none'
      });
    }

    return result;
  }

  /**
   * 模型坐标转换为渲染坐标
   * @param modelX 模型X坐标
   * @param modelY 模型Y坐标
   * @param context 渲染上下文
   * @returns 渲染坐标
   */
  static modelToRender(modelX: number, modelY: number, context?: RenderContext): Point {
    let renderX = modelX;
    let renderY = modelY;

    // 如果有渲染上下文，应用相应的变换
    if (context) {
      // 考虑画布旋转
      if (context.rotation && context.rotation !== 0) {
        const cos = Math.cos(context.rotation);
        const sin = Math.sin(context.rotation);
        const tempX = renderX;
        const tempY = renderY;
        renderX = tempX * cos - tempY * sin;
        renderY = tempX * sin + tempY * cos;
      }

      // 考虑画布偏移
      if (context.offset) {
        renderX = renderX + context.offset.x;
        renderY = renderY + context.offset.y;
      }

      // 考虑画布缩放
      if (context.scale) {
        renderX = renderX * context.scale.x;
        renderY = renderY * context.scale.y;
      }
    }

    const result = {
      x: this.roundToPrecision(renderX),
      y: this.roundToPrecision(renderY)
    };

    if (this.config.enableDebugLog) {
      console.log('🔧 CoordinateTransform: 模型→渲染', {
        model: { x: modelX, y: modelY },
        render: result,
        context: context ? {
          scale: context.scale,
          offset: context.offset,
          rotation: context.rotation
        } : 'none'
      });
    }

    return result;
  }

  /**
   * 渲染坐标转换为鼠标坐标
   * @param renderX 渲染X坐标
   * @param renderY 渲染Y坐标
   * @param context 渲染上下文
   * @returns 鼠标坐标
   */
  static renderToMouse(renderX: number, renderY: number, context?: RenderContext): Point {
    // 这是 modelToRender 的逆变换
    let mouseX = renderX;
    let mouseY = renderY;

    if (context) {
      // 逆向应用缩放
      if (context.scale) {
        mouseX = mouseX / context.scale.x;
        mouseY = mouseY / context.scale.y;
      }

      // 逆向应用偏移
      if (context.offset) {
        mouseX = mouseX - context.offset.x;
        mouseY = mouseY - context.offset.y;
      }

      // 逆向应用旋转
      if (context.rotation && context.rotation !== 0) {
        const cos = Math.cos(-context.rotation);
        const sin = Math.sin(-context.rotation);
        const tempX = mouseX;
        const tempY = mouseY;
        mouseX = tempX * cos - tempY * sin;
        mouseY = tempX * sin + tempY * cos;
      }
    }

    return {
      x: this.roundToPrecision(mouseX),
      y: this.roundToPrecision(mouseY)
    };
  }

  /**
   * 计算对象的全局坐标（考虑父对象变换）
   * @param model 对象模型
   * @returns 全局坐标
   */
  static getGlobalPosition(model: BaseObjectModel): Point {
    // 通过 _originalObject 获取实际的 PIXI 对象
    const pixiObject = (model as any)._originalObject;

    if (pixiObject && typeof pixiObject.toGlobal === 'function') {
      try {
        // 使用 PIXI 的 toGlobal 方法获取全局坐标
        const globalPos = pixiObject.toGlobal({ x: 0, y: 0 });

        const result = {
          x: this.roundToPrecision(globalPos.x),
          y: this.roundToPrecision(globalPos.y)
        };

        if (this.config.enableDebugLog) {
          console.log('🔧 CoordinateTransform: 获取全局坐标', {
            model: model.className,
            localPos: { x: model.x, y: model.y },
            globalPos: result,
            hasParent: !!pixiObject.parent
          });
        }

        return result;
      } catch (error) {
        console.warn('🔧 CoordinateTransform: PIXI toGlobal 失败，使用手动计算', error);
      }
    }

    // 回退到手动计算（简化版本）
    return {
      x: this.roundToPrecision(model.x),
      y: this.roundToPrecision(model.y)
    };
  }

  /**
   * 计算两点之间的距离
   * @param point1 第一个点
   * @param point2 第二个点
   * @returns 距离
   */
  static distance(point1: Point, point2: Point): number {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 计算两点之间的角度（弧度）
   * @param from 起始点
   * @param to 目标点
   * @returns 角度（弧度）
   */
  static angle(from: Point, to: Point): number {
    return Math.atan2(to.y - from.y, to.x - from.x);
  }

  /**
   * 将点绕指定中心旋转
   * @param point 要旋转的点
   * @param center 旋转中心
   * @param angle 旋转角度（弧度）
   * @returns 旋转后的点
   */
  static rotatePoint(point: Point, center: Point, angle: number): Point {
    const cos = Math.cos(angle);
    const sin = Math.sin(angle);

    const dx = point.x - center.x;
    const dy = point.y - center.y;

    return {
      x: this.roundToPrecision(center.x + dx * cos - dy * sin),
      y: this.roundToPrecision(center.y + dx * sin + dy * cos)
    };
  }

  /**
   * 将坐标限制在指定范围内
   * @param point 坐标点
   * @param bounds 边界 {minX, minY, maxX, maxY}
   * @returns 限制后的坐标
   */
  static clampToBounds(
    point: Point,
    bounds: { minX: number; minY: number; maxX: number; maxY: number }
  ): Point {
    return {
      x: Math.max(bounds.minX, Math.min(bounds.maxX, point.x)),
      y: Math.max(bounds.minY, Math.min(bounds.maxY, point.y))
    };
  }

  /**
   * 将坐标对齐到网格
   * @param point 坐标点
   * @param gridSize 网格大小
   * @returns 对齐后的坐标
   */
  static snapToGrid(point: Point, gridSize: number): Point {
    return {
      x: Math.round(point.x / gridSize) * gridSize,
      y: Math.round(point.y / gridSize) * gridSize
    };
  }

  /**
   * 根据精度设置四舍五入
   * @param value 数值
   * @returns 四舍五入后的数值
   */
  private static roundToPrecision(value: number): number {
    const factor = Math.pow(10, this.config.precision);
    return Math.round(value * factor) / factor;
  }

  /**
   * 创建变换信息
   * @param scale 缩放
   * @param offset 偏移
   * @param rotation 旋转
   * @returns 变换信息
   */
  static createTransformInfo(
    scale: Point = { x: 1, y: 1 },
    offset: Point = { x: 0, y: 0 },
    rotation: number = 0
  ): TransformInfo {
    return { scale, offset, rotation };
  }

  /**
   * 启用调试日志
   */
  static enableDebugLog(): void {
    this.config.enableDebugLog = true;
    console.log('🔧 CoordinateTransform: 调试日志已启用');
  }

  /**
   * 禁用调试日志
   */
  static disableDebugLog(): void {
    this.config.enableDebugLog = false;
    console.log('🔧 CoordinateTransform: 调试日志已禁用');
  }

  /**
   * 检查调试日志是否启用
   */
  static isDebugEnabled(): boolean {
    return this.config.enableDebugLog;
  }
}
