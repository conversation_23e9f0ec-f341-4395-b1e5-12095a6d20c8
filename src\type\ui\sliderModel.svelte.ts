import { BaseObjectModel } from '../baseObjectModel.svelte';

export class SliderModel extends BaseObjectModel {

    constructor(slider: any) {
        super(slider);

        // 初始化滑动条特有属性
        this.value = slider.value || 0;
        this.minValue = slider.minValue || 0;
        this.maxValue = slider.maxValue || 100;
        this.step = slider.step || 1;
        // 🔑 子组件绑定属性（所有组件都由外部绑定）
        this.boundRangeSprite = slider.boundRangeSprite || null;       // 范围精灵 (UIImage) - 定义有效范围 ⭐
        this.boundThumbSprite = slider.boundThumbSprite || null;       // 滑块精灵 (UIImage) - 可拖拽控制
        this.boundProgressSprite = slider.boundProgressSprite || null; // 进度精灵 (UIImage) - 显示当前进度
        this.boundLabelSprite = slider.boundLabelSprite || null;       // 文本精灵 (UILabel) - 显示数值

        // 行为属性
        this.enabled = slider.enabled !== false;

        // 事件代码属性
        this.onChangeCode = slider._eventCodes?.onChange || '';
        this.onDragStartCode = slider._eventCodes?.onDragStart || '';
        this.onDragEndCode = slider._eventCodes?.onDragEnd || '';

        console.log('🔧 SliderModel: 创建滑动条模型', slider);

        // setupSync() 已经在基类构造函数中调用了
    }

    // 滑动条数值属性
    value = $state(0);              // 当前值
    minValue = $state(0);           // 最小值
    maxValue = $state(100);         // 最大值
    step = $state(1);               // 步长



    // 🔑 子组件绑定属性（所有组件都由外部绑定）
    boundRangeSprite = $state(null);     // 绑定的范围精灵 (UIImage) - 定义有效范围 ⭐
    boundThumbSprite = $state(null);     // 绑定的滑块精灵 (UIImage) - 可拖拽控制
    boundProgressSprite = $state(null);  // 绑定的进度精灵 (UIImage) - 显示当前进度
    boundLabelSprite = $state(null);     // 绑定的文本精灵 (UILabel) - 显示数值

    // 行为属性
    enabled = $state(true);          // 是否启用

    // 事件代码属性
    onChangeCode = $state('');       // 值改变事件代码
    onDragStartCode = $state('');    // 开始拖拽事件代码
    onDragEndCode = $state('');      // 结束拖拽事件代码

    /**
     * 设置Slider特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Slider特有的属性
     */
    protected setupSpecificSync(): void {
        // 同步滑动条特有属性
        if (this._originalObject.setValue && typeof this._originalObject.setValue === 'function') {
            this._originalObject.setValue(this.value);
        } else {
            this._originalObject.value = this.value;
        }

        this._originalObject.minValue = this.minValue;
        this._originalObject.maxValue = this.maxValue;
        this._originalObject.step = this.step;

        // 🔑 同步子组件绑定属性 - 调用绑定方法而不是直接设置属性
        // 这样可以触发 UISlider 的尺寸计算和布局更新
        // boundTrackSprite已删除

        if (this.boundRangeSprite && this._originalObject.bindRangeSprite) {
            this._originalObject.bindRangeSprite(this.boundRangeSprite);
        } else {
            this._originalObject.boundRangeSprite = this.boundRangeSprite;
        }

        if (this.boundThumbSprite && this._originalObject.bindThumbSprite) {
            this._originalObject.bindThumbSprite(this.boundThumbSprite);
        } else {
            this._originalObject.boundThumbSprite = this.boundThumbSprite;
        }

        if (this.boundProgressSprite && this._originalObject.bindProgressSprite) {
            this._originalObject.bindProgressSprite(this.boundProgressSprite);
        } else {
            this._originalObject.boundProgressSprite = this.boundProgressSprite;
        }

        if (this.boundLabelSprite && this._originalObject.bindLabelSprite) {
            this._originalObject.bindLabelSprite(this.boundLabelSprite);
        } else {
            this._originalObject.boundLabelSprite = this.boundLabelSprite;
        }

        // 同步行为属性
        if (this._originalObject.setEnabled && typeof this._originalObject.setEnabled === 'function') {
            this._originalObject.setEnabled(this.enabled);
        } else {
            this._originalObject.enabled = this.enabled;
        }

        // orientation属性已删除

        // 同步事件代码属性
        if (!this._originalObject._eventCodes) {
            this._originalObject._eventCodes = {};
        }
        this._originalObject._eventCodes.onChange = this.onChangeCode;
        this._originalObject._eventCodes.onDragStart = this.onDragStartCode;
        this._originalObject._eventCodes.onDragEnd = this.onDragEndCode;
    }

    /**
     * 设置滑动条值
     */
    public setValue(value: number): void {
        // 限制范围
        value = Math.max(this.minValue, Math.min(this.maxValue, value));

        // 应用步长
        if (this.step > 0) {
            value = Math.round((value - this.minValue) / this.step) * this.step + this.minValue;
        }

        this.value = value;
    }

    /**
     * 设置滑动条范围
     */
    public setRange(minValue: number, maxValue: number): void {
        this.minValue = minValue;
        this.maxValue = maxValue;
        // 重新验证当前值
        this.setValue(this.value);
    }

    /**
     * 获取进度比例 (0-1)
     */
    public getProgress(): number {
        if (this.maxValue === this.minValue) return 0;
        return (this.value - this.minValue) / (this.maxValue - this.minValue);
    }

    /**
     * 获取绑定状态信息
     */
    public getBindingInfo(): {
        hasRange: boolean;
        hasThumb: boolean;
        hasProgress: boolean;
        hasLabel: boolean;
        rangeSprite?: any;
        thumbSprite?: any;
        progressSprite?: any;
        labelSprite?: any;
    } {
        return {
            hasRange: this.boundRangeSprite !== null,
            hasThumb: this.boundThumbSprite !== null,
            hasProgress: this.boundProgressSprite !== null,
            hasLabel: this.boundLabelSprite !== null,
            rangeSprite: this.boundRangeSprite,
            thumbSprite: this.boundThumbSprite,
            progressSprite: this.boundProgressSprite,
            labelSprite: this.boundLabelSprite
        };
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 创建UISlider容器对象
        codes.push(`${indent}const ${varName} = new UISlider({`);
        codes.push(`${indent}    width: ${this.width},`);
        codes.push(`${indent}    height: ${this.height},`);
        codes.push(`${indent}    value: ${this.value},`);
        codes.push(`${indent}    minValue: ${this.minValue},`);
        codes.push(`${indent}    maxValue: ${this.maxValue},`);
        codes.push(`${indent}    step: ${this.step},`);
        codes.push(`${indent}    enabled: ${this.enabled}`);
        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 重写特定属性设置代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns Slider特定属性设置代码
     */
    protected generateSpecificProperties(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 不在这里生成绑定代码，因为子对象还没创建
        // 绑定代码将在 generateBindingCode() 中生成

        // 事件代码
        if (this.onChangeCode) {
            codes.push(`${indent}// 值改变事件`);
            codes.push(`${indent}${varName}._eventCodes.onChange = '${this.onChangeCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onDragStartCode) {
            codes.push(`${indent}// 开始拖拽事件`);
            codes.push(`${indent}${varName}._eventCodes.onDragStart = '${this.onDragStartCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onDragEndCode) {
            codes.push(`${indent}// 结束拖拽事件`);
            codes.push(`${indent}${varName}._eventCodes.onDragEnd = '${this.onDragEndCode.replace(/'/g, "\\'")}';`);
        }

        return codes.join('\n');
    }

    /**
     * 重写代码生成方法，确保正确的生成顺序
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 生成的代码字符串
     */
    public generateCreationCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        // 1. 对象创建代码
        codes.push(this.generateObjectCreation(varName, indent));

        // 2. 基础属性设置
        codes.push(this.generateBasicProperties(varName, indent));

        // 3. 特定属性设置（不包含绑定）
        codes.push(this.generateSpecificProperties(varName, indent));

        // 4. 子对象代码生成
        if (this.generateChildrenCode) {
            codes.push(this.generateChildrenCreation(varName, indent));
        }

        // 5. 🔑 绑定代码（在子对象创建之后）
        codes.push(this.generateBindingCode(varName, indent));

        return codes.filter(code => code.trim()).join('\n');
    }

    /**
     * 生成绑定代码（在子对象创建之后调用）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 绑定代码
     */
    protected generateBindingCode(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 辅助函数：查找绑定对象对应的子对象变量名
        const findChildVarName = (boundObject: any): string | null => {
            if (!boundObject) return null;

            // 在子对象中查找匹配的对象
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child.getOriginalObject() === boundObject) {
                    return `${varName}_child${i}`;
                }
            }
            return null;
        };

        // 只有当有绑定对象时才添加注释和绑定代码
        let hasBindings = false;

        // 检查是否有任何绑定
        if (this.boundRangeSprite || this.boundThumbSprite ||
            this.boundProgressSprite || this.boundLabelSprite) {
            codes.push(`${indent}// 绑定子组件`);
            hasBindings = true;
        }

        // 绑定子组件（如果有的话）- boundTrackSprite已删除

        if (this.boundRangeSprite) {
            const childVarName = findChildVarName(this.boundRangeSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定范围精灵 (UIImage) - 定义有效范围`);
                codes.push(`${indent}${varName}.bindRangeSprite(${childVarName});`);
            }
        }

        if (this.boundThumbSprite) {
            const childVarName = findChildVarName(this.boundThumbSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定滑块精灵 (UIImage) - 可拖拽控制`);
                codes.push(`${indent}${varName}.bindThumbSprite(${childVarName});`);
            }
        }

        if (this.boundProgressSprite) {
            const childVarName = findChildVarName(this.boundProgressSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定进度精灵 (UIImage) - 显示当前进度`);
                codes.push(`${indent}${varName}.bindProgressSprite(${childVarName});`);
            }
        }

        if (this.boundLabelSprite) {
            const childVarName = findChildVarName(this.boundLabelSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定文本精灵 (UILabel) - 显示数值`);
                codes.push(`${indent}${varName}.bindLabelSprite(${childVarName});`);
            }
        }

        return codes.join('\n');
    }

    /**
     * 克隆当前Slider对象 - 调用插件的 clone 方法
     */
    clone(): SliderModel {
        console.log('🔄 SliderModel: 开始克隆Slider对象（调用插件方法）');

        // 1. 调用原始 UISlider 对象的 clone 方法
        const originalUISlider = this.getOriginalObject();
        if (!originalUISlider || typeof originalUISlider.clone !== 'function') {
            console.error('❌ SliderModel: 原始对象没有 clone 方法');
            throw new Error('UISlider 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUISlider = originalUISlider.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 SliderModel 包装克隆的对象
        const clonedModel = new SliderModel(clonedUISlider);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ SliderModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UISlider 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UISlider 特有属性
            minValue: this.minValue,
            maxValue: this.maxValue,
            value: this.value,
            step: this.step,
            sliderWidth: this.sliderWidth,
            sliderHeight: this.sliderHeight,

            // 事件代码
            _eventCodes: {
                onChange: this.onChangeCode
            }
        };
    }



}

// 注册SliderModel到基类容器
BaseObjectModel.registerModel('UISlider', SliderModel);
BaseObjectModel.registerModel('Slider', SliderModel);
