/**
 * 类型识别和处理工具
 */

import { RPG_MAKER_CLASSES, type ClassInfo } from '../../engine/treeData';

/**
 * 节点类型信息接口
 */
export interface NodeTypeInfo {
  // 类型节点信息
  isTypeNode: boolean;
  classType?: string;
  classInfo?: ClassInfo;
  ico?: string;

  // 继承类型信息
  inheritedType?: string;
  parentClassInfo?: ClassInfo;

  // 模型对象关联信息
  hasModelObject?: boolean;
  modelObject?: any;
  modelClassName?: string;
  modelPropertyName?: string;
  modelClassInfo?: ClassInfo;

  // 窗口类型标识
  isWindowType?: boolean;
}

/**
 * 模型对象关联信息接口
 */
export interface ModelAssociationInfo {
  hasModelObject: boolean;
  modelObject?: any;
  modelClassName?: string;
  modelPropertyName?: string;
  modelClassInfo?: ClassInfo;
}

/**
 * RPG Maker MZ 原生类型列表
 */
const RPG_MAKER_TYPES = [
  // Game Objects
  'Game_Character', 'Game_Player', 'Game_Follower', 'Game_Vehicle', 'Game_Event',
  'Game_Actor', 'Game_Enemy', 'Game_Party', 'Game_Troop',
  'Game_BattlerBase', 'Game_Battler', 'Game_Unit',
  'Game_Action', 'Game_ActionResult', 'Game_Item',
  'Game_Switches', 'Game_Variables', 'Game_SelfSwitches',
  'Game_Screen', 'Game_Picture', 'Game_Timer',
  'Game_Message', 'Game_Scrolling', 'Game_Interpreter',
  'Game_Map', 'Game_CommonEvent', 'Game_CharacterBase',

  // Scenes
  'Scene_Base', 'Scene_Boot', 'Scene_Title', 'Scene_Map', 'Scene_Menu',
  'Scene_Battle', 'Scene_GameEnd', 'Scene_Shop', 'Scene_Name',
  'Scene_ItemBase', 'Scene_Item', 'Scene_Skill', 'Scene_Equip',
  'Scene_Status', 'Scene_Options', 'Scene_File', 'Scene_Save',
  'Scene_Load', 'Scene_GameEnd', 'Scene_Debug',

  // Windows
  'Window_Base', 'Window_Selectable', 'Window_Command', 'Window_Help',
  'Window_Gold', 'Window_MenuCommand', 'Window_MenuStatus',
  'Window_MenuActor', 'Window_ItemCategory', 'Window_ItemList',
  'Window_SkillType', 'Window_SkillStatus', 'Window_SkillList',
  'Window_EquipStatus', 'Window_EquipCommand', 'Window_EquipSlot',
  'Window_EquipItem', 'Window_Status', 'Window_Options',
  'Window_SavefileList', 'Window_ShopCommand', 'Window_ShopBuy',
  'Window_ShopSell', 'Window_ShopNumber', 'Window_ShopStatus',
  'Window_NameEdit', 'Window_NameInput', 'Window_ChoiceList',
  'Window_NumberInput', 'Window_EventItem', 'Window_Message',
  'Window_ScrollText', 'Window_MapName', 'Window_BattleLog',
  'Window_PartyCommand', 'Window_ActorCommand', 'Window_BattleStatus',
  'Window_BattleActor', 'Window_BattleEnemy', 'Window_BattleSkill',
  'Window_BattleItem', 'Window_TitleCommand', 'Window_GameEnd',
  'Window_DebugRange', 'Window_DebugEdit',

  // Sprites
  'Sprite', 'Sprite_Character', 'Sprite_Battler', 'Sprite_Actor',
  'Sprite_Enemy', 'Sprite_Button', 'Sprite_Gauge', 'Sprite_Name',
  'Sprite_StateIcon', 'Sprite_StateOverlay', 'Sprite_Weapon',
  'Sprite_Balloon', 'Sprite_Picture', 'Sprite_Timer',
  'Sprite_Destination', 'Sprite_Clickable', 'Sprite_Animation',
  'Sprite_AnimationMV', 'Sprite_Damage',

  // Managers
  'DataManager', 'ConfigManager', 'StorageManager', 'ImageManager',
  'AudioManager', 'PluginManager', 'BattleManager', 'SceneManager',
  'SoundManager', 'TextManager', 'ColorManager',

  // Core Classes
  'Bitmap', 'Graphics', 'Input', 'TouchInput', 'Utils', 'JsonEx',
  'Video', 'Weather', 'ToneFilter', 'ToneSprite',
  'ScreenSprite', 'Tilemap', 'ShaderTilemap', 'TilingSprite',
  'Point', 'Rectangle', 'Polygon'
];

/**
 * 判断是否为 RPG Maker MZ 原生类型
 * @param className 类名
 * @returns 是否为 RPG Maker MZ 类型
 */
export function isRPGMakerMZType(className: string): boolean {
  return RPG_MAKER_TYPES.includes(className);
}

/**
 * 判断是否为窗口类型
 * @param className 类名
 * @returns 是否为窗口类型
 */
export function isWindowType(className: string): boolean {
  return className.startsWith('Window_') || className === 'Window';
}

/**
 * 检查模型对象关联
 * @param displayObject 显示对象
 * @returns 模型对象关联信息
 */
export function checkModelObjectAssociation(displayObject: any): ModelAssociationInfo {
  // 常见的模型对象属性名（显示对象关联的模型对象）
  const modelPropertyNames = [
    '_character', '_actor', '_enemy', '_battler', '_gameObject', '_data'
  ];

  for (const propName of modelPropertyNames) {
    if (displayObject[propName]) {
      const modelObject = displayObject[propName];
      const modelClassName = modelObject.constructor?.name;

      if (modelClassName && RPG_MAKER_CLASSES.has(modelClassName)) {
        return {
          hasModelObject: true,
          modelObject: modelObject,
          modelClassName: modelClassName,
          modelPropertyName: propName,
          modelClassInfo: RPG_MAKER_CLASSES.get(modelClassName)
        };
      }
    }
  }

  return { hasModelObject: false };
}

/**
 * 确定节点类型信息
 * @param object 对象实例
 * @param parentType 父节点类型
 * @param isMainType 是否是主类型节点
 * @returns 节点类型信息
 */
export function determineNodeType(
  object: any,
  parentType?: string,
  isMainType: boolean = false
): NodeTypeInfo {
  // 1. 检查对象的构造函数名称是否在 RPG_MAKER_CLASSES 中
  const objectClassName = object.constructor?.name;
  const classInfo = objectClassName ? RPG_MAKER_CLASSES.get(objectClassName) : undefined;

  // 2. 检查模型对象关联
  const modelAssociation = checkModelObjectAssociation(object);

  // 3. 确定是否是新类型节点
  const isNewType = !!classInfo;

  // 4. 检查是否是窗口类型
  const isWindow = objectClassName ? isWindowType(objectClassName) : false;

  if (objectClassName === 'Scene_Map') {
    console.log(`🔍 [determineNodeType] Scene_Map 窗口类型检查:`, { objectClassName, isWindow });
  }

  if (classInfo) {
    // 对象本身就是已定义的类型
    console.log(`[TypeInfo] 找到类型节点: ${objectClassName}`, { object, classInfo });
    return {
      isTypeNode: true,
      classType: objectClassName,
      classInfo: classInfo,
      ico: classInfo.ico,
      isWindowType: isWindow,
      ...modelAssociation
    };
  } else if (parentType) {
    // 继承父节点类型
    const parentClassInfo = RPG_MAKER_CLASSES.get(parentType);
    console.log(`[TypeInfo] 继承父类型: ${parentType}`, { objectClassName, parentType, object });
    return {
      isTypeNode: false,
      inheritedType: parentType,
      parentClassInfo: parentClassInfo,
      isWindowType: isWindow,
      ...modelAssociation
    };
  } else {
    // 尝试从对象的原型链或特殊属性推断类型
    const inferredType = inferObjectType(object);
    if (inferredType) {
      const inferredClassInfo = RPG_MAKER_CLASSES.get(inferredType);
      return {
        isTypeNode: false,
        inheritedType: inferredType,
        parentClassInfo: inferredClassInfo,
        isWindowType: isWindow,
        ...modelAssociation
      };
    }
  }

  console.log(`[TypeInfo] 未找到类型信息`, { objectClassName, parentType, object });
  return {
    isTypeNode: false,
    isWindowType: isWindow,
    ...modelAssociation
  };
}

/**
 * 推断对象类型
 * @param object 对象实例
 * @returns 推断的类型名称
 */
function inferObjectType(object: any): string | undefined {
  // 检查常见的 RPG Maker MZ 对象特征
  if (object._character) {
    return 'Sprite_Character';
  }

  if (object._actor) {
    return 'Window_Base'; // 或其他相关窗口类型
  }

  if (object._battler) {
    return 'Sprite_Battler';
  }

  if (object.bitmap && object.anchor !== undefined) {
    return 'Sprite'; // PIXI Sprite 或 RPG Maker MZ Sprite
  }

  if (object.children && Array.isArray(object.children)) {
    return 'Container'; // PIXI Container 或类似容器
  }

  // 检查是否是窗口类型
  if (object.contents && object.windowskin) {
    return 'Window_Base';
  }

  return undefined;
}



/**
 * 获取对象的类型名称
 * @param obj 对象实例
 * @returns 类型名称
 */
export function getObjectTypeName(obj: any): string {
  if (!obj) return 'Unknown';

  // 尝试从构造函数获取名称
  if (obj.constructor && obj.constructor.name) {
    return obj.constructor.name;
  }

  // 尝试从原型链获取
  if (obj.__proto__ && obj.__proto__.constructor && obj.__proto__.constructor.name) {
    return obj.__proto__.constructor.name;
  }

  // 使用 toString 方法
  const toString = Object.prototype.toString.call(obj);
  const match = toString.match(/\[object (\w+)\]/);
  if (match) {
    return match[1];
  }

  return 'Unknown';
}

/**
 * 获取对象的显示名称
 * @param obj 对象实例
 * @param typeName 类型名称
 * @returns 显示名称
 */
export function getObjectDisplayName(obj: any, typeName: string): string {
  // 如果是 Sprite_Character 对象，尝试从其 _character 属性获取信息
  if (obj && obj._character) {
    const character = obj._character;

    // 获取角色名称
    if (character._characterName) {
      return `${typeName} (${character._characterName})`;
    }

    // 获取事件ID
    if (typeof character._eventId === 'number') {
      return `${typeName} (Event ${character._eventId})`;
    }

    // 获取演员ID
    if (typeof character._actorId === 'number') {
      return `${typeName} (Actor ${character._actorId})`;
    }

    // 获取角色类型
    const characterType = getObjectTypeName(character);
    if (characterType && characterType !== 'Object') {
      return `${typeName} (${characterType})`;
    }
  }

  // 如果对象有 name 属性
  if (obj && typeof obj.name === 'string' && obj.name.trim()) {
    return `${typeName} (${obj.name})`;
  }

  // 如果是角色相关对象，尝试获取角色名称
  if (obj && obj._characterName) {
    return `${typeName} (${obj._characterName})`;
  }

  // 如果是事件对象，尝试获取事件ID
  if (obj && typeof obj._eventId === 'number') {
    return `${typeName} (Event ${obj._eventId})`;
  }

  // 如果是演员对象，尝试获取演员ID
  if (obj && typeof obj._actorId === 'number') {
    return `${typeName} (Actor ${obj._actorId})`;
  }

  // 如果是 Sprite 对象，提供更详细的信息
  if (typeName.includes('Sprite')) {
    // 尝试获取更多 Sprite 信息
    if (obj && obj.constructor && obj.constructor.name) {
      const spriteType = obj.constructor.name;

      // 如果有位置信息
      if (typeof obj.x === 'number' && typeof obj.y === 'number') {
        return `${spriteType} (${Math.round(obj.x)}, ${Math.round(obj.y)})`;
      }

      return spriteType;
    }

    return `${typeName}`;
  }

  // 默认返回类型名称
  return typeName;
}

/**
 * 生成唯一的节点ID
 * @param typeName 类型名称
 * @param obj 对象实例
 * @returns 唯一ID
 */
export function generateNodeId(typeName: string, obj: any): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);

  // 尝试使用对象的唯一标识
  let uniqueId = '';
  if (obj && obj._eventId !== undefined) {
    uniqueId = `_event_${obj._eventId}`;
  } else if (obj && obj._actorId !== undefined) {
    uniqueId = `_actor_${obj._actorId}`;
  } else if (obj && obj._characterName) {
    uniqueId = `_char_${obj._characterName}_${obj._characterIndex || 0}`;
  }

  return `${typeName}${uniqueId}_${timestamp}_${random}`;
}

/**
 * 获取对象的子对象
 * @param obj 对象实例
 * @returns 子对象数组
 */
export function getObjectChildren(obj: any): any[] {
  if (!obj || typeof obj !== 'object') {
    return [];
  }

  // 处理包装对象
  let actualObj = obj;
  if (obj.displayObject && typeof obj.displayObject === 'object') {
    actualObj = obj.displayObject;
  }

  // 添加调试信息，特别是对Scene_Map
  const objTypeName = getObjectTypeName(actualObj);
  if (objTypeName === 'Scene_Map') {
    console.log(`🔍 [getObjectChildren] Scene_Map:`, {
      hasChildren: !!actualObj.children,
      childrenLength: actualObj.children ? actualObj.children.length : 0,
      children: actualObj.children ? actualObj.children.map(child => getObjectTypeName(child)) : []
    });
  }

  // 只检查 children 数组（显示对象的子对象）
  if (actualObj.children && Array.isArray(actualObj.children)) {
    const children = actualObj.children.filter(child => child != null);
    if (objTypeName === 'Scene_Map' && children.length > 0) {
      console.log(`🔍 [getObjectChildren] Scene_Map 返回子对象:`, children.map(child => getObjectTypeName(child)));
    }
    return children;
  }

  if (objTypeName === 'Scene_Map') {
    console.log(`🔍 [getObjectChildren] Scene_Map 没有 children 数组`);
  }

  return [];
}

/**
 * 检查对象是否有子对象
 * @param obj 对象实例
 * @returns 是否有子对象
 */
export function hasChildren(obj: any): boolean {
  return getObjectChildren(obj).length > 0;
}
