/**
 * 复制工具类
 * 提供通用的复制功能和工具方法
 */

import type { ObjectType, ObjectStructure, CopyOptions } from './types';

/**
 * 复制工具类
 */
export class CopyUtils {
  
  /**
   * 检测对象类型
   * @param obj 要检测的对象
   * @returns 对象类型
   */
  static detectObjectType(obj: any): ObjectType {
    if (!obj) return ObjectType.UNKNOWN;

    // 检查包装结构
    if (obj.displayObject && obj.gameObject) {
      if (obj.gameType) {
        switch (obj.gameType) {
          case 'Game_Player': return ObjectType.GAME_PLAYER;
          case 'Game_Event': return ObjectType.GAME_EVENT;
          case 'Game_Follower': return ObjectType.GAME_FOLLOWER;
          case 'Game_Vehicle': return ObjectType.GAME_VEHICLE;
          case 'Game_Character': return ObjectType.GAME_CHARACTER;
        }
      }
    }

    // 检查直接对象
    if (obj._character) {
      const character = obj._character;
      if (character.constructor) {
        switch (character.constructor.name) {
          case 'Game_Player': return ObjectType.GAME_PLAYER;
          case 'Game_Event': return ObjectType.GAME_EVENT;
          case 'Game_Follower': return ObjectType.GAME_FOLLOWER;
          case 'Game_Vehicle': return ObjectType.GAME_VEHICLE;
          case 'Game_Character': return ObjectType.GAME_CHARACTER;
        }
      }
    }

    // 检查对象本身的构造函数
    if (obj.constructor) {
      switch (obj.constructor.name) {
        case 'Game_Player': return ObjectType.GAME_PLAYER;
        case 'Game_Event': return ObjectType.GAME_EVENT;
        case 'Game_Follower': return ObjectType.GAME_FOLLOWER;
        case 'Game_Vehicle': return ObjectType.GAME_VEHICLE;
        case 'Game_Character': return ObjectType.GAME_CHARACTER;
        case 'Sprite_Character': return ObjectType.SPRITE_CHARACTER;
      }
    }

    return ObjectType.UNKNOWN;
  }

  /**
   * 解析对象结构
   * @param obj 要解析的对象
   * @returns 对象结构信息
   */
  static parseObjectStructure(obj: any): ObjectStructure {
    if (!obj) {
      return { isWrapper: false };
    }

    // 检查是否为包装结构
    if (obj.displayObject && obj.gameObject) {
      return {
        isWrapper: true,
        displayObject: obj.displayObject,
        gameObject: obj.gameObject,
        type: obj.type,
        gameType: obj.gameType,
        displayName: obj.displayName
      };
    }

    // 检查是否为 Sprite_Character
    if (obj._character) {
      return {
        isWrapper: false,
        displayObject: obj,
        gameObject: obj._character,
        type: 'Sprite_Character',
        gameType: obj._character.constructor?.name
      };
    }

    // 直接的游戏对象
    return {
      isWrapper: false,
      gameObject: obj,
      gameType: obj.constructor?.name
    };
  }

  /**
   * 复制显示属性
   * @param source 源对象
   * @param target 目标对象
   */
  static copyDisplayProperties(source: any, target: any): void {
    if (!source || !target) return;

    console.log('[CopyUtils] 开始复制显示属性');

    // 基本显示属性
    const basicProperties = ['alpha', 'tint', 'rotation', 'visible'];
    
    for (const prop of basicProperties) {
      if (source[prop] !== undefined && this.isPropertyWritable(target, prop)) {
        try {
          target[prop] = source[prop];
          console.log(`[CopyUtils] 复制属性 ${prop}:`, source[prop]);
        } catch (error) {
          console.warn(`[CopyUtils] 复制属性 ${prop} 失败:`, error);
        }
      }
    }

    // 复制缩放
    if (source.scale && target.scale) {
      try {
        if (typeof target.scale.set === 'function') {
          target.scale.set(source.scale.x || 1, source.scale.y || 1);
        } else {
          target.scale.x = source.scale.x || 1;
          target.scale.y = source.scale.y || 1;
        }
        console.log('[CopyUtils] 复制缩放属性');
      } catch (error) {
        console.warn('[CopyUtils] 复制缩放属性失败:', error);
      }
    }

    // 复制锚点
    if (source.anchor && target.anchor) {
      try {
        if (typeof target.anchor.set === 'function') {
          target.anchor.set(source.anchor.x || 0, source.anchor.y || 0);
        } else {
          target.anchor.x = source.anchor.x || 0;
          target.anchor.y = source.anchor.y || 0;
        }
        console.log('[CopyUtils] 复制锚点属性');
      } catch (error) {
        console.warn('[CopyUtils] 复制锚点属性失败:', error);
      }
    }

    console.log('[CopyUtils] 显示属性复制完成');
  }

  /**
   * 检查属性是否可写
   * @param obj 对象
   * @param prop 属性名
   * @returns 是否可写
   */
  static isPropertyWritable(obj: any, prop: string): boolean {
    try {
      const descriptor = Object.getOwnPropertyDescriptor(obj, prop);
      if (descriptor) {
        return descriptor.writable !== false && !descriptor.get;
      }
      
      // 尝试设置相同的值
      const originalValue = obj[prop];
      obj[prop] = originalValue;
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 生成唯一名称
   * @param baseName 基础名称
   * @param suffix 后缀
   * @returns 唯一名称
   */
  static generateUniqueName(baseName: string, suffix: string): string {
    if (!baseName) {
      baseName = 'CopiedObject';
    }

    // 移除已有的复制后缀
    const cleanName = baseName.replace(/_copy.*$/, '');
    
    // 生成时间戳和随机数
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    
    return `${cleanName}${suffix}_${timestamp}_${random}`;
  }

  /**
   * 应用位置偏移
   * @param gameObject 游戏对象
   * @param offset 偏移量
   */
  static applyPositionOffset(gameObject: any, offset: { x: number; y: number }): void {
    if (!gameObject || !offset) return;

    console.log('[CopyUtils] 应用位置偏移:', offset);

    try {
      // 使用 setPosition 方法（如果存在）
      if (typeof gameObject.setPosition === 'function') {
        const newX = (gameObject._x || 0) + offset.x;
        const newY = (gameObject._y || 0) + offset.y;
        gameObject.setPosition(newX, newY);
        console.log(`[CopyUtils] 使用 setPosition 设置位置: (${newX}, ${newY})`);
      } else {
        // 直接设置坐标属性
        if (gameObject._x !== undefined) {
          gameObject._x += offset.x;
        }
        if (gameObject._y !== undefined) {
          gameObject._y += offset.y;
        }
        console.log('[CopyUtils] 直接设置坐标属性');
      }
    } catch (error) {
      console.warn('[CopyUtils] 应用位置偏移失败:', error);
    }
  }

  /**
   * 更新显示名称
   * @param obj 对象
   * @param suffix 后缀
   */
  static updateDisplayName(obj: any, suffix: string): void {
    if (!obj) return;

    try {
      if (obj.displayName) {
        const baseName = obj.displayName.replace(/_copy.*$/, '');
        obj.displayName = this.generateUniqueName(baseName, suffix);
        console.log('[CopyUtils] 更新显示名称:', obj.displayName);
      }

      if (obj.name) {
        const baseName = obj.name.replace(/_copy.*$/, '');
        obj.name = this.generateUniqueName(baseName, suffix);
        console.log('[CopyUtils] 更新对象名称:', obj.name);
      }
    } catch (error) {
      console.warn('[CopyUtils] 更新名称失败:', error);
    }
  }

  /**
   * 等待资源加载
   * @param timeout 超时时间（毫秒）
   * @returns 是否加载成功
   */
  static async waitForResources(timeout: number = 5000): Promise<boolean> {
    const { objManage } = await import('../../objManage');
    
    const resourceStatus = objManage.getResourceStatus();
    if (resourceStatus.all) {
      return true;
    }

    console.log('[CopyUtils] 等待资源加载完成...');
    return objManage.waitForResources(timeout);
  }
}
