/**
 * 对象管理器
 * 负责管理舞台上的RPG Maker MZ对象
 * 舞台上只能有一个对象，添加新对象时会清除现有对象
 */

declare global {
  interface Window {
    Graphics: any;
    ImageManager: any;
    DataManager: any;
    EffectManager: any;
    AudioManager: any;
  }
}

class ObjectManager {
  private currentObject: any = null;

  /**
   * 添加对象到舞台
   * 会先清除现有对象，然后添加新对象
   * @param obj RPG Maker MZ对象（精灵等）
   * @param centerObject 是否居中对象，默认true，场景对象通常为false
   */
  public addToStage(obj: any, centerObject: boolean = true): void {
    console.log('=== 添加对象到舞台 ===');

    try {
      // 1. 检查引擎是否准备就绪
      if (!this.isEngineReady()) {
        console.error('引擎未准备就绪，无法添加对象到舞台');
        return;
      }

      // 2. 清除现有对象
      this.clearStage();

      // 3. 处理异步bitmap加载
      this.handleObjectSetup(obj, centerObject);

    } catch (error) {
      console.error('添加对象到舞台失败:', error);
    }
  }

  /**
   * 检查引擎和资源是否准备就绪
   * @returns boolean
   */
  private isEngineReady(): boolean {
    // 检查Graphics对象
    if (!window.Graphics || !window.Graphics._app || !window.Graphics._app.stage) {
      console.warn('Graphics引擎未初始化');
      return false;
    }

    // 检查数据库是否加载完成
    if (window.DataManager && !window.DataManager.isDatabaseLoaded()) {
      console.warn('数据库尚未加载完成');
      return false;
    }

    // 检查图片管理器是否准备就绪
    if (window.ImageManager && !window.ImageManager.isReady()) {
      console.warn('图片资源尚未加载完成');
      return false;
    }

    return true;
  }

  /**
   * 清除舞台上的现有对象
   */
  private clearStage(): void {
    if (this.currentObject) {
      console.log('清除现有对象');

      // 从舞台移除
      if (this.currentObject.parent) {
        this.currentObject.parent.removeChild(this.currentObject);
      }

      this.currentObject = null;
    }
  }

  /**
   * 处理对象设置（包括异步bitmap加载）
   * @param obj 要添加的对象
   * @param centerObject 是否居中对象
   */
  private handleObjectSetup(obj: any, centerObject: boolean = true): void {
    console.log('处理对象设置:', obj.constructor?.name || 'Unknown');

    // 更新对象以设置bitmap（对于RPG Maker MZ精灵很重要）
    if (obj.update) {
      obj.update();
    }

    // 检查对象类型和bitmap状态
    const bitmapInfo = this.analyzeBitmapStatus(obj);
    console.log('Bitmap状态分析:', bitmapInfo);

    if (bitmapInfo.hasBitmap) {
      if (bitmapInfo.isReady) {
        // bitmap已准备就绪，直接添加
        console.log('Bitmap已准备就绪，直接添加到舞台');
        this.finalizeObjectSetup(obj, centerObject);
      } else {
        // bitmap还在加载，等待完成
        console.log('等待bitmap加载完成...');
        if (bitmapInfo.bitmap && typeof bitmapInfo.bitmap.addLoadListener === 'function') {
          bitmapInfo.bitmap.addLoadListener(() => {
            console.log('Bitmap加载完成，添加到舞台');
            this.finalizeObjectSetup(obj, centerObject);
          });
        } else {
          // 如果没有 addLoadListener 方法，使用轮询方式
          console.log('使用轮询方式等待bitmap加载...');
          const checkBitmapReady = () => {
            if (bitmapInfo.bitmap && typeof bitmapInfo.bitmap.isReady === 'function' && bitmapInfo.bitmap.isReady()) {
              console.log('Bitmap加载完成（轮询检测），添加到舞台');
              this.finalizeObjectSetup(obj, centerObject);
            } else {
              setTimeout(checkBitmapReady, 100);
            }
          };
          checkBitmapReady();
        }
      }
    } else {
      // 没有bitmap的对象（如Graphics对象、容器等），直接添加
      console.log('对象无需bitmap，直接添加到舞台');
      this.finalizeObjectSetup(obj, centerObject);
    }
  }

  /**
   * 分析对象的bitmap状态
   * @param obj 要分析的对象
   * @returns bitmap状态信息
   */
  private analyzeBitmapStatus(obj: any): {
    hasBitmap: boolean;
    isReady: boolean;
    bitmap: any;
    type: string;
  } {
    let bitmap = null;
    let hasBitmap = false;
    let isReady = false;
    let type = 'unknown';

    // 检查直接的bitmap属性
    if (obj.bitmap) {
      bitmap = obj.bitmap;
      hasBitmap = true;
      isReady = (bitmap && typeof bitmap.isReady === 'function') ? bitmap.isReady() : false;
      type = 'direct';
    }
    // 检查是否是Sprite_Character类型
    else if (obj._character && obj._character._characterName) {
      // 这是一个角色精灵，需要检查角色图像
      const characterName = obj._character._characterName;
      if (characterName && window.ImageManager) {
        bitmap = window.ImageManager.loadCharacter(characterName);
        hasBitmap = true;
        isReady = (bitmap && typeof bitmap.isReady === 'function') ? bitmap.isReady() : false;
        type = 'character';

        // 更新精灵的bitmap
        obj.bitmap = bitmap;
      }
    }
    // 检查是否是其他类型的精灵
    else if (obj.texture && obj.texture.baseTexture) {
      // PIXI精灵，检查纹理状态
      isReady = obj.texture.baseTexture.valid;
      type = 'pixi';
    }

    return { hasBitmap, isReady, bitmap, type };
  }

  /**
   * 完成对象设置并添加到舞台
   * @param obj 要添加的对象
   * @param centerObject 是否居中对象
   */
  private finalizeObjectSetup(obj: any, centerObject: boolean = true): void {
    console.log('完成对象设置并添加到舞台');

    try {
      // 确保对象状态正确
      if (obj.update) {
        obj.update();
      }

      // 设置基本属性
      this.setupObjectProperties(obj);

      // 设置对象位置
      if (centerObject) {
        this.centerObject(obj);
      } else {
        // 场景对象等不需要居中，保持在 (0, 0)
        obj.x = 0;
        obj.y = 0;
        console.log(`对象位置设置为 (0, 0)`);
      }

      // 添加到舞台
      window.Graphics._app.stage.addChild(obj);

      // 保存当前对象引用
      this.currentObject = obj;

      // 输出调试信息
      this.logObjectInfo(obj);

    } catch (error) {
      console.error('完成对象设置失败:', error);
      throw error;
    }
  }

  /**
   * 设置对象的基本属性
   * @param obj 要设置的对象
   */
  private setupObjectProperties(obj: any): void {
    // 设置可见性
    obj.visible = true;

    // 设置透明度
    if (obj.alpha !== undefined) {
      obj.alpha = 1;
    }

    // 设置缩放（如果需要）
    if (obj.scale) {
      obj.scale.x = 1;
      obj.scale.y = 1;
    }


  }

  /**
   * 将对象居中显示
   * @param obj 要居中的对象
   */
  private centerObject(obj: any): void {
    const centerX = window.Graphics.width / 2;
    const centerY = window.Graphics.height / 2;

    obj.x = centerX;
    obj.y = centerY;

    console.log(`对象已居中，位置: (${obj.x}, ${obj.y})`);
  }

  /**
   * 输出对象信息
   * @param obj 要输出信息的对象
   */
  private logObjectInfo(obj: any): void {
    console.log('=== 对象信息 ===');
    console.log('类型:', obj.constructor?.name || 'Unknown');
    console.log('位置:', `(${obj.x || 0}, ${obj.y || 0})`);
    console.log('尺寸:', `${obj.width || 'unknown'} x ${obj.height || 'unknown'}`);
    console.log('可见性:', obj.visible);
    console.log('透明度:', obj.alpha);
    console.log('缩放:', obj.scale ? `(${obj.scale.x}, ${obj.scale.y})` : 'unknown');
    console.log('锚点:', obj.anchor ? `(${obj.anchor.x}, ${obj.anchor.y})` : 'unknown');

    if (obj.bitmap) {
      console.log('Bitmap:', {
        url: obj.bitmap.url || 'unknown',
        size: `${obj.bitmap.width || 0} x ${obj.bitmap.height || 0}`,
        ready: (obj.bitmap && typeof obj.bitmap.isReady === 'function') ? obj.bitmap.isReady() : 'unknown',
        type: obj.bitmap.constructor?.name || 'unknown'
      });
    } else {
      console.warn('⚠️ 对象没有 bitmap！');
    }

    // 检查角色相关信息
    if (obj._character) {
      console.log('角色信息:', {
        characterName: obj._character._characterName,
        characterIndex: obj._character._characterIndex,
        direction: obj._character._direction,
        x: obj._character._x,
        y: obj._character._y
      });
    }

    // 检查舞台信息
    const stage = window.Graphics._app.stage;
    console.log(`舞台子对象数量: ${stage.children.length}`);
    console.log('舞台尺寸:', `${window.Graphics.width} x ${window.Graphics.height}`);

    // 检查对象是否真的在舞台上
    const isOnStage = stage.children.includes(obj);
    console.log('对象是否在舞台上:', isOnStage);

    console.log('=== 对象添加完成 ===');
  }

  /**
   * 获取当前舞台上的对象
   * @returns 当前对象或null
   */
  public getCurrentObject(): any {
    return this.currentObject;
  }

  /**
   * 检查舞台是否为空
   * @returns boolean
   */
  public isEmpty(): boolean {
    return this.currentObject === null;
  }

  /**
   * 获取资源加载状态
   * @returns 资源状态信息
   */
  public getResourceStatus(): {
    database: boolean;
    images: boolean;
    effects: boolean;
    audio: boolean;
    all: boolean;
  } {
    const database = window.DataManager ? window.DataManager.isDatabaseLoaded() : false;
    const images = window.ImageManager ? window.ImageManager.isReady() : false;
    const effects = window.EffectManager ? window.EffectManager.isReady() : true; // 可选
    const audio = true; // 音频通常不需要等待全部加载完成

    return {
      database,
      images,
      effects,
      audio,
      all: database && images && effects && audio
    };
  }

  /**
   * 等待资源加载完成
   * @param timeout 超时时间（毫秒），默认10秒
   * @returns Promise<boolean> 是否成功加载
   */
  public waitForResources(timeout: number = 10000): Promise<boolean> {
    return new Promise((resolve) => {
      const startTime = Date.now();

      const checkResources = () => {
        const status = this.getResourceStatus();

        if (status.all) {
          console.log('所有资源加载完成');
          resolve(true);
          return;
        }

        if (Date.now() - startTime > timeout) {
          console.warn('资源加载超时');
          console.log('当前资源状态:', status);
          resolve(false);
          return;
        }

        // 继续等待
        setTimeout(checkResources, 100);
      };

      checkResources();
    });
  }

  /**
   * 预加载常用资源
   */
  public preloadCommonResources(): void {
    console.log('预加载常用资源...');

    if (window.ImageManager) {
      // 预加载系统图片
      window.ImageManager.loadSystem('Window');
      window.ImageManager.loadSystem('IconSet');

      // 预加载常用角色图片
      window.ImageManager.loadCharacter('Actor1');
      window.ImageManager.loadCharacter('Actor2');
    }

    if (window.AudioManager) {
      // 预加载常用音效
      window.AudioManager.loadStaticSe({ name: 'Cursor1' });
      window.AudioManager.loadStaticSe({ name: 'Decision1' });
    }

    console.log('预加载请求已发送');
  }
}

// 导出单例实例
export const objManage = new ObjectManager();