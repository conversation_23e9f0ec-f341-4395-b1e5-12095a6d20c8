/**
 * RPG Maker MZ 对象创建器集合
 * 提供各种游戏对象的创建和管理功能
 */

import { createActorSprite, create<PERSON><PERSON><PERSON>, createTest<PERSON>haracter } from './character/CharacterCreator';
import { createEvent } from './character/EventCreator';
import { createFollower } from './character/FollowerCreator';
import { createPlayer } from './character/PlayerCreator';
import { createVehicle } from './character/VehicleCreator';

// Scene creators
import { createScene } from './scene/SceneCreator';
import { createSceneBoot } from './scene/SceneBootCreator';
import { createSceneTitle } from './scene/SceneTitleCreator';
import { createSceneMap } from './scene/SceneMapCreator';
import { createSceneBattle } from './scene/SceneBattleCreator';
import { createSceneMenuBase } from './scene/SceneMenuBaseCreator';
import { createSceneMenu } from './scene/SceneMenuCreator';
import { createSceneItemBase } from './scene/SceneItemBaseCreator';
import { createSceneItem } from './scene/SceneItemCreator';
import { createSceneSkill } from './scene/SceneSkillCreator';
import { createSceneEquip } from './scene/SceneEquipCreator';
import { createSceneStatus } from './scene/SceneStatusCreator';
import { createSceneOptions } from './scene/SceneOptionsCreator';
import { createSceneFile } from './scene/SceneFileCreator';
import { createSceneSave } from './scene/SceneSaveCreator';
import { createSceneLoad } from './scene/SceneLoadCreator';

// Window creators
import { createWindow } from './window/WindowCreator';
import { createWindowBase } from './window/WindowBaseCreator';
import { createWindowScrollable } from './window/WindowScrollableCreator';
import { createWindowSelectable } from './window/WindowSelectableCreator';
import { createWindowCommand } from './window/WindowCommandCreator';
import { createWindowHorzCommand } from './window/WindowHorzCommandCreator';
import { createWindowMenuCommand } from './window/WindowMenuCommandCreator';
import { createWindowItemCategory } from './window/WindowItemCategoryCreator';
import { createWindowMenuStatus } from './window/WindowMenuStatusCreator';

export {
  createCharacter,
  createActorSprite,
  createRandomCharacter,
  getAvailableActors,
  preloadAllCharacterImages,
  createTestCharacter,
  createLargeCharacter
} from './character/CharacterCreator';

// Export scene creators
export {
  createScene,
  preloadAllSceneResources
} from './scene/SceneCreator';

export { createSceneBoot } from './scene/SceneBootCreator';
export { createSceneTitle } from './scene/SceneTitleCreator';
export { createSceneMap } from './scene/SceneMapCreator';
export { createSceneBattle } from './scene/SceneBattleCreator';
export { createSceneMenuBase } from './scene/SceneMenuBaseCreator';
export { createSceneMenu } from './scene/SceneMenuCreator';
export { createSceneItemBase } from './scene/SceneItemBaseCreator';
export { createSceneItem } from './scene/SceneItemCreator';
export { createSceneSkill } from './scene/SceneSkillCreator';
export { createSceneEquip } from './scene/SceneEquipCreator';
export { createSceneStatus } from './scene/SceneStatusCreator';
export { createSceneOptions } from './scene/SceneOptionsCreator';
export { createSceneFile } from './scene/SceneFileCreator';
export { createSceneSave } from './scene/SceneSaveCreator';
export { createSceneLoad } from './scene/SceneLoadCreator';

// Export window creators
export {
  createWindow,
  preloadAllWindowResources
} from './window/WindowCreator';

export { createWindowBase } from './window/WindowBaseCreator';
export { createWindowScrollable } from './window/WindowScrollableCreator';
export { createWindowSelectable } from './window/WindowSelectableCreator';
export { createWindowCommand } from './window/WindowCommandCreator';
export { createWindowHorzCommand } from './window/WindowHorzCommandCreator';
export { createWindowMenuCommand } from './window/WindowMenuCommandCreator';
export { createWindowItemCategory } from './window/WindowItemCategoryCreator';
export { createWindowMenuStatus } from './window/WindowMenuStatusCreator';

/**
 * 根据类型字符串创建对应的游戏对象
 * @param className 类名字符串
 * @param options 创建选项
 * @returns 创建的对象或null
 */
export async function createObjectByClassName(
  className: string,
  options: any = {}
): Promise<any> {
  console.log(`=== 创建对象: ${className} ===`);
  console.log('创建选项:', options);

  try {
    // 导入全局状态管理
    const { setRootObject } = await import('../../stores/objectState');

    let createdObject: any = null;

    switch (className) {
      // Game_Character 相关类
      case 'Game_Character':
        createdObject = await createCharacter(
          options.characterName || 'Actor1',
          options.characterIndex || 0,
          options.direction || 2
        );
        break;

      case 'Game_Player':
        createdObject = await createPlayer(options);
        break;

      case 'Game_Follower':
        createdObject = await createFollower(options);
        break;

      case 'Game_Vehicle':
        createdObject = await createVehicle(options);
        break;

      case 'Game_Event':
        createdObject = await createEvent(options);
        break;

      // 其他角色相关类
      case 'Game_Actor':
        if (options.actorId) {
          createdObject = await createActorSprite(options.actorId);
        } else {
          console.warn('创建 Game_Actor 需要 actorId 参数');
          createdObject = await createTestCharacter();
        }
        break;

      // Scene 相关类
      case 'Scene_Base':
        createdObject = await createScene('Scene_Base', options);
        break;

      case 'Scene_Boot':
        createdObject = await createSceneBoot(options);
        break;

      case 'Scene_Title':
        createdObject = await createSceneTitle(options);
        break;

      case 'Scene_Map':
        createdObject = await createSceneMap(options);
        break;

      case 'Scene_Battle':
        createdObject = await createSceneBattle(options);
        break;

      case 'Scene_MenuBase':
        createdObject = await createSceneMenuBase(options);
        break;

      case 'Scene_Menu':
        createdObject = await createSceneMenu(options);
        break;

      case 'Scene_ItemBase':
        createdObject = await createSceneItemBase(options);
        break;

      case 'Scene_Item':
        createdObject = await createSceneItem(options);
        break;

      case 'Scene_Skill':
        createdObject = await createSceneSkill(options);
        break;

      case 'Scene_Equip':
        createdObject = await createSceneEquip(options);
        break;

      case 'Scene_Status':
        createdObject = await createSceneStatus(options);
        break;

      case 'Scene_Options':
        createdObject = await createSceneOptions(options);
        break;

      case 'Scene_File':
        createdObject = await createSceneFile(options);
        break;

      case 'Scene_Save':
        createdObject = await createSceneSave(options);
        break;

      case 'Scene_Load':
        createdObject = await createSceneLoad(options);
        break;

      // Window 相关类
      case 'Window_Base':
        createdObject = await createWindowBase(options);
        break;

      case 'Window_Scrollable':
        createdObject = await createWindowScrollable(options);
        break;

      case 'Window_Selectable':
        createdObject = await createWindowSelectable(options);
        break;

      case 'Window_Command':
        createdObject = await createWindowCommand(options);
        break;

      case 'Window_HorzCommand':
        createdObject = await createWindowHorzCommand(options);
        break;

      case 'Window_MenuCommand':
        createdObject = await createWindowMenuCommand(options);
        break;

      case 'Window_ItemCategory':
        createdObject = await createWindowItemCategory(options);
        break;

      case 'Window_MenuStatus':
        createdObject = await createWindowMenuStatus(options);
        break;

      // 默认情况
      default:
        console.warn(`暂不支持创建类型: ${className}`);
        console.log('支持的类型:', [
          'Game_Character',
          'Game_Player',
          'Game_Follower',
          'Game_Vehicle',
          'Game_Event',
          'Game_Actor',
          'Scene_Base',
          'Scene_Boot',
          'Scene_Title',
          'Scene_Map',
          'Scene_Battle',
          'Scene_MenuBase',
          'Scene_Menu',
          'Scene_ItemBase',
          'Scene_Item',
          'Scene_Skill',
          'Scene_Equip',
          'Scene_Status',
          'Scene_Options',
          'Scene_File',
          'Scene_Save',
          'Scene_Load',
          'Window_Base',
          'Window_Scrollable',
          'Window_Selectable',
          'Window_Command',
          'Window_HorzCommand',
          'Window_MenuCommand',
          'Window_ItemCategory',
          'Window_MenuStatus'
        ]);

        // 如果是角色相关的类，创建一个默认角色
        if (className.includes('Character') || className.includes('Actor')) {
          console.log('检测到角色相关类，创建默认角色');
          createdObject = await createTestCharacter();
        }
        // 如果是场景相关的类，创建一个默认场景
        else if (className.includes('Scene')) {
          console.log('检测到场景相关类，创建默认场景');
          createdObject = await createScene(className, options);
        }
        // 如果是窗口相关的类，创建一个默认窗口
        else if (className.includes('Window')) {
          console.log('检测到窗口相关类，创建默认窗口');
          createdObject = await createWindow(className, options);
        } else {
          return null;
        }
        break;
    }

    // 如果成功创建对象，更新全局状态
    if (createdObject) {
      console.log(`对象创建成功，更新全局状态: ${className}`);
      setRootObject(createdObject, className);
    }

    return createdObject;
  } catch (error) {
    console.error(`创建对象 ${className} 失败:`, error);
    throw error;
  }
}

/**
 * 获取支持的类型列表
 * @returns 支持的类型数组
 */
export function getSupportedClassNames(): string[] {
  return [
    // Game Objects
    'Game_Character',
    'Game_Player',
    'Game_Follower',
    'Game_Vehicle',
    'Game_Event',
    'Game_Actor',

    // Scenes
    'Scene_Base',
    'Scene_Boot',
    'Scene_Title',
    'Scene_Map',
    'Scene_Battle',
    'Scene_MenuBase',
    'Scene_Menu',
    'Scene_ItemBase',
    'Scene_Item',
    'Scene_Skill',
    'Scene_Equip',
    'Scene_Status',
    'Scene_Options',
    'Scene_File',
    'Scene_Save',
    'Scene_Load',

    // Windows
    'Window_Base',
    'Window_Scrollable',
    'Window_Selectable',
    'Window_Command',
    'Window_HorzCommand',
    'Window_MenuCommand',
    'Window_ItemCategory',
    'Window_MenuStatus'
  ];
}

/**
 * 检查类型是否支持
 * @param className 类名
 * @returns 是否支持
 */
export function isClassNameSupported(className: string): boolean {
  return getSupportedClassNames().includes(className);
}
