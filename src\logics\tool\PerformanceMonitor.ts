/**
 * 性能监控工具 - 监控Tool系统的性能表现
 */

export interface PerformanceMetrics {
  dragLatency: number[];
  renderLatency: number[];
  coordinateTransformTime: number[];
  modelUpdateTime: number[];
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor | null = null;
  private metrics: PerformanceMetrics = {
    dragLatency: [],
    renderLatency: [],
    coordinateTransformTime: [],
    modelUpdateTime: []
  };
  
  private timers: Map<string, number> = new Map();
  private enabled: boolean = false;
  private maxSamples: number = 100;

  static getInstance(): PerformanceMonitor {
    if (!this.instance) {
      this.instance = new PerformanceMonitor();
    }
    return this.instance;
  }

  /**
   * 启用性能监控
   */
  enable(): void {
    this.enabled = true;
    console.log('📊 PerformanceMonitor: 性能监控已启用');
  }

  /**
   * 禁用性能监控
   */
  disable(): void {
    this.enabled = false;
    console.log('📊 PerformanceMonitor: 性能监控已禁用');
  }

  /**
   * 开始计时
   */
  startTimer(name: string): void {
    if (!this.enabled) return;
    this.timers.set(name, performance.now());
  }

  /**
   * 结束计时并记录
   */
  endTimer(name: string, category: keyof PerformanceMetrics): void {
    if (!this.enabled) return;
    
    const startTime = this.timers.get(name);
    if (startTime === undefined) return;

    const duration = performance.now() - startTime;
    this.timers.delete(name);

    // 记录到对应的指标数组
    const metrics = this.metrics[category];
    metrics.push(duration);

    // 保持数组大小在限制内
    if (metrics.length > this.maxSamples) {
      metrics.shift();
    }
  }

  /**
   * 获取性能统计
   */
  getStats(): {
    dragLatency: { avg: number; max: number; min: number; count: number };
    renderLatency: { avg: number; max: number; min: number; count: number };
    coordinateTransformTime: { avg: number; max: number; min: number; count: number };
    modelUpdateTime: { avg: number; max: number; min: number; count: number };
  } {
    const calculateStats = (values: number[]) => {
      if (values.length === 0) {
        return { avg: 0, max: 0, min: 0, count: 0 };
      }
      
      const sum = values.reduce((a, b) => a + b, 0);
      return {
        avg: Number((sum / values.length).toFixed(2)),
        max: Number(Math.max(...values).toFixed(2)),
        min: Number(Math.min(...values).toFixed(2)),
        count: values.length
      };
    };

    return {
      dragLatency: calculateStats(this.metrics.dragLatency),
      renderLatency: calculateStats(this.metrics.renderLatency),
      coordinateTransformTime: calculateStats(this.metrics.coordinateTransformTime),
      modelUpdateTime: calculateStats(this.metrics.modelUpdateTime)
    };
  }

  /**
   * 打印性能报告
   */
  printReport(): void {
    if (!this.enabled) {
      console.log('📊 PerformanceMonitor: 性能监控未启用');
      return;
    }

    const stats = this.getStats();
    console.group('📊 Tool系统性能报告');
    
    console.log('🎯 拖动延迟:', stats.dragLatency);
    console.log('🎨 渲染延迟:', stats.renderLatency);
    console.log('🔧 坐标转换时间:', stats.coordinateTransformTime);
    console.log('📝 模型更新时间:', stats.modelUpdateTime);
    
    // 性能建议
    if (stats.dragLatency.avg > 16) {
      console.warn('⚠️ 拖动延迟过高 (>16ms)，建议优化');
    }
    if (stats.renderLatency.avg > 16) {
      console.warn('⚠️ 渲染延迟过高 (>16ms)，建议优化');
    }
    if (stats.coordinateTransformTime.avg > 1) {
      console.warn('⚠️ 坐标转换时间过长 (>1ms)，建议优化');
    }
    
    console.groupEnd();
  }

  /**
   * 清除所有指标
   */
  clearMetrics(): void {
    this.metrics = {
      dragLatency: [],
      renderLatency: [],
      coordinateTransformTime: [],
      modelUpdateTime: []
    };
    this.timers.clear();
    console.log('📊 PerformanceMonitor: 指标已清除');
  }

  /**
   * 设置最大样本数
   */
  setMaxSamples(count: number): void {
    this.maxSamples = count;
  }

  /**
   * 检查是否启用
   */
  isEnabled(): boolean {
    return this.enabled;
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance();
