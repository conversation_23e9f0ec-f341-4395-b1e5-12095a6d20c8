import {
  StaticDataType,
  DATA_TYPE_TO_GLOBAL,
  DATA_TYPE_DISPLAY_NAMES,
  type DataTypeInfo,
  type FieldInfo,
  type DataSelection,
  type DataManagerConfig
} from '../types/dataTypes';
import { FieldParser } from '../parsers/FieldParser';

export class DataManager {
  private cache: Map<StaticDataType, DataTypeInfo> = new Map();
  private config: DataManagerConfig;

  constructor(config: Partial<DataManagerConfig> = {}) {
    this.config = {
      enableCache: true,
      maxCacheSize: 50,
      autoRefresh: false,
      ...config
    };
  }

  /**
   * 获取所有可用的数据类型
   */
  getAvailableDataTypes(): Array<{ type: StaticDataType; displayName: string }> {
    return Object.values(StaticDataType).map(type => ({
      type,
      displayName: DATA_TYPE_DISPLAY_NAMES[type]
    }));
  }

  /**
   * 获取指定数据类型的信息
   */
  async getDataTypeInfo(dataType: StaticDataType): Promise<DataTypeInfo | null> {
    // 检查缓存
    if (this.config.enableCache && this.cache.has(dataType)) {
      return this.cache.get(dataType)!;
    }

    try {
      const globalVar = DATA_TYPE_TO_GLOBAL[dataType];
      const data = this.getGlobalData(globalVar);

      if (!data) {
        console.warn(`Data not available for ${dataType}`);
        return null;
      }

      const fields = this.parseDataFields(data);
      const dataTypeInfo: DataTypeInfo = {
        type: dataType,
        globalVar,
        displayName: DATA_TYPE_DISPLAY_NAMES[dataType],
        fields,
        sampleData: this.getSampleData(data)
      };

      // 缓存结果
      if (this.config.enableCache) {
        this.cache.set(dataType, dataTypeInfo);
        this.cleanupCache();
      }

      return dataTypeInfo;
    } catch (error) {
      console.error(`Error getting data type info for ${dataType}:`, error);
      return null;
    }
  }

  /**
   * 获取指定数据类型的所有字段
   */
  async getFields(dataType: StaticDataType): Promise<FieldInfo[]> {
    const dataTypeInfo = await this.getDataTypeInfo(dataType);
    return dataTypeInfo?.fields || [];
  }

  /**
   * 搜索字段
   */
  async searchFields(dataType: StaticDataType, searchTerm: string): Promise<FieldInfo[]> {
    const fields = await this.getFields(dataType);
    return this.filterFields(fields, searchTerm);
  }

  /**
   * 创建数据选择对象
   */
  createSelection(dataType: StaticDataType, fieldPath: string, fieldInfo: FieldInfo): DataSelection {
    const displayText = FieldParser.getFieldDisplayText(
      fieldPath,
      fieldInfo,
      DATA_TYPE_DISPLAY_NAMES[dataType]
    );

    return {
      dataType,
      fieldPath,
      fieldInfo,
      displayText
    };
  }

  /**
   * 获取字段的实际值
   */
  getFieldValue(dataType: StaticDataType, fieldPath: string, itemIndex?: number): any {
    try {
      const globalVar = DATA_TYPE_TO_GLOBAL[dataType];
      const data = this.getGlobalData(globalVar);

      if (!data) return null;

      // 如果指定了索引，获取数组中的特定项
      let targetData = data;
      if (typeof itemIndex === 'number' && Array.isArray(data)) {
        targetData = data[itemIndex];
      }

      if (!targetData) return null;

      // 解析字段路径
      const pathParts = fieldPath.split('.');
      let value = targetData;

      for (const part of pathParts) {
        if (value === null || value === undefined) break;
        value = value[part];
      }

      return value;
    } catch (error) {
      console.error(`Error getting field value for ${dataType}.${fieldPath}:`, error);
      return null;
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 刷新指定数据类型的缓存
   */
  refreshDataType(dataType: StaticDataType): void {
    this.cache.delete(dataType);
  }

  // 私有方法

  private getGlobalData(globalVar: string): any {
    try {
      // 在浏览器环境中访问全局变量
      return (window as any)[globalVar];
    } catch (error) {
      console.warn(`Global variable ${globalVar} not available:`, error);
      return null;
    }
  }

  private parseDataFields(data: any): FieldInfo[] {
    if (Array.isArray(data)) {
      // 对于数组数据，找到第一个非null项进行解析
      const sampleItem = data.find(item => item !== null && item !== undefined);
      if (sampleItem) {
        return FieldParser.parseFields(sampleItem);
      }
    } else if (typeof data === 'object' && data !== null) {
      return FieldParser.parseFields(data);
    }

    return [];
  }

  private getSampleData(data: any): any {
    if (Array.isArray(data)) {
      return data.find(item => item !== null && item !== undefined) || null;
    }
    return data;
  }

  private filterFields(fields: FieldInfo[], searchTerm: string): FieldInfo[] {
    const term = searchTerm.toLowerCase();
    const result: FieldInfo[] = [];

    for (const field of fields) {
      if (field.name.toLowerCase().includes(term) ||
          field.displayName.toLowerCase().includes(term) ||
          (field.description && field.description.toLowerCase().includes(term))) {
        result.push(field);
      }

      // 递归搜索子字段
      if (field.children) {
        const childMatches = this.filterFields(field.children, searchTerm);
        result.push(...childMatches);
      }
    }

    return result;
  }

  private cleanupCache(): void {
    if (this.cache.size > this.config.maxCacheSize) {
      // 删除最旧的缓存项（简单的LRU实现）
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
  }
}
