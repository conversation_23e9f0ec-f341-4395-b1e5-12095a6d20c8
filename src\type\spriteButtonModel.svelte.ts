import { BaseObjectModel } from './baseObjectModel.svelte';

/**
 * Sprite Button 模型类
 * 继承自 BaseObjectModel，专门用于处理自定义 SpriteButton 对象
 */
export class SpriteButtonModel extends BaseObjectModel {

    constructor(spriteButton: any) {
        super(spriteButton);

        // 初始化 Sprite 相关属性
        this.bitmap = spriteButton.bitmap;
        this.blendMode = spriteButton.blendMode || 0;
        this.mask = spriteButton.mask;
        this.filters = spriteButton.filters;

        // 初始化自定义 SpriteButton 属性
        this.buttonType = spriteButton._buttonType || 'normal';
        this.buttonText = spriteButton._buttonText || spriteButton.getText?.() || 'Button';
        this.buttonName = spriteButton.name || '';
        this.currentState = spriteButton._currentState || 'normal';
        this.isEnabled = spriteButton._isEnabled !== undefined ? spriteButton._isEnabled : true;
        this.isPressed = spriteButton._isPressed || false;

        // 事件处理器（保存为字符串）
        this.onClickCode = spriteButton._onClickCode || '';
        this.onHoverCode = spriteButton._onHoverCode || '';
        this.onPressCode = spriteButton._onPressCode || '';

        // 按钮组
        this.buttonGroup = spriteButton._buttonGroup || '';

        console.log('🔧 SpriteButtonModel: 创建自定义 SpriteButton 模型', {
            buttonType: this.buttonType,
            buttonText: this.buttonText,
            buttonName: this.buttonName,
            currentState: this.currentState,
            isEnabled: this.isEnabled,
            sprite: spriteButton
        });

        // setupSync() 已经在基类构造函数中调用了
    }

    // 自定义 SpriteButton 属性
    buttonType = $state('normal');    // 按钮类型: 'normal', 'toggle', 'radio'
    buttonText = $state('Button');    // 按钮文本
    buttonName = $state('');          // 按钮名称
    currentState = $state('normal');  // 当前状态: 'normal', 'hover', 'pressed', 'disabled'
    isEnabled = $state(true);         // 是否启用
    isPressed = $state(false);        // 是否按下（用于 toggle 和 radio 类型）

    // 事件处理代码
    onClickCode = $state('');         // 点击事件代码
    onHoverCode = $state('');         // 悬停事件代码
    onPressCode = $state('');         // 按下事件代码

    // 按钮组（用于 radio 类型）
    buttonGroup = $state('');         // 按钮组名称

    // Sprite 相关属性
    blendMode = $state(0);            // 混合模式
    mask = $state(null);              // 遮罩，复杂对象，暂时直接保存
    filters = $state(null);           // 滤镜，复杂对象，暂时直接保存
    bitmap = $state(null);            // 实际bitmap值
    /**
     * 设置SpriteButton特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理SpriteButton特有的属性
     */
    protected setupSpecificSync(): void {
        // 同步Sprite特有属性
        this._originalObject.bitmap = this.bitmap;
        this._originalObject.blendMode = this.blendMode;
        this._originalObject.mask = this.mask;
        this._originalObject.filters = this.filters;

        // 同步自定义 SpriteButton 属性
        const oldButtonType = this._originalObject._buttonType;
        const oldButtonText = this._originalObject._buttonText;
        const oldOnClickCode = this._originalObject._onClickCode;

        // 同步基本属性
        this._originalObject._buttonType = this.buttonType;
        this._originalObject._buttonText = this.buttonText;
        this._originalObject.name = this.buttonName;
        this._originalObject._currentState = this.currentState;
        this._originalObject._isEnabled = this.isEnabled;
        this._originalObject._isPressed = this.isPressed;
        this._originalObject._buttonGroup = this.buttonGroup;

        // 同步事件代码
        this._originalObject._onClickCode = this.onClickCode;
        this._originalObject._onHoverCode = this.onHoverCode;
        this._originalObject._onPressCode = this.onPressCode;

        // 如果按钮文本发生变化，更新文本
        if (oldButtonText !== this.buttonText && typeof this._originalObject.setText === 'function') {
            this._originalObject.setText(this.buttonText);
            console.log('🔧 SpriteButtonModel: 按钮文本已更新', this.buttonText);
        }

        // 如果按钮类型发生变化，更新按钮类型
        if (oldButtonType !== this.buttonType && typeof this._originalObject.setButtonType === 'function') {
            this._originalObject.setButtonType(this.buttonType);
            console.log('🔧 SpriteButtonModel: 按钮类型已更新', this.buttonType);
        }

        // 如果启用状态发生变化，更新启用状态
        if (typeof this._originalObject.setEnabled === 'function') {
            this._originalObject.setEnabled(this.isEnabled);
        }

        // 如果点击事件代码发生变化，设置点击处理函数
        if (oldOnClickCode !== this.onClickCode) {
            if (this.onClickCode && this.onClickCode.trim() !== '') {
                try {
                    const clickHandler = new Function(this.onClickCode);
                    if (typeof this._originalObject.setClickHandler === 'function') {
                        this._originalObject.setClickHandler(clickHandler);
                    }
                    console.log('🔧 SpriteButtonModel: 已设置点击处理函数', this.onClickCode);
                } catch (error) {
                    console.error('🔧 SpriteButtonModel: 创建点击处理函数失败', error);
                }
            } else {
                // 清除点击处理函数
                if (typeof this._originalObject.setClickHandler === 'function') {
                    this._originalObject.setClickHandler(null);
                }
                console.log('🔧 SpriteButtonModel: 已清除点击处理函数');
            }
        }

        // 同步 name 属性（如果 buttonName 有值）
        if (this.buttonName) {
            this._originalObject.name = this.buttonName;
        }

        // 触发重绘
        if (this._originalObject.bitmap && this._originalObject.elements && typeof this._originalObject.bitmap.redrawing === 'function') {
            this._originalObject.bitmap.redrawing();
            this.buttonText = this._originalObject.getText?.();
            console.log('🔧 SpriteButtonModel: 触发 bitmap 重绘');
        }
    }

    /**
     * 获取按钮类型显示名称
     */
    getButtonTypeLabel(): string {
        const buttonTypeMap: Record<string, string> = {
            'cancel': '取消 (Cancel)',
            'pageup': '上一页 (Page Up)',
            'pagedown': '下一页 (Page Down)',
            'down': '向下 (Down)',
            'up': '向上 (Up)',
            'down2': '向下2 (Down2)',
            'up2': '向上2 (Up2)',
            'ok': '确定 (OK)',
            'menu': '菜单 (Menu)'
        };
        return buttonTypeMap[this.buttonType] || this.buttonType || '未设置';
    }

    /**
     * 设置按钮点击事件代码
     */
    setButtonEvent(eventCode: string): void {
        this.onClickCode = eventCode;
        console.log('🔧 SpriteButtonModel: 设置按钮点击事件', eventCode);
    }

    /**
     * 生成对象创建代码（实现抽象方法）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        return `${indent}const ${varName} = new SpriteButton();`;
    }





}

// 注册SpriteButtonModel到基类容器
BaseObjectModel.registerModel('Sprite_Button', SpriteButtonModel);
BaseObjectModel.registerModel('SpriteButton', SpriteButtonModel);