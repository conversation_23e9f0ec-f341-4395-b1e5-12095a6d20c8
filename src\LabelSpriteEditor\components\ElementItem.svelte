<script lang="ts">
  import type { Element, TextElement, ImageElement } from '../../type/bitmap.svelte';

  // Props
  let {
    element,
    index,
    totalElements = 0,
    isSelected = false,
    onSelect = () => {},
    onRemove = () => {},
    onMoveUp = () => {},
    onMoveDown = () => {},
    onCopy = () => {}
  }: {
    element: Element;
    index: number;
    totalElements?: number;
    isSelected?: boolean;
    onSelect?: (index: number) => void;
    onRemove?: (index: number) => void;
    onMoveUp?: (index: number) => void;
    onMoveDown?: (index: number) => void;
    onCopy?: (element: Element) => void;
  } = $props();

  // 获取元素显示信息
  function getElementInfo(element: Element) {
    if (element.type === 'text') {
      const textElement = element as TextElement;
      return {
        type: 'text',
        icon: '📝',
        title: textElement.text || '空文本',
        subtitle: `位置: (${textElement.x}, ${textElement.y})`,
        details: `对齐: ${textElement.align || 'left'}, 行高: ${textElement.lineHeight || 36}`
      };
    } else if (element.type === 'image') {
      const imageElement = element as ImageElement;
      const sourceName = typeof imageElement.source === 'string'
        ? imageElement.source.split('/').pop() || '未知图片'
        : imageElement.source?._originalPath?.split('/').pop() || '未知图片';

      return {
        type: 'image',
        icon: '🖼️',
        title: sourceName,
        subtitle: `位置: (${imageElement.dx}, ${imageElement.dy})`,
        details: `尺寸: ${imageElement.dw}×${imageElement.dh}, 裁剪: ${imageElement.sw}×${imageElement.sh}`
      };
    }

    return {
      type: 'unknown',
      icon: '❓',
      title: '未知元素',
      subtitle: '',
      details: ''
    };
  }

  let elementInfo = $derived(getElementInfo(element));

  // 事件处理
  function handleClick() {
    console.log('ElementItem 点击，设置选中索引:', index);
    onSelect(index);
  }

  function handleKeydown(event: KeyboardEvent) {
    // 🔧 Ctrl+D 快捷键复制元素
    if (event.ctrlKey && event.key.toLowerCase() === 'd') {
      event.preventDefault();
      event.stopPropagation();
      console.log('🔧 ElementItem: Ctrl+D 快捷键触发复制，元素索引:', index);
      onCopy(element);
      return;
    }

    // 原有的选择功能
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleClick();
    }
  }

  function handleRemove(event: Event) {
    event.stopPropagation();
    onRemove(index);
  }

  function handleMoveUp(event: Event) {
    event.stopPropagation();
    onMoveUp(index);
  }

  function handleMoveDown(event: Event) {
    event.stopPropagation();
    onMoveDown(index);
  }

  function handleCopy(event: Event) {
    event.stopPropagation();
    onCopy(element);
  }

  // 🔧 全局键盘监听 - 当元素被选中时响应 Ctrl+D
  function handleGlobalKeydown(event: KeyboardEvent) {
    // 只有当前元素被选中时才响应
    if (!isSelected) return;

    // Ctrl+D 快捷键复制
    if (event.ctrlKey && event.key.toLowerCase() === 'd') {
      event.preventDefault();
      event.stopPropagation();
      console.log('🔧 ElementItem: 全局 Ctrl+D 快捷键触发复制，元素索引:', index);
      onCopy(element);
    }
  }

  // 🔧 监听全局键盘事件
  $effect(() => {
    if (typeof window !== 'undefined') {
      window.addEventListener('keydown', handleGlobalKeydown);

      return () => {
        window.removeEventListener('keydown', handleGlobalKeydown);
      };
    }
  });
</script>

<div
  class="element-item"
  class:selected={isSelected}
  onclick={handleClick}
  onkeydown={handleKeydown}
  role="button"
  tabindex="0"
>
  <div class="element-content">
    <div class="element-header">
      <span class="element-icon">{elementInfo.icon}</span>
      <div class="element-info">
        <div class="element-title">{elementInfo.title}</div>
        <div class="element-subtitle">{elementInfo.subtitle}</div>
      </div>
      <div class="element-index">#{index + 1}</div>
    </div>

    <div class="element-details">{elementInfo.details}</div>

    <div class="element-actions">
      <button
        class="action-btn move-up"
        onclick={handleMoveUp}
        title="上移"
        disabled={index === 0}
      >
        ↑
      </button>
      <button
        class="action-btn move-down"
        onclick={handleMoveDown}
        title="下移"
        disabled={index >= totalElements - 1}
      >
        ↓
      </button>
      <button
        class="action-btn copy"
        onclick={handleCopy}
        title="复制"
      >
        📋
      </button>
      <button
        class="action-btn remove"
        onclick={handleRemove}
        title="删除"
      >
        🗑️
      </button>
    </div>
  </div>
</div>

<style>
  .element-item {
    padding: 8px;
    margin: 4px 0;
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 6px;
    background: var(--theme-surface, #ffffff);
    cursor: pointer;
    transition: all 0.15s ease;
  }

  .element-item:hover {
    border-color: var(--theme-border-hover, #d1d5db);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  .element-item.selected {
    background: var(--theme-primary-light, #eff6ff);
    border-color: var(--theme-primary, #3b82f6);
    box-shadow: 0 0 0 1px var(--theme-primary, #3b82f6);
  }

  .element-item:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--theme-primary, #3b82f6);
  }

  .element-item.selected:focus {
    box-shadow: 0 0 0 1px var(--theme-primary, #3b82f6), 0 0 0 3px rgba(59, 130, 246, 0.3);
  }

  .element-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .element-header {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .element-icon {
    font-size: 16px;
    flex-shrink: 0;
  }

  .element-info {
    flex: 1;
    min-width: 0;
  }

  .element-title {
    font-weight: 500;
    font-size: 14px;
    color: var(--theme-text, #111827);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .element-subtitle {
    font-size: 12px;
    color: var(--theme-text-secondary, #6b7280);
  }

  .element-index {
    font-size: 12px;
    color: var(--theme-text-tertiary, #9ca3af);
    font-weight: 500;
  }

  .element-details {
    font-size: 11px;
    color: var(--theme-text-secondary, #6b7280);
    margin-left: 24px;
  }

  .element-actions {
    display: flex;
    gap: 4px;
    margin-left: 24px;
    opacity: 0;
    transition: opacity 0.15s ease;
  }

  .element-item:hover .element-actions,
  .element-item.selected .element-actions {
    opacity: 1;
  }

  .action-btn {
    padding: 2px 6px;
    font-size: 12px;
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 4px;
    background: var(--theme-surface, #ffffff);
    cursor: pointer;
    transition: all 0.15s ease;
  }

  .action-btn:hover:not(:disabled) {
    background: var(--theme-surface-hover, #f3f4f6);
    border-color: var(--theme-border-hover, #d1d5db);
  }

  .action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .action-btn.remove:hover:not(:disabled) {
    background: var(--theme-danger-light, #fef2f2);
    border-color: var(--theme-danger, #ef4444);
    color: var(--theme-danger, #ef4444);
  }

  .action-btn.copy:hover:not(:disabled) {
    background: var(--theme-success-light, #f0fdf4);
    border-color: var(--theme-success, #22c55e);
    color: var(--theme-success, #22c55e);
  }
</style>
