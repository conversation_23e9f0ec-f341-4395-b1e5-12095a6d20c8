//=============================================================================
// main.js v3.0.0 - 先加载CustomResourcePath插件，然后使用原始项目main.js
//=============================================================================

// 设置编辑器模式标志
window.EDITOR_MODE = true;

// 获取项目配置
function getProjectConfig() {
    return window.PROJECT_CONFIG || null;
}

// 获取原始项目main.js的路径
function getOriginalMainJsPath() {
    const projectConfig = getProjectConfig();
    if (projectConfig && projectConfig.scripts) {
        const mainScript = projectConfig.scripts.find(script => script.local_path.endsWith('main.js'));
        if (mainScript) {
            return mainScript.url;
        }
    }
    return 'js/main.js'; // 默认路径
}
// 临时忽略这些错误
window.addEventListener('error', function (event) {
    const msg = event.error?.message || '';
    if (msg.includes('removeChild') || msg.includes('Unexpected token')) {
        event.preventDefault();
        return false;
    }
});

class Main {
    constructor() {

        this.error = null;
        // 立即设置到全局变量，供插件加载使用
        window.RPGMakerMainInstance = this;
    }

    async run() {
        // 设置项目配置（如果有的话）
        this.setupProjectConfig();

        this.showLoadingSpinner();

        try {
            // 第一步：加载CustomResourcePath插件
            console.log('=== 第一步：加载 CustomResourcePath 插件 ===');
            await this.loadCustomResourcePathPlugin();

            // 第二步：使用 Tauri API 加载项目脚本（而不是执行原始 main.js）
            console.log('=== 第二步：使用 Tauri API 加载项目脚本 ===');
            await this.loadProjectScriptsWithTauri();

            // 第三步：启动 RPG Maker MZ
            console.log('=== 第三步：启动 RPG Maker MZ ===');
            this.startRPGMakerMZ();

        } catch (error) {
            console.error('启动失败:', error);
            this.printError('启动失败', error.message || String(error));
        }
    }

    setupProjectConfig() {
        // 如果有项目配置，设置到全局变量
        const projectConfig = getProjectConfig();
        if (projectConfig) {
            console.log('检测到项目配置:', projectConfig);
            window.PROJECT_CONFIG = projectConfig;
        }
    }

    async loadCustomResourcePathPlugin() {
        console.log('加载 CustomResourcePath 插件...');

        try {
            const script = document.createElement("script");
            script.type = "text/javascript";
            script.src = "src/engine/js/plugins/CustomResourcePath.js";

            // 先添加到页面，再等待加载完成
            document.body.appendChild(script);

            // 等待脚本加载完成
            await new Promise((resolve, reject) => {
                let completed = false; // 标记是否已完成

                script.onload = () => {
                    if (!completed) {
                        completed = true;
                        console.log('CustomResourcePath 插件 onload 事件触发');
                        // 等待一小段时间确保插件代码执行完成
                        setTimeout(() => {
                            console.log('CustomResourcePath 插件执行完成');
                            resolve(undefined);
                        }, 100);
                    }
                };
                script.onerror = (error) => {
                    if (!completed) {
                        completed = true;
                        console.error('CustomResourcePath 插件 onerror 事件触发:', error);
                        reject(error);
                    }
                };

                // 添加超时处理
                setTimeout(() => {
                    if (!completed) {
                        completed = true;
                        console.error('CustomResourcePath 插件加载超时');
                        reject(new Error('插件加载超时'));
                    }
                }, 10000);
            });

            console.log('CustomResourcePath 插件加载完成');

        } catch (error) {
            console.error('CustomResourcePath 插件加载失败:', error);
            // 不要抛出错误，继续执行
            console.log('继续执行，不依赖 CustomResourcePath 插件');
        }
    }

    // 添加全局错误处理器来捕获语法错误
    setupGlobalErrorHandler() {
        // 保存原始的错误处理器
        const originalErrorHandler = window.onerror;

        // 监控网络请求
        this.setupNetworkMonitoring();

        window.addEventListener('error', (event) => {
            console.error('🚨 全局错误事件:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                type: event.type
            });

            if (event.error && event.error.message && event.error.message.includes('Unexpected token')) {
                console.error('🚨 检测到语法错误 - 详细分析:');

                // 尝试找到问题脚本
                const scripts = document.querySelectorAll('script');
                console.log(`📋 当前页面共有 ${scripts.length} 个脚本标签`);

                scripts.forEach((script, index) => {
                    const scriptPath = script.getAttribute('data-script-path');
                    const filePath = script.getAttribute('data-file-path');

                    if (script.textContent && script.textContent.length > 0) {
                        const lines = script.textContent.split('\n');
                        const firstLine = lines[0] || '';
                        const secondLine = lines[1] || '';

                        console.log(`📄 脚本 ${index} [${scriptPath || '未知'}]:`, {
                            scriptPath: scriptPath,
                            filePath: filePath,
                            firstLine: firstLine.substring(0, 100),
                            secondLine: secondLine.substring(0, 100),
                            totalLines: lines.length,
                            length: script.textContent.length,
                            startsWithHTML: firstLine.trim().startsWith('<'),
                            containsHTML: script.textContent.includes('<!DOCTYPE') || script.textContent.includes('<html>')
                        });

                        // 如果是最近添加的脚本，显示更多内容
                        if (index >= scripts.length - 3) {
                            console.log(`🔍 最近脚本 ${index} 的前10行:`, lines.slice(0, 10));
                        }
                    } else if (script.src) {
                        console.log(`📄 外部脚本 ${index}:`, {
                            src: script.src,
                            scriptPath: scriptPath,
                            filePath: filePath
                        });
                    }
                });
            }
        });

        // 也监听 unhandledrejection 事件
        window.addEventListener('unhandledrejection', (event) => {
            console.error('🚨 未处理的 Promise 拒绝:', event.reason);
        });
    }

    // 监控网络请求
    setupNetworkMonitoring() {
        // 监控 fetch 请求（带调用堆栈追踪）
        const originalFetch = window.fetch;
        window.fetch = async function(...args) {
            const url = String(args[0]);

            // 特别追踪 is_window_maximized 请求
            if (url.includes('is_window_maximized')) {
                console.log('🚨 检测到 is_window_maximized 请求!');
                console.log('🚨 完整调用堆栈:');
                const stack = new Error().stack;
                if (stack) {
                    const stackLines = stack.split('\n');
                    stackLines.forEach((line, index) => {
                        if (index > 0) { // 跳过第一行 "Error"
                            console.log(`  ${index}: ${line.trim()}`);
                        }
                    });
                } else {
                    console.log('  无法获取调用堆栈');
                }
                console.log('🚨 请求参数:', args);
            } else {
                console.log('🌐 Fetch 请求:', url);
            }

            try {
                const response = await originalFetch.apply(this, args);

                if (url.includes('is_window_maximized')) {
                    console.log('🚨 is_window_maximized 响应完成');
                } else {
                    console.log('🌐 Fetch 响应:', {
                        url: url,
                        status: response.status,
                        contentType: response.headers.get('content-type')
                    });
                }
                return response;
            } catch (error) {
                console.error('🌐 Fetch 错误:', url, error);
                throw error;
            }
        };

        // 监控 XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;

        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._requestUrl = url;
            console.log('🌐 XHR 打开:', method, url);
            return originalXHROpen.call(this, method, url, ...args);
        };

        XMLHttpRequest.prototype.send = function(...args) {
            const url = this._requestUrl;
            console.log('🌐 XHR 发送:', url);

            this.addEventListener('load', () => {
                const responseInfo = {
                    url: url,
                    status: this.status,
                    contentType: this.getResponseHeader('content-type'),
                    responseType: this.responseType
                };

                // 只有在 responseType 为空或 'text' 时才尝试读取 responseText
                if (this.responseType === '' || this.responseType === 'text') {
                    try {
                        responseInfo.responseLength = this.responseText ? this.responseText.length : 0;
                        responseInfo.responseStart = this.responseText ? this.responseText.substring(0, 100) : '';
                    } catch (e) {
                        responseInfo.responseTextError = e.message;
                    }
                } else {
                    responseInfo.responseLength = this.response ? this.response.byteLength || this.response.length : 0;
                }

                console.log('🌐 XHR 响应:', responseInfo);
            });

            this.addEventListener('error', () => {
                console.error('🌐 XHR 错误:', url, this.status);
            });

            return originalXHRSend.apply(this, args);
        };
    }

    async loadProjectScriptsWithTauri() {
        console.log('使用 Tauri API 加载项目脚本...');

        // 设置全局错误处理器
        this.setupGlobalErrorHandler();

        try {
            // 获取项目配置中的脚本列表
            const projectConfig = getProjectConfig();
            if (!projectConfig || !projectConfig.scripts) {
                throw new Error('没有找到项目配置或脚本列表');
            }

            // 按正确顺序排列核心脚本
            const orderedCoreScripts = [
                'js/libs/pixi.js',
                'js/libs/pako.min.js',
                'js/libs/localforage.min.js',
                'js/libs/effekseer.min.js',
                'js/libs/vorbisdecoder.js', // 重新启用，但会特殊处理
                'js/rmmz_core.js',
                'js/rmmz_managers.js',
                'js/rmmz_objects.js',
                'js/rmmz_scenes.js',
                'js/rmmz_sprites.js',
                'js/rmmz_windows.js',
                'js/plugins.js'

            ];

            // 动态导入 Tauri API
            const { TauriAPI } = await import('../../lib/tauriAPI');

            // 按顺序加载每个脚本，添加延迟确保执行完成
            for (let i = 0; i < orderedCoreScripts.length; i++) {
                const scriptPath = orderedCoreScripts[i];
                const script = projectConfig.scripts.find(s => s.local_path.replace(/\\/g, '/') === scriptPath);
                if (script) {
                    console.log(`加载脚本 [${i+1}/${orderedCoreScripts.length}]:`, scriptPath);

                    // 从 file:// URL 提取文件路径
                    const filePath = script.url.replace('file://', '');

                    // 使用 Tauri API 读取脚本内容
                    const result = await TauriAPI.Project.readProjectFile(filePath);

                    if (result.success && result.data) {
                        // 验证读取的内容是否为有效的JavaScript
                        const content = result.data;

                        // 检查是否包含HTML标签（可能是错误页面）
                        if (content.trim().startsWith('<') || content.includes('<!DOCTYPE') || content.includes('<html>')) {
                            console.error('✗ 检测到HTML内容，不是有效的JavaScript文件:', scriptPath);
                            console.error('文件内容前100字符:', content.substring(0, 100));
                            throw new Error(`脚本文件包含HTML内容: ${scriptPath}`);
                        }

                        // 检查文件是否为空
                        if (!content.trim()) {
                            console.error('✗ 脚本文件为空:', scriptPath);
                            throw new Error(`脚本文件为空: ${scriptPath}`);
                        }

                        // 记录文件大小和前几行内容用于调试
                        console.log(`📄 脚本文件信息 [${scriptPath}]:`, {
                            size: content.length,
                            firstLine: content.split('\n')[0].substring(0, 50),
                            filePath: filePath
                        });

                        // 创建脚本元素并设置内容
                        const scriptElement = document.createElement("script");
                        scriptElement.type = "text/javascript";

                        // 添加脚本标识符用于调试
                        scriptElement.setAttribute('data-script-path', scriptPath);
                        scriptElement.setAttribute('data-file-path', filePath);

                        // 添加错误处理
                        scriptElement.onerror = (error) => {
                            console.error('✗ 脚本执行错误:', scriptPath, error);
                            console.error('脚本内容前200字符:', content.substring(0, 200));
                        };

                        // 特殊处理 vorbisdecoder.js
                        if (scriptPath === 'js/libs/vorbisdecoder.js') {
                            console.log('🎵 特殊处理 vorbisdecoder.js (WebAssembly 音频解码器)');

                            // 临时禁用全局错误处理器，避免 WebAssembly 错误被捕获
                            const originalErrorHandler = window.onerror;
                            const originalUnhandledRejection = window.onunhandledrejection;
                            const vorbisErrors = [];

                            // 临时禁用错误处理
                            window.onerror = function(message, source, lineno, colno, error) {
                                const messageStr = String(message);
                                if (messageStr.includes('Unexpected token') && source && source.includes('localhost:1420')) {
                                    console.warn('🎵 忽略 Vorbis 相关的语法错误:', messageStr);
                                    vorbisErrors.push(messageStr);
                                    return true; // 阻止错误传播
                                }
                                // 其他错误正常处理
                                if (originalErrorHandler) {
                                    return originalErrorHandler.call(this, message, source, lineno, colno, error);
                                }
                                return false;
                            };

                            window.onunhandledrejection = function(event) {
                                const reason = String(event.reason);
                                if (reason.includes('vorbis') || reason.includes('wasm') || reason.includes('WebAssembly')) {
                                    console.warn('🎵 忽略 Vorbis 相关的 Promise 拒绝:', reason);
                                    vorbisErrors.push(reason);
                                    event.preventDefault(); // 阻止错误传播
                                    return;
                                }
                                // 其他错误正常处理
                                if (originalUnhandledRejection) {
                                    return originalUnhandledRejection.call(this, event);
                                }
                            };

                            try {
                                // 在隔离的环境中执行
                                const isolatedScript = document.createElement('script');
                                isolatedScript.type = 'text/javascript';
                                isolatedScript.setAttribute('data-script-path', scriptPath);
                                isolatedScript.setAttribute('data-file-path', filePath);

                                // 添加错误处理
                                isolatedScript.onerror = (error) => {
                                    console.warn('🎵 Vorbis 解码器加载警告:', error);
                                    return true; // 阻止错误传播
                                };

                                isolatedScript.textContent = content;
                                document.body.appendChild(isolatedScript);

                                // 等待 WebAssembly 模块初始化
                                await new Promise(resolve => {
                                    setTimeout(() => {
                                        console.log('🎵 Vorbis 解码器处理完成');
                                        if (vorbisErrors.length > 0) {
                                            console.warn('🎵 Vorbis 解码器产生了一些警告 (已忽略):', vorbisErrors.length, '个');
                                        }
                                        resolve(undefined);
                                    }, 300); // 给 WebAssembly 更多时间初始化
                                });

                            } catch (vorbisError) {
                                console.warn('🎵 Vorbis 解码器加载失败，但继续执行:', vorbisError);
                                // 不抛出错误，允许游戏继续运行（可能没有 Vorbis 音频支持）
                            } finally {
                                // 恢复原始的错误处理器
                                setTimeout(() => {
                                    window.onerror = originalErrorHandler;
                                    window.onunhandledrejection = originalUnhandledRejection;
                                    console.log('🎵 已恢复全局错误处理器');
                                }, 100);
                            }
                        } else {
                            // 普通脚本的处理逻辑
                            try {
                                scriptElement.textContent = content;

                                // 添加到页面并执行
                                document.body.appendChild(scriptElement);

                                // 等待脚本执行完成
                                await new Promise(resolve => {
                                    setTimeout(() => {
                                        console.log(`✓ 脚本 ${scriptPath} 执行完成，无立即错误`);

                                        // 特殊检查：如果是 rmmz_managers.js，额外等待和检查
                                        if (scriptPath === 'js/rmmz_managers.js') {
                                            console.log('🔍 rmmz_managers.js 执行完成，检查全局状态...');
                                            console.log('DataManager 状态:', typeof window.DataManager);
                                            console.log('ImageManager 状态:', typeof window.ImageManager);
                                            console.log('AudioManager 状态:', typeof window.AudioManager);
                                            console.log('当前脚本数量:', document.querySelectorAll('script').length);
                                        }

                                        resolve(undefined);
                                    }, scriptPath === 'js/rmmz_managers.js' ? 100 : 50);
                                });

                            } catch (syntaxError) {
                                console.error('✗ 脚本语法错误:', scriptPath, syntaxError);
                                console.error('错误详情:', {
                                    message: syntaxError.message,
                                    stack: syntaxError.stack,
                                    contentPreview: content.substring(0, 500)
                                });
                                throw syntaxError;
                            }
                        }

                        console.log('✓ 脚本加载成功:', scriptPath);
                    } else {
                        console.error('✗ 脚本加载失败:', scriptPath, result.error);
                        console.error('文件路径:', filePath);
                        throw new Error(`脚本加载失败: ${scriptPath} - ${result.error}`);
                    }
                } else {
                    console.warn('⚠ 脚本未找到:', scriptPath);
                }
            }

            console.log('所有核心脚本加载完成');

            // 加载 TypeIdGenerator 桥接文件
            // await this.loadTypeIdGeneratorBridge();

            // 加载本地插件
            await this.loadLocalPluginsFromConfig(projectConfig, TauriAPI);

            // 保存 TauriAPI 到实例，供后续使用
            this.TauriAPI = TauriAPI;

        } catch (error) {
            console.error('加载项目脚本失败:', error);
            throw error;
        }
    }

    // async loadTypeIdGeneratorBridge() {
    //     console.log('加载 TypeIdGenerator 桥接文件...');

    //     try {
    //         // 动态导入 TypeIdGenerator 桥接文件
    //         await import('./typeIdGeneratorBridge.js');
    //         console.log('TypeIdGenerator 桥接文件加载完成');
    //     } catch (error) {
    //         console.error('TypeIdGenerator 桥接文件加载失败:', error);
    //         // 不抛出错误，继续执行
    //     }
    // }

    async loadLocalPluginsFromConfig(projectConfig, TauriAPI) {
        console.log('开始加载本地插件...');

        try {
            // 使用本地的简化插件配置（这些是我们项目中的插件，不是游戏项目中的）
            const localPlugins = [
                // "spriteExpandLocal",
                "RPGEditor_BitmapTracker",
                "CustomResourcePath",
                "RPGEditor_PrototypeModifications",
                "layout",
                "CustomAnimation",
                "RPGMakerMZTypes",
                "RPGEditor_DisableDefaultInput",
                 "spriteEventExpand",
                  "uiImage",
                 "uiSlider",
                 'uiBase',
                 "uiButton",
                 "UIAtlas",
                 "switch",
                 "uiList",
                  "uiItem",
                  "debugEventBlocking"
                
                //  "componentsExpand"
                // 注意：OperationRecorder 应该通过项目插件系统加载，不在这里加载
                // "UIPositionAdjuster"  // 注释掉表示不加载
            ];

            console.log('使用本地插件配置，找到插件列表:', localPlugins.length, '个插件');

            // 加载每个插件
            for (const pluginName of localPlugins) {
                await this.loadLocalPlugin(pluginName, projectConfig.projectPath, TauriAPI);
            }

            console.log('本地插件加载完成');

        } catch (error) {
            console.error('加载本地插件失败:', error);
        }
    }

    parsePluginsFile(content) {
        try {
            // 首先尝试解析简化的 localPlugins 数组
            const localPluginsMatch = content.match(/const\s+localPlugins\s*=\s*(\[[\s\S]*?\]);/);
            if (localPluginsMatch) {
                console.log('使用简化的 localPlugins 格式');
                const pluginNames = eval('(' + localPluginsMatch[1] + ')');
                // 转换为标准格式
                return pluginNames.map(name => ({
                    name: name,
                    status: true,
                    parameters: {}
                }));
            }

            // 回退到标准的 $plugins 数组
            const pluginsMatch = content.match(/const\s+\$plugins\s*=\s*(\[[\s\S]*?\]);/);
            if (pluginsMatch) {
                console.log('使用标准的 $plugins 格式');
                const pluginsArray = eval('(' + pluginsMatch[1] + ')');
                return pluginsArray;
            }

            console.error('无法在 plugins.js 中找到 localPlugins 或 $plugins 数组');
            return null;

        } catch (error) {
            console.error('解析 plugins.js 失败:', error);
            return null;
        }
    }

    async loadLocalPlugin(pluginName, projectPath, TauriAPI) {
        try {
            console.log('加载本地插件:', pluginName);

            // 构建本地插件文件路径（在我们的项目中）
            const localPluginPath = `src/engine/js/plugins/${pluginName}.js`;

            // 创建脚本元素并直接加载本地插件
            const script = document.createElement("script");
            script.type = "text/javascript";
            script.src = localPluginPath;
            script.async = false;
            script.defer = true;

            // 添加加载完成处理
            script.onload = () => {
                console.log('✓ 本地插件加载成功:', pluginName);
            };

            // 添加错误处理
            script.onerror = (error) => {
                console.error('✗ 本地插件加载失败:', pluginName, error);
            };

            document.body.appendChild(script);

        } catch (error) {
            console.error('✗ 加载本地插件异常:', pluginName, error);
        }
    }

    startRPGMakerMZ() {
        console.log('启动 RPG Maker MZ...');

        // 模拟原生的启动流程
        this.waitForPluginSetup(() => {
            this.initEffekseerRuntime();
        });
    }

    waitForPluginSetup(callback) {
        // 等待 PluginManager 和 $plugins 都准备好
        const checkInterval = setInterval(() => {
            if (typeof window.PluginManager !== 'undefined' && typeof window.$plugins !== 'undefined') {
                console.log('设置插件系统...');

                // 重写插件加载逻辑，使用 Tauri API 读取本地文件
                const originalLoadScript = window.PluginManager.loadScript;
                window.PluginManager.loadScript = function (name) {
                    console.log('尝试加载项目插件:', name);

                    // 创建 script 元素
                    const script = document.createElement('script');
                    script.type = 'text/javascript';
                    script.async = false;

                    // 构建插件文件路径
                    const projectConfig = getProjectConfig();
                    if (projectConfig && projectConfig.projectPath) {
                        // 修复路径：确保使用正确的文件扩展名
                        const pluginFileName = name.endsWith('.js') ? name : name + '.js';
                        const pluginPath = projectConfig.projectPath + '/js/plugins/' + pluginFileName;
                        console.log('项目插件加载详情:');
                        console.log('  插件名称:', name);
                        console.log('  插件文件名:', pluginFileName);
                        console.log('  项目路径:', projectConfig.projectPath);
                        console.log('  完整插件路径:', pluginPath);

                        // 使用 Tauri API 异步读取插件文件
                        (async () => {
                            try {
                                // 获取 TauriAPI 实例
                                const mainInstance = window.RPGMakerMainInstance;
                                if (!mainInstance || !mainInstance.TauriAPI) {
                                    console.warn('TauriAPI 不可用，跳过插件加载:', name);
                                    return;
                                }

                                const result = await mainInstance.TauriAPI.Project.readProjectFile(pluginPath);

                                if (result.success && result.data) {
                                    const content = result.data;

                                    // 验证插件内容
                                    if (content.trim().startsWith('<') || content.includes('<!DOCTYPE') || content.includes('<html>')) {
                                        console.warn('✗ 项目插件包含HTML内容，跳过:', name);
                                        console.warn('内容前100字符:', content.substring(0, 100));
                                        return;
                                    }

                                    if (!content.trim()) {
                                        console.warn('✗ 项目插件文件为空，跳过:', name);
                                        return;
                                    }

                                    console.log('✓ 项目插件文件读取成功:', name);
                                    console.log(`📄 插件文件信息 [${name}]:`, {
                                        size: content.length,
                                        firstLine: content.split('\n')[0].substring(0, 50),
                                        pluginPath: pluginPath
                                    });

                                    // 设置脚本内容并执行
                                    script.text = content;
                                    script.onerror = (error) => {
                                        console.error('✗ 项目插件执行错误:', name, error);
                                        console.error('插件内容前200字符:', content.substring(0, 200));
                                    };
                                    document.head.appendChild(script);
                                    console.log('✓ 项目插件加载完成:', name);
                                } else {
                                    console.warn('✗ 项目插件文件不存在或读取失败:', name);
                                    console.warn('  错误详情:', result.error);
                                    console.warn('  插件路径:', pluginPath);
                                    // 插件文件不存在或读取失败，跳过（这是正常的，因为不是所有插件都存在）
                                }
                            } catch (error) {
                                console.warn('✗ 项目插件加载异常:', name, error);
                                // 插件加载失败，跳过
                            }
                        })();
                    } else {
                        console.warn('项目配置未找到，跳过插件加载:', name);
                    }

                    return script;
                };

                // 模拟原生的插件设置
                try {
                    window.PluginManager.setup(window.$plugins);
                    console.log('插件设置完成');
                } catch (error) {
                    console.error('插件设置失败，但继续执行:', error);
                }

                clearInterval(checkInterval);
                callback();
            } else {
                console.log('等待 PluginManager 和 $plugins 加载...');
            }
        }, 100);
    }

    async initEffekseerRuntime() {
        console.log('初始化 Effekseer Runtime...');

        // 检查 effekseer 是否可用
        if (typeof window.effekseer !== 'undefined') {
            const onLoad = this.onEffekseerLoad.bind(this);
            const onError = this.onEffekseerError.bind(this);

            try {
                // 尝试使用项目路径中的 WASM 文件
                const projectConfig = getProjectConfig();

                if (projectConfig && projectConfig.projectPath) {
                    const wasmPath = projectConfig.projectPath + "/js/libs/effekseer.wasm";
                    console.log('尝试通过 Tauri API 加载 Effekseer WASM:', wasmPath);

                    // 通过 Tauri API 读取 WASM 文件
                    const result = await this.TauriAPI.Project.readProjectBinaryFile(wasmPath);

                    if (result.success && result.data) {
                        console.log('WASM 文件读取成功，大小:', result.data.length, 'bytes');

                        // 创建 Blob URL
                        const wasmBlob = new Blob([result.data], { type: 'application/wasm' });
                        const wasmUrl = URL.createObjectURL(wasmBlob);

                        console.log('创建 WASM Blob URL 成功，开始初始化 Effekseer');

                        // 使用 Blob URL 初始化 Effekseer
                        window.effekseer.initRuntime(wasmUrl, onLoad, onError);
                    } else {
                        console.warn('WASM 文件读取失败，尝试使用原始路径:', result.error);
                        // 尝试使用原始方法作为备用
                        const effekseerWasmUrl = projectConfig.projectPath + "/js/libs/effekseer.wasm";
                        window.effekseer.initRuntime(effekseerWasmUrl, onLoad, onError);
                    }
                } else {
                    console.warn('项目配置不可用，使用默认 WASM 路径');
                    window.effekseer.initRuntime("js/libs/effekseer.wasm", onLoad, onError);
                }
            } catch (error) {
                console.error('Effekseer 初始化失败:', error);
                // 如果 Effekseer 失败，直接启动
                this.onEffekseerLoad();
            }
        } else {
            console.warn('Effekseer 不可用，直接启动引擎');
            this.onEffekseerLoad();
        }
    }

    onEffekseerLoad() {
        console.log('Effekseer 加载完成，启动 Scene_Boot...');
        this.eraseLoadingSpinner();

        // 设置场景监听器
        this.setupSceneMonitoring();

        // 启动引擎
        if (typeof window.SceneManager !== 'undefined' && typeof window.Scene_Boot !== 'undefined') {
            window.SceneManager.run(window.Scene_Boot);
            console.log('RPG Maker MZ 启动成功');
        } else {
            console.error('SceneManager 或 Scene_Boot 未定义');
        }
    }

    onEffekseerError() {
        console.error('Effekseer 加载失败，但继续启动引擎');
        this.onEffekseerLoad(); // 即使失败也继续启动
    }

    patchGraphicsEndLoading() {
        if (typeof window.Graphics !== 'undefined' && window.Graphics.endLoading) {
            window.Graphics.endLoading = function () {
                const loadingSpinner = document.getElementById("loadingSpinner");
                if (loadingSpinner && loadingSpinner.parentElement) {
                    try {
                        loadingSpinner.parentElement.removeChild(loadingSpinner);
                        console.log('Graphics.endLoading: loadingSpinner 已从其父容器中移除');
                        return true;
                    } catch (error) {
                        console.warn('Graphics.endLoading: 移除 loadingSpinner 时出错:', error);
                        // 如果移除失败，尝试隐藏元素
                        loadingSpinner.style.display = 'none';
                        return true;
                    }
                } else {
                    return false;
                }
            };

            console.log('Graphics.endLoading 方法已修复');
        }

        // 同时修复 Graphics.startLoading 方法
        if (typeof window.Graphics !== 'undefined' && window.Graphics.startLoading) {
            window.Graphics.startLoading = function () {
                const loadingSpinner = document.getElementById("loadingSpinner");
                if (!loadingSpinner && this._loadingSpinner) {
                    // 如果 loadingSpinner 不存在，需要添加到合适的容器中
                    const rpgGameContainer = document.querySelector('.rpg-game-container');
                    if (rpgGameContainer) {
                        rpgGameContainer.appendChild(this._loadingSpinner);
                        console.log('Graphics.startLoading: loadingSpinner 已添加到 RPG 游戏容器');
                    } else {
                        document.body.appendChild(this._loadingSpinner);
                        console.log('Graphics.startLoading: loadingSpinner 已添加到 document.body');
                    }
                }
            };

            console.log('Graphics.startLoading 方法已修复');
        }
    }

    patchImageLoading() {
        console.log('修复图片路径问题...');

        try {
            // 修复 ImageManager.loadFace 方法
            if (typeof window.ImageManager !== 'undefined' && window.ImageManager.loadFace) {
                const originalLoadFace = window.ImageManager.loadFace;

                window.ImageManager.loadFace = function (filename) {
                    // 检查 filename 是否是函数
                    if (typeof filename === 'function') {
                        console.warn('检测到 faceName 是函数，尝试调用获取实际文件名');
                        try {
                            filename = filename.call(this);
                        } catch (error) {
                            console.error('调用 faceName 函数失败:', error);
                            filename = ''; // 使用空字符串作为默认值
                        }
                    }

                    // 确保 filename 是字符串
                    if (typeof filename !== 'string') {
                        console.warn('faceName 不是字符串，使用空字符串:', filename);
                        filename = '';
                    }

                    console.log('ImageManager.loadFace 调用，文件名:', filename);
                    return originalLoadFace.call(this, filename);
                };

                console.log('ImageManager.loadFace 方法已修复');
            }

            // 修复 Game_Actor.prototype.faceName 方法
            if (typeof window.Game_Actor !== 'undefined' && window.Game_Actor.prototype.faceName) {
                const originalFaceName = window.Game_Actor.prototype.faceName;

                window.Game_Actor.prototype.faceName = function () {
                    try {
                        const result = originalFaceName.call(this);
                        // 确保返回值是字符串
                        if (typeof result === 'function') {
                            console.warn('Game_Actor.faceName 返回了函数，尝试调用');
                            return result.call(this) || '';
                        }
                        return result || '';
                    } catch (error) {
                        console.error('Game_Actor.faceName 调用失败:', error);
                        return '';
                    }
                };

                console.log('Game_Actor.faceName 方法已修复');
            }

            // 通用的图片路径验证函数
            window.validateImagePath = function (path) {
                if (typeof path === 'function') {
                    console.warn('图片路径是函数，尝试调用:', path);
                    try {
                        path = path();
                    } catch (error) {
                        console.error('调用图片路径函数失败:', error);
                        return '';
                    }
                }

                if (typeof path !== 'string') {
                    console.warn('图片路径不是字符串:', path);
                    return '';
                }

                return path;
            };

        } catch (error) {
            console.error('修复图片路径问题失败:', error);
        }
    }

    setupSceneMonitoring() {
        console.log('设置场景监听器...');

        try {

            // 保存原始的 SceneManager 方法
            const _SceneManager_onSceneCreate = window.SceneManager.onSceneCreate;
            const _SceneManager_onSceneStart = window.SceneManager.onSceneStart;

            // 重写 Scene_Map.createDisplayObjects 来在显示对象创建后刷新对象树
            const _Scene_Map_createDisplayObjects = window.Scene_Map.prototype.createDisplayObjects;
            window.Scene_Map.prototype.createDisplayObjects = function () {
                console.log('🔍 [调试] Scene_Map.createDisplayObjects 被调用');
                const result = _Scene_Map_createDisplayObjects.call(this);
                console.log('🔍 [调试] createDisplayObjects 完成');

                // Scene_Map 的显示对象创建完成后，重新刷新对象树
                console.log('🔍 [调试] Scene_Map 显示对象创建完成，子对象数量:', this.children ? this.children.length : 0);
                console.log('🔍 [调试] Scene_Map 子对象:', this.children ? this.children.map(child => child.constructor.name) : []);

                const sceneName = this.constructor.name;

                // 延迟一帧后刷新对象树，确保所有子对象都已添加
                setTimeout(() => {
                    console.log('🔍 [调试] 延迟刷新 Scene_Map 对象树');
                    // 动态导入并重新设置根对象以刷新对象树
                    import('../../stores/sceneModelStore').then(({ setCurrentScene }) => {
                        setCurrentScene(this, sceneName);
                        console.log('🔍 [调试] Scene_Map 对象树已刷新，最终子对象数量:', this.children ? this.children.length : 0);
                    }).catch(error => {
                        console.error('刷新 Scene_Map 对象树失败:', error);
                    });
                }, 0);

                return result;
            };

            // 添加对 DataManager.loadMapData 的调试，并处理异步加载
            const _DataManager_loadMapData = window.DataManager.loadMapData;
            window.DataManager.loadMapData = function (mapId) {
                console.log('🔍 [调试] DataManager.loadMapData 开始, mapId:', mapId);
                console.log('🔍 [调试] 加载前 $dataMap:', window.$dataMap ? {
                    parallaxName: window.$dataMap.parallaxName,
                    data: window.$dataMap.data ? window.$dataMap.data.slice(0, 5) : 'undefined'
                } : 'null');

                // 标记数据正在加载
                this._mapDataLoading = true;
                this._mapDataLoaded = false;

                const result = _DataManager_loadMapData.call(this, mapId);

                console.log('🔍 [调试] DataManager.loadMapData 调用完成');
                return result;
            };

            // 重写 DataManager.isMapLoaded 来处理异步加载
            const _DataManager_isMapLoaded = window.DataManager.isMapLoaded;
            window.DataManager.isMapLoaded = function () {
                // 如果正在加载，等待真正完成
                if (this._mapDataLoading) {
                    // 不要过早返回true，让异步加载完成
                    return false;
                }

                // 使用原生检查
                return _DataManager_isMapLoaded.call(this);
            };

            // 重写 onSceneCreate - 场景创建时触发
            window.SceneManager.onSceneCreate = function () {
                // 调用原始方法
                _SceneManager_onSceneCreate.call(this);

                // 场景创建完成后直接设置根对象
                if (this._scene) {
                    const sceneName = this._scene.constructor.name;
                    console.log('场景创建完成:', sceneName);

                    // 动态导入并直接调用 setCurrentScene
                    import('../../stores/sceneModelStore').then(({ setCurrentScene }) => {
                        setCurrentScene(this._scene, sceneName);
                    }).catch(error => {
                        console.error('导入 sceneModelStore 失败:', error);
                    });
                }
            };

            // 重写 onSceneStart - 场景启动时触发
            window.SceneManager.onSceneStart = function () {
                // 调用原始方法
                _SceneManager_onSceneStart.call(this);

                // 场景启动完成后直接设置根对象
                if (this._scene) {
                    const sceneName = this._scene.constructor.name;
                    console.log('场景启动完成:', sceneName);

                    // Scene_Map 的刷新逻辑已移至 onMapLoaded，这里不再需要

                    // 动态导入并直接调用 setCurrentScene
                    import('../../stores/sceneModelStore').then(({ setCurrentScene }) => {
                        setCurrentScene(this._scene, sceneName);
                    }).catch(error => {
                        console.error('导入 sceneModelStore 失败:', error);
                    });
                }
            };

            console.log('场景监听器设置完成');

        } catch (error) {
            console.error('设置场景监听器失败:', error);
        }
    }



    showLoadingSpinner() {
        const loadingSpinner = document.createElement("div");
        const loadingSpinnerImage = document.createElement("div");
        loadingSpinner.id = "loadingSpinner";
        loadingSpinnerImage.id = "loadingSpinnerImage";
        loadingSpinner.appendChild(loadingSpinnerImage);
        document.body.appendChild(loadingSpinner);
    }

    eraseLoadingSpinner() {
        const loadingSpinner = document.getElementById("loadingSpinner");
        if (loadingSpinner && loadingSpinner.parentElement) {
            try {
                loadingSpinner.parentElement.removeChild(loadingSpinner);
                console.log('loadingSpinner 已从其父容器中移除');
            } catch (error) {
                console.warn('移除 loadingSpinner 时出错:', error);
                // 如果移除失败，尝试隐藏元素
                loadingSpinner.style.display = 'none';
            }
        }
    }

    printError(name, message) {
        this.eraseLoadingSpinner();
        if (!document.getElementById("errorPrinter")) {
            const errorPrinter = document.createElement("div");
            errorPrinter.id = "errorPrinter";
            errorPrinter.innerHTML = this.makeErrorHtml(name, message);
            document.body.appendChild(errorPrinter);
        }
    }

    makeErrorHtml(name, message) {
        const nameDiv = document.createElement("div");
        const messageDiv = document.createElement("div");
        nameDiv.id = "errorName";
        messageDiv.id = "errorMessage";
        nameDiv.innerHTML = name;
        messageDiv.innerHTML = message;
        return nameDiv.outerHTML + messageDiv.outerHTML;
    }

}

// 防止重复初始化
if (window.RPGMakerMainInstance) {
    console.log('检测到已有 Main 实例，跳过重复初始化');
} else {
    console.log('创建新的 Main 实例');
    const main = new Main(); // 实例会在构造函数中自动设置到 window.RPGMakerMainInstance

    main.run().catch(error => {
        console.error('Main 启动失败:', error);
    });
}

//-----------------------------------------------------------------------------
