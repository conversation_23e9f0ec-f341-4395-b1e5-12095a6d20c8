// 数据对象结构
export interface DataObject {
  name: string;
  label: string;
  fields: DataField[];
}

export interface DataField {
  name: string;
  label: string;
  value: string;
  type?: 'field' | 'list'; // 字段类型：单个字段或列表
}

// 扩展 Window 类型以包含 RPG Maker MZ 的全局变量
declare global {
  interface Window {
    $dataActors: any[];
    $dataSystem: any;
    $dataItems: any[];
    $dataWeapons: any[];
    $dataArmors: any[];
    $dataSkills: any[];
    $gameVariables: any;
    $gameSwitches: any;
    $gameParty: any;
    DataManager: any;
  }
}

// 获取分类的数据对象
export function getDataObjects(): DataObject[] {
  const objects: DataObject[] = [];

  try {
    // $dataActors - 角色数据
    if (window.$dataActors && Array.isArray(window.$dataActors)) {
      for (let i = 1; i < Math.min(window.$dataActors.length, 10); i++) {
        const actor = window.$dataActors[i];
        if (actor && actor.name) {
          const fields: <PERSON>Field[] = [];
          try {
            Object.keys(actor).forEach(key => {
              if (typeof actor[key] !== 'object' && typeof actor[key] !== 'function') {
                fields.push({
                  name: key,
                  label: key,
                  value: `window.$dataActors[${i}].${key}`
                });
              }
            });
          } catch (error) {
            console.warn('Error processing actor fields:', error);
          }

          objects.push({
            name: `$dataActors[${i}]`,
            label: `${actor.name} (角色${i})`,
            fields
          });
        }
      }
    }
  } catch (error) {
    console.warn('Error processing $dataActors:', error);
  }

  try {
    // $dataSystem - 系统数据
    if (window.$dataSystem && typeof window.$dataSystem === 'object') {
      const fields: DataField[] = [];
      try {
        Object.keys(window.$dataSystem).forEach(key => {
          if (typeof window.$dataSystem[key] !== 'object' && typeof window.$dataSystem[key] !== 'function') {
            fields.push({
              name: key,
              label: key,
              value: `window.$dataSystem.${key}`
            });
          }
        });
      } catch (error) {
        console.warn('Error processing $dataSystem fields:', error);
      }

      if (fields.length > 0) {
        objects.push({
          name: '$dataSystem',
          label: '系统数据',
          fields
        });
      }
    }
  } catch (error) {
    console.warn('Error processing $dataSystem:', error);
  }

  // $gameVariables - 变量
  if (window.$dataSystem && window.$dataSystem.variables) {
    const fields: DataField[] = [];
    for (let i = 1; i < Math.min(window.$dataSystem.variables.length, 21); i++) {
      if (window.$dataSystem.variables[i]) {
        fields.push({
          name: `value(${i})`,
          label: `${i}: ${window.$dataSystem.variables[i]}`,
          value: `window.$gameVariables.value(${i})`
        });
      }
    }

    objects.push({
      name: '$gameVariables',
      label: '游戏变量',
      fields
    });
  }

  // $gameSwitches - 开关
  if (window.$dataSystem && window.$dataSystem.switches) {
    const fields: DataField[] = [];
    for (let i = 1; i < Math.min(window.$dataSystem.switches.length, 21); i++) {
      if (window.$dataSystem.switches[i]) {
        fields.push({
          name: `value(${i})`,
          label: `${i}: ${window.$dataSystem.switches[i]}`,
          value: `window.$gameSwitches.value(${i})`
        });
      }
    }

    objects.push({
      name: '$gameSwitches',
      label: '游戏开关',
      fields
    });
  }

  // $gameParty - 队伍
  const partyFields: DataField[] = [
    { name: 'gold()', label: '金币数量', value: 'window.$gameParty.gold()' },
    { name: 'allMembers().length', label: '队伍人数', value: 'window.$gameParty.allMembers().length' },
    { name: 'size()', label: '队伍大小', value: 'window.$gameParty.size()' },
    { name: 'leader().name', label: '队长名称', value: 'window.$gameParty.leader().name' },
    { name: 'leader().level', label: '队长等级', value: 'window.$gameParty.leader().level' }
  ];

  objects.push({
    name: '$gameParty',
    label: '游戏队伍',
    fields: partyFields
  });

  // 添加存档文件数据
  try {
    const saveFileFields: DataField[] = [];

    // 检查存档文件
    for (let i = 1; i <= 20; i++) {
      saveFileFields.push({
        name: `savefileInfo(${i}).title`,
        label: `存档${i} - 标题`,
        value: `window.DataManager.savefileInfo(${i})?.title || '空存档'`
      });
      saveFileFields.push({
        name: `savefileInfo(${i}).playtime`,
        label: `存档${i} - 游戏时间`,
        value: `window.DataManager.savefileInfo(${i})?.playtime || '00:00:00'`
      });
      saveFileFields.push({
        name: `savefileInfo(${i}).timestamp`,
        label: `存档${i} - 时间戳`,
        value: `window.DataManager.savefileInfo(${i})?.timestamp || 0`
      });
    }

    objects.push({
      name: 'SaveFiles',
      label: '存档文件',
      fields: saveFileFields
    });
  } catch (error) {
    console.warn('Error processing save files:', error);
  }

  // 添加背包物品数据
  try {
    if (window.$dataItems && Array.isArray(window.$dataItems)) {
      const inventoryFields: DataField[] = [];

      for (let i = 1; i < Math.min(window.$dataItems.length, 50); i++) {
        const item = window.$dataItems[i];
        if (item && item.name) {
          inventoryFields.push({
            name: `numItems(${i})`,
            label: `${item.name} - 数量`,
            value: `window.$gameParty.numItems(window.$dataItems[${i}])`
          });
          inventoryFields.push({
            name: `item[${i}].name`,
            label: `${item.name} - 名称`,
            value: `window.$dataItems[${i}].name`
          });
          inventoryFields.push({
            name: `item[${i}].iconIndex`,
            label: `${item.name} - 图标索引`,
            value: `window.$dataItems[${i}].iconIndex`
          });
          inventoryFields.push({
            name: `item[${i}].price`,
            label: `${item.name} - 价格`,
            value: `window.$dataItems[${i}].price`
          });
        }
      }

      objects.push({
        name: 'Inventory',
        label: '背包物品',
        fields: inventoryFields
      });
    }
  } catch (error) {
    console.warn('Error processing inventory:', error);
  }

  // 添加技能数据
  try {
    if (window.$dataSkills && Array.isArray(window.$dataSkills)) {
      const skillFields: DataField[] = [];

      for (let i = 1; i < Math.min(window.$dataSkills.length, 30); i++) {
        const skill = window.$dataSkills[i];
        if (skill && skill.name) {
          skillFields.push({
            name: `skill[${i}].name`,
            label: `${skill.name} - 名称`,
            value: `window.$dataSkills[${i}].name`
          });
          skillFields.push({
            name: `skill[${i}].iconIndex`,
            label: `${skill.name} - 图标索引`,
            value: `window.$dataSkills[${i}].iconIndex`
          });
          skillFields.push({
            name: `skill[${i}].mpCost`,
            label: `${skill.name} - MP消耗`,
            value: `window.$dataSkills[${i}].mpCost`
          });
        }
      }

      objects.push({
        name: 'Skills',
        label: '技能列表',
        fields: skillFields
      });
    }
  } catch (error) {
    console.warn('Error processing skills:', error);
  }

  // 添加武器数据
  try {
    if (window.$dataWeapons && Array.isArray(window.$dataWeapons)) {
      const weaponFields: DataField[] = [];

      for (let i = 1; i < Math.min(window.$dataWeapons.length, 30); i++) {
        const weapon = window.$dataWeapons[i];
        if (weapon && weapon.name) {
          weaponFields.push({
            name: `weapon[${i}].name`,
            label: `${weapon.name} - 名称`,
            value: `window.$dataWeapons[${i}].name`
          });
          weaponFields.push({
            name: `weapon[${i}].iconIndex`,
            label: `${weapon.name} - 图标索引`,
            value: `window.$dataWeapons[${i}].iconIndex`
          });
          weaponFields.push({
            name: `weapon[${i}].price`,
            label: `${weapon.name} - 价格`,
            value: `window.$dataWeapons[${i}].price`
          });
          weaponFields.push({
            name: `numItems(weapon[${i}])`,
            label: `${weapon.name} - 持有数量`,
            value: `window.$gameParty.numItems(window.$dataWeapons[${i}])`
          });
        }
      }

      objects.push({
        name: 'Weapons',
        label: '武器数据',
        fields: weaponFields
      });
    }
  } catch (error) {
    console.warn('Error processing weapons:', error);
  }

  // 添加防具数据
  try {
    if (window.$dataArmors && Array.isArray(window.$dataArmors)) {
      const armorFields: DataField[] = [];

      for (let i = 1; i < Math.min(window.$dataArmors.length, 30); i++) {
        const armor = window.$dataArmors[i];
        if (armor && armor.name) {
          armorFields.push({
            name: `armor[${i}].name`,
            label: `${armor.name} - 名称`,
            value: `window.$dataArmors[${i}].name`
          });
          armorFields.push({
            name: `armor[${i}].iconIndex`,
            label: `${armor.name} - 图标索引`,
            value: `window.$dataArmors[${i}].iconIndex`
          });
          armorFields.push({
            name: `armor[${i}].price`,
            label: `${armor.name} - 价格`,
            value: `window.$dataArmors[${i}].price`
          });
          armorFields.push({
            name: `numItems(armor[${i}])`,
            label: `${armor.name} - 持有数量`,
            value: `window.$gameParty.numItems(window.$dataArmors[${i}])`
          });
        }
      }

      objects.push({
        name: 'Armors',
        label: '防具数据',
        fields: armorFields
      });
    }
  } catch (error) {
    console.warn('Error processing armors:', error);
  }

  // 添加角色状态数据
  try {
    const actorStatusFields: DataField[] = [];

    for (let i = 1; i <= 8; i++) {
      actorStatusFields.push({
        name: `actor(${i}).name`,
        label: `角色${i} - 名称`,
        value: `window.$gameActors.actor(${i})?.name || '未加入'`
      });
      actorStatusFields.push({
        name: `actor(${i}).level`,
        label: `角色${i} - 等级`,
        value: `window.$gameActors.actor(${i})?.level || 0`
      });
      actorStatusFields.push({
        name: `actor(${i}).exp`,
        label: `角色${i} - 经验值`,
        value: `window.$gameActors.actor(${i})?.exp || 0`
      });
      actorStatusFields.push({
        name: `actor(${i}).hp`,
        label: `角色${i} - 当前HP`,
        value: `window.$gameActors.actor(${i})?.hp || 0`
      });
      actorStatusFields.push({
        name: `actor(${i}).mp`,
        label: `角色${i} - 当前MP`,
        value: `window.$gameActors.actor(${i})?.mp || 0`
      });
    }

    objects.push({
      name: 'ActorStatus',
      label: '角色状态',
      fields: actorStatusFields
    });
  } catch (error) {
    console.warn('Error processing actor status:', error);
  }

  return objects;
}

// 获取列表数据源（专门用于 UIList 等组件）
export function getListDataSources(): DataObject[] {
  const objects: DataObject[] = [];

  try {
    // 存档文件列表
    const saveFileFields: DataField[] = [];

    // 生成存档文件列表表达式
    saveFileFields.push({
      name: 'allSaveFiles',
      label: '所有存档文件（1-20）',
      value: `Array.from({length: 20}, (_, i) => i + 1).map(id => ({
        id,
        title: window.DataManager.savefileInfo(id)?.title || '空存档',
        playtime: window.DataManager.savefileInfo(id)?.playtime || '00:00:00',
        timestamp: window.DataManager.savefileInfo(id)?.timestamp || 0,
        exists: !!window.DataManager.savefileInfo(id)
      }))`,
      type: 'list'
    });

    // 只获取有效存档
    saveFileFields.push({
      name: 'validSaveFiles',
      label: '有效存档文件',
      value: `Array.from({length: 20}, (_, i) => i + 1)
        .map(id => window.DataManager.savefileInfo(id))
        .filter(info => info)
        .map((info, index) => ({
          id: index + 1,
          title: info.title,
          playtime: info.playtime,
          timestamp: info.timestamp
        }))`,
      type: 'list'
    });

    objects.push({
      name: 'SaveFilesList',
      label: '存档文件列表',
      fields: saveFileFields
    });
  } catch (error) {
    console.warn('Error processing save files list:', error);
  }

  try {
    // 背包物品列表
    const inventoryFields: DataField[] = [];

    // 所有道具数据
    inventoryFields.push({
      name: 'allItems',
      label: '所有道具数据',
      value: `window.$dataItems.slice(1).filter(item => item).map(item => ({
        id: item.id,
        name: item.name,
        iconIndex: item.iconIndex,
        description: item.description,
        price: item.price,
        count: window.$gameParty.numItems(item)
      }))`,
      type: 'list'
    });

    // 持有的道具
    inventoryFields.push({
      name: 'ownedItems',
      label: '持有的道具',
      value: `window.$dataItems.slice(1).filter(item => item && window.$gameParty.numItems(item) > 0).map(item => ({
        id: item.id,
        name: item.name,
        iconIndex: item.iconIndex,
        description: item.description,
        price: item.price,
        count: window.$gameParty.numItems(item)
      }))`,
      type: 'list'
    });

    objects.push({
      name: 'InventoryList',
      label: '背包物品列表',
      fields: inventoryFields
    });
  } catch (error) {
    console.warn('Error processing inventory list:', error);
  }

  try {
    // 技能列表
    const skillFields: DataField[] = [];

    // 所有技能数据
    skillFields.push({
      name: 'allSkills',
      label: '所有技能数据',
      value: `window.$dataSkills.slice(1).filter(skill => skill).map(skill => ({
        id: skill.id,
        name: skill.name,
        iconIndex: skill.iconIndex,
        description: skill.description,
        mpCost: skill.mpCost,
        scope: skill.scope,
        occasion: skill.occasion
      }))`,
      type: 'list'
    });

    // 队伍已学会的技能
    skillFields.push({
      name: 'learnedSkills',
      label: '队伍已学会的技能',
      value: `window.$gameParty.allMembers().flatMap(actor =>
        actor.skills().map(skillId => window.$dataSkills[skillId])
      ).filter((skill, index, self) => skill && self.findIndex(s => s.id === skill.id) === index)
      .map(skill => ({
        id: skill.id,
        name: skill.name,
        iconIndex: skill.iconIndex,
        description: skill.description,
        mpCost: skill.mpCost
      }))`,
      type: 'list'
    });

    objects.push({
      name: 'SkillsList',
      label: '技能列表',
      fields: skillFields
    });
  } catch (error) {
    console.warn('Error processing skills list:', error);
  }

  try {
    // 武器列表
    const weaponFields: DataField[] = [];

    // 所有武器数据
    weaponFields.push({
      name: 'allWeapons',
      label: '所有武器数据',
      value: `window.$dataWeapons.slice(1).filter(weapon => weapon).map(weapon => ({
        id: weapon.id,
        name: weapon.name,
        iconIndex: weapon.iconIndex,
        description: weapon.description,
        price: weapon.price,
        params: weapon.params,
        count: window.$gameParty.numItems(weapon)
      }))`,
      type: 'list'
    });

    // 持有的武器
    weaponFields.push({
      name: 'ownedWeapons',
      label: '持有的武器',
      value: `window.$dataWeapons.slice(1).filter(weapon => weapon && window.$gameParty.numItems(weapon) > 0).map(weapon => ({
        id: weapon.id,
        name: weapon.name,
        iconIndex: weapon.iconIndex,
        description: weapon.description,
        price: weapon.price,
        params: weapon.params,
        count: window.$gameParty.numItems(weapon)
      }))`,
      type: 'list'
    });

    objects.push({
      name: 'WeaponsList',
      label: '武器列表',
      fields: weaponFields
    });
  } catch (error) {
    console.warn('Error processing weapons list:', error);
  }

  try {
    // 防具列表
    const armorFields: DataField[] = [];

    // 所有防具数据
    armorFields.push({
      name: 'allArmors',
      label: '所有防具数据',
      value: `window.$dataArmors.slice(1).filter(armor => armor).map(armor => ({
        id: armor.id,
        name: armor.name,
        iconIndex: armor.iconIndex,
        description: armor.description,
        price: armor.price,
        params: armor.params,
        count: window.$gameParty.numItems(armor)
      }))`,
      type: 'list'
    });

    // 持有的防具
    armorFields.push({
      name: 'ownedArmors',
      label: '持有的防具',
      value: `window.$dataArmors.slice(1).filter(armor => armor && window.$gameParty.numItems(armor) > 0).map(armor => ({
        id: armor.id,
        name: armor.name,
        iconIndex: armor.iconIndex,
        description: armor.description,
        price: armor.price,
        params: armor.params,
        count: window.$gameParty.numItems(armor)
      }))`,
      type: 'list'
    });

    objects.push({
      name: 'ArmorsList',
      label: '防具列表',
      fields: armorFields
    });
  } catch (error) {
    console.warn('Error processing armors list:', error);
  }

  try {
    // 角色列表
    const actorFields: DataField[] = [];

    // 所有角色数据
    actorFields.push({
      name: 'allActors',
      label: '所有角色数据',
      value: `window.$dataActors.slice(1).filter(actor => actor).map(actor => ({
        id: actor.id,
        name: actor.name,
        nickname: actor.nickname,
        profile: actor.profile,
        classId: actor.classId,
        characterName: actor.characterName,
        characterIndex: actor.characterIndex,
        faceName: actor.faceName,
        faceIndex: actor.faceIndex,
        initialLevel: actor.initialLevel
      }))`,
      type: 'list'
    });

    // 队伍成员
    actorFields.push({
      name: 'partyMembers',
      label: '当前队伍成员',
      value: `window.$gameParty.allMembers().map(actor => ({
        id: actor.actorId(),
        name: actor.name(),
        nickname: actor.nickname(),
        level: actor.level,
        exp: actor.currentExp(),
        hp: actor.hp,
        mp: actor.mp,
        params: actor.params,
        classId: actor.currentClass().id,
        className: actor.currentClass().name,
        characterName: actor.characterName(),
        characterIndex: actor.characterIndex(),
        faceName: actor.faceName(),
        faceIndex: actor.faceIndex()
      }))`,
      type: 'list'
    });

    objects.push({
      name: 'ActorsList',
      label: '角色列表',
      fields: actorFields
    });
  } catch (error) {
    console.warn('Error processing actors list:', error);
  }

  return objects;
}

// 静态数据作为备用
export const staticDataObjects: DataObject[] = [
  {
    name: '$gameParty',
    label: '游戏队伍',
    fields: [
      { name: 'gold()', label: '金币数量', value: 'window.$gameParty.gold()' },
      { name: 'allMembers().length', label: '队伍人数', value: 'window.$gameParty.allMembers().length' },
      { name: 'leader().name', label: '队长名称', value: 'window.$gameParty.leader().name' },
      { name: 'leader().level', label: '队长等级', value: 'window.$gameParty.leader().level' }
    ]
  },
  {
    name: '$gameVariables',
    label: '游戏变量',
    fields: [
      { name: 'value(1)', label: '变量001', value: 'window.$gameVariables.value(1)' },
      { name: 'value(2)', label: '变量002', value: 'window.$gameVariables.value(2)' },
      { name: 'value(3)', label: '变量003', value: 'window.$gameVariables.value(3)' }
    ]
  },
  {
    name: 'SaveFiles',
    label: '存档文件',
    fields: [
      { name: 'savefileInfo(1).title', label: '存档1 - 标题', value: 'window.DataManager.savefileInfo(1)?.title || "空存档"' },
      { name: 'savefileInfo(1).playtime', label: '存档1 - 游戏时间', value: 'window.DataManager.savefileInfo(1)?.playtime || "00:00:00"' },
      { name: 'savefileInfo(2).title', label: '存档2 - 标题', value: 'window.DataManager.savefileInfo(2)?.title || "空存档"' },
      { name: 'savefileInfo(2).playtime', label: '存档2 - 游戏时间', value: 'window.DataManager.savefileInfo(2)?.playtime || "00:00:00"' }
    ]
  },
  {
    name: 'Inventory',
    label: '背包物品',
    fields: [
      { name: 'numItems(1)', label: '道具001 - 数量', value: 'window.$gameParty.numItems(window.$dataItems[1])' },
      { name: 'item[1].name', label: '道具001 - 名称', value: 'window.$dataItems[1]?.name || "未知道具"' },
      { name: 'item[1].iconIndex', label: '道具001 - 图标索引', value: 'window.$dataItems[1]?.iconIndex || 0' }
    ]
  },
  {
    name: 'Skills',
    label: '技能列表',
    fields: [
      { name: 'skill[1].name', label: '技能001 - 名称', value: 'window.$dataSkills[1]?.name || "未知技能"' },
      { name: 'skill[1].iconIndex', label: '技能001 - 图标索引', value: 'window.$dataSkills[1]?.iconIndex || 0' },
      { name: 'skill[1].mpCost', label: '技能001 - MP消耗', value: 'window.$dataSkills[1]?.mpCost || 0' }
    ]
  },
  {
    name: 'ActorStatus',
    label: '角色状态',
    fields: [
      { name: 'actor(1).name', label: '角色1 - 名称', value: 'window.$gameActors.actor(1)?.name || "未加入"' },
      { name: 'actor(1).level', label: '角色1 - 等级', value: 'window.$gameActors.actor(1)?.level || 0' },
      { name: 'actor(1).hp', label: '角色1 - 当前HP', value: 'window.$gameActors.actor(1)?.hp || 0' },
      { name: 'actor(1).mp', label: '角色1 - 当前MP', value: 'window.$gameActors.actor(1)?.mp || 0' }
    ]
  }
];
