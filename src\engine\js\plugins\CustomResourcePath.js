/*:
 * @plugindesc 自定义资源路径插件 - 修改游戏资源的加载路径
 * <AUTHOR>
 *
 * @param BasePath
 * @text 基础路径
 * @desc 资源文件的基础路径
 * @default ../projects/Project3/
 *
 * @help
 * 这个插件允许修改RPG Maker MZ游戏资源的加载路径，
 * 使其可以从自定义位置加载资源文件。
 * 将相对路径转换为绝对路径
 */

(() => {
  const pluginName = "CustomResourcePath";

  // 获取动态项目路径
  function getBasePath() {
    // 优先从全局配置获取
    if (window.PROJECT_CONFIG && window.PROJECT_CONFIG.projectPath) {
      return window.PROJECT_CONFIG.projectPath + "/";
    }

    // 备用：从插件参数获取
    if (typeof PluginManager !== 'undefined') {
      const parameters = PluginManager.parameters(pluginName);
      if (parameters["BasePath"]) {
        return parameters["BasePath"];
      }
    }

    // 如果没有配置，返回空字符串（这会导致相对路径加载失败，提醒用户需要先选择项目）
    console.warn('CustomResourcePath: 未找到项目路径配置，请先选择项目');
    return "";
  }

  // 延迟执行依赖于 RPG Maker MZ 对象的代码
  function setupRPGMakerOverrides() {
    if (typeof DataManager === 'undefined' || typeof ImageManager === 'undefined') {
      console.log("CustomResourcePath: RPG Maker MZ 对象尚未加载，稍后重试");
      setTimeout(setupRPGMakerOverrides, 100);
      return;
    }

    console.log("CustomResourcePath: 设置 RPG Maker MZ 对象重写");

    // 修改数据文件加载路径 - 使用 Tauri API，保持原生异步行为
    const _DataManager_loadDataFile = DataManager.loadDataFile;
    DataManager.loadDataFile = async function (name, src) {
      const basePath = getBasePath();
      const filePath = basePath + "data/" + src;
      console.log('加载数据文件:', filePath);

      try {
        // 动态导入 Tauri API
        const { TauriAPI } = await import('../../../lib/tauriAPI');

        // 使用 Tauri API 读取文件
        const result = await TauriAPI.Project.readProjectFile(filePath);

        if (result.success && result.data) {
          // 解析 JSON 数据
          const data = JSON.parse(result.data);
          window[name] = data;

          // 模拟原生的 XMLHttpRequest 成功回调
          if (window.DataManager && window.DataManager.onXhrLoad) {
            const mockXhr = {
              responseText: result.data,
              status: 200
            };
            window.DataManager.onXhrLoad(mockXhr, name, src, filePath);
          }

          // 标记地图数据加载完成
          if (name === '$dataMap' && window.DataManager && window.DataManager._mapDataLoading) {
            window.DataManager._mapDataLoading = false;
            console.log('🔍 [调试] 地图数据异步加载真正完成');
          }

          console.log(`✓ 数据文件加载成功: ${name}`);
          console.log(`✓ 数据内容验证 - ${name}:`, {
            parallaxName: data.parallaxName,
            dataLength: data.data ? data.data.length : 'undefined',
            dataFirst10: data.data ? data.data.slice(0, 10) : 'undefined'
          });
        } else {
          console.error(`✗ 数据文件加载失败: ${name}`, result.error || '未知错误');
          if (window.DataManager && window.DataManager.onXhrError) {
            window.DataManager.onXhrError(name, src, filePath);
          }
        }
      } catch (error) {
        console.error(`✗ 数据文件加载异常: ${name}`, error);
        if (window.DataManager && window.DataManager.onXhrError) {
          window.DataManager.onXhrError(name, src, filePath);
        }
      }
    };

    // 规范化路径分隔符（统一使用正斜杠）
    function normalizePath(path) {
      if (!path) return "";

      // 将所有反斜杠替换为正斜杠
      let normalized = path.replace(/\\/g, '/');

      // 移除重复的斜杠
      normalized = normalized.replace(/\/+/g, '/');

      // 对于 Windows 绝对路径，保持驱动器字母后的冒号
      if (/^[A-Za-z]:/.test(normalized)) {
        // 确保驱动器字母后是正斜杠
        normalized = normalized.replace(/^([A-Za-z]):\//, '$1:/');
      }

      return normalized;
    }

    // 修改图片加载路径 - 保持缓存系统
    const _ImageManager_loadBitmapFromUrl = ImageManager.loadBitmapFromUrl;
    ImageManager.loadBitmapFromUrl = function (url) {
      // 先规范化输入URL
      const normalizedUrl = normalizePath(url);

      // 检查 url 是否已经是绝对路径
      const isAbsolutePath = (path) => {
        // Windows 绝对路径：C:/ 或 D:/ 等
        if (/^[A-Za-z]:\//.test(path)) return true;
        // Unix 绝对路径：以 / 开头
        if (path.startsWith('/')) return true;
        // URL 路径：包含 ://
        if (path.includes('://')) return true;
        // Blob URL
        if (path.startsWith('blob:')) return true;
        return false;
      };

      let filePath;
      if (isAbsolutePath(normalizedUrl)) {
        // 如果已经是绝对路径，直接使用
        filePath = normalizedUrl;
        console.log('使用已有的绝对路径:', filePath);
      } else {
        // 如果是相对路径，构建完整的文件路径
        const basePath = getBasePath();
        const normalizedBasePath = normalizePath(basePath);

        // 确保路径正确拼接
        const cleanBasePath = normalizedBasePath.replace(/\/+$/, ''); // 移除末尾的斜杠
        const cleanUrl = normalizedUrl.replace(/^\/+/, ''); // 移除开头的斜杠
        filePath = cleanBasePath + '/' + cleanUrl;
        filePath = normalizePath(filePath); // 再次规范化

        console.log('构建完整文件路径:', { basePath: normalizedBasePath, url: normalizedUrl, filePath });
      }

      // 使用完整路径作为缓存键值，避免不同项目间的缓存冲突
      const cacheKey = filePath;

      // 检查缓存
      const cache = url.includes("/system/") ? this._system : this._cache;
      if (cache[cacheKey]) {
        console.log('从缓存返回图片:', url, '(缓存键:', cacheKey, ')');
        return cache[cacheKey];  // 返回缓存的 Bitmap
      }

      console.log('创建新图片 Bitmap:', filePath);

      // 创建 Bitmap 对象
      const BitmapClass = window.Bitmap;
      const bitmap = Object.create(BitmapClass.prototype);
      bitmap.initialize();
      bitmap._url = filePath;
      bitmap._originalPath = url; // 保存原始路径，用于代码生成
      bitmap._loadingState = 'loading';

      // 使用完整路径作为缓存键值
      cache[cacheKey] = bitmap;

      // 异步加载真实的图片数据
      (async () => {
        try {
          // 动态导入 Tauri API
          const { TauriAPI } = await import('../../../lib/tauriAPI');

          // 使用 Tauri API 读取图片文件（二进制）
          const result = await TauriAPI.Project.readProjectBinaryFile(filePath);

          if (result.success && result.data) {
            // 创建 Blob URL
            const blob = new Blob([result.data], { type: 'image/png' });
            const blobUrl = URL.createObjectURL(blob);

            // 创建 Image 对象
            const image = new Image();
            image.onload = () => {
              // 设置 bitmap 的属性
              bitmap._image = image;
              bitmap._url = blobUrl;
              bitmap._loadingState = 'loaded';

              // 直接用 Image 创建 BaseTexture - 这是关键！
              // 不要先创建 Canvas，因为 _createCanvas 会覆盖 BaseTexture
              bitmap._createBaseTexture(image);

              // 触发加载完成事件 - 这会通知所有使用这个 bitmap 的 Sprite
              bitmap._callLoadListeners();

              console.log(`✓ 图片加载成功: ${url}`);
              console.log('Bitmap 最终状态:', {
                url: bitmap._url,
                loadingState: bitmap._loadingState,
                width: bitmap.width,
                height: bitmap.height,
                ready: bitmap.isReady(),
                hasBaseTexture: !!bitmap.baseTexture,
                baseTextureWidth: bitmap.baseTexture ? bitmap.baseTexture.width : 0,
                baseTextureHeight: bitmap.baseTexture ? bitmap.baseTexture.height : 0,
                baseTextureValid: bitmap.baseTexture ? bitmap.baseTexture.valid : false,
                baseTextureResource: bitmap.baseTexture ? !!bitmap.baseTexture.resource : false,
                hasImage: !!bitmap._image,
                imageWidth: bitmap._image ? bitmap._image.width : 0,
                imageHeight: bitmap._image ? bitmap._image.height : 0,
                loadListenersCount: bitmap._loadListeners ? bitmap._loadListeners.length : 0
              });
            };

            image.onerror = () => {
              bitmap._loadingState = 'error';
              console.error(`✗ 图片加载失败: ${url}`);
            };

            image.src = blobUrl;

          } else {
            bitmap._loadingState = 'error';
            console.error(`✗ 图片加载失败: ${url}`, result.error || '未知错误');
          }
        } catch (error) {
          bitmap._loadingState = 'error';
          console.error(`✗ 图片加载异常: ${url}`, error);
        }
      })();

      return bitmap;
    };

    // 修改音频加载路径 - 使用 Tauri API
    const _AudioManager_createBuffer = AudioManager.createBuffer;
    AudioManager.createBuffer = function (folder, name) {
      const basePath = getBasePath();
      const ext = this.audioFileExt();
      const filePath = basePath + "audio/" + folder + Utils.encodeURI(name) + ext;
      console.log('加载音频:', filePath);

      // 创建一个临时的空 Blob URL 来避免 undefined
      const emptyBlob = new Blob([], { type: 'audio/ogg' });
      const tempUrl = URL.createObjectURL(emptyBlob);

      // 使用临时 URL 创建 WebAudio 对象
      const buffer = new WebAudio(tempUrl);
      buffer.name = name;
      buffer.frameCount = Graphics.frameCount;
      buffer._originalUrl = filePath;

      // 立即停止原始的加载过程
      if (buffer._xhr) {
        buffer._xhr.abort();
        buffer._xhr = null;
      }

      // 重写可能触发网络请求的方法
      buffer._startLoading = function () {
        // 阻止原始的网络请求
        return;
      };

      buffer._startFetching = function () {
        // 阻止原始的网络请求
        return;
      };

      // 异步加载音频数据
      (async () => {
        try {
          // 动态导入 Tauri API
          const { TauriAPI } = await import('../../../lib/tauriAPI');

          // 使用 Tauri API 读取音频文件（二进制）
          const result = await TauriAPI.Project.readProjectBinaryFile(filePath);

          if (result.success && result.data) {
            // 创建真实的 Blob URL
            const blob = new Blob([result.data], { type: 'audio/ogg' });
            const blobUrl = URL.createObjectURL(blob);

            // 清理临时 URL
            URL.revokeObjectURL(tempUrl);

            // 更新 WebAudio 对象的 URL
            buffer._url = blobUrl;

            // 重新初始化音频缓冲区
            if (buffer._startLoading) {
              buffer._startLoading();
            }

            console.log(`✓ 音频加载成功: ${name}`);
          } else {
            console.error(`✗ 音频加载失败: ${name}`, result.error || '未知错误');
          }
        } catch (error) {
          console.error(`✗ 音频加载异常: ${name}`, error);
        }
      })();

      return buffer;
    };

    // 修改效果文件加载路径 (如果存在EffectManager)
    if (typeof EffectManager !== 'undefined') {
      const _EffectManager_load = EffectManager.load;
      EffectManager.load = function (filename) {
        // 暂时禁用特效路径修改，直接使用原始方法
        // 避免 Effect 类未定义的问题
        console.log('加载特效 (使用原始方法):', filename);
        return _EffectManager_load.call(this, filename);
      };
    }

    // 修改视频加载路径
    const _Video_play = Video.play;
    Video.play = function (src) {
      const basePath = getBasePath();
      const url = basePath + "movies/" + src;
      this._element.src = url;
      this._element.onended = this._onEnd.bind(this);
      this._element.onerror = this._onError.bind(this);
      this._element.play();
    };

    // 修改字体加载路径 - 使用 Tauri API
    if (typeof FontManager !== 'undefined') {
      const _FontManager_load = FontManager.load;
      FontManager.load = async function (family, filename) {
        if (filename) {
          const basePath = getBasePath();
          const filePath = basePath + "fonts/" + filename;
          console.log('加载字体:', filePath);

          try {
            // 动态导入 Tauri API
            const { TauriAPI } = await import('../../../lib/tauriAPI');

            // 使用 Tauri API 读取字体文件（二进制）
            const result = await TauriAPI.Project.readProjectBinaryFile(filePath);

            if (result.success && result.data) {
              // 创建 Blob URL
              const blob = new Blob([result.data], { type: 'font/woff' });
              const blobUrl = URL.createObjectURL(blob);

              const font = new FontFace(family, "url(" + blobUrl + ")");
              const loadedFont = await font.load();
              document.fonts.add(loadedFont);

              console.log(`✓ 字体加载成功: ${filename}`);
              return loadedFont;
            } else {
              console.error(`✗ 字体加载失败: ${filename}`, result.error || '未知错误');
              return null;
            }
          } catch (error) {
            console.error(`✗ 字体加载异常: ${filename}`, error);
            return null;
          }
        } else {
          return null;
        }
      };
    }

    // 修改CSS中的字体URL
    document.addEventListener('DOMContentLoaded', function () {
      // 查找所有样式表
      for (let i = 0; i < document.styleSheets.length; i++) {
        try {
          const styleSheet = document.styleSheets[i];
          // 遍历样式规则
          const rules = styleSheet.cssRules || styleSheet.rules;
          if (!rules) continue;

          for (let j = 0; j < rules.length; j++) {
            const rule = rules[j];
            // 检查是否是字体规则
            if (rule.type === CSSRule.FONT_FACE_RULE) {
              const src = rule.style.getPropertyValue('src');
              if (src && src.includes('url(') && src.includes('/fonts/')) {
                // 替换字体URL
                const basePath = getBasePath();
                const newSrc = src.replace(/url\(['"]?([^'")]+)['"]?\)/g, function (match, url) {
                  if (url.startsWith('/fonts/')) {
                    return 'url(' + basePath + 'fonts/' + url.split('/fonts/')[1] + ')';
                  }
                  return match;
                });

                if (newSrc !== src) {
                  rule.style.setProperty('src', newSrc);
                  console.log('修改字体路径:', src, '->', newSrc);
                }
              }
            }
          }
        } catch (e) {
          // 跨域样式表可能会抛出安全错误，忽略它们
          console.log('无法处理样式表:', e);
        }
      }
    });
  } // 结束 setupRPGMakerOverrides 函数

  console.log("CustomResourcePath插件已加载，基础路径：" + getBasePath());


  
  // 保存原始的坐标转换方法
  const _Graphics_pageToCanvasX = Graphics.pageToCanvasX;
  const _Graphics_pageToCanvasY = Graphics.pageToCanvasY;

  // 重写坐标转换方法,使用getBoundingClientRect获取准确位置
  Graphics.pageToCanvasX = function (x) {
    if (this._canvas) {
      const rect = this._canvas.getBoundingClientRect();
      return Math.round((x - rect.left) / this._realScale);
    }
    return 0;
  };

  Graphics.pageToCanvasY = function (y) {
    if (this._canvas) {
      const rect = this._canvas.getBoundingClientRect();
      return Math.round((y - rect.top) / this._realScale);
    }
    return 0;
  };

  // 重写isInsideCanvas方法,使用新的坐标计算
  const _Graphics_isInsideCanvas = Graphics.isInsideCanvas;
  Graphics.isInsideCanvas = function (x, y) {
    const rect = this._canvas.getBoundingClientRect();
    const canvasX = this.pageToCanvasX(x + rect.left);
    const canvasY = this.pageToCanvasY(y + rect.top);
    return canvasX >= 0 && canvasX < this._width &&
      canvasY >= 0 && canvasY < this._height;
  };
  // 启动 RPG Maker MZ 对象重写设置
  setupRPGMakerOverrides();

})();