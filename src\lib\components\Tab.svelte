<script lang="ts">
  /**
   * Tab 标签页组件
   * 基于 Skeleton UI 和全局主题色彩
   */
  
  export interface TabItem {
    id: string;
    label: string;
    icon?: string;
    disabled?: boolean;
  }
  
  // Props
  export let tabs: TabItem[] = [];
  export let activeTab: string = '';
  export let variant: 'default' | 'pills' | 'underline' = 'default';
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let fullWidth: boolean = false;
  
  // Events
  export let onTabChange: (tabId: string) => void = () => {};
  
  // 内部状态
  let currentTab = $state(activeTab || (tabs.length > 0 ? tabs[0].id : ''));
  
  // 监听外部 activeTab 变化
  $effect(() => {
    if (activeTab && activeTab !== currentTab) {
      currentTab = activeTab;
    }
  });
  
  // 切换标签页
  function handleTabClick(tabId: string, disabled?: boolean) {
    if (disabled) return;
    
    currentTab = tabId;
    onTabChange(tabId);
  }
  
  // 获取标签页样式类
  function getTabClass(tab: TabItem) {
    const baseClass = 'tab-item';
    const sizeClass = `tab-${size}`;
    const variantClass = `tab-${variant}`;
    const activeClass = currentTab === tab.id ? 'tab-active' : '';
    const disabledClass = tab.disabled ? 'tab-disabled' : '';
    
    return [baseClass, sizeClass, variantClass, activeClass, disabledClass]
      .filter(Boolean)
      .join(' ');
  }
</script>

<div class="tab-container" class:full-width={fullWidth}>
  <div class="tab-list" role="tablist">
    {#each tabs as tab}
      <button
        type="button"
        role="tab"
        class={getTabClass(tab)}
        aria-selected={currentTab === tab.id}
        aria-disabled={tab.disabled}
        disabled={tab.disabled}
        onclick={() => handleTabClick(tab.id, tab.disabled)}
      >
        {#if tab.icon}
          <span class="tab-icon">{tab.icon}</span>
        {/if}
        <span class="tab-label">{tab.label}</span>
      </button>
    {/each}
  </div>
  
  <!-- 标签页内容插槽 -->
  <div class="tab-content" role="tabpanel">
    <slot {currentTab} />
  </div>
</div>

<style>
  .tab-container {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .tab-container.full-width .tab-list {
    width: 100%;
  }
  
  .tab-container.full-width .tab-item {
    flex: 1;
  }
  
  .tab-list {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    border-bottom: 1px solid var(--theme-border);
    background-color: var(--theme-surface);
  }
  
  .tab-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border: none;
    background: transparent;
    color: var(--theme-text-secondary);
    font-family: var(--font-family-base);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-base);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    position: relative;
  }
  
  .tab-item:hover:not(.tab-disabled) {
    background-color: var(--theme-surface-light);
    color: var(--theme-text);
  }
  
  .tab-item.tab-active {
    background-color: var(--theme-primary);
    color: var(--theme-text-inverse);
  }
  
  .tab-item.tab-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* 尺寸变体 */
  .tab-sm {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-sm);
  }
  
  .tab-md {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-base);
  }
  
  .tab-lg {
    padding: var(--spacing-3) var(--spacing-5);
    font-size: var(--font-size-lg);
  }
  
  /* 样式变体 */
  .tab-pills {
    border-radius: var(--border-radius-large);
    margin-bottom: var(--spacing-1);
  }
  
  .tab-pills.tab-active {
    background-color: var(--theme-primary);
  }
  
  .tab-underline {
    border-radius: 0;
    border-bottom: 2px solid transparent;
  }
  
  .tab-underline.tab-active {
    background-color: transparent;
    color: var(--theme-primary);
    border-bottom-color: var(--theme-primary);
  }
  
  .tab-icon {
    font-size: 1.1em;
  }
  
  .tab-label {
    white-space: nowrap;
  }
  
  .tab-content {
    flex: 1;
    padding: var(--spacing-4);
    background-color: var(--theme-background);
  }
  
  /* 焦点样式 */
  .tab-item:focus-visible {
    outline: 2px solid var(--theme-primary);
    outline-offset: 2px;
  }
</style>
