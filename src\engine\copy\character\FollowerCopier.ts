/**
 * 跟随者对象复制器
 */

import type { FollowerCopyOptions, CopyResult } from './types';
import { DEFAULT_COPY_OPTIONS } from './types';
import { CopyUtils } from './CopyUtils';

/**
 * 跟随者复制器类
 */
export class FollowerCopier {
  
  /**
   * 复制跟随者对象
   * @param sourceFollower 源跟随者对象
   * @param options 复制选项
   * @returns 复制结果
   */
  static async copy(sourceFollower: any, options: FollowerCopyOptions = {}): Promise<CopyResult> {
    console.log('=== FollowerCopier: 开始复制跟随者对象 ===');
    console.log('源对象:', sourceFollower);
    console.log('复制选项:', options);

    try {
      // 合并默认选项
      const copyOptions = { 
        ...DEFAULT_COPY_OPTIONS, 
        ...options,
        newMemberIndex: options.newMemberIndex !== undefined ? options.newMemberIndex : Math.floor(Math.random() * 3)
      };

      // 等待资源加载
      const resourcesReady = await CopyUtils.waitForResources();
      if (!resourcesReady) {
        throw new Error('资源加载超时');
      }

      // 解析源对象结构
      const structure = CopyUtils.parseObjectStructure(sourceFollower);
      console.log('源对象结构:', structure);

      if (!structure.gameObject) {
        throw new Error('无法找到有效的游戏对象');
      }

      const sourceGameObject = structure.gameObject;
      const sourceSprite = structure.displayObject;

      // 验证是否为跟随者对象
      if (sourceGameObject.constructor?.name !== 'Game_Follower') {
        throw new Error(`期望 Game_Follower 对象，实际得到: ${sourceGameObject.constructor?.name}`);
      }

      console.log('源跟随者信息:', {
        memberIndex: sourceGameObject._memberIndex,
        characterName: sourceGameObject._characterName,
        characterIndex: sourceGameObject._characterIndex,
        direction: sourceGameObject._direction,
        x: sourceGameObject._x,
        y: sourceGameObject._y
      });

      // 动态导入创建器
      const { createFollower } = await import('../../creators/character/FollowerCreator');

      // 创建新的跟随者对象
      const newFollower = await createFollower({
        memberIndex: copyOptions.newMemberIndex,
        characterName: sourceGameObject._characterName,
        characterIndex: sourceGameObject._characterIndex,
        direction: sourceGameObject._direction,
        x: sourceGameObject._x,
        y: sourceGameObject._y
      });

      if (!newFollower) {
        throw new Error('创建新跟随者对象失败');
      }

      // 应用位置偏移
      if (newFollower.gameObject && copyOptions.positionOffset) {
        CopyUtils.applyPositionOffset(newFollower.gameObject, copyOptions.positionOffset);
      }

      // 复制显示属性
      if (newFollower.displayObject && sourceSprite) {
        CopyUtils.copyDisplayProperties(sourceSprite, newFollower.displayObject);
      }

      // 更新显示名称
      CopyUtils.updateDisplayName(newFollower, copyOptions.nameSuffix);

      console.log('跟随者对象复制成功:', newFollower);

      return {
        success: true,
        copiedObject: newFollower,
        sourceType: CopyUtils.detectObjectType(sourceFollower),
        targetType: CopyUtils.detectObjectType(newFollower)
      };

    } catch (error) {
      console.error('FollowerCopier: 复制跟随者对象失败:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        sourceType: CopyUtils.detectObjectType(sourceFollower)
      };
    }
  }

  /**
   * 验证跟随者对象是否可以复制
   * @param sourceFollower 源跟随者对象
   * @returns 是否可以复制
   */
  static canCopy(sourceFollower: any): boolean {
    try {
      const structure = CopyUtils.parseObjectStructure(sourceFollower);
      
      if (!structure.gameObject) {
        return false;
      }

      // 检查是否为跟随者对象
      const gameObjectType = structure.gameObject.constructor?.name;
      if (gameObjectType !== 'Game_Follower') {
        return false;
      }

      // 检查必要属性
      const gameObject = structure.gameObject;
      return !!(
        gameObject._memberIndex !== undefined &&
        gameObject._characterName &&
        gameObject._characterIndex !== undefined
      );

    } catch (error) {
      console.warn('FollowerCopier: 验证对象时出错:', error);
      return false;
    }
  }

  /**
   * 获取跟随者对象信息
   * @param sourceFollower 源跟随者对象
   * @returns 跟随者信息
   */
  static getFollowerInfo(sourceFollower: any): any {
    try {
      const structure = CopyUtils.parseObjectStructure(sourceFollower);
      
      if (!structure.gameObject) {
        return null;
      }

      const gameObject = structure.gameObject;
      
      return {
        type: 'Game_Follower',
        memberIndex: gameObject._memberIndex,
        characterName: gameObject._characterName,
        characterIndex: gameObject._characterIndex,
        direction: gameObject._direction,
        position: { x: gameObject._x, y: gameObject._y },
        isWrapper: structure.isWrapper,
        displayName: structure.displayName
      };

    } catch (error) {
      console.warn('FollowerCopier: 获取跟随者信息时出错:', error);
      return null;
    }
  }
}
