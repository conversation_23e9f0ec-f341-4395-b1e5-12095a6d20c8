/**
 * 模型注册中心
 * 统一导入所有模型类，确保它们的注册代码被执行
 * 避免循环依赖问题
 */

// 导入所有模型类，触发它们的注册代码
import './spriteModel.svelte';
import './containerModel.svelte';
import './ui/labelModel.svelte';
import './ui/imageModel.svelte';
import './ui/buttonModel.svelte';
import './ui/sliderModel.svelte';
import './ui/listModel.svelte';
import './ui/itemModel.svelte';
import './ui/atlasModel.svelte';
import './spriteButtonModel.svelte';
import './senceModel.svelte';

console.log('🏭 ModelRegistry: 所有模型类已导入，注册完成');
