/**
 * ObjectTree 历史记录功能测试
 * 验证拖拽、删除、复制操作的撤销功能
 */

import { historyManager } from '../historyManager';
import type { BaseObjectModel } from '../type/baseObjectModel.svelte';

/**
 * 测试历史记录功能
 */
export class ObjectTreeHistoryTest {

  /**
   * 测试拖拽操作的历史记录
   */
  static testDragHistory(draggedObject: BaseObjectModel, targetParent: BaseObjectModel, targetIndex: number) {
    console.log('🧪 测试拖拽历史记录功能');

    // 记录操作前的状态
    const originalParent = draggedObject.parent;
    const originalIndex = originalParent ? originalParent.children.indexOf(draggedObject) : -1;

    console.log('📊 操作前状态:', {
      object: draggedObject.className,
      originalParent: originalParent?.className || 'null',
      originalIndex,
      targetParent: targetParent.className,
      targetIndex
    });

    // 执行拖拽操作
    draggedObject.setParentAndIndex(targetParent, targetIndex);

    console.log('📊 操作后状态:', {
      object: draggedObject.className,
      currentParent: draggedObject.parent?.className || 'null',
      currentIndex: draggedObject.parent ? draggedObject.parent.children.indexOf(draggedObject) : -1
    });

    // 获取历史记录状态
    const historyState = historyManager.getState();
    console.log('📊 历史记录状态:', {
      undoStackSize: historyState.undoStack.length,
      redoStackSize: historyState.redoStack.length,
      canUndo: historyState.canUndo,
      canRedo: historyState.canRedo
    });

    return {
      originalParent,
      originalIndex,
      historyState
    };
  }

  /**
   * 测试撤销操作
   */
  static testUndo(expectedObject?: BaseObjectModel, expectedParent?: BaseObjectModel | null, expectedIndex?: number) {
    console.log('🧪 测试撤销操作');

    const beforeUndo = historyManager.getState();
    console.log('📊 撤销前状态:', beforeUndo);

    if (beforeUndo.canUndo) {
      const success = historyManager.undo();

      const afterUndo = historyManager.getState();
      console.log('📊 撤销后状态:', {
        success,
        undoStackSize: afterUndo.undoStack.length,
        redoStackSize: afterUndo.redoStack.length,
        canUndo: afterUndo.canUndo,
        canRedo: afterUndo.canRedo
      });

      // 🎯 验证撤销后的状态
      if (expectedObject && expectedParent !== undefined && expectedIndex !== undefined) {
        const actualParent = expectedObject.parent;
        const actualIndex = actualParent ? actualParent.children.indexOf(expectedObject) : -1;

        console.log('📊 撤销后验证:', {
          object: expectedObject.className,
          expectedParent: expectedParent?.className || 'null',
          actualParent: actualParent?.className || 'null',
          expectedIndex,
          actualIndex,
          parentMatch: actualParent === expectedParent,
          indexMatch: actualIndex === expectedIndex
        });

        if (actualParent === expectedParent && actualIndex === expectedIndex) {
          console.log('✅ 撤销验证成功：对象已恢复到正确位置');
        } else {
          console.warn('❌ 撤销验证失败：对象位置不正确');
        }
      }

      return { success, afterUndo };
    } else {
      console.log('❌ 无法撤销：没有可撤销的操作');
      return { success: false, afterUndo: beforeUndo };
    }
  }

  /**
   * 测试重做操作
   */
  static testRedo() {
    console.log('🧪 测试重做操作');

    const beforeRedo = historyManager.getState();
    console.log('📊 重做前状态:', beforeRedo);

    if (beforeRedo.canRedo) {
      const success = historyManager.redo();

      const afterRedo = historyManager.getState();
      console.log('📊 重做后状态:', {
        success,
        undoStackSize: afterRedo.undoStack.length,
        redoStackSize: afterRedo.redoStack.length,
        canUndo: afterRedo.canUndo,
        canRedo: afterRedo.canRedo
      });

      return { success, afterRedo };
    } else {
      console.log('❌ 无法重做：没有可重做的操作');
      return { success: false, afterRedo: beforeRedo };
    }
  }

  /**
   * 测试删除操作的历史记录
   */
  static testDeleteHistory(targetObject: BaseObjectModel) {
    console.log('🧪 测试删除历史记录功能');

    // 记录删除前的状态
    const originalParent = targetObject.parent;
    const originalIndex = originalParent ? originalParent.children.indexOf(targetObject) : -1;

    // 记录父节点的所有子节点（用于验证）
    const siblingsBefore = originalParent ? [...originalParent.children] : [];

    console.log('📊 删除前状态:', {
      object: targetObject.className,
      parent: originalParent?.className || 'null',
      index: originalIndex,
      siblings: siblingsBefore.map((child, idx) => `${idx}: ${child.className}`),
      totalSiblings: siblingsBefore.length
    });

    // 执行删除操作
    targetObject.destroyWithHistory();

    console.log('📊 删除后状态: 对象已被销毁');

    // 验证删除后的父节点状态
    if (originalParent) {
      console.log('📊 删除后父节点状态:', {
        parent: originalParent.className,
        remainingSiblings: originalParent.children.map((child, idx) => `${idx}: ${child.className}`),
        totalRemaining: originalParent.children.length
      });
    }

    // 获取历史记录状态
    const historyState = historyManager.getState();
    console.log('📊 历史记录状态:', {
      undoStackSize: historyState.undoStack.length,
      redoStackSize: historyState.redoStack.length,
      canUndo: historyState.canUndo,
      canRedo: historyState.canRedo
    });

    return {
      originalParent,
      originalIndex,
      siblingsBefore,
      historyState
    };
  }

  /**
   * 测试复制操作的历史记录
   */
  static testCopyHistory(sourceObject: BaseObjectModel) {
    console.log('🧪 测试复制历史记录功能');

    console.log('📊 复制前状态:', {
      sourceObject: sourceObject.className,
      parent: sourceObject.parent?.className || 'null'
    });

    // 执行复制操作
    const copiedObject = sourceObject.cloneWithHistory();

    if (copiedObject) {
      console.log('📊 复制后状态:', {
        sourceObject: sourceObject.className,
        copiedObject: copiedObject.className,
        copiedParent: copiedObject.parent?.className || 'null'
      });
    } else {
      console.log('❌ 复制失败');
    }

    // 获取历史记录状态
    const historyState = historyManager.getState();
    console.log('📊 历史记录状态:', {
      undoStackSize: historyState.undoStack.length,
      redoStackSize: historyState.redoStack.length,
      canUndo: historyState.canUndo,
      canRedo: historyState.canRedo
    });

    return {
      copiedObject,
      historyState
    };
  }

  /**
   * 🎯 专门测试删除恢复的索引位置
   */
  static testDeleteRestoreIndex(targetObject: BaseObjectModel) {
    console.log('🧪 专门测试删除恢复的索引位置');

    const parent = targetObject.parent;
    if (!parent) {
      console.error('❌ 测试对象没有父节点');
      return null;
    }

    // 记录删除前的详细状态
    const originalIndex = parent.children.indexOf(targetObject);
    const originalSiblings = parent.children.map((child, idx) => ({
      index: idx,
      className: child.className,
      isTarget: child === targetObject
    }));

    console.log('📊 删除前详细状态:', {
      targetObject: targetObject.className,
      parent: parent.className,
      originalIndex,
      siblings: originalSiblings
    });

    // 执行删除
    console.log('\n--- 执行删除操作 ---');
    targetObject.destroyWithHistory();

    // 记录删除后状态
    const afterDeleteSiblings = parent.children.map((child, idx) => ({
      index: idx,
      className: child.className
    }));

    console.log('📊 删除后状态:', {
      parent: parent.className,
      siblings: afterDeleteSiblings,
      targetRemoved: !parent.children.includes(targetObject)
    });

    // 执行撤销
    console.log('\n--- 执行撤销操作 ---');
    const undoSuccess = historyManager.undo();

    if (undoSuccess) {
      // 验证恢复后的状态
      const afterUndoIndex = parent.children.indexOf(targetObject);
      const afterUndoSiblings = parent.children.map((child, idx) => ({
        index: idx,
        className: child.className,
        isTarget: child === targetObject
      }));

      console.log('📊 撤销后状态:', {
        targetObject: targetObject.className,
        parent: parent.className,
        restoredIndex: afterUndoIndex,
        siblings: afterUndoSiblings
      });

      // 🎯 验证索引是否正确恢复
      const indexCorrect = afterUndoIndex === originalIndex;
      const parentCorrect = targetObject.parent === parent;

      console.log('📊 恢复验证结果:', {
        expectedIndex: originalIndex,
        actualIndex: afterUndoIndex,
        indexCorrect,
        parentCorrect,
        overallSuccess: indexCorrect && parentCorrect
      });

      if (indexCorrect && parentCorrect) {
        console.log('✅ 删除恢复测试成功：对象已恢复到正确的索引位置');
      } else {
        console.error('❌ 删除恢复测试失败：对象位置不正确');
      }

      return {
        success: indexCorrect && parentCorrect,
        originalIndex,
        restoredIndex: afterUndoIndex,
        originalSiblings,
        afterUndoSiblings
      };
    } else {
      console.error('❌ 撤销操作失败');
      return null;
    }
  }

  /**
   * 完整的测试流程
   */
  static runCompleteTest(testObjects: BaseObjectModel[]) {
    console.log('🧪 开始完整的 ObjectTree 历史记录测试');

    if (testObjects.length < 2) {
      console.error('❌ 测试需要至少2个对象');
      return;
    }

    const [obj1, obj2] = testObjects;

    try {
      // 1. 测试拖拽
      console.log('\n=== 1. 测试拖拽操作 ===');
      const dragResult = this.testDragHistory(obj1, obj2, 0);

      // 2. 测试撤销拖拽
      console.log('\n=== 2. 测试撤销拖拽 ===');
      this.testUndo(obj1, dragResult.originalParent, dragResult.originalIndex);

      // 3. 测试重做拖拽
      console.log('\n=== 3. 测试重做拖拽 ===');
      this.testRedo();

      // 4. 测试删除恢复索引
      console.log('\n=== 4. 测试删除恢复索引 ===');
      this.testDeleteRestoreIndex(obj1);

      // 5. 测试复制
      console.log('\n=== 5. 测试复制操作 ===');
      const copyResult = this.testCopyHistory(obj2);

      // 6. 测试撤销复制
      if (copyResult.copiedObject) {
        console.log('\n=== 6. 测试撤销复制 ===');
        this.testUndo();
      }

      console.log('\n✅ 完整测试流程结束');

    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    }
  }
}

// 导出测试函数供控制台使用
(window as any).ObjectTreeHistoryTest = ObjectTreeHistoryTest;

// 🎯 添加快速测试删除恢复的便捷方法
(window as any).testDeleteRestore = function(objectIndex: number = 1) {
  try {
    // 获取当前场景的对象
    const state = (window as any).getCurrentState?.();
    if (!state || !state.currentScene) {
      console.error('❌ 无法获取当前场景');
      return;
    }

    const objects = state.currentScene.children;
    if (objects.length <= objectIndex) {
      console.error(`❌ 场景中没有索引为 ${objectIndex} 的对象`);
      return;
    }

    const targetObject = objects[objectIndex];
    console.log(`🎯 测试对象: ${targetObject.className}`);

    return ObjectTreeHistoryTest.testDeleteRestoreIndex(targetObject);
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
};

console.log('🧪 ObjectTree 历史记录测试工具已加载');
console.log('使用方法:');
console.log('  ObjectTreeHistoryTest.runCompleteTest([object1, object2])');
console.log('  testDeleteRestore(1) // 快速测试删除恢复，参数为对象索引');
