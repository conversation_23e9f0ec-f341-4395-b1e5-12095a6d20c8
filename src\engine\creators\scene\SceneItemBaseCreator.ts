/**
 * Scene_ItemBase 创建器
 * 专门用于创建物品基类场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';
import { type MenuBaseSceneOptions } from './SceneMenuBaseCreator';

declare global {
    interface Window {
        Window_Help: any;
        Window_ItemCategory: any;
        Window_ItemList: any;
        Window_MenuActor: any;
    }
}

/**
 * 物品基类场景创建选项
 */
export interface ItemBaseSceneOptions extends MenuBaseSceneOptions {
    /** 是否显示帮助窗口 */
    showHelpWindow?: boolean;
    /** 是否显示分类窗口 */
    showCategoryWindow?: boolean;
    /** 初始分类 */
    initialCategory?: string;
}

/**
 * 创建物品基类场景
 * @param options 创建选项
 * @returns 创建的物品基类场景实例
 */
export async function createSceneItemBase(options: ItemBaseSceneOptions = {}): Promise<any> {
    console.log('=== 创建物品基类场景 Scene_ItemBase ===');
    
    try {
        // 预加载物品基类场景资源
        await preloadItemBaseResources(options);
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_ItemBase', defaultOptions);
        
        // Scene_ItemBase 特定的设置
        setupItemBaseScene(scene, options);
        
        console.log('Scene_ItemBase 创建完成，场景属性:', {
            started: scene._started,
            active: scene._active,
            hasHelpWindow: !!scene._helpWindow,
            hasCategoryWindow: !!scene._categoryWindow,
            hasItemWindow: !!scene._itemWindow,
            hasActorWindow: !!scene._actorWindow
        });
        
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_ItemBase 失败:', error);
        throw error;
    }
}

/**
 * 预加载物品基类场景资源
 * @param options 物品基类场景选项
 */
async function preloadItemBaseResources(options: ItemBaseSceneOptions): Promise<void> {
    console.log('预加载物品基类场景资源...');
    
    if (!window.ImageManager) {
        console.warn('ImageManager 未加载，跳过物品基类资源预加载');
        return;
    }
    
    try {
        // 预加载物品系统图片
        window.ImageManager.loadSystem('Window');
        window.ImageManager.loadSystem('IconSet');
        
        // 预加载角色头像
        if (window.$gameParty && window.$gameParty.allMembers) {
            const members = window.$gameParty.allMembers();
            members.forEach((actor: any) => {
                if (actor && actor.faceName) {
                    window.ImageManager.loadFace(actor.faceName);
                    console.log('预加载角色头像:', actor.faceName);
                }
            });
        }
        
        console.log('物品基类场景资源预加载完成');
        
    } catch (error) {
        console.error('预加载物品基类场景资源失败:', error);
        // 不抛出错误，允许场景创建继续
    }
}

/**
 * 设置物品基类场景
 * @param scene 场景实例
 * @param options 物品基类场景选项
 */
function setupItemBaseScene(scene: any, options: ItemBaseSceneOptions): void {
    console.log('设置物品基类场景...');
    
    try {
        // 设置帮助窗口显示
        if (options.showHelpWindow !== undefined && scene._helpWindow) {
            scene._helpWindow.visible = options.showHelpWindow;
            console.log('设置帮助窗口显示:', options.showHelpWindow);
        }
        
        // 设置分类窗口显示
        if (options.showCategoryWindow !== undefined && scene._categoryWindow) {
            scene._categoryWindow.visible = options.showCategoryWindow;
            console.log('设置分类窗口显示:', options.showCategoryWindow);
        }
        
        // 设置初始分类
        if (options.initialCategory && scene._categoryWindow) {
            // 这里需要根据具体的分类窗口实现来设置
            console.log('设置初始分类:', options.initialCategory);
        }
        
        console.log('物品基类场景设置完成');
        
    } catch (error) {
        console.error('设置物品基类场景失败:', error);
    }
}

/**
 * 创建并启动物品基类场景
 * @param options 创建选项
 * @returns 创建的物品基类场景实例
 */
export async function createAndStartSceneItemBase(options: ItemBaseSceneOptions = {}): Promise<any> {
    console.log('=== 创建并启动 Scene_ItemBase ===');
    
    const scene = await createSceneItemBase({
        ...options,
        autoStart: true
    });
    
    console.log('Scene_ItemBase 已创建并启动');
    return scene;
}

/**
 * 创建简单的物品基类场景（用于测试）
 * @returns 创建的物品基类场景实例
 */
export async function createSimpleSceneItemBase(): Promise<any> {
    console.log('=== 创建简单物品基类场景 ===');
    
    try {
        const scene = await createSceneItemBase({
            showHelpWindow: true,
            showCategoryWindow: true,
            initialCategory: 'item',
            backgroundType: 1, // 暗化背景
            autoStart: false,
            addToStage: true
        });
        
        console.log('简单物品基类场景创建成功');
        return scene;
        
    } catch (error) {
        console.error('创建简单物品基类场景失败:', error);
        throw error;
    }
}
