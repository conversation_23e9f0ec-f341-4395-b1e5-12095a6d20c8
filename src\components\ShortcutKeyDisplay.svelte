<script lang="ts">
  /**
   * 快捷键显示组件
   * 显示当前可用的快捷键列表
   */
  
  import { getRegisteredShortcuts } from '../shortcutKey';

  // Props
  interface Props {
    visible?: boolean;
    position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  }

  let {
    visible = false,
    position = 'bottom-right'
  }: Props = $props();

  // 获取已注册的快捷键
  let shortcuts = $derived(() => {
    try {
      return getRegisteredShortcuts();
    } catch (error) {
      console.warn('获取快捷键列表失败:', error);
      return [];
    }
  });

  // 格式化快捷键显示
  function formatShortcutKey(key: string): string {
    return key
      .split('+')
      .map(part => {
        switch (part.toLowerCase()) {
          case 'ctrl': return 'Ctrl';
          case 'alt': return 'Alt';
          case 'shift': return 'Shift';
          case 'delete': return 'Del';
          case 'escape': return 'Esc';
          default: return part.toUpperCase();
        }
      })
      .join(' + ');
  }
</script>

{#if visible && shortcuts.length > 0}
  <div class="shortcut-display" class:position-{position}>
    <div class="shortcut-header">
      <span class="shortcut-title">⌨️ 快捷键</span>
    </div>
    
    <div class="shortcut-list">
      {#each shortcuts as shortcut}
        <div class="shortcut-item">
          <span class="shortcut-key">{formatShortcutKey(shortcut.key)}</span>
          <span class="shortcut-desc">{shortcut.description}</span>
        </div>
      {/each}
    </div>
  </div>
{/if}

<style>
  .shortcut-display {
    position: fixed;
    z-index: 1000;
    background: var(--theme-surface, #fff);
    border: 1px solid var(--theme-border, #e2e8f0);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 12px;
    min-width: 250px;
    max-width: 350px;
    font-size: 12px;
  }

  .position-top-left {
    top: 20px;
    left: 20px;
  }

  .position-top-right {
    top: 20px;
    right: 20px;
  }

  .position-bottom-left {
    bottom: 20px;
    left: 20px;
  }

  .position-bottom-right {
    bottom: 20px;
    right: 20px;
  }

  .shortcut-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--theme-border-light, #f0f0f0);
  }

  .shortcut-title {
    font-weight: 600;
    color: var(--theme-text, #333);
    font-size: 13px;
  }

  .shortcut-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 6px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .shortcut-item:hover {
    background: var(--theme-surface-hover, rgba(0, 0, 0, 0.05));
  }

  .shortcut-key {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--theme-primary, #2196F3);
    background: var(--theme-surface-light, #f8f9fa);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    white-space: nowrap;
  }

  .shortcut-desc {
    color: var(--theme-text-secondary, #666);
    margin-left: 8px;
    flex: 1;
    text-align: right;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .shortcut-display {
      position: relative;
      width: 100%;
      max-width: none;
      margin: 8px 0;
    }

    .position-top-left,
    .position-top-right,
    .position-bottom-left,
    .position-bottom-right {
      position: relative;
      top: auto;
      right: auto;
      bottom: auto;
      left: auto;
    }
  }
</style>
