<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import type { ItemModel } from '../../type/ui/itemModel.svelte';
    import PropertyContainer from '../PropertyContainer.svelte';
    import SafeInput from '../SafeInput.svelte';
    import Select from '../Select.svelte';
    import DataBindingModal from '../DataBindingModal.svelte';

    // 事件分发器
    const dispatch = createEventDispatcher();

    // Props
    export let itemModel: ItemModel;

    // 状态
    let showDataBindingModal = false;

    // 数据绑定信息
    $: bindingInfo = itemModel ? itemModel.getBindingInfo() : '未绑定数据';
    $: bindingCount = itemModel ? itemModel.getDataBindings().size : 0;

    /**
     * 打开数据绑定模态框
     */
    function openDataBindingModal() {
        showDataBindingModal = true;
    }

    /**
     * 关闭数据绑定模态框
     */
    function closeDataBindingModal() {
        showDataBindingModal = false;
    }

    /**
     * 处理属性变化
     */
    function handlePropertyChange() {
        dispatch('change');
    }
</script>

<div class="item-property-panel">
    <!-- 基础属性 -->
    <PropertyContainer label="基础属性">
        <PropertyContainer label="名称" labelWidth="60px">
            <SafeInput 
                bind:value={itemModel.name}
                placeholder="输入组件名称"
                on:change={handlePropertyChange}
            />
        </PropertyContainer>

        <PropertyContainer label="位置" labelWidth="60px">
            <div class="position-inputs">
                <SafeInput 
                    bind:value={itemModel.x}
                    type="number"
                    placeholder="X"
                    on:change={handlePropertyChange}
                />
                <SafeInput 
                    bind:value={itemModel.y}
                    type="number"
                    placeholder="Y"
                    on:change={handlePropertyChange}
                />
            </div>
        </PropertyContainer>

        <PropertyContainer label="尺寸" labelWidth="60px">
            <div class="size-inputs">
                <SafeInput 
                    bind:value={itemModel.width}
                    type="number"
                    placeholder="宽度"
                    on:change={handlePropertyChange}
                />
                <SafeInput 
                    bind:value={itemModel.height}
                    type="number"
                    placeholder="高度"
                    on:change={handlePropertyChange}
                />
            </div>
        </PropertyContainer>

        <PropertyContainer label="可见性" labelWidth="60px">
            <div class="visibility-controls">
                <label class="checkbox-label">
                    <input 
                        type="checkbox" 
                        bind:checked={itemModel.visible}
                        on:change={handlePropertyChange}
                    />
                    可见
                </label>
                <label class="checkbox-label">
                    <input 
                        type="checkbox" 
                        bind:checked={itemModel.enabled}
                        on:change={handlePropertyChange}
                    />
                    启用
                </label>
            </div>
        </PropertyContainer>

        <PropertyContainer label="透明度" labelWidth="60px">
            <SafeInput 
                bind:value={itemModel.alpha}
                type="number"
                min="0"
                max="1"
                step="0.1"
                placeholder="0.0 - 1.0"
                on:change={handlePropertyChange}
            />
        </PropertyContainer>
    </PropertyContainer>

    <!-- 数据绑定 -->
    <PropertyContainer label="数据绑定">
        <PropertyContainer label="绑定状态" labelWidth="80px">
            <div class="binding-status">
                <span class="binding-info">{bindingInfo}</span>
                <span class="binding-count">({bindingCount} 个绑定)</span>
            </div>
        </PropertyContainer>

        <PropertyContainer label="配置" labelWidth="80px">
            <button 
                class="config-btn"
                on:click={openDataBindingModal}
            >
                配置数据绑定
            </button>
        </PropertyContainer>

        {#if bindingCount > 0}
            <PropertyContainer label="绑定列表" labelWidth="80px">
                <div class="binding-list">
                    {#each [...itemModel.getDataBindings().entries()] as [childModel, fieldName]}
                        <div class="binding-item">
                            <span class="component-name">{childModel.className}</span>
                            <span class="arrow">→</span>
                            <span class="field-name">{fieldName}</span>
                        </div>
                    {/each}
                </div>
            </PropertyContainer>
        {/if}
    </PropertyContainer>

    <!-- 事件代码 -->
    <PropertyContainer label="事件代码">
        <PropertyContainer label="点击事件" labelWidth="80px">
            <textarea 
                bind:value={itemModel.onClickCode}
                placeholder="// 点击时执行的代码"
                rows="3"
                on:change={handlePropertyChange}
            ></textarea>
        </PropertyContainer>

        <PropertyContainer label="双击事件" labelWidth="80px">
            <textarea 
                bind:value={itemModel.onDoubleClickCode}
                placeholder="// 双击时执行的代码"
                rows="3"
                on:change={handlePropertyChange}
            ></textarea>
        </PropertyContainer>

        <PropertyContainer label="悬停事件" labelWidth="80px">
            <textarea 
                bind:value={itemModel.onHoverCode}
                placeholder="// 悬停时执行的代码"
                rows="3"
                on:change={handlePropertyChange}
            ></textarea>
        </PropertyContainer>
    </PropertyContainer>

    <!-- 变换属性 -->
    <PropertyContainer label="变换属性">
        <PropertyContainer label="缩放" labelWidth="60px">
            <div class="scale-inputs">
                <SafeInput 
                    bind:value={itemModel.scaleX}
                    type="number"
                    step="0.1"
                    placeholder="X缩放"
                    on:change={handlePropertyChange}
                />
                <SafeInput 
                    bind:value={itemModel.scaleY}
                    type="number"
                    step="0.1"
                    placeholder="Y缩放"
                    on:change={handlePropertyChange}
                />
            </div>
        </PropertyContainer>

        <PropertyContainer label="旋转" labelWidth="60px">
            <SafeInput 
                bind:value={itemModel.rotation}
                type="number"
                step="0.1"
                placeholder="旋转角度"
                on:change={handlePropertyChange}
            />
        </PropertyContainer>

        <PropertyContainer label="层级" labelWidth="60px">
            <SafeInput 
                bind:value={itemModel.zIndex}
                type="number"
                placeholder="Z轴层级"
                on:change={handlePropertyChange}
            />
        </PropertyContainer>
    </PropertyContainer>
</div>

<!-- 数据绑定模态框 -->
<DataBindingModal 
    bind:isOpen={showDataBindingModal}
    bind:itemModel={itemModel}
    on:close={closeDataBindingModal}
/>

<style>
    .item-property-panel {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 12px;
        background: #1e1e1e;
        border-radius: 6px;
        color: white;
    }

    .position-inputs,
    .size-inputs,
    .scale-inputs {
        display: flex;
        gap: 8px;
    }

    .position-inputs :global(.safe-input),
    .size-inputs :global(.safe-input),
    .scale-inputs :global(.safe-input) {
        flex: 1;
    }

    .visibility-controls {
        display: flex;
        gap: 16px;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #ccc;
        font-size: 14px;
        cursor: pointer;
    }

    .checkbox-label input[type="checkbox"] {
        margin: 0;
    }

    .binding-status {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .binding-info {
        color: #4CAF50;
        font-size: 14px;
    }

    .binding-count {
        color: #ccc;
        font-size: 12px;
    }

    .config-btn {
        background: #2196F3;
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        width: 100%;
    }

    .config-btn:hover {
        background: #1976D2;
    }

    .binding-list {
        border: 1px solid #444;
        border-radius: 4px;
        max-height: 120px;
        overflow-y: auto;
    }

    .binding-item {
        display: flex;
        align-items: center;
        padding: 6px 8px;
        border-bottom: 1px solid #444;
        gap: 6px;
        font-size: 12px;
    }

    .binding-item:last-child {
        border-bottom: none;
    }

    .component-name {
        color: #4CAF50;
        font-weight: bold;
        min-width: 60px;
    }

    .arrow {
        color: #ccc;
    }

    .field-name {
        color: #2196F3;
        flex: 1;
    }

    textarea {
        width: 100%;
        background: #333;
        border: 1px solid #555;
        border-radius: 4px;
        color: white;
        padding: 8px;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 12px;
        resize: vertical;
        min-height: 60px;
    }

    textarea:focus {
        outline: none;
        border-color: #2196F3;
    }

    textarea::placeholder {
        color: #888;
    }
</style>
