/**
 * 角色复制相关类型定义
 */

/**
 * 复制选项基础接口
 */
export interface BaseCopyOptions {
  /** 位置偏移 */
  positionOffset?: { x: number; y: number };
  /** 名称后缀 */
  nameSuffix?: string;
  /** 是否复制子对象 */
  copyChildren?: boolean;
}

/**
 * 玩家复制选项
 */
export interface PlayerCopyOptions extends BaseCopyOptions {
  /** 新的角色ID */
  newActorId?: number;
}

/**
 * 事件复制选项
 */
export interface EventCopyOptions extends BaseCopyOptions {
  /** 新的事件ID */
  newEventId?: number;
  /** 新的地图ID */
  newMapId?: number;
}

/**
 * 跟随者复制选项
 */
export interface FollowerCopyOptions extends BaseCopyOptions {
  /** 新的成员索引 */
  newMemberIndex?: number;
}

/**
 * 载具复制选项
 */
export interface VehicleCopyOptions extends BaseCopyOptions {
  /** 载具类型 */
  vehicleType?: 'boat' | 'ship' | 'airship';
}

/**
 * 角色复制选项
 */
export interface CharacterCopyOptions extends BaseCopyOptions {
  /** 角色图像名称 */
  characterName?: string;
  /** 角色图像索引 */
  characterIndex?: number;
}

/**
 * 统一复制选项
 */
export type CopyOptions = BaseCopyOptions & 
  PlayerCopyOptions & 
  EventCopyOptions & 
  FollowerCopyOptions & 
  VehicleCopyOptions & 
  CharacterCopyOptions;

/**
 * 对象类型枚举
 */
export enum ObjectType {
  GAME_PLAYER = 'Game_Player',
  GAME_EVENT = 'Game_Event',
  GAME_FOLLOWER = 'Game_Follower',
  GAME_VEHICLE = 'Game_Vehicle',
  GAME_CHARACTER = 'Game_Character',
  SPRITE_CHARACTER = 'Sprite_Character',
  UNKNOWN = 'Unknown'
}

/**
 * 复制结果接口
 */
export interface CopyResult {
  /** 复制成功 */
  success: boolean;
  /** 复制的对象 */
  copiedObject?: any;
  /** 错误信息 */
  error?: string;
  /** 原始对象类型 */
  sourceType?: ObjectType;
  /** 复制的对象类型 */
  targetType?: ObjectType;
}

/**
 * 对象结构信息
 */
export interface ObjectStructure {
  /** 是否为包装结构 */
  isWrapper: boolean;
  /** 显示对象 */
  displayObject?: any;
  /** 游戏对象 */
  gameObject?: any;
  /** 对象类型 */
  type?: string;
  /** 游戏对象类型 */
  gameType?: string;
  /** 显示名称 */
  displayName?: string;
}

/**
 * 默认复制选项
 */
export const DEFAULT_COPY_OPTIONS: Required<BaseCopyOptions> = {
  positionOffset: { x: 1, y: 1 },
  nameSuffix: '_copy',
  copyChildren: false
};
