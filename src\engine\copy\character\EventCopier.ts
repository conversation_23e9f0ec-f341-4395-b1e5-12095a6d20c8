/**
 * 事件对象复制器
 */

import type { EventCopyOptions, CopyResult } from './types';
import { DEFAULT_COPY_OPTIONS } from './types';
import { CopyUtils } from './CopyUtils';

/**
 * 事件复制器类
 */
export class EventCopier {
  
  /**
   * 复制事件对象
   * @param sourceEvent 源事件对象
   * @param options 复制选项
   * @returns 复制结果
   */
  static async copy(sourceEvent: any, options: EventCopyOptions = {}): Promise<CopyResult> {
    console.log('=== EventCopier: 开始复制事件对象 ===');
    console.log('源对象:', sourceEvent);
    console.log('复制选项:', options);

    try {
      // 合并默认选项
      const copyOptions = { 
        ...DEFAULT_COPY_OPTIONS, 
        ...options,
        newEventId: options.newEventId || Math.floor(Math.random() * 1000) + 100
      };

      // 等待资源加载
      const resourcesReady = await CopyUtils.waitForResources();
      if (!resourcesReady) {
        throw new Error('资源加载超时');
      }

      // 解析源对象结构
      const structure = CopyUtils.parseObjectStructure(sourceEvent);
      console.log('源对象结构:', structure);

      if (!structure.gameObject) {
        throw new Error('无法找到有效的游戏对象');
      }

      const sourceGameObject = structure.gameObject;
      const sourceSprite = structure.displayObject;

      // 验证是否为事件对象
      if (sourceGameObject.constructor?.name !== 'Game_Event') {
        throw new Error(`期望 Game_Event 对象，实际得到: ${sourceGameObject.constructor?.name}`);
      }

      console.log('源事件信息:', {
        eventId: sourceGameObject._eventId,
        mapId: sourceGameObject._mapId,
        characterName: sourceGameObject._characterName,
        characterIndex: sourceGameObject._characterIndex,
        direction: sourceGameObject._direction,
        x: sourceGameObject._x,
        y: sourceGameObject._y
      });

      // 获取源事件的页面数据
      let sourcePages = [];
      try {
        if (sourceGameObject.event && typeof sourceGameObject.event === 'function') {
          const eventData = sourceGameObject.event();
          if (eventData && eventData.pages) {
            sourcePages = JSON.parse(JSON.stringify(eventData.pages));
            console.log('复制事件页面数据:', sourcePages.length, '个页面');
          }
        }
      } catch (error) {
        console.warn('获取事件页面数据失败:', error);
      }

      // 动态导入创建器
      const { createEvent } = await import('../../creators/character/EventCreator');

      // 创建新的事件对象
      const newEvent = await createEvent({
        eventId: copyOptions.newEventId,
        mapId: options.newMapId || sourceGameObject._mapId || 1,
        characterName: sourceGameObject._characterName,
        characterIndex: sourceGameObject._characterIndex,
        direction: sourceGameObject._direction,
        x: sourceGameObject._x,
        y: sourceGameObject._y,
        pages: sourcePages.length > 0 ? sourcePages : undefined
      });

      if (!newEvent) {
        throw new Error('创建新事件对象失败');
      }

      // 应用位置偏移
      if (newEvent.gameObject && copyOptions.positionOffset) {
        CopyUtils.applyPositionOffset(newEvent.gameObject, copyOptions.positionOffset);
      }

      // 复制显示属性
      if (newEvent.displayObject && sourceSprite) {
        CopyUtils.copyDisplayProperties(sourceSprite, newEvent.displayObject);
      }

      // 更新显示名称
      CopyUtils.updateDisplayName(newEvent, copyOptions.nameSuffix);

      console.log('事件对象复制成功:', newEvent);

      return {
        success: true,
        copiedObject: newEvent,
        sourceType: CopyUtils.detectObjectType(sourceEvent),
        targetType: CopyUtils.detectObjectType(newEvent)
      };

    } catch (error) {
      console.error('EventCopier: 复制事件对象失败:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        sourceType: CopyUtils.detectObjectType(sourceEvent)
      };
    }
  }

  /**
   * 批量复制事件对象
   * @param sourceEvents 源事件对象数组
   * @param options 复制选项
   * @returns 复制结果数组
   */
  static async copyMultiple(
    sourceEvents: any[], 
    options: EventCopyOptions = {}
  ): Promise<CopyResult[]> {
    console.log(`=== EventCopier: 批量复制 ${sourceEvents.length} 个事件对象 ===`);

    const results: CopyResult[] = [];

    for (let i = 0; i < sourceEvents.length; i++) {
      const sourceEvent = sourceEvents[i];
      
      // 为每个对象生成不同的偏移和ID
      const eventOptions = {
        ...options,
        newEventId: (options.newEventId || 100) + i,
        positionOffset: {
          x: (options.positionOffset?.x || 1) + i,
          y: (options.positionOffset?.y || 1) + i
        }
      };

      const result = await this.copy(sourceEvent, eventOptions);
      results.push(result);

      // 如果复制失败，记录但继续处理其他对象
      if (!result.success) {
        console.warn(`第 ${i + 1} 个事件对象复制失败:`, result.error);
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`批量复制完成: ${successCount}/${sourceEvents.length} 成功`);

    return results;
  }

  /**
   * 验证事件对象是否可以复制
   * @param sourceEvent 源事件对象
   * @returns 是否可以复制
   */
  static canCopy(sourceEvent: any): boolean {
    try {
      const structure = CopyUtils.parseObjectStructure(sourceEvent);
      
      if (!structure.gameObject) {
        return false;
      }

      // 检查是否为事件对象
      const gameObjectType = structure.gameObject.constructor?.name;
      if (gameObjectType !== 'Game_Event') {
        return false;
      }

      // 检查必要属性
      const gameObject = structure.gameObject;
      return !!(
        gameObject._eventId !== undefined &&
        gameObject._mapId !== undefined &&
        gameObject._characterName &&
        gameObject._characterIndex !== undefined
      );

    } catch (error) {
      console.warn('EventCopier: 验证对象时出错:', error);
      return false;
    }
  }

  /**
   * 获取事件对象信息
   * @param sourceEvent 源事件对象
   * @returns 事件信息
   */
  static getEventInfo(sourceEvent: any): any {
    try {
      const structure = CopyUtils.parseObjectStructure(sourceEvent);
      
      if (!structure.gameObject) {
        return null;
      }

      const gameObject = structure.gameObject;
      
      return {
        type: 'Game_Event',
        eventId: gameObject._eventId,
        mapId: gameObject._mapId,
        characterName: gameObject._characterName,
        characterIndex: gameObject._characterIndex,
        direction: gameObject._direction,
        position: { x: gameObject._x, y: gameObject._y },
        isWrapper: structure.isWrapper,
        displayName: structure.displayName,
        hasPages: !!(gameObject.event && gameObject.event())
      };

    } catch (error) {
      console.warn('EventCopier: 获取事件信息时出错:', error);
      return null;
    }
  }
}
