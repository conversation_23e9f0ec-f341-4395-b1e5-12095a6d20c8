<script lang="ts">
  import TopPanel from './components/TopPanel.svelte';
  import LeftPanel from './components/LeftPanel.svelte';
  import RightPanel from './components/RightPanel.svelte';
</script>

<div class="app">
  <!-- 顶部面板 -->
  <div class="top-section">
    <TopPanel />
  </div>

  <!-- 主要内容区域 -->
  <div class="main-section">
    <!-- 左侧面板 -->
    <div class="left-section">
      <LeftPanel />
    </div>

    <!-- 中间内容区域 -->
    <div class="center-section">
      <div class="canvas-placeholder">
        <div class="placeholder-content">
          <div class="icon">🎨</div>
          <h3>画布区域</h3>
          <p>这里将显示标签精灵的预览画布</p>
          <p class="hint">选择左侧的元素来编辑属性</p>
        </div>
      </div>
    </div>

    <!-- 右侧面板 -->
    <div class="right-section">
      <RightPanel />
    </div>
  </div>
</div>
