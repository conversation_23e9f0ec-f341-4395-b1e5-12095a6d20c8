/**
 * Scene_Equip 创建器
 * 专门用于创建装备场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';
import { type MenuBaseSceneOptions } from './SceneMenuBaseCreator';

/**
 * 装备场景创建选项
 */
export interface EquipSceneOptions extends MenuBaseSceneOptions {
    /** 角色ID */
    actorId?: number;
    /** 初始选中的装备槽索引 */
    initialSlotIndex?: number;
}

/**
 * 创建装备场景
 * @param options 创建选项
 * @returns 创建的装备场景实例
 */
export async function createSceneEquip(options: EquipSceneOptions = {}): Promise<any> {
    console.log('=== 创建装备场景 Scene_Equip ===');
    
    try {
        // 预加载装备场景资源
        BaseSceneCreator.preloadSceneResources('Scene_Equip');
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Equip', defaultOptions);
        
        // Scene_Equip 特定的设置
        if (options.actorId !== undefined && scene._actor) {
            console.log('设置装备场景角色ID:', options.actorId);
        }
        
        if (options.initialSlotIndex !== undefined && scene._slotWindow) {
            scene._slotWindow.select(options.initialSlotIndex);
            console.log('设置初始装备槽索引:', options.initialSlotIndex);
        }
        
        console.log('Scene_Equip 创建完成');
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Equip 失败:', error);
        throw error;
    }
}

/**
 * 创建并启动装备场景
 * @param options 创建选项
 * @returns 创建的装备场景实例
 */
export async function createAndStartSceneEquip(options: EquipSceneOptions = {}): Promise<any> {
    return createSceneEquip({ ...options, autoStart: true });
}

/**
 * 创建简单的装备场景（用于测试）
 * @returns 创建的装备场景实例
 */
export async function createSimpleSceneEquip(): Promise<any> {
    return createSceneEquip({
        actorId: 1,
        initialSlotIndex: 0,
        backgroundType: 1,
        autoStart: false,
        addToStage: true
    });
}
