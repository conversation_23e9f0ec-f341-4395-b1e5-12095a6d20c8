import { BaseObjectModel } from '../baseObjectModel.svelte';

export class ListModel extends BaseObjectModel {

    constructor(list: any) {
        super(list);

        // 初始化列表特有属性
        this.dataSource = list.dataSource || [];
        this.selectionMode = list.selectionMode || 'single';
        this.itemHeight = list.itemHeight || 32;
        this.itemSpacing = list.itemSpacing || 5;
        this.virtualScrolling = list.virtualScrolling !== false;

        // 🔑 新的数据绑定属性
        this.itemTemplate = list.itemTemplate || null;
        this.itemInstances = list.itemInstances || [];

        // 行为属性
        this.enabled = list.enabled !== false;

        // 事件代码属性
        this.onSelectionChangeCode = list._eventCodes?.onSelectionChange || '';
        this.onItemClickCode = list._eventCodes?.onItemClick || '';
        this.onItemDoubleClickCode = list._eventCodes?.onItemDoubleClick || '';
        this.onItemHoverCode = list._eventCodes?.onItemHover || '';

        console.log('📋 ListModel: 创建列表模型', list);

        // 🆕 检查是否有默认创建的itemTemplate，如果有则添加到子对象中
        this.checkAndAddDefaultItemTemplate();

        // setupSync() 已经在基类构造函数中调用了
    }

    /**
     * 检查并添加默认的itemTemplate到子对象中
     */
    private checkAndAddDefaultItemTemplate(): void {
        const originalList = this.getOriginalObject();
        if (!originalList) return;

        // 检查原始对象是否有itemTemplate
        if (originalList.itemTemplate && originalList.itemTemplate.uiComponentType === 'UIItem') {
            console.log('📋 ListModel: 发现默认itemTemplate，添加到子对象');

            // 检查是否已经在子对象中
            const existingTemplate = this.children.find(child =>
                child.getOriginalObject() === originalList.itemTemplate
            );

            if (!existingTemplate) {
                // 添加itemTemplate到子对象中
                try {
                    this.addChild(originalList.itemTemplate);
                    console.log('✅ ListModel: 默认itemTemplate已添加到子对象');
                } catch (error) {
                    console.warn('⚠️ ListModel: 添加默认itemTemplate失败:', error);
                }
            }
        }
    }

    // 列表数据属性
    dataSource = $state([]);                    // 数据源
    selectionMode = $state('single');           // 选择模式: single, multiple, none
    itemHeight = $state(32);                    // 项目高度
    itemSpacing = $state(5);                    // 项目间距
    virtualScrolling = $state(true);            // 虚拟滚动

    // 🔑 新的数据绑定属性
    itemTemplate = $state<any>(null);           // UIItem 模板
    itemInstances = $state<any[]>([]);          // 实例化的 UIItem 数组

    // 行为属性
    enabled = $state(true);                     // 是否启用

    // 事件代码属性
    onSelectionChangeCode = $state('');         // 选择变化事件代码
    onItemClickCode = $state('');               // 项目点击事件代码
    onItemDoubleClickCode = $state('');         // 项目双击事件代码
    onItemHoverCode = $state('');               // 项目悬停事件代码

    /**
     * 设置List特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理List特有的属性
     */
    protected setupSpecificSync(): void {
        // 同步列表特有属性
        this._originalObject.dataSource = this.dataSource;
        this._originalObject.selectionMode = this.selectionMode;

        // 🔑 检查布局相关属性是否变化，如果变化则触发重新布局
        const itemHeightChanged = this._originalObject.itemHeight !== this.itemHeight;
        const itemSpacingChanged = this._originalObject.itemSpacing !== this.itemSpacing;

        this._originalObject.itemHeight = this.itemHeight;
        this._originalObject.itemSpacing = this.itemSpacing;
        this._originalObject.virtualScrolling = this.virtualScrolling;

        // 🔑 如果间距变化，调用updateLayout方法
        if (itemSpacingChanged && this._originalObject.updateLayout) {
            console.log('🔄 ListModel: itemSpacing变化，触发布局更新');
            this._originalObject.updateLayout();
        }

        // 🔑 如果项目高度变化，需要重新渲染
        if (itemHeightChanged && this._originalObject.refreshItems) {
            console.log('🔄 ListModel: itemHeight变化，触发重新渲染');
            this._originalObject.refreshItems();
        }

        // 🔑 同步子组件绑定属性 - 调用绑定方法而不是直接设置属性
        if (this.boundBackgroundSprite && this._originalObject.bindBackgroundSprite) {
            this._originalObject.bindBackgroundSprite(this.boundBackgroundSprite);
        } else {
            this._originalObject.boundBackgroundSprite = this.boundBackgroundSprite;
        }

        if (this.boundBorderSprite && this._originalObject.bindBorderSprite) {
            this._originalObject.bindBorderSprite(this.boundBorderSprite);
        } else {
            this._originalObject.boundBorderSprite = this.boundBorderSprite;
        }

        if (this.boundScrollbarSprite && this._originalObject.bindScrollbarSprite) {
            this._originalObject.bindScrollbarSprite(this.boundScrollbarSprite);
        } else {
            this._originalObject.boundScrollbarSprite = this.boundScrollbarSprite;
        }

        // 同步行为属性
        if (this._originalObject.setEnabled && typeof this._originalObject.setEnabled === 'function') {
            this._originalObject.setEnabled(this.enabled);
        } else {
            this._originalObject.enabled = this.enabled;
        }

        // 同步事件代码属性
        if (!this._originalObject._eventCodes) {
            this._originalObject._eventCodes = {};
        }
        this._originalObject._eventCodes.onSelectionChange = this.onSelectionChangeCode;
        this._originalObject._eventCodes.onItemClick = this.onItemClickCode;
        this._originalObject._eventCodes.onItemDoubleClick = this.onItemDoubleClickCode;
        this._originalObject._eventCodes.onItemHover = this.onItemHoverCode;

        // 如果数据源发生变化，更新列表
        if (this._originalObject.setDataSource && typeof this._originalObject.setDataSource === 'function') {
            this._originalObject.setDataSource(this.dataSource);
        }
    }

    /**
     * 设置数据源
     */
    public setDataSource(dataSource: any[]): void {
        this.dataSource = dataSource || [];
    }

    /**
     * 添加数据项
     */
    public addItem(item: any): void {
        this.dataSource = [...this.dataSource, item];
    }

    /**
     * 移除数据项
     */
    public removeItem(index: number): void {
        if (index >= 0 && index < this.dataSource.length) {
            this.dataSource = this.dataSource.filter((_, i) => i !== index);
        }
    }

    /**
     * 清空数据
     */
    public clearData(): void {
        this.dataSource = [];
    }

    /**
     * 获取数据项数量
     */
    public getItemCount(): number {
        return this.dataSource.length;
    }

    /**
     * 设置选择模式
     */
    public setSelectionMode(mode: 'single' | 'multiple' | 'none'): void {
        this.selectionMode = mode;
    }

    /**
     * 绑定数据源
     * @param dataSource RPG Maker MZ 数据数组
     */
    bindDataSource(dataSource: any[]): void {
        this.dataSource = dataSource;

        // 同步到原始对象
        const originalList = this.getOriginalObject();
        if (originalList && typeof originalList.bindDataSource === 'function') {
            originalList.bindDataSource(dataSource);
        }

        console.log('📊 ListModel: 绑定数据源', dataSource);
    }

    /**
     * 绑定 UIItem 模板
     * @param itemTemplate UIItem 模板对象
     */
    bindItemTemplate(itemTemplate: any): void {
        this.itemTemplate = itemTemplate;

        // 同步到原始对象
        const originalList = this.getOriginalObject();
        if (originalList && typeof originalList.bindItemTemplate === 'function') {
            originalList.bindItemTemplate(itemTemplate);
        }

        // 🆕 确保模板也在子对象列表中
        if (itemTemplate && !this.children.find(child => child.getOriginalObject() === itemTemplate)) {
            try {
                this.addChild(itemTemplate);
                console.log('📋 ListModel: UIItem模板已添加到子对象列表');
            } catch (error) {
                console.warn('⚠️ ListModel: 添加UIItem模板到子对象失败:', error);
            }
        }

        console.log('📋 ListModel: 绑定 UIItem 模板', itemTemplate);
    }

    /**
     * 刷新列表项
     */
    refreshItems(): void {
        // 同步到原始对象
        const originalList = this.getOriginalObject();
        if (originalList && typeof originalList.refreshItems === 'function') {
            // 先调用原始对象的刷新方法，创建显示对象
            originalList.refreshItems();

            // 然后将新创建的显示对象转换为模型对象
            this.syncItemInstancesToModels();
        }

        console.log('🔄 ListModel: 刷新列表项');
    }

    /**
     * 将显示对象的完整子对象树同步为模型对象
     * 🔑 利用BaseObjectModel的完整对象树处理机制
     */
    private syncItemInstancesToModels(): void {
        const originalList = this.getOriginalObject();
        if (!originalList) {
            return;
        }

        console.log('🔄 ListModel: 开始同步完整子对象树到模型对象');
        console.log('📊 原始对象子对象数量:', originalList.children?.length || 0);

        // 🔑 关键修改：利用BaseObjectModel的完整机制重新同步整个子对象树
        // 这会自动处理所有子对象（包括itemContainer、itemTemplate、itemInstances等）
        this.children = this.createChildrenModels(originalList.children || []);

        console.log('✅ ListModel: 完整子对象树同步完成，模型子对象数量:', this.children.length);

        // 🔍 调试：显示子对象类型分布
        const childTypes = this.children.map(child => child.className);
        console.log('📋 子对象类型分布:', childTypes);
    }

    /**
     * 获取绑定状态信息
     */
    public getBindingInfo(): string {
        const info: string[] = [];

        if (this.dataSource.length > 0) info.push(`数据源: ${this.dataSource.length} 项`);
        if (this.itemTemplate) info.push('模板: 已设置');
        if (this.itemInstances.length > 0) info.push(`实例: ${this.itemInstances.length} 个`);

        return info.length > 0 ? info.join(', ') : '未绑定数据';
    }

    /**
     * 获取事件配置信息
     */
    public getEventInfo(): string {
        const events: string[] = [];

        if (this.onSelectionChangeCode) events.push('选择变化');
        if (this.onItemClickCode) events.push('项目点击');
        if (this.onItemDoubleClickCode) events.push('项目双击');
        if (this.onItemHoverCode) events.push('项目悬停');

        return events.length > 0 ? `已配置: ${events.join(', ')}` : '未配置事件';
    }

    /**
     * 重写对象创建代码生成
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const properties: string[] = [];

        // 基础属性
        properties.push(`x: ${this.x}`);
        properties.push(`y: ${this.y}`);
        properties.push(`width: ${this.width}`);
        properties.push(`height: ${this.height}`);
        properties.push(`enabled: ${this.enabled}`);

        // 列表特有属性
        if (this.dataSource.length > 0) {
            properties.push(`dataSource: ${JSON.stringify(this.dataSource)}`);
        }
        properties.push(`selectionMode: '${this.selectionMode}'`);
        properties.push(`itemHeight: ${this.itemHeight}`);
        properties.push(`itemSpacing: ${this.itemSpacing}`);
        properties.push(`virtualScrolling: ${this.virtualScrolling}`);

        return `${indent}const ${varName} = new UIList({\n${indent}    ${properties.join(',\n' + indent + '    ')}\n${indent}});`;
    }

    /**
     * 重写特定属性设置代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns List特定属性设置代码
     */
    protected generateSpecificProperties(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 不在这里生成绑定代码，因为子对象还没创建
        // 绑定代码将在 generateBindingCode() 中生成

        // 事件代码设置
        if (this.onSelectionChangeCode) {
            codes.push(`${indent}// 选择变化事件`);
            codes.push(`${indent}${varName}._eventCodes.onSelectionChange = '${this.onSelectionChangeCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onItemClickCode) {
            codes.push(`${indent}// 项目点击事件`);
            codes.push(`${indent}${varName}._eventCodes.onItemClick = '${this.onItemClickCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onItemDoubleClickCode) {
            codes.push(`${indent}// 项目双击事件`);
            codes.push(`${indent}${varName}._eventCodes.onItemDoubleClick = '${this.onItemDoubleClickCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onItemHoverCode) {
            codes.push(`${indent}// 项目悬停事件`);
            codes.push(`${indent}${varName}._eventCodes.onItemHover = '${this.onItemHoverCode.replace(/'/g, "\\'")}';`);
        }

        return codes.join('\n');
    }

    /**
     * 重写代码生成方法，确保正确的生成顺序
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 生成的代码字符串
     */
    public generateCreationCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        // 1. 对象创建代码
        codes.push(this.generateObjectCreation(varName, indent));

        // 2. 基础属性设置
        codes.push(this.generateBasicProperties(varName, indent));

        // 3. 特定属性设置（不包含绑定）
        codes.push(this.generateSpecificProperties(varName, indent));

        // 4. 子对象代码生成
        if (this.generateChildrenCode) {
            codes.push(this.generateChildrenCreation(varName, indent));
        }

        // 5. 🔑 绑定代码（在子对象创建之后）
        codes.push(this.generateBindingCode(varName, indent));

        return codes.filter(code => code.trim()).join('\n');
    }

    /**
     * 生成绑定代码（在子对象创建之后调用）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 绑定代码
     */
    protected generateBindingCode(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 辅助函数：查找绑定对象对应的子对象变量名
        const findChildVarName = (boundObject: any): string | null => {
            if (!boundObject) return null;

            // 在子对象中查找匹配的对象
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child.getOriginalObject() === boundObject) {
                    return `${varName}_child${i}`;
                }
            }
            return null;
        };

        // 🆕 特殊处理：绑定itemTemplate
        const originalList = this.getOriginalObject();
        if (originalList && originalList.itemTemplate) {
            const templateVarName = findChildVarName(originalList.itemTemplate);
            if (templateVarName) {
                codes.push(`${indent}// 绑定UIItem模板`);
                codes.push(`${indent}${varName}.bindItemTemplate(${templateVarName});`);
            }
        }

        return codes.join('\n');
    }

    /**
     * 重写子对象创建代码生成，特殊处理itemTemplate
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 子对象创建代码
     */
    protected generateChildrenCreation(varName: string, indent: string): string {
        if (this.children.length === 0) {
            return '';
        }

        const codes: string[] = [];
        codes.push(`${indent}// 添加子对象`);

        const originalList = this.getOriginalObject();

        this.children.forEach((child, index) => {
            const childVarName = `${varName}_child${index}`;
            const childOriginal = child.getOriginalObject();

            // 生成子对象创建代码
            codes.push(child.generateCreationCode(childVarName, indent));

            // 🔑 特殊处理：如果是itemTemplate，不直接添加到UIList
            // itemTemplate会通过bindItemTemplate方法绑定
            if (originalList && childOriginal === originalList.itemTemplate) {
                codes.push(`${indent}// itemTemplate将通过bindItemTemplate方法绑定`);
            } else {
                // 普通子对象直接添加
                codes.push(`${indent}${varName}.addChild(${childVarName});`);
            }
        });

        return codes.join('\n');
    }

    /**
     * 克隆当前List对象 - 调用插件的 clone 方法
     */
    clone(): ListModel {
        console.log('🔄 ListModel: 开始克隆List对象（调用插件方法）');

        // 1. 调用原始 UIList 对象的 clone 方法
        const originalUIList = this.getOriginalObject();
        if (!originalUIList || typeof originalUIList.clone !== 'function') {
            console.error('❌ ListModel: 原始对象没有 clone 方法');
            throw new Error('UIList 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUIList = originalUIList.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 ListModel 包装克隆的对象
        const clonedModel = new ListModel(clonedUIList);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ ListModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重新建立绑定关系
     * @param clonedUIList 克隆的UIList对象
     * @param clonedChildren 克隆的子对象数组
     */
    private rebuildBindings(clonedUIList: any, clonedChildren: any[]): void {
        console.log('🔗 ListModel: 重新建立绑定关系');

        // 找到原始绑定对象在子对象数组中的索引
        const findChildIndex = (boundObject: any): number => {
            if (!boundObject) return -1;
            for (let i = 0; i < this.children.length; i++) {
                if (this.children[i].getOriginalObject() === boundObject) {
                    return i;
                }
            }
            return -1;
        };

        // 重新绑定背景精灵
        if (this.boundBackgroundSprite) {
            const index = findChildIndex(this.boundBackgroundSprite);
            if (index >= 0 && index < clonedChildren.length) {
                const clonedSprite = clonedChildren[index].getOriginalObject();
                if (clonedUIList.bindBackgroundSprite) {
                    clonedUIList.bindBackgroundSprite(clonedSprite);
                    console.log('🔗 重新绑定背景精灵');
                }
            }
        }

        // 重新绑定边框精灵
        if (this.boundBorderSprite) {
            const index = findChildIndex(this.boundBorderSprite);
            if (index >= 0 && index < clonedChildren.length) {
                const clonedSprite = clonedChildren[index].getOriginalObject();
                if (clonedUIList.bindBorderSprite) {
                    clonedUIList.bindBorderSprite(clonedSprite);
                    console.log('🔗 重新绑定边框精灵');
                }
            }
        }

        // 重新绑定滚动条精灵
        if (this.boundScrollbarSprite) {
            const index = findChildIndex(this.boundScrollbarSprite);
            if (index >= 0 && index < clonedChildren.length) {
                const clonedSprite = clonedChildren[index].getOriginalObject();
                if (clonedUIList.bindScrollbarSprite) {
                    clonedUIList.bindScrollbarSprite(clonedSprite);
                    console.log('🔗 重新绑定滚动条精灵');
                }
            }
        }
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UIList 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UIList 特有属性
            dataSource: JSON.parse(JSON.stringify(this.dataSource)), // 深拷贝
            selectionMode: this.selectionMode,
            itemHeight: this.itemHeight,
            itemSpacing: this.itemSpacing,
            virtualScrolling: this.virtualScrolling,
            enabled: this.enabled,

            // 事件代码
            _eventCodes: {
                onSelectionChange: this.onSelectionChangeCode,
                onItemClick: this.onItemClickCode,
                onItemDoubleClick: this.onItemDoubleClickCode,
                onItemHover: this.onItemHoverCode
            }
        };
    }
}

// 注册ListModel到基类容器
BaseObjectModel.registerModel('UIList', ListModel);
BaseObjectModel.registerModel('List', ListModel);
