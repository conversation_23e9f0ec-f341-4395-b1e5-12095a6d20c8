/**
 * 角色复制管理器
 * 统一的角色对象复制入口点
 */

import type { CopyOptions, CopyResult, ObjectType } from './types';
import { CopyUtils } from './CopyUtils';
import { PlayerCopier } from './PlayerCopier';
import { EventCopier } from './EventCopier';
import { CharacterCopier } from './CharacterCopier';
import { FollowerCopier } from './FollowerCopier';
import { VehicleCopier } from './VehicleCopier';

/**
 * 角色复制管理器类
 */
export class CharacterCopyManager {
  
  /**
   * 复制角色对象（统一入口）
   * @param sourceObject 源对象
   * @param options 复制选项
   * @returns 复制结果
   */
  static async copyObject(sourceObject: any, options: CopyOptions = {}): Promise<CopyResult> {
    console.log('=== CharacterCopyManager: 开始复制对象 ===');
    console.log('源对象:', sourceObject);
    console.log('复制选项:', options);

    try {
      // 检测对象类型
      const objectType = CopyUtils.detectObjectType(sourceObject);
      console.log('检测到对象类型:', objectType);

      // 根据类型分发到对应的复制器
      switch (objectType) {
        case 'Game_Player':
          return await PlayerCopier.copy(sourceObject, options);
          
        case 'Game_Event':
          return await EventCopier.copy(sourceObject, options);
          
        case 'Game_Character':
          return await CharacterCopier.copy(sourceObject, options);
          
        case 'Game_Follower':
          return await FollowerCopier.copy(sourceObject, options);
          
        case 'Game_Vehicle':
          return await VehicleCopier.copy(sourceObject, options);
          
        case 'Sprite_Character':
          // 对于 Sprite_Character，需要进一步检测其关联的游戏对象类型
          return await this.copySpriteCharacter(sourceObject, options);
          
        default:
          throw new Error(`不支持复制的对象类型: ${objectType}`);
      }

    } catch (error) {
      console.error('CharacterCopyManager: 复制对象失败:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        sourceType: CopyUtils.detectObjectType(sourceObject)
      };
    }
  }

  /**
   * 复制 Sprite_Character 对象
   * @param sourceSprite 源精灵对象
   * @param options 复制选项
   * @returns 复制结果
   */
  private static async copySpriteCharacter(sourceSprite: any, options: CopyOptions = {}): Promise<CopyResult> {
    if (!sourceSprite._character) {
      throw new Error('Sprite_Character 对象缺少 _character 属性');
    }

    const character = sourceSprite._character;
    const characterType = character.constructor?.name;

    console.log('Sprite_Character 关联的游戏对象类型:', characterType);

    // 根据关联的游戏对象类型选择复制器
    switch (characterType) {
      case 'Game_Player':
        return await PlayerCopier.copy(sourceSprite, options);
        
      case 'Game_Event':
        return await EventCopier.copy(sourceSprite, options);
        
      case 'Game_Character':
        return await CharacterCopier.copy(sourceSprite, options);
        
      case 'Game_Follower':
        return await FollowerCopier.copy(sourceSprite, options);
        
      case 'Game_Vehicle':
        return await VehicleCopier.copy(sourceSprite, options);
        
      default:
        throw new Error(`不支持的 Sprite_Character 关联类型: ${characterType}`);
    }
  }

  /**
   * 批量复制对象
   * @param sourceObjects 源对象数组
   * @param options 复制选项
   * @returns 复制结果数组
   */
  static async copyMultiple(sourceObjects: any[], options: CopyOptions = {}): Promise<CopyResult[]> {
    console.log(`=== CharacterCopyManager: 批量复制 ${sourceObjects.length} 个对象 ===`);

    const results: CopyResult[] = [];

    for (let i = 0; i < sourceObjects.length; i++) {
      const sourceObject = sourceObjects[i];
      
      // 为每个对象生成不同的偏移
      const objectOptions = {
        ...options,
        positionOffset: {
          x: (options.positionOffset?.x || 1) + i,
          y: (options.positionOffset?.y || 1) + i
        }
      };

      const result = await this.copyObject(sourceObject, objectOptions);
      results.push(result);

      // 如果复制失败，记录但继续处理其他对象
      if (!result.success) {
        console.warn(`第 ${i + 1} 个对象复制失败:`, result.error);
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`批量复制完成: ${successCount}/${sourceObjects.length} 成功`);

    return results;
  }

  /**
   * 验证对象是否可以复制
   * @param sourceObject 源对象
   * @returns 是否可以复制
   */
  static canCopy(sourceObject: any): boolean {
    try {
      const objectType = CopyUtils.detectObjectType(sourceObject);
      
      switch (objectType) {
        case 'Game_Player':
          return PlayerCopier.canCopy(sourceObject);
          
        case 'Game_Event':
          return EventCopier.canCopy(sourceObject);
          
        case 'Game_Character':
          return CharacterCopier.canCopy(sourceObject);
          
        case 'Game_Follower':
          return FollowerCopier.canCopy(sourceObject);
          
        case 'Game_Vehicle':
          return VehicleCopier.canCopy(sourceObject);
          
        case 'Sprite_Character':
          // 检查关联的游戏对象
          if (sourceObject._character) {
            const characterType = sourceObject._character.constructor?.name;
            switch (characterType) {
              case 'Game_Player':
                return PlayerCopier.canCopy(sourceObject);
              case 'Game_Event':
                return EventCopier.canCopy(sourceObject);
              case 'Game_Character':
                return CharacterCopier.canCopy(sourceObject);
              case 'Game_Follower':
                return FollowerCopier.canCopy(sourceObject);
              case 'Game_Vehicle':
                return VehicleCopier.canCopy(sourceObject);
            }
          }
          return false;
          
        default:
          return false;
      }

    } catch (error) {
      console.warn('CharacterCopyManager: 验证对象时出错:', error);
      return false;
    }
  }

  /**
   * 获取对象信息
   * @param sourceObject 源对象
   * @returns 对象信息
   */
  static getObjectInfo(sourceObject: any): any {
    try {
      const objectType = CopyUtils.detectObjectType(sourceObject);
      
      switch (objectType) {
        case 'Game_Player':
          return PlayerCopier.getPlayerInfo(sourceObject);
          
        case 'Game_Event':
          return EventCopier.getEventInfo(sourceObject);
          
        case 'Game_Character':
          return CharacterCopier.getCharacterInfo(sourceObject);
          
        case 'Game_Follower':
          return FollowerCopier.getFollowerInfo(sourceObject);
          
        case 'Game_Vehicle':
          return VehicleCopier.getVehicleInfo(sourceObject);
          
        case 'Sprite_Character':
          // 根据关联的游戏对象类型获取信息
          if (sourceObject._character) {
            const characterType = sourceObject._character.constructor?.name;
            switch (characterType) {
              case 'Game_Player':
                return PlayerCopier.getPlayerInfo(sourceObject);
              case 'Game_Event':
                return EventCopier.getEventInfo(sourceObject);
              case 'Game_Character':
                return CharacterCopier.getCharacterInfo(sourceObject);
              case 'Game_Follower':
                return FollowerCopier.getFollowerInfo(sourceObject);
              case 'Game_Vehicle':
                return VehicleCopier.getVehicleInfo(sourceObject);
            }
          }
          return null;
          
        default:
          return null;
      }

    } catch (error) {
      console.warn('CharacterCopyManager: 获取对象信息时出错:', error);
      return null;
    }
  }

  /**
   * 获取支持的对象类型列表
   * @returns 支持的对象类型数组
   */
  static getSupportedTypes(): ObjectType[] {
    return [
      'Game_Player',
      'Game_Event',
      'Game_Character',
      'Game_Follower',
      'Game_Vehicle',
      'Sprite_Character'
    ];
  }
}
