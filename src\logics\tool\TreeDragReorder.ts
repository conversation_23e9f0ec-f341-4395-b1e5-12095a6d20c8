/**
 * 树形拖拽重排工具 - Unity风格的拖拽重排实现
 */

import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';

/**
 * 拖拽位置类型
 */
export type DropPosition = 'before' | 'after' | 'on-node' | 'none';

/**
 * 拖拽结果
 */
export interface DragResult {
  success: boolean;
  operation: 'move-to-child' | 'reorder' | 'invalid';
  message?: string;
}

/**
 * 拖拽目标信息
 */
export interface DropTargetInfo {
  targetNode: BaseObjectModel;
  position: DropPosition;
  canDrop: boolean;
  reason?: string;
}

/**
 * 树形拖拽重排工具类
 */
export class TreeDragReorder {
  private static instance: TreeDragReorder | null = null;
  private rootScene: BaseObjectModel | null = null;

  static getInstance(): TreeDragReorder {
    if (!this.instance) {
      this.instance = new TreeDragReorder();
    }
    return this.instance;
  }

  /**
   * 设置根场景
   */
  setRootScene(rootScene: BaseObjectModel): void {
    this.rootScene = rootScene;
  }

  /**
   * 计算拖拽释放位置
   * @param targetElement 目标DOM元素
   * @param mouseY 鼠标Y坐标
   * @returns 释放位置类型
   */
  calculateDropPosition(targetElement: Element, mouseY: number): DropPosition {
    const rect = targetElement.getBoundingClientRect();
    const nodeHeight = rect.height;
    const relativeY = mouseY - rect.top;

    // Unity风格：分成三个区域
    // 上25%: before, 中50%: on-node, 下25%: after
    let position: DropPosition;
    if (relativeY < nodeHeight * 0.25) {
      position = 'before';
    } else if (relativeY > nodeHeight * 0.75) {
      position = 'after';
    } else {
      position = 'on-node';
    }

    console.log('🎯 TreeDragReorder: 计算位置', {
      mouseY,
      rectTop: rect.top,
      relativeY,
      nodeHeight,
      position
    });

    return position;
  }

  /**
   * 检查是否可以拖拽到目标位置
   * @param draggedNode 被拖拽的节点
   * @param targetNode 目标节点
   * @param position 拖拽位置
   * @returns 检查结果
   */
  canDropOn(
    draggedNode: BaseObjectModel,
    targetNode: BaseObjectModel,
    position: DropPosition
  ): { canDrop: boolean; reason?: string } {
    // 1. 不能拖拽到自己
    if (draggedNode === targetNode) {
      return { canDrop: false, reason: '不能拖拽到自己' };
    }

    // 2. 防止循环引用：不能将父节点拖拽到子节点中
    if (position === 'on-node' && this.isDescendant(draggedNode, targetNode)) {
      return { canDrop: false, reason: '不能将父节点拖拽到自己的子节点中' };
    }

    // 3. 对于 before/after 位置，也要检查目标节点的父对象
    if ((position === 'before' || position === 'after')) {
      const targetParent = this.findParent(targetNode);

      // 检查是否是根节点（没有父对象）
      if (!targetParent) {
        return { canDrop: false, reason: '不能拖拽到根节点的同级位置' };
      }

      // 检查循环引用
      if (this.isDescendant(draggedNode, targetParent)) {
        return { canDrop: false, reason: '不能将父节点拖拽到自己的子节点同级' };
      }
    }

    // 4. 检查是否已经在正确位置（避免无意义操作）
    if (this.isAlreadyInPosition(draggedNode, targetNode, position)) {
      return { canDrop: false, reason: '已经在目标位置' };
    }

    return { canDrop: true };
  }

  /**
   * 检查节点A是否是节点B的祖先（或者B是A的后代）
   * @param ancestor 祖先节点
   * @param descendant 后代节点
   * @returns true 如果 descendant 是 ancestor 的后代
   */
  private isDescendant(ancestor: BaseObjectModel, descendant: BaseObjectModel): boolean {
    // 如果是同一个节点，也算是后代关系（防止拖拽到自己）
    if (descendant === ancestor) return true;

    // 递归检查所有子节点
    for (const child of ancestor.children) {
      if (this.isDescendant(child, descendant)) return true;
    }
    return false;
  }

  /**
   * 检查节点是否已经在目标位置
   */
  private isAlreadyInPosition(
    draggedNode: BaseObjectModel,
    targetNode: BaseObjectModel,
    position: DropPosition
  ): boolean {
    const draggedParent = this.findParent(draggedNode);
    const targetParent = this.findParent(targetNode);

    if (position === 'on-node') {
      // 检查是否已经是目标节点的直接子节点
      return targetNode.children.includes(draggedNode);
    } else {
      // 检查是否已经在相邻位置
      if (draggedParent !== targetParent) return false;

      const siblings = draggedParent?.children || [];
      const draggedIndex = siblings.indexOf(draggedNode);
      const targetIndex = siblings.indexOf(targetNode);

      if (position === 'before') {
        return draggedIndex === targetIndex - 1;
      } else if (position === 'after') {
        return draggedIndex === targetIndex + 1;
      }
    }

    return false;
  }

  /**
   * 执行拖拽操作
   * @param draggedNode 被拖拽的节点
   * @param targetNode 目标节点
   * @param position 拖拽位置
   * @returns 操作结果
   */
  executeDrop(
    draggedNode: BaseObjectModel,
    targetNode: BaseObjectModel,
    position: DropPosition
  ): DragResult {
    // 1. 验证操作是否有效
    const validation = this.canDropOn(draggedNode, targetNode, position);
    if (!validation.canDrop) {
      return {
        success: false,
        operation: 'invalid',
        message: validation.reason
      };
    }

    try {
      // 2. 执行相应的操作（setParentAndIndex会处理移除和添加）
      if (position === 'on-node') {
        // 移动为子对象
        draggedNode.setParentAndIndex(targetNode);
        return {
          success: true,
          operation: 'move-to-child',
          message: `已将 ${draggedNode.className} 移动到 ${targetNode.className} 下`
        };
      } else {
        // 调整同级顺序
        const targetParent = this.findParent(targetNode);
        if (!targetParent) {
          throw new Error('找不到目标节点的父对象');
        }

        const targetIndex = targetParent.children.indexOf(targetNode);
        const insertIndex = position === 'before' ? targetIndex : targetIndex + 1;

        draggedNode.setParentAndIndex(targetParent, insertIndex);
        return {
          success: true,
          operation: 'reorder',
          message: `已调整 ${draggedNode.className} 的位置`
        };
      }
    } catch (error) {
      console.error('拖拽操作失败:', error);
      return {
        success: false,
        operation: 'invalid',
        message: '操作失败: ' + (error as Error).message
      };
    }
  }



  /**
   * 查找节点的父节点
   * 从根场景开始递归查找
   */
  private findParent(targetNode: BaseObjectModel): BaseObjectModel | null {
    if (!this.rootScene) {
      console.error('TreeDragReorder: 根场景未设置');
      return null;
    }
    return this.findParentInTree(this.rootScene, targetNode);
  }

  /**
   * 在指定树中查找父节点
   */
  private findParentInTree(parent: BaseObjectModel, targetNode: BaseObjectModel): BaseObjectModel | null {
    // 检查直接子节点
    if (parent.children.includes(targetNode)) {
      return parent;
    }

    // 递归查找子树
    for (const child of parent.children) {
      const found = this.findParentInTree(child, targetNode);
      if (found) {
        return found;
      }
    }

    return null;
  }





  /**
   * 获取拖拽目标信息
   */
  getDropTargetInfo(
    draggedNode: BaseObjectModel,
    targetElement: Element,
    targetNode: BaseObjectModel,
    mouseY: number
  ): DropTargetInfo {
    const position = this.calculateDropPosition(targetElement, mouseY);
    const validation = this.canDropOn(draggedNode, targetNode, position);

    return {
      targetNode,
      position,
      canDrop: validation.canDrop,
      reason: validation.reason
    };
  }
}

// 导出单例实例
export const treeDragReorder = TreeDragReorder.getInstance();
