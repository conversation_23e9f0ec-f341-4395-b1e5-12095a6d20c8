import { BaseObjectModel } from '../baseObjectModel.svelte';

export class SwitchModel extends BaseObjectModel {

    constructor(switchObj: any) {
        super(switchObj);

        // 初始化开关特有属性
        this.isOn = Boolean(switchObj.isOn || false);
        this.enabled = switchObj.enabled !== false;
        this.animationDuration = switchObj.animationDuration || 200;

        // 🔑 核心绑定组件（仅需2个）
        this.boundBackgroundSprite = switchObj.boundBackgroundSprite || null; // 背景轨道 (UIImage)
        this.boundKnobSprite = switchObj.boundKnobSprite || null;             // 滑块按钮 (UIImage)

        // 事件代码属性
        this.onChangeCode = switchObj._eventCodes?.onChange || '';
        this.onToggleCode = switchObj._eventCodes?.onToggle || '';

        console.log('🔧 SwitchModel: 创建开关模型', switchObj);

        // setupSync() 已经在基类构造函数中调用了
    }

    // 开关状态属性
    isOn = $state(false);                // 开关状态 (true/false)
    enabled = $state(true);              // 是否启用
    animationDuration = $state(200);     // 动画时长(ms)

    // 🔑 核心绑定组件（仅需2个）
    boundBackgroundSprite = $state(null); // 绑定的背景轨道精灵 (UIImage)
    boundKnobSprite = $state(null);       // 绑定的滑块按钮精灵 (UIImage)

    // 事件代码属性
    onChangeCode = $state('');           // 状态改变事件代码
    onToggleCode = $state('');           // 切换事件代码

    /**
     * 设置Switch特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Switch特有的属性
     */
    protected setupSpecificSync(): void {
        // 同步开关特有属性
        if (this._originalObject.setValue && typeof this._originalObject.setValue === 'function') {
            this._originalObject.setValue(this.isOn);
        } else {
            this._originalObject.isOn = this.isOn;
        }

        // 🔑 同步子组件绑定属性 - 调用绑定方法而不是直接设置属性
        // 这样可以触发 UISwitch 的尺寸计算和布局更新

        if (this.boundBackgroundSprite && this._originalObject.bindBackgroundSprite) {
            this._originalObject.bindBackgroundSprite(this.boundBackgroundSprite);
        } else {
            this._originalObject.boundBackgroundSprite = this.boundBackgroundSprite;
        }

        if (this.boundKnobSprite && this._originalObject.bindKnobSprite) {
            this._originalObject.bindKnobSprite(this.boundKnobSprite);
        } else {
            this._originalObject.boundKnobSprite = this.boundKnobSprite;
        }

        // 同步行为属性
        if (this._originalObject.setEnabled && typeof this._originalObject.setEnabled === 'function') {
            this._originalObject.setEnabled(this.enabled);
        } else {
            this._originalObject.enabled = this.enabled;
        }

        if (this._originalObject.setAnimationDuration && typeof this._originalObject.setAnimationDuration === 'function') {
            this._originalObject.setAnimationDuration(this.animationDuration);
        } else {
            this._originalObject.animationDuration = this.animationDuration;
        }

        // 同步事件代码属性
        if (!this._originalObject._eventCodes) {
            this._originalObject._eventCodes = {};
        }
        this._originalObject._eventCodes.onChange = this.onChangeCode;
        this._originalObject._eventCodes.onToggle = this.onToggleCode;
    }

    /**
     * 设置开关状态
     */
    public setValue(isOn: boolean): void {
        this.isOn = Boolean(isOn);
    }

    /**
     * 切换开关状态
     */
    public toggle(): void {
        this.setValue(!this.isOn);
    }

    /**
     * 获取开关状态
     */
    public getValue(): boolean {
        return this.isOn;
    }

    /**
     * 设置动画时长
     */
    public setAnimationDuration(duration: number): void {
        this.animationDuration = Math.max(0, duration);
    }

    /**
     * 获取绑定状态信息
     */
    public getBindingInfo(): {
        hasBackground: boolean;
        hasKnob: boolean;
        backgroundSprite?: any;
        knobSprite?: any;
    } {
        return {
            hasBackground: this.boundBackgroundSprite !== null,
            hasKnob: this.boundKnobSprite !== null,
            backgroundSprite: this.boundBackgroundSprite,
            knobSprite: this.boundKnobSprite
        };
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 创建UISwitch容器对象
        codes.push(`${indent}const ${varName} = new UISwitch({`);
        codes.push(`${indent}    width: ${this.width},`);
        codes.push(`${indent}    height: ${this.height},`);
        codes.push(`${indent}    isOn: ${this.isOn},`);
        codes.push(`${indent}    enabled: ${this.enabled},`);
        codes.push(`${indent}    animationDuration: ${this.animationDuration}`);
        codes.push(`${indent}});`);

        // 设置基础属性
        codes.push(`${indent}${varName}.x = ${this.x};`);
        codes.push(`${indent}${varName}.y = ${this.y};`);

        if (this.name) {
            codes.push(`${indent}${varName}.name = '${this.name}';`);
        }

        return codes.join('\n');
    }

    /**
     * 重写代码生成方法，确保正确的生成顺序
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 生成的代码字符串
     */
    public generateCreationCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        // 1. 对象创建代码
        codes.push(this.generateObjectCreation(varName, indent));

        // 2. 基础属性设置
        codes.push(this.generateBasicProperties(varName, indent));

        // 3. 特定属性设置（不包含绑定）
        codes.push(this.generateSpecificProperties(varName, indent));

        // 4. 子对象代码生成
        if (this.generateChildrenCode) {
            codes.push(this.generateChildrenCreation(varName, indent));
        }

        // 5. 🔑 绑定代码（在子对象创建之后）
        codes.push(this.generateBindingCode(varName, indent));

        return codes.filter(code => code.trim()).join('\n');
    }

    /**
     * 生成绑定代码（在子对象创建之后调用）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 绑定代码
     */
    public generateBindingCode(varName: string, indent: string): string {
        const codes: string[] = [];

        // 生成绑定代码
        if (this.boundBackgroundSprite) {
            const bgVarName = this.getChildVariableName(this.boundBackgroundSprite, varName);
            codes.push(`${indent}${varName}.bindBackgroundSprite(${bgVarName});`);
        }

        if (this.boundKnobSprite) {
            const knobVarName = this.getChildVariableName(this.boundKnobSprite, varName);
            codes.push(`${indent}${varName}.bindKnobSprite(${knobVarName});`);
        }

        // 生成事件代码 - 设置为字符串，与 UIButton 保持一致
        if (this.onChangeCode.trim()) {
            codes.push(`${indent}${varName}.onChangeCode = \`${this.onChangeCode}\`;`);
        }

        if (this.onToggleCode.trim()) {
            codes.push(`${indent}${varName}.onToggleCode = \`${this.onToggleCode}\`;`);
        }

        return codes.join('\n');
    }

    /**
     * 获取子对象的变量名
     */
    private getChildVariableName(childObject: any, parentVarName: string): string {
        // 在父对象的子对象中查找对应的变量名
        const childIndex = this.children.findIndex(child => child.getOriginalObject() === childObject);
        if (childIndex !== -1) {
            return `${parentVarName}_child${childIndex}`;
        }
        return 'unknownChild';
    }

    /**
     * 重写克隆方法 - 调用插件的 clone 方法
     */
    public clone(): SwitchModel {
        console.log('🔄 SwitchModel: 开始克隆Switch对象（调用插件方法）');

        // 1. 调用原始 UISwitch 对象的 clone 方法
        const originalUISwitch = this.getOriginalObject();
        if (!originalUISwitch || typeof originalUISwitch.clone !== 'function') {
            console.error('❌ SwitchModel: 原始对象没有 clone 方法');
            throw new Error('UISwitch 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUISwitch = originalUISwitch.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 SwitchModel 包装克隆的对象
        const clonedModel = new SwitchModel(clonedUISwitch);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ SwitchModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UISwitch 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UISwitch 特有属性
            isOn: this.isOn,
            enabled: this.enabled,
            animationDuration: this.animationDuration,

            // 事件代码
            onToggle: this.onToggleCode,
            onTurnOn: this.onChangeCode, // 复用 onChange 作为 onTurnOn
            onTurnOff: this.onChangeCode // 复用 onChange 作为 onTurnOff
        };
    }

    /**
     * 重写销毁方法
     */
    public destroy(): void {
        console.log(`🔧 SwitchModel: 销毁开关模型 ${this.className}`);

        // 清理绑定的组件引用
        this.boundBackgroundSprite = null;
        this.boundKnobSprite = null;

        // 调用基类销毁方法
        super.destroy();
    }
}

// 注册SwitchModel到基类容器
BaseObjectModel.registerModel('UISwitch', SwitchModel);
BaseObjectModel.registerModel('Switch', SwitchModel);
