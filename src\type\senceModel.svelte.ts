import { BaseObjectModel } from './baseObjectModel.svelte';

/**
 * 响应式场景模型类
 * 管理场景对象及其子对象的响应式状态
 */
export class SceneModel extends BaseObjectModel {

    constructor(scene: any) {
        super(scene);
        console.log('🔧 SceneModel: 创建场景模型', scene);
        // setupSync() 已经在基类构造函数中调用了
    }



    /**
     * 设置Scene特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Scene特有的属性
     */
    protected setupSpecificSync(): void {
        // 触发场景更新
        if (typeof this._originalObject.refresh === 'function') {
            this._originalObject.refresh();
        }

        console.log('🔧 SceneModel: 场景特有属性已同步');
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        return `${indent}const ${varName} = new Scene_Base();`;
    }

    /**
     * 手动同步属性到原始场景对象（向后兼容）
     * 现在主要用于外部调用或特殊情况
     */
    public syncToOriginalScene(): void {
        if (this._originalObject) {
            // 同步基本属性到原始场景对象
            this._originalObject.x = this.x;
            this._originalObject.y = this.y;
            this._originalObject.alpha = this.alpha;
            this._originalObject.visible = this.visible;

            // 触发场景更新
            if (typeof this._originalObject.refresh === 'function') {
                this._originalObject.refresh();
            }

            console.log('🔧 SceneModel: 场景属性已手动同步');
        }
    }

    /**
     * 获取原始场景对象
     */
    getOriginalScene(): any {
        return this._originalObject;
    }
}

// 注册SceneModel到基类容器
BaseObjectModel.registerModel('Scene', SceneModel);
BaseObjectModel.registerModel('Scene_Base', SceneModel);
BaseObjectModel.registerModel('Scene_Title', SceneModel);
BaseObjectModel.registerModel('Scene_Map', SceneModel);