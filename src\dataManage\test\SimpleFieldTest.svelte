<script lang="ts">
  import { DataManager } from '../managers/DataManager';
  import { FieldParser } from '../parsers/FieldParser';

  let testResult = $state<any>(null);
  let isLoading = $state(false);

  const dataManager = new DataManager();

  async function testFieldParsing() {
    isLoading = true;
    testResult = null;

    try {
      // 测试获取角色数据
      console.log('开始测试字段解析...');
      
      const dataTypeInfo = await dataManager.getDataTypeInfo('actors');
      console.log('数据类型信息:', dataTypeInfo);

      if (dataTypeInfo) {
        testResult = {
          dataType: 'actors',
          fieldsCount: dataTypeInfo.fields.length,
          fields: dataTypeInfo.fields,
          sampleData: dataTypeInfo.sampleData
        };
      } else {
        testResult = { error: '无法获取数据类型信息' };
      }
    } catch (error) {
      console.error('测试失败:', error);
      testResult = { error: error.toString() };
    } finally {
      isLoading = false;
    }
  }

  // 递归计算字段总数
  function countAllFields(fields: any[]): number {
    let count = fields.length;
    for (const field of fields) {
      if (field.children && field.children.length > 0) {
        count += countAllFields(field.children);
      }
    }
    return count;
  }

  // 查找有子字段的字段
  function findFieldsWithChildren(fields: any[]): any[] {
    const result = [];
    for (const field of fields) {
      if (field.children && field.children.length > 0) {
        result.push({
          name: field.name,
          displayName: field.displayName,
          childrenCount: field.children.length,
          children: field.children.map(c => ({ name: c.name, displayName: c.displayName }))
        });
      }
    }
    return result;
  }
</script>

<div class="test-container">
  <h2>字段解析测试</h2>
  
  <div class="test-section">
    <button 
      class="test-button"
      onclick={testFieldParsing}
      disabled={isLoading}
    >
      {isLoading ? '测试中...' : '测试角色数据字段解析'}
    </button>
  </div>

  {#if testResult}
    <div class="result-section">
      {#if testResult.error}
        <div class="error">
          <h3>错误</h3>
          <p>{testResult.error}</p>
        </div>
      {:else}
        <div class="success">
          <h3>测试结果</h3>
          <div class="result-item">
            <strong>数据类型:</strong> {testResult.dataType}
          </div>
          <div class="result-item">
            <strong>顶级字段数量:</strong> {testResult.fieldsCount}
          </div>
          <div class="result-item">
            <strong>总字段数量:</strong> {countAllFields(testResult.fields)}
          </div>
          
          {#if testResult.sampleData}
            <div class="sample-data">
              <h4>示例数据:</h4>
              <pre>{JSON.stringify(testResult.sampleData, null, 2)}</pre>
            </div>
          {/if}

          <div class="nested-fields">
            <h4>有子字段的字段:</h4>
            {#each findFieldsWithChildren(testResult.fields) as field}
              <div class="nested-field">
                <strong>{field.displayName} ({field.name})</strong> - {field.childrenCount} 个子字段
                <ul>
                  {#each field.children as child}
                    <li>{child.displayName} ({child.name})</li>
                  {/each}
                </ul>
              </div>
            {/each}
          </div>

          <div class="all-fields">
            <h4>所有字段:</h4>
            <pre>{JSON.stringify(testResult.fields, null, 2)}</pre>
          </div>
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .test-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .test-section {
    margin-bottom: 20px;
    text-align: center;
  }

  .test-button {
    padding: 12px 24px;
    background: #007acc;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .test-button:hover:not(:disabled) {
    background: #005a9e;
  }

  .test-button:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  .result-section {
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
  }

  .error {
    color: #d32f2f;
  }

  .success {
    color: #333;
  }

  .result-item {
    margin-bottom: 8px;
    padding: 4px 0;
  }

  .result-item strong {
    color: #495057;
    margin-right: 8px;
  }

  .sample-data, .nested-fields, .all-fields {
    margin-top: 16px;
  }

  .sample-data h4, .nested-fields h4, .all-fields h4 {
    margin-bottom: 8px;
    color: #333;
  }

  .sample-data pre, .all-fields pre {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    overflow-x: auto;
    font-size: 12px;
    max-height: 300px;
    overflow-y: auto;
  }

  .nested-field {
    margin-bottom: 12px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
  }

  .nested-field ul {
    margin: 8px 0 0 20px;
    padding: 0;
  }

  .nested-field li {
    margin-bottom: 4px;
    font-size: 14px;
    color: #666;
  }
</style>
