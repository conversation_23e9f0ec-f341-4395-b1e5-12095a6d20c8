/**
 * 工具集成器 - 与现有选中对象系统集成
 */

import { ObjectSelectionTool } from './ObjectSelectionTool';
import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';
import type { ToolConfig, ToolEventCallbacks, RenderContext } from './types';
import { CoordinateTransform } from './CoordinateTransform';
import { performanceMonitor } from './PerformanceMonitor';

// 导入现有的状态管理
import {
  sceneModelState,
  hasSelectedObjects,
  primarySelectedObjectInfo,
  selectObject as storeSelectObject,
  getCurrentState
} from '../../stores/sceneModelStore';

export class ToolIntegration {
  private selectionTool: ObjectSelectionTool;
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private animationFrameId: number | null = null;
  private isInitialized: boolean = false;

  // 事件监听器引用（用于清理）
  private eventListeners: Map<string, EventListener> = new Map();

  constructor(config?: Partial<ToolConfig>) {
    // 创建工具实例
    this.selectionTool = new ObjectSelectionTool(config, {
      onDragStart: this.handleDragStart.bind(this),
      onDragMove: this.handleDragMove.bind(this),
      onDragEnd: this.handleDragEnd.bind(this),
      onSelectionChange: this.handleSelectionChange.bind(this)
    });

    // 监听状态变化
    this.setupStoreSubscriptions();
  }

  /**
   * 初始化工具（绑定到Canvas）
   */
  initialize(canvas: HTMLCanvasElement): void {
    if (this.isInitialized) {
      this.destroy();
    }

    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');

    if (!this.ctx) {
      throw new Error('无法获取Canvas 2D上下文');
    }

    // 设置渲染上下文
    const renderContext: RenderContext = {
      canvas: this.canvas,
      ctx: this.ctx,
      width: canvas.width,
      height: canvas.height,
      scale: { x: 1, y: 1 },
      offset: { x: 0, y: 0 },
      rotation: 0
    };

    this.selectionTool.setRenderContext(renderContext);

    // 生产环境下禁用调试日志以提升性能
    // CoordinateTransform.enableDebugLog();

    // 启用性能监控（可选）
    // performanceMonitor.enable();

    // 绑定事件监听器
    this.setupEventListeners();

    // 开始渲染循环
    this.startRenderLoop();

    this.isInitialized = true;
    console.log('🎯 ObjectSelectionTool 已初始化', {
      renderContext: {
        scale: renderContext.scale,
        offset: renderContext.offset,
        rotation: renderContext.rotation
      }
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.canvas) return;

    // 鼠标事件
    const mouseDownHandler = (e: MouseEvent) => {
      const rect = this.canvas!.getBoundingClientRect();
      const mouseInfo = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
        button: e.button,
        ctrlKey: e.ctrlKey,
        shiftKey: e.shiftKey,
        altKey: e.altKey
      };

      this.selectionTool.onMouseDown(mouseInfo);
    };

    const mouseMoveHandler = (e: MouseEvent) => {
      const rect = this.canvas!.getBoundingClientRect();
      const mouseInfo = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
        button: e.button,
        ctrlKey: e.ctrlKey,
        shiftKey: e.shiftKey,
        altKey: e.altKey
      };

      this.selectionTool.onMouseMove(mouseInfo);
    };

    const mouseUpHandler = (e: MouseEvent) => {
      const rect = this.canvas!.getBoundingClientRect();
      const mouseInfo = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
        button: e.button,
        ctrlKey: e.ctrlKey,
        shiftKey: e.shiftKey,
        altKey: e.altKey
      };

      this.selectionTool.onMouseUp(mouseInfo);
    };

    // 键盘事件
    const keyDownHandler = (e: KeyboardEvent) => {
      this.selectionTool.onKeyDown(e);
    };

    // 绑定事件
    this.canvas.addEventListener('mousedown', mouseDownHandler);
    this.canvas.addEventListener('mousemove', mouseMoveHandler);
    this.canvas.addEventListener('mouseup', mouseUpHandler);
    document.addEventListener('keydown', keyDownHandler);

    // 保存引用用于清理
    this.eventListeners.set('mousedown', mouseDownHandler);
    this.eventListeners.set('mousemove', mouseMoveHandler);
    this.eventListeners.set('mouseup', mouseUpHandler);
    this.eventListeners.set('keydown', keyDownHandler);
  }

  /**
   * 设置状态订阅
   */
  private setupStoreSubscriptions(): void {
    // 监听选中对象变化
    sceneModelState.subscribe(state => {
      if (state.selectedObjects.length > 0 && state.primarySelectedIndex >= 0) {
        const primaryObject = state.selectedObjects[state.primarySelectedIndex];
        this.selectionTool.selectObject(primaryObject);
      } else {
        this.selectionTool.clearSelection();
      }
    });
  }

  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    const render = () => {
      if (!this.ctx || !this.canvas) return;

      // 清除画布，避免拖影效果
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

      // 渲染选中工具UI
      this.selectionTool.render(this.ctx);

      this.animationFrameId = requestAnimationFrame(render);
    };

    this.animationFrameId = requestAnimationFrame(render);
  }

  /**
   * 停止渲染循环
   */
  private stopRenderLoop(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * 拖动开始回调
   */
  private handleDragStart(object: BaseObjectModel, dragType: string): void {
    console.log('🎯 拖动开始:', object.className, dragType);
    // 可以在这里添加额外的逻辑，比如禁用其他交互
  }

  /**
   * 拖动移动回调
   */
  private handleDragMove(object: BaseObjectModel, deltaX: number, deltaY: number): void {
    // 对象位置已经在DragController中更新
    // 这里可以添加额外的逻辑，比如更新UI显示
  }

  /**
   * 拖动结束回调
   */
  private handleDragEnd(object: BaseObjectModel, finalX: number, finalY: number): void {
    console.log('🎯 拖动结束:', object.className, { x: finalX, y: finalY });
    // 可以在这里保存状态、触发更新等
  }

  /**
   * 选择变化回调
   */
  private handleSelectionChange(object: BaseObjectModel | null): void {
    console.log('🎯 选择变化:', object?.className || 'null');
    // 可以在这里同步到其他系统
  }

  /**
   * 手动选择对象（会更新全局状态）
   */
  selectObjectAndUpdateStore(object: BaseObjectModel | null): void {
    // 同步到状态管理
    storeSelectObject(object);
    // 工具会通过状态订阅自动更新
  }

  /**
   * 内部选择对象（仅更新工具状态，不影响store）
   */
  selectObject(object: BaseObjectModel | null): void {
    this.selectionTool.selectObject(object);
  }

  /**
   * 获取当前选中对象
   */
  getSelectedObject(): BaseObjectModel | null {
    return this.selectionTool.getSelectedObject();
  }

  /**
   * 启用/禁用工具
   */
  setEnabled(enabled: boolean): void {
    this.selectionTool.setEnabled(enabled);
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ToolConfig>): void {
    this.selectionTool.updateConfig(config);
  }

  /**
   * 启用性能监控
   */
  enablePerformanceMonitoring(): void {
    performanceMonitor.enable();
    console.log('📊 性能监控已启用');
  }

  /**
   * 禁用性能监控
   */
  disablePerformanceMonitoring(): void {
    performanceMonitor.disable();
    console.log('📊 性能监控已禁用');
  }

  /**
   * 打印性能报告
   */
  printPerformanceReport(): void {
    performanceMonitor.printReport();
  }

  /**
   * 清除性能指标
   */
  clearPerformanceMetrics(): void {
    performanceMonitor.clearMetrics();
  }

  /**
   * 销毁工具
   */
  destroy(): void {
    // 停止渲染
    this.stopRenderLoop();

    // 移除事件监听器
    if (this.canvas) {
      this.eventListeners.forEach((listener, event) => {
        if (event === 'keydown') {
          document.removeEventListener(event, listener);
        } else {
          this.canvas!.removeEventListener(event, listener);
        }
      });
    }

    this.eventListeners.clear();

    // 销毁工具
    this.selectionTool.destroy();

    // 清理引用
    this.canvas = null;
    this.ctx = null;
    this.isInitialized = false;

    console.log('🎯 ObjectSelectionTool 已销毁');
  }

  /**
   * 检查是否已初始化
   */
  isToolInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * 获取工具实例（用于高级操作）
   */
  getTool(): ObjectSelectionTool {
    return this.selectionTool;
  }
}
