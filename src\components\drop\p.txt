📁 src/
├── 📁 stores/
│   └── dragStore.ts                    # 全局拖拽状态管理
├── 📁 components/drop/
│   ├── DragSource.svelte              # 拖拽源组件（自定义实现）
│   ├── DropTarget.svelte              # 拖拽目标组件（自定义实现）
│   └── DragDropTest.svelte            # 测试组件
├── 📁 left/objectTree/
│   └── SimpleObjectTreeNode.svelte    # 对象树节点（集成拖拽源）
└── 📁 right/propertyPanel/panels/
    └── sliderPropertyPanel.svelte     # 滑块属性面板（集成拖拽目标）