/**
 * Scene_Menu 创建器
 * 专门用于创建主菜单场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';
import { type MenuBaseSceneOptions } from './SceneMenuBaseCreator';

declare global {
    interface Window {
        Window_MenuCommand: any;
        Window_MenuStatus: any;
        Window_Gold: any;
    }
}

/**
 * 主菜单场景创建选项
 */
export interface MenuSceneOptions extends MenuBaseSceneOptions {
    /** 初始选中的命令索引 */
    initialCommandIndex?: number;
    /** 自定义命令列表 */
    customCommands?: string[];
    /** 是否显示状态窗口 */
    showStatusWindow?: boolean;
}

/**
 * 创建主菜单场景
 * @param options 创建选项
 * @returns 创建的主菜单场景实例
 */
export async function createSceneMenu(options: MenuSceneOptions = {}): Promise<any> {
    console.log('=== 创建主菜单场景 Scene_Menu ===');
    
    try {
        // 预加载主菜单场景资源
        await preloadMenuResources(options);
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Menu', defaultOptions);
        
        // Scene_Menu 特定的设置
        setupMenuScene(scene, options);
        
        console.log('Scene_Menu 创建完成，场景属性:', {
            started: scene._started,
            active: scene._active,
            hasCommandWindow: !!scene._commandWindow,
            hasStatusWindow: !!scene._statusWindow,
            hasGoldWindow: !!scene._goldWindow
        });
        
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Menu 失败:', error);
        throw error;
    }
}

/**
 * 预加载主菜单场景资源
 * @param options 主菜单场景选项
 */
async function preloadMenuResources(options: MenuSceneOptions): Promise<void> {
    console.log('预加载主菜单场景资源...');
    
    if (!window.ImageManager) {
        console.warn('ImageManager 未加载，跳过主菜单资源预加载');
        return;
    }
    
    try {
        // 预加载菜单系统图片
        window.ImageManager.loadSystem('Window');
        window.ImageManager.loadSystem('IconSet');
        
        // 预加载角色头像和行走图
        if (window.$gameParty && window.$gameParty.allMembers) {
            const members = window.$gameParty.allMembers();
            members.forEach((actor: any) => {
                if (actor) {
                    // 预加载头像
                    if (actor.faceName) {
                        window.ImageManager.loadFace(actor.faceName);
                        console.log('预加载角色头像:', actor.faceName);
                    }
                    
                    // 预加载行走图
                    if (actor.characterName) {
                        window.ImageManager.loadCharacter(actor.characterName);
                        console.log('预加载角色行走图:', actor.characterName);
                    }
                }
            });
        }
        
        console.log('主菜单场景资源预加载完成');
        
    } catch (error) {
        console.error('预加载主菜单场景资源失败:', error);
        // 不抛出错误，允许场景创建继续
    }
}

/**
 * 设置主菜单场景
 * @param scene 场景实例
 * @param options 主菜单场景选项
 */
function setupMenuScene(scene: any, options: MenuSceneOptions): void {
    console.log('设置主菜单场景...');
    
    try {
        // 设置初始命令选择
        if (options.initialCommandIndex !== undefined && scene._commandWindow) {
            scene._commandWindow.select(options.initialCommandIndex);
            console.log('设置初始命令索引:', options.initialCommandIndex);
        }
        
        // 设置状态窗口显示
        if (options.showStatusWindow !== undefined && scene._statusWindow) {
            scene._statusWindow.visible = options.showStatusWindow;
            console.log('设置状态窗口显示:', options.showStatusWindow);
        }
        
        // 设置金钱窗口显示
        if (options.showGoldWindow !== undefined && scene._goldWindow) {
            scene._goldWindow.visible = options.showGoldWindow;
            console.log('设置金钱窗口显示:', options.showGoldWindow);
        }
        
        console.log('主菜单场景设置完成');
        
    } catch (error) {
        console.error('设置主菜单场景失败:', error);
    }
}

/**
 * 创建并启动主菜单场景
 * @param options 创建选项
 * @returns 创建的主菜单场景实例
 */
export async function createAndStartSceneMenu(options: MenuSceneOptions = {}): Promise<any> {
    console.log('=== 创建并启动 Scene_Menu ===');
    
    const scene = await createSceneMenu({
        ...options,
        autoStart: true
    });
    
    console.log('Scene_Menu 已创建并启动');
    return scene;
}

/**
 * 创建简单的主菜单场景（用于测试）
 * @returns 创建的主菜单场景实例
 */
export async function createSimpleSceneMenu(): Promise<any> {
    console.log('=== 创建简单主菜单场景 ===');
    
    try {
        const scene = await createSceneMenu({
            initialCommandIndex: 0,
            showStatusWindow: true,
            showGoldWindow: true,
            backgroundType: 1, // 暗化背景
            autoStart: false,
            addToStage: true
        });
        
        console.log('简单主菜单场景创建成功');
        return scene;
        
    } catch (error) {
        console.error('创建简单主菜单场景失败:', error);
        throw error;
    }
}
