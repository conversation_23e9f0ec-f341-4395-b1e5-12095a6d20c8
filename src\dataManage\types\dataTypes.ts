// RPG Maker MZ 静态数据类型定义

export enum StaticDataType {
  ACTORS = 'actors',
  CLASSES = 'classes',
  SKILLS = 'skills',
  ITEMS = 'items',
  WEAPONS = 'weapons',
  ARMORS = 'armors',
  ENEMIES = 'enemies',
  TROOPS = 'troops',
  STATES = 'states',
  ANIMATIONS = 'animations',
  TILESETS = 'tilesets',
  COMMON_EVENTS = 'commonEvents',
  SYSTEM = 'system',
  MAP_INFOS = 'mapInfos'
}

// 数据类型映射到全局变量名
export const DATA_TYPE_TO_GLOBAL: Record<StaticDataType, string> = {
  [StaticDataType.ACTORS]: '$dataActors',
  [StaticDataType.CLASSES]: '$dataClasses',
  [StaticDataType.SKILLS]: '$dataSkills',
  [StaticDataType.ITEMS]: '$dataItems',
  [StaticDataType.WEAPONS]: '$dataWeapons',
  [StaticDataType.ARMORS]: '$dataArmors',
  [StaticDataType.ENEMIES]: '$dataEnemies',
  [StaticDataType.TROOPS]: '$dataTroops',
  [StaticDataType.STATES]: '$dataStates',
  [StaticDataType.ANIMATIONS]: '$dataAnimations',
  [StaticDataType.TILESETS]: '$dataTilesets',
  [StaticDataType.COMMON_EVENTS]: '$dataCommonEvents',
  [StaticDataType.SYSTEM]: '$dataSystem',
  [StaticDataType.MAP_INFOS]: '$dataMapInfos'
};

// 数据类型的中文显示名称
export const DATA_TYPE_DISPLAY_NAMES: Record<StaticDataType, string> = {
  [StaticDataType.ACTORS]: '角色数据',
  [StaticDataType.CLASSES]: '职业数据',
  [StaticDataType.SKILLS]: '技能数据',
  [StaticDataType.ITEMS]: '物品数据',
  [StaticDataType.WEAPONS]: '武器数据',
  [StaticDataType.ARMORS]: '防具数据',
  [StaticDataType.ENEMIES]: '敌人数据',
  [StaticDataType.TROOPS]: '敌群数据',
  [StaticDataType.STATES]: '状态数据',
  [StaticDataType.ANIMATIONS]: '动画数据',
  [StaticDataType.TILESETS]: '图块集数据',
  [StaticDataType.COMMON_EVENTS]: '公共事件数据',
  [StaticDataType.SYSTEM]: '系统数据',
  [StaticDataType.MAP_INFOS]: '地图信息数据'
};

// 字段类型枚举
export enum FieldType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  ARRAY = 'array',
  OBJECT = 'object',
  NULL = 'null'
}

// 字段信息接口
export interface FieldInfo {
  name: string;           // 字段名
  type: FieldType;        // 字段类型
  displayName: string;    // 显示名称
  description?: string;   // 字段描述
  isArray?: boolean;      // 是否为数组
  arrayItemType?: FieldType; // 数组元素类型
  children?: FieldInfo[]; // 子字段（对象类型）
}

// 数据类型信息接口
export interface DataTypeInfo {
  type: StaticDataType;
  globalVar: string;
  displayName: string;
  fields: FieldInfo[];
  sampleData?: any;
}

// 选择结果接口
export interface DataSelection {
  dataType: StaticDataType;
  fieldPath: string;      // 字段路径，如 "name" 或 "params.0" 或 "meta.note"
  fieldInfo: FieldInfo;
  displayText: string;    // 用于显示的文本
}

// 数据管理器配置
export interface DataManagerConfig {
  enableCache: boolean;
  maxCacheSize: number;
  autoRefresh: boolean;
}
