{"$schema": "https://schema.tauri.app/config/2", "productName": "rpgmakemzl", "version": "0.1.0", "identifier": "com.rpgmakemzl.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../build"}, "app": {"windows": [{"title": "rpgmakemzl", "width": 1200, "height": 800, "decorations": false, "transparent": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}