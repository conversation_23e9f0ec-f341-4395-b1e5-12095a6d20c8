/**
 * 调试UIList事件阻挡问题的测试脚本
 * 用于验证为什么UIButton在UIList内部无法接收点击事件
 */

(() => {
    'use strict';

    console.log('🔍 开始加载事件阻挡调试脚本');

    // 等待RPG Maker MZ完全加载
    setTimeout(() => {
        if (typeof PIXI === 'undefined' || typeof TouchInput === 'undefined') {
            console.error('❌ PIXI或TouchInput未加载，无法进行调试');
            return;
        }

        console.log('✅ 开始事件阻挡调试测试');

        // 创建测试场景
        function createTestScene() {
            console.log('🧪 创建测试场景');

            // 获取当前场景的容器
            const scene = SceneManager._scene;
            if (!scene) {
                console.error('❌ 无法获取当前场景');
                return;
            }

            // 创建测试UIList
            const testList = new UIList({
                x: 100,
                y: 100,
                width: 300,
                height: 200
            });
            testList.name = 'TestUIList';

            // 创建测试UIButton（在UIList外部）
            const buttonOutside = new UIButton({
                x: 50,
                y: 50,
                width: 100,
                height: 40
            });
            buttonOutside.name = 'ButtonOutside';

            // 创建测试UIButton（在UIList内部）
            const buttonInside = new UIButton({
                x: 50,
                y: 50,
                width: 100,
                height: 40
            });
            buttonInside.name = 'ButtonInside';

            // 添加事件监听器
            buttonOutside._eventCodes = {
                onClick: 'console.log("🎯 外部按钮被点击！");'
            };

            buttonInside._eventCodes = {
                onClick: 'console.log("🎯 内部按钮被点击！");'
            };

            // 将按钮添加到场景
            scene.addChild(buttonOutside);
            scene.addChild(testList);
            testList.addChild(buttonInside);

            console.log('✅ 测试场景创建完成', {
                buttonOutside: {
                    bounds: buttonOutside.getBounds(),
                    parent: buttonOutside.parent?.constructor.name
                },
                buttonInside: {
                    bounds: buttonInside.getBounds(),
                    parent: buttonInside.parent?.constructor.name
                },
                testList: {
                    bounds: testList.getBounds(),
                    interactive: testList.interactive,
                    interactiveChildren: testList.interactiveChildren
                }
            });

            // 添加全局调试函数
            window.debugEventBlocking = function() {
                console.log('🔍 开始事件阻挡调试');

                const touchX = TouchInput.x;
                const touchY = TouchInput.y;

                console.log('📍 当前触摸位置:', { x: touchX, y: touchY });

                // 检查外部按钮
                const outsideBounds = buttonOutside.getBounds();
                const outsideHit = touchX >= outsideBounds.x && touchX <= outsideBounds.x + outsideBounds.width &&
                                 touchY >= outsideBounds.y && touchY <= outsideBounds.y + outsideBounds.height;

                console.log('🔍 外部按钮检测:', {
                    bounds: outsideBounds,
                    hit: outsideHit,
                    isBeingTouched: buttonOutside.isBeingTouched ? buttonOutside.isBeingTouched() : 'no method'
                });

                // 检查内部按钮
                const insideBounds = buttonInside.getBounds();
                const insideHit = touchX >= insideBounds.x && touchX <= insideBounds.x + insideBounds.width &&
                                touchY >= insideBounds.y && touchY <= insideBounds.y + insideBounds.height;

                console.log('🔍 内部按钮检测:', {
                    bounds: insideBounds,
                    hit: insideHit,
                    isBeingTouched: buttonInside.isBeingTouched ? buttonInside.isBeingTouched() : 'no method'
                });

                // 检查UIList容器
                const listBounds = testList.getBounds();
                const listHit = touchX >= listBounds.x && touchX <= listBounds.x + listBounds.width &&
                              touchY >= listBounds.y && touchY <= listBounds.y + listBounds.height;

                console.log('🔍 UIList容器检测:', {
                    bounds: listBounds,
                    hit: listHit,
                    interactive: testList.interactive,
                    interactiveChildren: testList.interactiveChildren
                });

                // 🔍 新增：检查update方法是否被正确调用
                console.log('🔍 Update方法调用检测:', {
                    outsideButtonHasUpdate: typeof buttonOutside.update === 'function',
                    insideButtonHasUpdate: typeof buttonInside.update === 'function',
                    outsideButtonOriginalUpdate: !!buttonOutside._originalUpdate,
                    insideButtonOriginalUpdate: !!buttonInside._originalUpdate
                });

                // 🔍 新增：检查事件代码是否存在
                console.log('🔍 事件代码检测:', {
                    outsideButtonEvents: buttonOutside._eventCodes,
                    insideButtonEvents: buttonInside._eventCodes
                });

                // 🔍 新增：检查父子关系
                console.log('🔍 父子关系检测:', {
                    outsideButtonParent: buttonOutside.parent?.constructor.name,
                    insideButtonParent: buttonInside.parent?.constructor.name,
                    listChildren: testList.children.map(child => child.constructor.name),
                    listItemContainer: testList.itemContainer ? testList.itemContainer.children.map(child => child.constructor.name) : 'no itemContainer'
                });

                // 检查PIXI的interaction manager状态
                console.log('🔍 PIXI交互管理器状态:', {
                    hasInteractionManager: !!Graphics.app?.renderer?.plugins?.interaction,
                    interactionManagerEnabled: Graphics.app?.renderer?.plugins?.interaction?.autoPreventDefault,
                    pixiVersion: PIXI.VERSION
                });

                // 检查RPG Maker MZ的TouchInput状态
                console.log('🔍 TouchInput状态:', {
                    isPressed: TouchInput.isPressed(),
                    isTriggered: TouchInput.isTriggered(),
                    isReleased: TouchInput.isReleased(),
                    x: TouchInput.x,
                    y: TouchInput.y
                });
            };

            // 🔍 新增：强制触发内部按钮事件的测试函数
            window.forceInsideButtonClick = function() {
                console.log('🧪 强制触发内部按钮点击事件');
                if (buttonInside && typeof buttonInside.executeEvent === 'function') {
                    buttonInside.executeEvent('onClick');
                } else {
                    console.error('❌ 内部按钮没有executeEvent方法');
                }
            };

            // 🔍 新增：手动调用内部按钮的processButtonTouch方法
            window.manualProcessTouch = function() {
                console.log('🧪 手动调用内部按钮的processButtonTouch');
                if (buttonInside && typeof buttonInside.processButtonTouch === 'function') {
                    buttonInside.processButtonTouch();
                } else {
                    console.error('❌ 内部按钮没有processButtonTouch方法');
                }
            };

            console.log('✅ 调试函数已添加到window.debugEventBlocking');
            console.log('💡 使用方法：');
            console.log('  - debugEventBlocking() - 调试事件阻挡问题');
            console.log('  - forceInsideButtonClick() - 强制触发内部按钮事件');
            console.log('  - manualProcessTouch() - 手动调用内部按钮的触摸处理');

            // 🔍 新增：监听TouchInput事件，查看是否被拦截
            const originalOnMouseDown = TouchInput._onMouseDown;
            TouchInput._onMouseDown = function(event) {
                console.log('🖱️ TouchInput._onMouseDown被调用:', {
                    x: event.clientX,
                    y: event.clientY,
                    target: event.target.tagName,
                    button: event.button
                });
                return originalOnMouseDown.call(this, event);
            };

            const originalOnMouseUp = TouchInput._onMouseUp;
            TouchInput._onMouseUp = function(event) {
                console.log('🖱️ TouchInput._onMouseUp被调用:', {
                    x: event.clientX,
                    y: event.clientY,
                    target: event.target.tagName,
                    button: event.button
                });
                return originalOnMouseUp.call(this, event);
            };

            console.log('✅ TouchInput事件监听器已设置');
        }

        // 检查必要的类是否存在
        if (typeof UIList === 'undefined') {
            console.error('❌ UIList类未找到，请确保uiList.js已加载');
            return;
        }

        if (typeof UIButton === 'undefined') {
            console.error('❌ UIButton类未找到，请确保uiButton.js已加载');
            return;
        }

        // 等待场景加载完成后创建测试
        if (SceneManager._scene) {
            createTestScene();
        } else {
            // 监听场景变化
            const originalGoto = SceneManager.goto;
            SceneManager.goto = function(sceneClass) {
                const result = originalGoto.apply(this, arguments);
                setTimeout(() => {
                    if (this._scene) {
                        createTestScene();
                    }
                }, 1000);
                return result;
            };
        }

    }, 2000); // 延迟2秒确保所有插件加载完成

    console.log('✅ 事件阻挡调试脚本加载完成');

})();
