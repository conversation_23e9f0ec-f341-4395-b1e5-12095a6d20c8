/**
 * 调试UIList事件阻挡问题的测试脚本
 * 用于验证为什么UIButton在UIList内部无法接收点击事件
 */

(() => {
    'use strict';

    console.log('🔍 开始加载事件阻挡调试脚本');

    // 等待RPG Maker MZ完全加载
    setTimeout(() => {
        if (typeof PIXI === 'undefined' || typeof TouchInput === 'undefined') {
            console.error('❌ PIXI或TouchInput未加载，无法进行调试');
            return;
        }

        console.log('✅ 开始事件阻挡调试测试');

        // 创建测试场景
        function createTestScene() {
            console.log('🧪 创建测试场景');

            // 获取当前场景的容器
            const scene = SceneManager._scene;
            if (!scene) {
                console.error('❌ 无法获取当前场景');
                return;
            }

            // 创建测试UIList
            const testList = new UIList({
                x: 100,
                y: 100,
                width: 300,
                height: 200
            });
            testList.name = 'TestUIList';

            // 创建测试UIButton（在UIList外部）
            const buttonOutside = new UIButton({
                x: 50,
                y: 50,
                width: 100,
                height: 40
            });
            buttonOutside.name = 'ButtonOutside';

            // 创建测试UIButton（在UIList内部）
            const buttonInside = new UIButton({
                x: 50,
                y: 50,
                width: 100,
                height: 40
            });
            buttonInside.name = 'ButtonInside';

            // 添加事件监听器
            buttonOutside._eventCodes = {
                onClick: 'console.log("🎯 外部按钮被点击！");'
            };

            buttonInside._eventCodes = {
                onClick: 'console.log("🎯 内部按钮被点击！");'
            };

            // 将按钮添加到场景
            scene.addChild(buttonOutside);
            scene.addChild(testList);
            testList.addChild(buttonInside);

            console.log('✅ 测试场景创建完成', {
                buttonOutside: {
                    bounds: buttonOutside.getBounds(),
                    parent: buttonOutside.parent?.constructor.name
                },
                buttonInside: {
                    bounds: buttonInside.getBounds(),
                    parent: buttonInside.parent?.constructor.name
                },
                testList: {
                    bounds: testList.getBounds(),
                    interactive: testList.interactive,
                    interactiveChildren: testList.interactiveChildren
                }
            });

            // 添加全局调试函数
            window.debugEventBlocking = function() {
                console.log('🔍 开始事件阻挡调试');
                
                const touchX = TouchInput.x;
                const touchY = TouchInput.y;

                console.log('📍 当前触摸位置:', { x: touchX, y: touchY });

                // 检查外部按钮
                const outsideBounds = buttonOutside.getBounds();
                const outsideHit = touchX >= outsideBounds.x && touchX <= outsideBounds.x + outsideBounds.width &&
                                 touchY >= outsideBounds.y && touchY <= outsideBounds.y + outsideBounds.height;

                console.log('🔍 外部按钮检测:', {
                    bounds: outsideBounds,
                    hit: outsideHit,
                    isBeingTouched: buttonOutside.isBeingTouched ? buttonOutside.isBeingTouched() : 'no method'
                });

                // 检查内部按钮
                const insideBounds = buttonInside.getBounds();
                const insideHit = touchX >= insideBounds.x && touchX <= insideBounds.x + insideBounds.width &&
                                touchY >= insideBounds.y && touchY <= insideBounds.y + insideBounds.height;

                console.log('🔍 内部按钮检测:', {
                    bounds: insideBounds,
                    hit: insideHit,
                    isBeingTouched: buttonInside.isBeingTouched ? buttonInside.isBeingTouched() : 'no method'
                });

                // 检查UIList容器
                const listBounds = testList.getBounds();
                const listHit = touchX >= listBounds.x && touchX <= listBounds.x + listBounds.width &&
                              touchY >= listBounds.y && touchY <= listBounds.y + listBounds.height;

                console.log('🔍 UIList容器检测:', {
                    bounds: listBounds,
                    hit: listHit,
                    interactive: testList.interactive,
                    interactiveChildren: testList.interactiveChildren
                });

                // 检查PIXI的interaction manager状态
                console.log('🔍 PIXI交互管理器状态:', {
                    hasInteractionManager: !!Graphics.app?.renderer?.plugins?.interaction,
                    interactionManagerEnabled: Graphics.app?.renderer?.plugins?.interaction?.autoPreventDefault,
                    pixiVersion: PIXI.VERSION
                });

                // 检查RPG Maker MZ的TouchInput状态
                console.log('🔍 TouchInput状态:', {
                    isPressed: TouchInput.isPressed(),
                    isTriggered: TouchInput.isTriggered(),
                    isReleased: TouchInput.isReleased(),
                    x: TouchInput.x,
                    y: TouchInput.y
                });
            };

            console.log('✅ 调试函数已添加到window.debugEventBlocking');
            console.log('💡 使用方法：在控制台输入 debugEventBlocking() 来调试事件阻挡问题');
        }

        // 检查必要的类是否存在
        if (typeof UIList === 'undefined') {
            console.error('❌ UIList类未找到，请确保uiList.js已加载');
            return;
        }

        if (typeof UIButton === 'undefined') {
            console.error('❌ UIButton类未找到，请确保uiButton.js已加载');
            return;
        }

        // 等待场景加载完成后创建测试
        if (SceneManager._scene) {
            createTestScene();
        } else {
            // 监听场景变化
            const originalGoto = SceneManager.goto;
            SceneManager.goto = function(sceneClass) {
                const result = originalGoto.apply(this, arguments);
                setTimeout(() => {
                    if (this._scene) {
                        createTestScene();
                    }
                }, 1000);
                return result;
            };
        }

    }, 2000); // 延迟2秒确保所有插件加载完成

    console.log('✅ 事件阻挡调试脚本加载完成');

})();
