<script lang="ts">
  /**
   * 类继承关系图主面板
   */

  import { onMount } from 'svelte';
  import { BaseClassType, getRootClassesForType } from './tree';
  import ClassTreeView from './ClassTreeView.svelte';

  // 状态
  let selectedBaseType: BaseClassType = BaseClassType.GAME_OBJECTS;
  let selectedRootClass: string = '';
  let containerWidth: number = 800;
  let containerHeight: number = 500;
  let treeContainer: HTMLDivElement;

  // 使用 ResizeObserver 来安全地获取容器尺寸
  function updateContainerSize() {
    if (treeContainer) {
      const rect = treeContainer.getBoundingClientRect();
      containerWidth = Math.max(rect.width, 400);
      containerHeight = Math.max(rect.height, 300);
      console.log('容器尺寸更新:', containerWidth, 'x', containerHeight);
    }
  }

  // 组件挂载后设置 ResizeObserver
  onMount(() => {
    if (treeContainer) {
      updateContainerSize();

      const resizeObserver = new ResizeObserver(() => {
        updateContainerSize();
      });

      resizeObserver.observe(treeContainer);

      return () => {
        resizeObserver.disconnect();
      };
    }
  });

  // 基类型选项
  const baseTypeOptions = [
    { value: BaseClassType.GAME_OBJECTS, label: 'Game Objects (游戏对象)' },
    { value: BaseClassType.SCENES, label: 'Scenes (场景)' },
    { value: BaseClassType.WINDOWS, label: 'Windows (窗口)' },
    { value: BaseClassType.MANAGERS, label: 'Managers (管理器)' },
    { value: BaseClassType.CORE_CLASSES, label: 'Core Classes (核心类)' },
    { value: BaseClassType.INPUT_SYSTEM, label: 'Input System (输入系统)' },
    { value: BaseClassType.GRAPHICS, label: 'Graphics (图形)' },
    { value: BaseClassType.UTILITIES, label: 'Utilities (工具)' }
  ];

  // 响应式计算根类选项
  $: rootClassOptions = getRootClassesForType(selectedBaseType).map(className => ({
    value: className,
    label: className
  }));

  // 当基类型改变时，自动选择第一个根类
  $: {
    if (selectedBaseType) {
      const rootClasses = getRootClassesForType(selectedBaseType);
      if (rootClasses.length > 0) {
        selectedRootClass = rootClasses[0];
      }
    }
  }



  /**
   * 处理节点点击
   */
  async function handleNodeClick(event: { className: string; node: any }) {
    const { className } = event;
    console.log(`点击了类节点: ${className}`);

    try {
      // 调用统一的对象创建方法
      const { createObjectByClassName } = await import('../../engine/creators/index');

      console.log(`尝试创建对象: ${className}`);
      const createdObject = await createObjectByClassName(className, {
        // 可以根据需要传递不同的选项
        characterName: 'Actor1',
        characterIndex: 0,
        direction: 2
      });

      if (createdObject) {
        console.log(`成功创建 ${className} 对象:`, createdObject);
      } else {
        console.log(`${className} 类型暂不支持创建`);
      }
    } catch (error) {
      console.error(`创建 ${className} 对象失败:`, error);
    }
  }

  /**
   * 重置视图
   */
  function resetView() {
    selectedBaseType = BaseClassType.GAME_OBJECTS;
    console.log('重置视图');
  }

  /**
   * 导出图片
   */
  function exportImage() {
    console.log('导出继承关系图为图片');
    // TODO: 实现导出功能
  }
</script>

<div class="class-tree-panel">
  <!-- 控制面板 -->
  <div class="control-panel">
    <!-- 基类型选择 -->
    <div class="control-group">
      <label for="base-type-select">基类型:</label>
      <select
        id="base-type-select"
        bind:value={selectedBaseType}
      >
        {#each baseTypeOptions as option}
          <option value={option.value}>{option.label}</option>
        {/each}
      </select>
    </div>

    <!-- 根类选择 -->
    {#if rootClassOptions.length > 1}
      <div class="control-group">
        <label for="root-class-select">根类:</label>
        <select
          id="root-class-select"
          bind:value={selectedRootClass}
        >
          {#each rootClassOptions as option}
            <option value={option.value}>{option.label}</option>
          {/each}
        </select>
      </div>
    {/if}

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <button
        class="btn btn-secondary"
        onclick={resetView}
        title="重置视图"
      >
        🔄 重置
      </button>

      <button
        class="btn btn-secondary"
        onclick={exportImage}
        title="导出图片"
      >
        📷 导出
      </button>
    </div>
  </div>

  <!-- 继承关系图 -->
  <div class="tree-container" bind:this={treeContainer}>
    {#if selectedRootClass}
      <ClassTreeView
        rootClassName={selectedRootClass}
        onnodeclick={handleNodeClick}
      />
    {:else}
      <div class="empty-state">
        <p>请选择一个基类型来查看继承关系图</p>
      </div>
    {/if}
  </div>

  <!-- 信息面板 -->
  <div class="info-panel">
    <div class="info-item">
      <span class="info-label">当前基类型:</span>
      <span class="info-value">{selectedBaseType}</span>
    </div>
    {#if selectedRootClass}
      <div class="info-item">
        <span class="info-label">根类:</span>
        <span class="info-value">{selectedRootClass}</span>
      </div>
    {/if}
  </div>
</div>

<style>
  .class-tree-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-background);
  }

  .control-panel {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    padding: var(--spacing-3);
    background: var(--theme-surface);
    border-bottom: 1px solid var(--theme-border);
    flex-wrap: nowrap;
    overflow-x: auto;
  }

  .control-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    white-space: nowrap;
    flex-shrink: 0;
  }

  .control-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--theme-text);
    margin: 0;
  }

  .control-group select {
    padding: var(--spacing-1) var(--spacing-2);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    background: var(--theme-background);
    color: var(--theme-text);
    font-size: var(--font-size-sm);
    min-width: 120px;
  }

  .control-group select:focus {
    outline: none;
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px var(--theme-primary-alpha);
  }

  .action-buttons {
    display: flex;
    gap: var(--spacing-2);
    margin-left: auto;
    flex-shrink: 0;
  }

  .btn {
    padding: var(--spacing-1) var(--spacing-3);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    background: var(--theme-surface);
    color: var(--theme-text);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition-base);
    white-space: nowrap;
  }

  .btn:hover {
    background: var(--theme-surface-light);
    border-color: var(--theme-primary);
  }

  .btn:active {
    transform: translateY(1px);
  }

  .tree-container {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--theme-text-muted);
    font-style: italic;
  }

  .info-panel {
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--theme-surface);
    border-top: 1px solid var(--theme-border);
    display: flex;
    gap: var(--spacing-4);
    font-size: var(--font-size-sm);
  }

  .info-item {
    display: flex;
    gap: var(--spacing-1);
  }

  .info-label {
    font-weight: 500;
    color: var(--theme-text-secondary);
  }

  .info-value {
    color: var(--theme-text);
    font-weight: 600;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .control-panel {
      padding: var(--spacing-2);
      gap: var(--spacing-2);
    }

    .control-group select {
      min-width: 100px;
      font-size: var(--font-size-xs);
    }

    .btn {
      padding: var(--spacing-1) var(--spacing-2);
      font-size: var(--font-size-xs);
    }

    .info-panel {
      flex-direction: column;
      gap: var(--spacing-2);
    }
  }
</style>