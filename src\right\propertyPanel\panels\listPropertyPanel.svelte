<script lang="ts">
  /**
   * 响应式列表属性面板
   * 直接使用响应式模型对象，无需额外的函数
   */

  import {
    sceneModelState
  } from '../../../stores/sceneModelStore';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import Select from '../../../components/Select.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import DropTarget from '../../../components/drop/DropTarget.svelte';
  import EventSelectionModal from '../../../modals/EventSelectionModal.svelte';
  import DataBindingModal from '../../../modals/dataModals/DataBindingModal.svelte';
  import type { ListModel } from '../../../type/ui/listModel.svelte';
  import type { BaseObjectModel } from '../../../type/baseObjectModel.svelte';

  // 监听选中的对象 - 使用第一个选中的对象
  let currentState = $derived($sceneModelState);
  let list = $derived(currentState.selectedObjects[0] as ListModel | null);

  // 面板展开状态
  let isExpanded = $state(true);
  let showDataBindingModal = $state(false);

  // 选择模式选项
  const selectionModeOptions = [
    { value: 'single', label: '单选' },
    { value: 'multiple', label: '多选' },
    { value: 'none', label: '无选择' }
  ];

  // 事件类型选项
  const eventTypes = [
    { value: 'onSelectionChange', label: '选择变化事件' },
    { value: 'onItemClick', label: '项目点击事件' },
    { value: 'onItemDoubleClick', label: '项目双击事件' },
    { value: 'onItemHover', label: '项目悬停事件' }
  ];

  // 事件选择相关状态
  let showEventModal = $state(false);
  let selectedEventType = $state('onSelectionChange');

  // 获取列表状态信息
  let listInfo = $derived(() => {
    if (!list) return '未选择列表';
    const itemCount = list.dataSource?.length || 0;
    const hasTemplate = !!list.itemTemplate;
    const instanceCount = list.itemInstances?.length || 0;

    return `${itemCount} 项数据，${hasTemplate ? '有' : '无'}模板，${instanceCount} 个实例`;
  });

  // 获取事件配置信息
  let eventInfo = $derived(() => {
    if (!list) return '未配置事件';
    const events = [];
    if (list.onSelectionChangeCode) events.push('选择变化');
    if (list.onItemClickCode) events.push('项目点击');
    if (list.onItemDoubleClickCode) events.push('项目双击');
    if (list.onItemHoverCode) events.push('项目悬停');

    return events.length > 0 ? `已配置: ${events.join(', ')}` : '未配置事件';
  });






  // 刷新列表项
  function refreshListItems() {
    if (list) {
      list.refreshItems();
      console.log('🔄 已刷新列表项');
    }
  }

  // 打开数据绑定模态框
  function openDataBindingModal() {
    showDataBindingModal = true;
  }



  // 处理数据绑定确认
  function handleDataBindingConfirm(expression: string) {
    if (list) {
      try {
        // 评估表达式获取数据
        const data = eval(expression);
        if (Array.isArray(data)) {
          // 确保数据是对象数组
          const validData = data.filter(item => item && typeof item === 'object');

          if (validData.length === 0) {
            console.warn('⚠️ 绑定的数据是空数组或不包含有效对象');
            alert('绑定的数据不包含有效对象，请选择其他数据源');
            return;
          }

          // 设置数据源
          list.setDataSource(validData);
          console.log('✅ 已绑定列表数据源:', expression);
          console.log('📊 数据项数量:', validData.length);
          console.log('📋 数据示例:', validData.slice(0, 3));
        } else {
          // 如果不是数组，包装成数组
          if (data && typeof data === 'object') {
            list.setDataSource([data]);
            console.log('✅ 已绑定单个对象:', expression, data);
          } else {
            console.warn('⚠️ 绑定的数据不是对象:', data);
            alert('绑定的数据必须是对象或对象数组');
            return;
          }
        }
      } catch (error) {
        console.error('❌ 数据绑定失败:', error);
        alert('数据绑定失败: ' + (error instanceof Error ? error.message : String(error)));
        return;
      }
    }
    showDataBindingModal = false;
  }

  // 清空数据源
  function clearDataSource() {
    if (list && confirm('确定要清空所有数据吗？')) {
      list.clearData();
      console.log('🗑️ 已清空数据源');
    }
  }



  // 事件选择相关函数
  function openEventModal(eventType: string) {
    selectedEventType = eventType;
    showEventModal = true;
  }

  function closeEventModal() {
    showEventModal = false;
  }

  // 处理事件选择
  function handleEventSelected(event: CustomEvent) {
    const selectedEvent = event.detail;
    if (selectedEvent && selectedEvent.code && list) {
      // 根据当前选择的事件类型设置对应的事件代码
      switch (selectedEventType) {
        case 'onSelectionChange':
          list.onSelectionChangeCode = selectedEvent.code;
          break;
        case 'onItemClick':
          list.onItemClickCode = selectedEvent.code;
          break;
        case 'onItemDoubleClick':
          list.onItemDoubleClickCode = selectedEvent.code;
          break;
        case 'onItemHover':
          list.onItemHoverCode = selectedEvent.code;
          break;
      }
    }
    closeEventModal();
  }

  // 获取当前事件代码
  function getCurrentEventCode(eventType: string): string {
    if (!list) return '';
    switch (eventType) {
      case 'onSelectionChange':
        return list.onSelectionChangeCode || '';
      case 'onItemClick':
        return list.onItemClickCode || '';
      case 'onItemDoubleClick':
        return list.onItemDoubleClickCode || '';
      case 'onItemHover':
        return list.onItemHoverCode || '';
      default:
        return '';
    }
  }

  // 清除事件代码
  function clearEventCode(eventType: string) {
    if (!list) return;
    switch (eventType) {
      case 'onSelectionChange':
        list.onSelectionChangeCode = '';
        break;
      case 'onItemClick':
        list.onItemClickCode = '';
        break;
      case 'onItemDoubleClick':
        list.onItemDoubleClickCode = '';
        break;
      case 'onItemHover':
        list.onItemHoverCode = '';
        break;
    }
  }
</script>

{#if list}
  <AccordionPanel title="📋 列表属性" bind:expanded={isExpanded}>

    <!-- 列表状态信息 -->
    <div class="list-status">
      <Label text={listInfo()} />
    </div>

    <!-- 基础属性 -->
    <PropertyContainer>
    
        <Label text="选择模式:" />
        <Select
          bind:value={list.selectionMode}
          options={selectionModeOptions}
          placeholder="选择模式"
        />
 </PropertyContainer>
  <PropertyContainer>
    
        <Label text="项目高度:" />
        <input
          type="number"
          bind:value={list.itemHeight}
          min="16"
          max="100"
          class="number-input"
        />
   </PropertyContainer>

      <PropertyContainer>
        <Label text="项目间距:" />
        <input
          type="number"
          bind:value={list.itemSpacing}
          min="0"
          max="20"
          class="number-input"
        />
     </PropertyContainer>
     <PropertyContainer>
        <Label text="虚拟滚动:" />
        <Checkbox bind:checked={list.virtualScrolling} />
    </PropertyContainer>

     <PropertyContainer>
        <Label text="启用状态:" />
        <Checkbox bind:checked={list.enabled} />
    
    </PropertyContainer>

    <!-- 数据源配置 -->
    <PropertyContainer>
      <div class="section-header">
        <h4>数据源配置</h4>
      </div>
      <div class="property-row">
        <Label text="数据项数量:" />
        <span class="data-count">{list.dataSource.length}</span>
      </div>

      <div class="data-source-actions">
        <button class="bind-data-btn" onclick={openDataBindingModal}>
          🔗 选择数据源
        </button>

        <button class="clear-data-btn" onclick={clearDataSource}>
          🗑️ 清空数据
        </button>
      </div>

    </PropertyContainer>

    <!-- 列表操作 -->
    <PropertyContainer>
      <div class="section-header">
        <h4>列表操作</h4>
      </div>

      <div class="list-actions">
        <button class="refresh-btn" onclick={refreshListItems}>
          🔄 刷新列表项
        </button>
      </div>
    </PropertyContainer>

    <!-- 事件配置 -->
    <PropertyContainer>
      <div class="section-header">
        <h4>事件配置</h4>
      </div>

      <div class="property-row">
        <Label text="事件状态:" />
        <span class="event-status">{eventInfo()}</span>
      </div>
      
      {#each eventTypes as eventType}
        <div class="event-row">
          <Label>{eventType.label}:</Label>
          <div class="event-controls">
            <button 
              onclick={() => openEventModal(eventType.value)}
              class="event-btn"
              class:has-code={getCurrentEventCode(eventType.value)}
            >
              {getCurrentEventCode(eventType.value) ? '已配置' : '配置事件'}
            </button>
            {#if getCurrentEventCode(eventType.value)}
              <button 
                onclick={() => clearEventCode(eventType.value)}
                class="clear-btn"
                title="清除事件代码"
              >
                ✕
              </button>
            {/if}
          </div>
        </div>
      {/each}
    </PropertyContainer>
  </AccordionPanel>

  <!-- 事件选择模态框 -->
  {#if showEventModal}
    <EventSelectionModal
      onEventSelected={handleEventSelected}
      onClose={closeEventModal}
    />
  {/if}
{/if}

<!-- 数据绑定模态框 -->
<DataBindingModal
  bind:isOpen={showDataBindingModal}
  onConfirm={handleDataBindingConfirm}
  mode="list"
/>



<style>




  .event-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 4px 0;
  }

  .event-controls {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .event-btn {
    padding: 4px 8px;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    background: var(--theme-surface);
    color: var(--theme-text);
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
  }

  .event-btn.has-code {
    background: var(--theme-primary);
    color: white;
    border-color: var(--theme-primary);
  }

  .event-btn:hover {
    background: var(--theme-hover);
  }

  .event-btn.has-code:hover {
    background: var(--theme-primary-dark);
  }

  .clear-btn {
    padding: 2px 6px;
    border: 1px solid var(--theme-border);
    border-radius: 3px;
    background: var(--theme-surface);
    color: var(--theme-text);
    cursor: pointer;
    font-size: 10px;
    transition: all 0.2s ease;
  }

  .clear-btn:hover {
    background: var(--theme-danger);
    color: white;
    border-color: var(--theme-danger);
  }

  /* 新增样式 */
  .list-status {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 12px;
    color: var(--theme-text-secondary);
    font-size: 12px;
  }

  .section-header {
    margin-bottom: 8px;
  }

  .section-header h4 {
    margin: 0;
    color: var(--theme-text);
    font-size: 14px;
    font-weight: bold;
  }

  .property-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .property-row :global(.label) {
    min-width: 80px;
    font-size: 12px;
  }

  .number-input {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    color: var(--theme-text);
    padding: 4px 8px;
    font-size: 12px;
    width: 80px;
  }



  .refresh-btn {
    background: #9C27B0;
    border: none;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
  }

  .refresh-btn:hover {
    background: #7B1FA2;
  }

  /* 列表操作样式 */
  .list-actions {
    display: flex;
    justify-content: center;
    padding: 8px 0;
  }

  /* 数据源配置样式 */
  .data-source-actions {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    margin-top: 8px;
  }

  .bind-data-btn,
  .clear-data-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    flex: 1;
    min-width: 80px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .bind-data-btn {
    background: var(--theme-primary);
    color: white;
  }

  .bind-data-btn:hover {
    background: var(--theme-primary-dark);
  }

  .clear-data-btn {
    background: var(--theme-warning);
    color: white;
  }

  .clear-data-btn:hover {
    background: var(--theme-warning-dark);
  }



  .data-count {
    color: var(--theme-success);
    font-weight: bold;
    font-size: 12px;
  }
</style>
