<script lang="ts">
  import LabelSpriteEditor from '../components/LabelSpriteEditor.svelte';
  import type { bitmapProperties } from '../types';

  // 测试数据
  const testBitmapData: bitmapProperties = {
    fontBold: false,
    fontFace: 'GameFont',
    fontItalic: false,
    fontSize: 28,
    outlineColor: 'rgba(0, 0, 0, 0.5)',
    outlineWidth: 4,
    textColor: '#ffffff',
    _paintOpacity: 255,
    _smooth: true,
    elements: [
      {
        type: 'text',
        id: 'test-text-1',
        text: '测试文本',
        x: 100,
        y: 100,
        maxWidth: 200,
        lineHeight: 36,
        align: 'left'
      }
    ],
    regions: []
  };

  // 导出处理函数
  function handleExport(exportedData: bitmapProperties) {
    console.log('🎉 接收到导出的数据:', exportedData);

    // 显示导出结果
    exportResult = JSON.stringify(exportedData, null, 2);
    showResult = true;

    // 可以在这里将数据发送到服务器或进行其他处理
    alert('数据导出成功！编辑器将自动关闭。');
  }

  // 关闭处理函数
  function handleClose() {
    console.log('🎉 编辑器关闭');
    alert('编辑器已关闭！');
  }

  // 结果显示
  let showResult = false;
  let exportResult = '';

  function closeResult() {
    showResult = false;
  }
</script>

<div class="export-test">
  <div class="header">
    <h1>LabelSpriteEditor 导出功能测试</h1>
    <p>在编辑器中修改内容，然后点击右上角的"导出"按钮测试导出功能。</p>
  </div>

  <div class="editor-container">
    <LabelSpriteEditor
      bitmapData={testBitmapData}
      onExport={handleExport}
      onClose={handleClose}
    />
  </div>

  {#if showResult}
    <div class="result-overlay">
      <div class="result-modal">
        <div class="result-header">
          <h3>导出结果</h3>
          <button class="close-btn" onclick={closeResult}>×</button>
        </div>
        <div class="result-content">
          <pre>{exportResult}</pre>
        </div>
        <div class="result-footer">
          <button class="copy-btn" onclick={() => navigator.clipboard.writeText(exportResult)}>
            复制到剪贴板
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .export-test {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
  }

  .header {
    padding: 20px;
    background: white;
    border-bottom: 1px solid #e5e7eb;
    text-align: center;
  }

  .header h1 {
    margin: 0 0 10px 0;
    color: #111827;
    font-size: 24px;
  }

  .header p {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
  }

  .editor-container {
    flex: 1;
    margin: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  /* 结果显示模态框 */
  .result-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .result-modal {
    background: white;
    border-radius: 8px;
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.15);
    max-width: 80vw;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
  }

  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
  }

  .result-header h3 {
    margin: 0;
    color: #111827;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-btn:hover {
    color: #111827;
  }

  .result-content {
    flex: 1;
    padding: 20px;
    overflow: auto;
  }

  .result-content pre {
    background: #f9fafb;
    padding: 16px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.5;
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .result-footer {
    padding: 20px;
    border-top: 1px solid #e5e7eb;
    text-align: right;
  }

  .copy-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
  }

  .copy-btn:hover {
    background: #2563eb;
  }
</style>
