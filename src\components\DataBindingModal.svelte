<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import type { BaseObjectModel } from '../type/baseObjectModel.svelte';
    import type { ItemModel } from '../type/ui/itemModel.svelte';

    // 事件分发器
    const dispatch = createEventDispatcher();

    // Props
    export let isOpen = false;
    export let itemModel: ItemModel | null = null;

    // 状态
    let selectedChild: BaseObjectModel | null = null;
    let fieldName = '';
    let availableFields: string[] = [];

    // 预定义的 RPG Maker MZ 数据源字段
    const rpgMakerFields = {
        saveFiles: [
            'id', 'title', 'playtime', 'playtimeText', 'timestamp',
            'characterData.0.name', 'characterData.0.level', 'characterData.0.faceName',
            'characterData.0.faceIndex', 'characterData.0.characterName', 'characterData.0.characterIndex'
        ],
        inventory: [
            'id', 'name', 'iconIndex', 'description', 'note', 'etypeId',
            'price', 'params', 'count', 'amount'
        ],
        skills: [
            'id', 'name', 'iconIndex', 'description', 'note', 'stypeId',
            'mpCost', 'tpCost', 'scope', 'occasion', 'speed', 'successRate'
        ],
        actors: [
            'id', 'name', 'nickname', 'profile', 'classId', 'characterName',
            'characterIndex', 'faceName', 'faceIndex', 'level', 'exp', 'params'
        ],
        items: [
            'id', 'name', 'iconIndex', 'description', 'note', 'itypeId',
            'price', 'consumable', 'effects'
        ],
        weapons: [
            'id', 'name', 'iconIndex', 'description', 'note', 'etypeId',
            'price', 'params', 'animationId', 'traits'
        ],
        armors: [
            'id', 'name', 'iconIndex', 'description', 'note', 'etypeId',
            'price', 'params', 'traits'
        ]
    };

    // 当前选择的数据源类型
    let selectedDataSource = 'saveFiles';

    // 响应式更新可用字段
    $: {
        if (selectedDataSource && rpgMakerFields[selectedDataSource as keyof typeof rpgMakerFields]) {
            availableFields = rpgMakerFields[selectedDataSource as keyof typeof rpgMakerFields];
        }
    }

    // 获取子组件列表
    $: childComponents = itemModel ? itemModel.children : [];

    // 获取当前数据绑定
    $: currentBindings = itemModel ? itemModel.getDataBindings() : new Map();

    /**
     * 关闭模态框
     */
    function closeModal() {
        isOpen = false;
        selectedChild = null;
        fieldName = '';
        dispatch('close');
    }

    /**
     * 添加数据绑定
     */
    function addBinding() {
        if (!itemModel || !selectedChild || !fieldName.trim()) {
            alert('请选择子组件和字段名');
            return;
        }

        try {
            itemModel.addDataBinding(selectedChild, fieldName.trim());
            
            // 重置表单
            selectedChild = null;
            fieldName = '';
            
            console.log('✅ 数据绑定添加成功');
        } catch (error) {
            console.error('❌ 添加数据绑定失败:', error);
            alert('添加数据绑定失败: ' + error.message);
        }
    }

    /**
     * 移除数据绑定
     */
    function removeBinding(childModel: BaseObjectModel) {
        if (!itemModel) return;

        try {
            itemModel.removeDataBinding(childModel);
            console.log('✅ 数据绑定移除成功');
        } catch (error) {
            console.error('❌ 移除数据绑定失败:', error);
            alert('移除数据绑定失败: ' + error.message);
        }
    }

    /**
     * 清除所有绑定
     */
    function clearAllBindings() {
        if (!itemModel) return;

        if (confirm('确定要清除所有数据绑定吗？')) {
            try {
                itemModel.clearDataBindings();
                console.log('✅ 所有数据绑定已清除');
            } catch (error) {
                console.error('❌ 清除数据绑定失败:', error);
                alert('清除数据绑定失败: ' + error.message);
            }
        }
    }

    /**
     * 选择预定义字段
     */
    function selectField(field: string) {
        fieldName = field;
    }
</script>

{#if isOpen}
    <div class="modal-overlay" on:click={closeModal}>
        <div class="modal-content" on:click|stopPropagation>
            <div class="modal-header">
                <h3>数据绑定设置</h3>
                <button class="close-btn" on:click={closeModal}>×</button>
            </div>

            <div class="modal-body">
                <!-- 当前绑定列表 -->
                <div class="section">
                    <h4>当前数据绑定</h4>
                    {#if currentBindings.size > 0}
                        <div class="bindings-list">
                            {#each [...currentBindings.entries()] as [childModel, fieldName]}
                                <div class="binding-item">
                                    <span class="component-name">{childModel.className}</span>
                                    <span class="arrow">→</span>
                                    <span class="field-name">{fieldName}</span>
                                    <button 
                                        class="remove-btn"
                                        on:click={() => removeBinding(childModel)}
                                        title="移除绑定"
                                    >
                                        ×
                                    </button>
                                </div>
                            {/each}
                        </div>
                        <button class="clear-all-btn" on:click={clearAllBindings}>
                            清除所有绑定
                        </button>
                    {:else}
                        <p class="no-bindings">暂无数据绑定</p>
                    {/if}
                </div>

                <!-- 添加新绑定 -->
                <div class="section">
                    <h4>添加数据绑定</h4>
                    
                    <!-- 选择子组件 -->
                    <div class="form-group">
                        <label>选择子组件:</label>
                        <select bind:value={selectedChild}>
                            <option value={null}>请选择子组件</option>
                            {#each childComponents as child}
                                <option value={child}>{child.className} - {child.name || '未命名'}</option>
                            {/each}
                        </select>
                    </div>

                    <!-- 选择数据源类型 -->
                    <div class="form-group">
                        <label>数据源类型:</label>
                        <select bind:value={selectedDataSource}>
                            <option value="saveFiles">存档文件</option>
                            <option value="inventory">背包物品</option>
                            <option value="skills">技能列表</option>
                            <option value="actors">角色数据</option>
                            <option value="items">道具数据</option>
                            <option value="weapons">武器数据</option>
                            <option value="armors">防具数据</option>
                        </select>
                    </div>

                    <!-- 预定义字段 -->
                    <div class="form-group">
                        <label>常用字段:</label>
                        <div class="field-buttons">
                            {#each availableFields as field}
                                <button 
                                    class="field-btn"
                                    on:click={() => selectField(field)}
                                >
                                    {field}
                                </button>
                            {/each}
                        </div>
                    </div>

                    <!-- 自定义字段名 -->
                    <div class="form-group">
                        <label>字段名:</label>
                        <input 
                            type="text" 
                            bind:value={fieldName}
                            placeholder="输入字段名，如: name 或 characterData.0.name"
                        />
                    </div>

                    <button 
                        class="add-btn"
                        on:click={addBinding}
                        disabled={!selectedChild || !fieldName.trim()}
                    >
                        添加绑定
                    </button>
                </div>
            </div>
        </div>
    </div>
{/if}

<style>
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .modal-content {
        background: #2a2a2a;
        border-radius: 8px;
        width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        color: white;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #444;
    }

    .modal-header h3 {
        margin: 0;
        color: #fff;
    }

    .close-btn {
        background: none;
        border: none;
        color: #ccc;
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .close-btn:hover {
        color: #fff;
    }

    .modal-body {
        padding: 20px;
    }

    .section {
        margin-bottom: 24px;
    }

    .section h4 {
        margin: 0 0 12px 0;
        color: #fff;
        font-size: 16px;
    }

    .bindings-list {
        border: 1px solid #444;
        border-radius: 4px;
        max-height: 150px;
        overflow-y: auto;
    }

    .binding-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-bottom: 1px solid #444;
        gap: 8px;
    }

    .binding-item:last-child {
        border-bottom: none;
    }

    .component-name {
        color: #4CAF50;
        font-weight: bold;
        min-width: 80px;
    }

    .arrow {
        color: #ccc;
    }

    .field-name {
        color: #2196F3;
        flex: 1;
    }

    .remove-btn {
        background: #f44336;
        border: none;
        color: white;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .remove-btn:hover {
        background: #d32f2f;
    }

    .clear-all-btn {
        background: #ff9800;
        border: none;
        color: white;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 8px;
        font-size: 12px;
    }

    .clear-all-btn:hover {
        background: #f57c00;
    }

    .no-bindings {
        color: #ccc;
        font-style: italic;
        margin: 0;
    }

    .form-group {
        margin-bottom: 16px;
    }

    .form-group label {
        display: block;
        margin-bottom: 4px;
        color: #ccc;
        font-size: 14px;
    }

    .form-group select,
    .form-group input {
        width: 100%;
        padding: 8px;
        border: 1px solid #444;
        border-radius: 4px;
        background: #333;
        color: white;
        font-size: 14px;
    }

    .form-group select:focus,
    .form-group input:focus {
        outline: none;
        border-color: #2196F3;
    }

    .field-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        max-height: 120px;
        overflow-y: auto;
        border: 1px solid #444;
        border-radius: 4px;
        padding: 8px;
    }

    .field-btn {
        background: #444;
        border: 1px solid #666;
        color: #ccc;
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        white-space: nowrap;
    }

    .field-btn:hover {
        background: #555;
        color: white;
    }

    .add-btn {
        background: #4CAF50;
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        width: 100%;
    }

    .add-btn:hover:not(:disabled) {
        background: #45a049;
    }

    .add-btn:disabled {
        background: #666;
        cursor: not-allowed;
    }
</style>
