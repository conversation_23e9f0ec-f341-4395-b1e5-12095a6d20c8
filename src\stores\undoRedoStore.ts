/**
 * 撤销/重做系统
 * 基于响应式字段的历史记录管理
 */

import { writable } from 'svelte/store';
import type { BaseObjectModel } from '../type/baseObjectModel.svelte';

/**
 * 字段变化记录
 */
export interface FieldChange {
  objectId: string;           // 对象唯一ID
  fieldName: string;          // 字段名
  oldValue: any;              // 旧值
  newValue: any;              // 新值
  timestamp: number;          // 时间戳
}

/**
 * 操作记录（可能包含多个字段变化）
 */
export interface Operation {
  id: string;                 // 操作ID
  name: string;               // 操作名称（如"移动对象"、"修改属性"）
  changes: FieldChange[];     // 字段变化列表
  timestamp: number;          // 操作时间戳
}

/**
 * 撤销/重做状态
 */
export interface UndoRedoState {
  undoStack: Operation[];     // 撤销栈
  redoStack: Operation[];     // 重做栈
  maxHistorySize: number;     // 最大历史记录数
  isRecording: boolean;       // 是否正在记录操作
  currentBatch: FieldChange[]; // 当前批次的变化
}

/**
 * 初始状态
 */
const initialState: UndoRedoState = {
  undoStack: [],
  redoStack: [],
  maxHistorySize: 100,
  isRecording: true,
  currentBatch: []
};

/**
 * 撤销/重做状态存储
 */
export const undoRedoState = writable<UndoRedoState>(initialState);

/**
 * 撤销/重做管理器
 */
export class UndoRedoManager {
  private static instance: UndoRedoManager;
  private batchTimer: number | null = null;
  private batchDelay = 300; // 300ms内的变化合并为一个操作

  static getInstance(): UndoRedoManager {
    if (!this.instance) {
      this.instance = new UndoRedoManager();
    }
    return this.instance;
  }

  /**
   * 记录字段变化
   */
  recordFieldChange(
    object: BaseObjectModel, 
    fieldName: string, 
    oldValue: any, 
    newValue: any,
    operationName?: string
  ): void {
    undoRedoState.update(state => {
      if (!state.isRecording) return state;

      const change: FieldChange = {
        objectId: this.getObjectId(object),
        fieldName,
        oldValue,
        newValue,
        timestamp: Date.now()
      };

      // 添加到当前批次
      state.currentBatch.push(change);

      // 设置批次定时器
      this.scheduleBatchCommit(operationName || `修改 ${fieldName}`);

      return state;
    });
  }

  /**
   * 安排批次提交
   */
  private scheduleBatchCommit(operationName: string): void {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }

    this.batchTimer = setTimeout(() => {
      this.commitCurrentBatch(operationName);
    }, this.batchDelay);
  }

  /**
   * 提交当前批次
   */
  private commitCurrentBatch(operationName: string): void {
    undoRedoState.update(state => {
      if (state.currentBatch.length === 0) return state;

      const operation: Operation = {
        id: `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: operationName,
        changes: [...state.currentBatch],
        timestamp: Date.now()
      };

      // 添加到撤销栈
      state.undoStack.push(operation);

      // 限制历史记录大小
      if (state.undoStack.length > state.maxHistorySize) {
        state.undoStack.shift();
      }

      // 清空重做栈（新操作会使重做无效）
      state.redoStack = [];

      // 清空当前批次
      state.currentBatch = [];

      console.log(`📝 记录操作: ${operationName}`, operation);

      return state;
    });

    this.batchTimer = null;
  }

  /**
   * 撤销操作
   */
  undo(): boolean {
    let success = false;

    undoRedoState.update(state => {
      if (state.undoStack.length === 0) return state;

      const operation = state.undoStack.pop()!;
      
      // 停止记录，避免撤销操作被记录
      state.isRecording = false;

      try {
        // 逆序应用变化（撤销）
        for (let i = operation.changes.length - 1; i >= 0; i--) {
          const change = operation.changes[i];
          this.applyFieldChange(change.objectId, change.fieldName, change.oldValue);
        }

        // 添加到重做栈
        state.redoStack.push(operation);
        success = true;

        console.log(`↶ 撤销操作: ${operation.name}`);
      } catch (error) {
        console.error('撤销操作失败:', error);
        // 恢复操作到撤销栈
        state.undoStack.push(operation);
      } finally {
        // 恢复记录
        state.isRecording = true;
      }

      return state;
    });

    return success;
  }

  /**
   * 重做操作
   */
  redo(): boolean {
    let success = false;

    undoRedoState.update(state => {
      if (state.redoStack.length === 0) return state;

      const operation = state.redoStack.pop()!;
      
      // 停止记录
      state.isRecording = false;

      try {
        // 正序应用变化（重做）
        for (const change of operation.changes) {
          this.applyFieldChange(change.objectId, change.fieldName, change.newValue);
        }

        // 添加到撤销栈
        state.undoStack.push(operation);
        success = true;

        console.log(`↷ 重做操作: ${operation.name}`);
      } catch (error) {
        console.error('重做操作失败:', error);
        // 恢复操作到重做栈
        state.redoStack.push(operation);
      } finally {
        // 恢复记录
        state.isRecording = true;
      }

      return state;
    });

    return success;
  }

  /**
   * 应用字段变化
   */
  private applyFieldChange(objectId: string, fieldName: string, value: any): void {
    const object = this.findObjectById(objectId);
    if (!object) {
      throw new Error(`找不到对象: ${objectId}`);
    }

    // 直接设置响应式字段
    (object as any)[fieldName] = value;
  }

  /**
   * 获取对象ID
   */
  private getObjectId(object: BaseObjectModel): string {
    // 如果对象没有ID，生成一个
    if (!(object as any)._undoRedoId) {
      (object as any)._undoRedoId = `obj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    return (object as any)._undoRedoId;
  }

  /**
   * 根据ID查找对象
   */
  private findObjectById(objectId: string): BaseObjectModel | null {
    // 这里需要实现对象查找逻辑
    // 可以从当前场景中递归查找
    const scene = getCurrentScene();
    if (!scene) return null;

    return this.findObjectInTree(scene, objectId);
  }

  /**
   * 在对象树中查找对象
   */
  private findObjectInTree(root: BaseObjectModel, objectId: string): BaseObjectModel | null {
    if ((root as any)._undoRedoId === objectId) {
      return root;
    }

    for (const child of root.children) {
      const found = this.findObjectInTree(child, objectId);
      if (found) return found;
    }

    return null;
  }

  /**
   * 清空历史记录
   */
  clearHistory(): void {
    undoRedoState.update(state => ({
      ...state,
      undoStack: [],
      redoStack: [],
      currentBatch: []
    }));

    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    console.log('🗑️ 撤销/重做历史已清空');
  }

  /**
   * 获取历史记录统计
   */
  getHistoryStats(): { undoCount: number; redoCount: number; canUndo: boolean; canRedo: boolean } {
    let stats = { undoCount: 0, redoCount: 0, canUndo: false, canRedo: false };
    
    undoRedoState.subscribe(state => {
      stats = {
        undoCount: state.undoStack.length,
        redoCount: state.redoStack.length,
        canUndo: state.undoStack.length > 0,
        canRedo: state.redoStack.length > 0
      };
    })();

    return stats;
  }
}

// 导出单例实例
export const undoRedoManager = UndoRedoManager.getInstance();

// 导出便捷方法
export const undo = () => undoRedoManager.undo();
export const redo = () => undoRedoManager.redo();
export const clearHistory = () => undoRedoManager.clearHistory();

// 需要导入当前场景的方法
function getCurrentScene(): BaseObjectModel | null {
  // 这里需要从 sceneModelStore 获取当前场景
  // 为了避免循环依赖，可能需要通过回调或事件系统
  return null; // 临时返回
}

console.log('🔄 撤销/重做系统已初始化');
