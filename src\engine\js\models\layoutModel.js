/**
 * LayoutModel - UILayout的模型对象
 * 管理UILayout的所有属性和状态
 */

import { writable, derived } from 'svelte/store';

export class LayoutModel {
    constructor(displayObject = null) {
        console.log('📐 LayoutModel: 创建布局模型', displayObject?.constructor.name);

        // 关联的显示对象
        this.displayObject = displayObject;

        // 🔑 布局类型
        this.layoutType = writable('vertical'); // 'vertical', 'horizontal', 'grid'

        // 🔑 间距设置
        this.spacing = writable(5);              // 统一间距
        this.horizontalSpacing = writable(5);    // 水平间距
        this.verticalSpacing = writable(5);      // 垂直间距
        this.padding = writable(0);              // 内边距

        // 🔑 网格布局参数
        this.columns = writable(2);              // 网格列数
        this.rows = writable(0);                 // 网格行数（0表示自动）

        // 🔑 对齐方式
        this.mainAxisAlignment = writable('start');     // 主轴对齐：start, center, end, space-between, space-around
        this.crossAxisAlignment = writable('start');    // 交叉轴对齐：start, center, end, stretch

        // 🔑 容器尺寸
        this.containerWidth = writable(0);       // 容器宽度（0表示自动）
        this.containerHeight = writable(0);      // 容器高度（0表示自动）

        // 🔑 自动更新设置
        this.autoUpdate = writable(true);        // 是否自动更新布局

        // 🔑 高级设置
        this.wrapContent = writable(false);      // 是否自动换行（未来功能）
        this.reverseOrder = writable(false);     // 是否反向排列

        // 设置监听器
        this.setupSubscriptions();

        console.log('✅ LayoutModel: 布局模型创建完成');
    }

    /**
     * 设置属性变化监听器
     */
    setupSubscriptions() {
        // 监听布局类型变化
        this.layoutType.subscribe(value => {
            console.log('📐 LayoutModel: 布局类型变化', value);
            this.syncToDisplayObject();
        });

        // 监听间距变化
        this.spacing.subscribe(value => {
            console.log('📐 LayoutModel: 统一间距变化', value);
            // 同步更新水平和垂直间距
            this.horizontalSpacing.set(value);
            this.verticalSpacing.set(value);
            this.syncToDisplayObject();
        });

        this.horizontalSpacing.subscribe(value => {
            console.log('📐 LayoutModel: 水平间距变化', value);
            this.syncToDisplayObject();
        });

        this.verticalSpacing.subscribe(value => {
            console.log('📐 LayoutModel: 垂直间距变化', value);
            this.syncToDisplayObject();
        });

        // 监听其他属性变化
        this.padding.subscribe(value => {
            console.log('📐 LayoutModel: 内边距变化', value);
            this.syncToDisplayObject();
        });

        this.columns.subscribe(value => {
            console.log('📐 LayoutModel: 网格列数变化', value);
            this.syncToDisplayObject();
        });

        this.rows.subscribe(value => {
            console.log('📐 LayoutModel: 网格行数变化', value);
            this.syncToDisplayObject();
        });

        this.mainAxisAlignment.subscribe(value => {
            console.log('📐 LayoutModel: 主轴对齐变化', value);
            this.syncToDisplayObject();
        });

        this.crossAxisAlignment.subscribe(value => {
            console.log('📐 LayoutModel: 交叉轴对齐变化', value);
            this.syncToDisplayObject();
        });

        this.containerWidth.subscribe(value => {
            console.log('📐 LayoutModel: 容器宽度变化', value);
            this.syncToDisplayObject();
        });

        this.containerHeight.subscribe(value => {
            console.log('📐 LayoutModel: 容器高度变化', value);
            this.syncToDisplayObject();
        });

        this.autoUpdate.subscribe(value => {
            console.log('📐 LayoutModel: 自动更新设置变化', value);
            this.syncToDisplayObject();
        });
    }

    /**
     * 同步模型数据到显示对象
     */
    syncToDisplayObject() {
        if (!this.displayObject) {
            console.warn('⚠️ LayoutModel: 没有关联的显示对象');
            return;
        }

        console.log('🔄 LayoutModel: 同步数据到显示对象');

        try {
            // 获取当前值
            const layoutType = this.getLayoutType();
            const spacing = this.getSpacing();
            const horizontalSpacing = this.getHorizontalSpacing();
            const verticalSpacing = this.getVerticalSpacing();
            const padding = this.getPadding();
            const columns = this.getColumns();
            const rows = this.getRows();
            const mainAxisAlignment = this.getMainAxisAlignment();
            const crossAxisAlignment = this.getCrossAxisAlignment();
            const containerWidth = this.getContainerWidth();
            const containerHeight = this.getContainerHeight();
            const autoUpdate = this.getAutoUpdate();

            // 同步到显示对象
            this.displayObject.layoutType = layoutType;
            this.displayObject.spacing = spacing;
            this.displayObject.horizontalSpacing = horizontalSpacing;
            this.displayObject.verticalSpacing = verticalSpacing;
            this.displayObject.padding = padding;
            this.displayObject.columns = columns;
            this.displayObject.rows = rows;
            this.displayObject.mainAxisAlignment = mainAxisAlignment;
            this.displayObject.crossAxisAlignment = crossAxisAlignment;
            this.displayObject.containerWidth = containerWidth;
            this.displayObject.containerHeight = containerHeight;
            this.displayObject.autoUpdate = autoUpdate;

            // 触发布局更新
            if (typeof this.displayObject.updateLayout === 'function') {
                this.displayObject.updateLayout();
            }

            console.log('✅ LayoutModel: 数据同步完成');
        } catch (error) {
            console.error('❌ LayoutModel: 数据同步失败', error);
        }
    }

    /**
     * 从显示对象同步数据到模型
     */
    syncFromDisplayObject() {
        if (!this.displayObject) {
            console.warn('⚠️ LayoutModel: 没有关联的显示对象');
            return;
        }

        console.log('🔄 LayoutModel: 从显示对象同步数据');

        try {
            // 暂时禁用监听器，避免循环更新
            this.layoutType.set(this.displayObject.layoutType || 'vertical');
            this.spacing.set(this.displayObject.spacing || 5);
            this.horizontalSpacing.set(this.displayObject.horizontalSpacing || 5);
            this.verticalSpacing.set(this.displayObject.verticalSpacing || 5);
            this.padding.set(this.displayObject.padding || 0);
            this.columns.set(this.displayObject.columns || 2);
            this.rows.set(this.displayObject.rows || 0);
            this.mainAxisAlignment.set(this.displayObject.mainAxisAlignment || 'start');
            this.crossAxisAlignment.set(this.displayObject.crossAxisAlignment || 'start');
            this.containerWidth.set(this.displayObject.containerWidth || 0);
            this.containerHeight.set(this.displayObject.containerHeight || 0);
            this.autoUpdate.set(this.displayObject.autoUpdate !== false);

            console.log('✅ LayoutModel: 数据同步完成');
        } catch (error) {
            console.error('❌ LayoutModel: 数据同步失败', error);
        }
    }

    /**
     * 设置关联的显示对象
     */
    setDisplayObject(displayObject) {
        console.log('📐 LayoutModel: 设置显示对象', displayObject?.constructor.name);
        this.displayObject = displayObject;
        
        if (displayObject) {
            this.syncFromDisplayObject();
        }
    }

    // Getter方法
    getLayoutType() { return this.layoutType.get ? this.layoutType.get() : this.layoutType; }
    getSpacing() { return this.spacing.get ? this.spacing.get() : this.spacing; }
    getHorizontalSpacing() { return this.horizontalSpacing.get ? this.horizontalSpacing.get() : this.horizontalSpacing; }
    getVerticalSpacing() { return this.verticalSpacing.get ? this.verticalSpacing.get() : this.verticalSpacing; }
    getPadding() { return this.padding.get ? this.padding.get() : this.padding; }
    getColumns() { return this.columns.get ? this.columns.get() : this.columns; }
    getRows() { return this.rows.get ? this.rows.get() : this.rows; }
    getMainAxisAlignment() { return this.mainAxisAlignment.get ? this.mainAxisAlignment.get() : this.mainAxisAlignment; }
    getCrossAxisAlignment() { return this.crossAxisAlignment.get ? this.crossAxisAlignment.get() : this.crossAxisAlignment; }
    getContainerWidth() { return this.containerWidth.get ? this.containerWidth.get() : this.containerWidth; }
    getContainerHeight() { return this.containerHeight.get ? this.containerHeight.get() : this.containerHeight; }
    getAutoUpdate() { return this.autoUpdate.get ? this.autoUpdate.get() : this.autoUpdate; }

    /**
     * 获取布局信息摘要
     */
    getLayoutInfo() {
        return {
            layoutType: this.getLayoutType(),
            spacing: this.getSpacing(),
            horizontalSpacing: this.getHorizontalSpacing(),
            verticalSpacing: this.getVerticalSpacing(),
            padding: this.getPadding(),
            columns: this.getColumns(),
            rows: this.getRows(),
            mainAxisAlignment: this.getMainAxisAlignment(),
            crossAxisAlignment: this.getCrossAxisAlignment(),
            containerWidth: this.getContainerWidth(),
            containerHeight: this.getContainerHeight(),
            autoUpdate: this.getAutoUpdate()
        };
    }

    /**
     * 销毁模型
     */
    destroy() {
        console.log('💥 LayoutModel: 销毁布局模型');
        this.displayObject = null;
        // 注意：Svelte stores 不需要手动销毁
    }
}
