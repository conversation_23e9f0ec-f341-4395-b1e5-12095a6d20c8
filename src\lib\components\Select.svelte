<script lang="ts">
  /**
   * Select 下拉框组件
   * 基于 Skeleton UI 和全局主题色彩
   */
  
  export interface SelectOption {
    value: string | number;
    label: string;
    disabled?: boolean;
    group?: string;
  }
  
  // Props
  export let options: SelectOption[] = [];
  export let value: string | number = '';
  export let placeholder: string = '请选择...';
  export let disabled: boolean = false;
  export let required: boolean = false;
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let variant: 'default' | 'success' | 'warning' | 'error' = 'default';
  export let fullWidth: boolean = false;
  export let searchable: boolean = false;
  export let clearable: boolean = false;
  export let multiple: boolean = false;
  export let id: string = '';
  export let name: string = '';
  export let ariaLabel: string = '';
  
  // Events
  export let onChange: (value: string | number | (string | number)[], event: Event) => void = () => {};
  export let onFocus: (event: FocusEvent) => void = () => {};
  export let onBlur: (event: FocusEvent) => void = () => {};
  
  // 内部状态
  let isOpen = $state(false);
  let searchTerm = $state('');
  let selectElement: HTMLElement;
  let selectedValues = $state<(string | number)[]>(multiple ? (Array.isArray(value) ? value : []) : []);
  
  // 过滤选项
  $derived filteredOptions = searchable && searchTerm
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;
  
  // 获取选中的选项
  $derived selectedOption = options.find(option => option.value === value);
  $derived selectedLabel = selectedOption?.label || placeholder;
  
  // 分组选项
  $derived groupedOptions = filteredOptions.reduce((groups, option) => {
    const group = option.group || 'default';
    if (!groups[group]) groups[group] = [];
    groups[group].push(option);
    return groups;
  }, {} as Record<string, SelectOption[]>);
  
  // 处理选项选择
  function handleOptionSelect(option: SelectOption) {
    if (option.disabled) return;
    
    if (multiple) {
      const index = selectedValues.indexOf(option.value);
      if (index > -1) {
        selectedValues = selectedValues.filter(v => v !== option.value);
      } else {
        selectedValues = [...selectedValues, option.value];
      }
      onChange(selectedValues, new Event('change'));
    } else {
      value = option.value;
      onChange(value, new Event('change'));
      isOpen = false;
    }
  }
  
  // 处理清除
  function handleClear(event: Event) {
    event.stopPropagation();
    if (multiple) {
      selectedValues = [];
      onChange(selectedValues, event);
    } else {
      value = '';
      onChange('', event);
    }
  }
  
  // 切换下拉状态
  function toggleDropdown() {
    if (disabled) return;
    isOpen = !isOpen;
  }
  
  // 关闭下拉框
  function closeDropdown() {
    isOpen = false;
    searchTerm = '';
  }
  
  // 获取容器样式类
  function getContainerClass() {
    const baseClass = 'select-container';
    const sizeClass = `select-${size}`;
    const variantClass = `select-${variant}`;
    const openClass = isOpen ? 'select-open' : '';
    const disabledClass = disabled ? 'select-disabled' : '';
    const fullWidthClass = fullWidth ? 'select-full-width' : '';
    
    return [baseClass, sizeClass, variantClass, openClass, disabledClass, fullWidthClass]
      .filter(Boolean)
      .join(' ');
  }
  
  // 点击外部关闭下拉框
  function handleClickOutside(event: MouseEvent) {
    if (selectElement && !selectElement.contains(event.target as Node)) {
      closeDropdown();
    }
  }
  
  // 键盘导航
  function handleKeydown(event: KeyboardEvent) {
    if (disabled) return;
    
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        toggleDropdown();
        break;
      case 'Escape':
        closeDropdown();
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) toggleDropdown();
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (!isOpen) toggleDropdown();
        break;
    }
  }
</script>

<svelte:window onclick={handleClickOutside} />

<div 
  bind:this={selectElement}
  class={getContainerClass()}
  {id}
  role="combobox"
  aria-expanded={isOpen}
  aria-haspopup="listbox"
  aria-label={ariaLabel}
  tabindex={disabled ? -1 : 0}
  onkeydown={handleKeydown}
  onfocus={onFocus}
  onblur={onBlur}
>
  <div class="select-trigger" onclick={toggleDropdown}>
    <div class="select-value">
      {#if multiple && selectedValues.length > 0}
        <div class="select-tags">
          {#each selectedValues as val}
            {@const option = options.find(opt => opt.value === val)}
            {#if option}
              <span class="select-tag">
                {option.label}
                <button 
                  type="button" 
                  class="select-tag-remove"
                  onclick={(e) => { e.stopPropagation(); handleOptionSelect(option); }}
                >
                  ×
                </button>
              </span>
            {/if}
          {/each}
        </div>
      {:else if selectedLabel !== placeholder}
        {selectedLabel}
      {:else}
        <span class="select-placeholder">{placeholder}</span>
      {/if}
    </div>
    
    <div class="select-actions">
      {#if clearable && (value || selectedValues.length > 0)}
        <button 
          type="button" 
          class="select-clear"
          onclick={handleClear}
        >
          ×
        </button>
      {/if}
      <div class="select-arrow" class:rotated={isOpen}>
        ▼
      </div>
    </div>
  </div>
  
  {#if isOpen}
    <div class="select-dropdown">
      {#if searchable}
        <div class="select-search">
          <input
            type="text"
            bind:value={searchTerm}
            placeholder="搜索选项..."
            class="select-search-input"
          />
        </div>
      {/if}
      
      <div class="select-options" role="listbox">
        {#each Object.entries(groupedOptions) as [groupName, groupOptions]}
          {#if groupName !== 'default'}
            <div class="select-group-label">{groupName}</div>
          {/if}
          
          {#each groupOptions as option}
            <div
              class="select-option"
              class:selected={multiple ? selectedValues.includes(option.value) : value === option.value}
              class:disabled={option.disabled}
              role="option"
              aria-selected={multiple ? selectedValues.includes(option.value) : value === option.value}
              onclick={() => handleOptionSelect(option)}
            >
              {#if multiple}
                <input
                  type="checkbox"
                  checked={selectedValues.includes(option.value)}
                  disabled={option.disabled}
                  readonly
                />
              {/if}
              {option.label}
            </div>
          {/each}
        {/each}
        
        {#if filteredOptions.length === 0}
          <div class="select-no-options">没有找到选项</div>
        {/if}
      </div>
    </div>
  {/if}
  
  {#if name}
    <input type="hidden" {name} value={multiple ? selectedValues.join(',') : value} />
  {/if}
</div>

<style>
  .select-container {
    position: relative;
    display: inline-block;
    min-width: 200px;
  }
  
  .select-container.select-full-width {
    width: 100%;
  }
  
  .select-container.select-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .select-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-base);
  }
  
  .select-trigger:hover:not(.select-disabled .select-trigger) {
    border-color: var(--theme-border-dark);
  }
  
  .select-container.select-open .select-trigger {
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px rgba(74, 85, 104, 0.2);
  }
  
  .select-value {
    flex: 1;
    color: var(--theme-text);
    font-family: var(--font-family-base);
  }
  
  .select-placeholder {
    color: var(--theme-text-secondary);
    opacity: 0.7;
  }
  
  .select-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-1);
  }
  
  .select-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    background-color: var(--theme-primary);
    color: var(--theme-text-inverse);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-small);
    font-size: var(--font-size-sm);
  }
  
  .select-tag-remove {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    font-size: 1.2em;
    line-height: 1;
  }
  
  .select-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding-right: var(--spacing-2);
  }
  
  .select-clear {
    background: none;
    border: none;
    color: var(--theme-text-secondary);
    cursor: pointer;
    padding: 0;
    font-size: 1.2em;
    line-height: 1;
  }
  
  .select-clear:hover {
    color: var(--theme-text);
  }
  
  .select-arrow {
    color: var(--theme-text-secondary);
    font-size: 0.8em;
    transition: var(--transition-base);
  }
  
  .select-arrow.rotated {
    transform: rotate(180deg);
  }
  
  .select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px var(--theme-shadow);
    margin-top: var(--spacing-1);
    max-height: 300px;
    overflow: hidden;
  }
  
  .select-search {
    padding: var(--spacing-2);
    border-bottom: 1px solid var(--theme-border);
  }
  
  .select-search-input {
    width: 100%;
    padding: var(--spacing-2);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    background-color: var(--theme-background);
    color: var(--theme-text);
    font-family: var(--font-family-base);
  }
  
  .select-options {
    max-height: 250px;
    overflow-y: auto;
  }
  
  .select-group-label {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--theme-text-secondary);
    background-color: var(--theme-surface-light);
    border-bottom: 1px solid var(--theme-border);
  }
  
  .select-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    cursor: pointer;
    transition: var(--transition-base);
    color: var(--theme-text);
  }
  
  .select-option:hover:not(.disabled) {
    background-color: var(--theme-surface-light);
  }
  
  .select-option.selected {
    background-color: var(--theme-primary);
    color: var(--theme-text-inverse);
  }
  
  .select-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .select-no-options {
    padding: var(--spacing-4);
    text-align: center;
    color: var(--theme-text-secondary);
    font-style: italic;
  }
  
  /* 尺寸变体 */
  .select-sm .select-trigger {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
  }
  
  .select-md .select-trigger {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-base);
  }
  
  .select-lg .select-trigger {
    padding: var(--spacing-4) var(--spacing-5);
    font-size: var(--font-size-lg);
  }
  
  /* 状态变体 */
  .select-success .select-trigger {
    border-color: var(--theme-success);
  }
  
  .select-warning .select-trigger {
    border-color: var(--theme-warning);
  }
  
  .select-error .select-trigger {
    border-color: var(--theme-error);
  }
</style>
