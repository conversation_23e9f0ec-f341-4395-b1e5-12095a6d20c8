/**
 * Game_Event 创建器
 * 专门用于创建RPG Maker MZ事件角色
 */

import { objManage } from '../../objManage';

declare global {
    interface Window {
        Game_Event: any;
        Sprite_Character: any;
        Graphics: any;
        ImageManager: any;
        $dataMap: any;
        DataManager: any;
    }
}

/**
 * 创建事件精灵
 * @param options 创建选项
 * @returns 创建的事件精灵对象
 */
export async function createEvent(options: {
    eventId?: number;
    mapId?: number;
    characterName?: string;
    characterIndex?: number;
    direction?: number;
    x?: number;
    y?: number;
    pages?: any[];
} = {}): Promise<any> {
    console.log('=== 创建 Game_Event 精灵 ===');
    console.log('创建选项:', options);

    try {
        // 1. 检查资源是否准备就绪
        const resourceStatus = objManage.getResourceStatus();
        if (!resourceStatus.all) {
            console.log('等待资源加载完成...', resourceStatus);
            const loaded = await objManage.waitForResources();
            if (!loaded) {
                throw new Error('资源加载超时');
            }
        }

        // 2. 检查必要的类是否存在
        if (!window.Game_Event || !window.Sprite_Character) {
            throw new Error('RPG Maker MZ Event 类未加载');
        }

        // 3. 确保地图数据存在
        const eventId = options.eventId || 1;
        const mapId = options.mapId || 1;

        // 检查并创建地图数据
        if (!window.$dataMap) {
            console.log('$dataMap 不存在，创建空地图数据');
            if (window.DataManager && window.DataManager.makeEmptyMap) {
                window.DataManager.makeEmptyMap();
            } else {
                // 手动创建空地图数据
                window.$dataMap = {
                    data: [],
                    events: [],
                    width: 100,
                    height: 100,
                    scrollType: 3
                };
            }
        }

        // 确保 events 数组存在
        if (!window.$dataMap.events) {
            window.$dataMap.events = [];
        }

        // 创建基本的事件数据结构
        const eventData = {
            id: eventId,
            name: `Event_${eventId}`,
            note: '',
            pages: options.pages || [{
                conditions: {
                    actorId: 1,
                    actorValid: false,
                    itemId: 1,
                    itemValid: false,
                    selfSwitchCh: 'A',
                    selfSwitchValid: false,
                    switch1Id: 1,
                    switch1Valid: false,
                    switch2Id: 1,
                    switch2Valid: false,
                    variableId: 1,
                    variableValid: false,
                    variableValue: 0
                },
                directionFix: false,
                image: {
                    characterIndex: options.characterIndex !== undefined ? options.characterIndex : 0,
                    characterName: options.characterName || 'People1',
                    direction: options.direction || 2,
                    pattern: 0,
                    tileId: 0
                },
                list: [
                    { code: 101, indent: 0, parameters: ['', 0, 0, 2] },
                    { code: 401, indent: 0, parameters: ['Hello! I am an event.'] },
                    { code: 0, indent: 0, parameters: [] }
                ],
                moveFrequency: 3,
                moveRoute: {
                    list: [{ code: 0, parameters: [] }],
                    repeat: true,
                    skippable: false,
                    wait: false
                },
                moveSpeed: 3,
                moveType: 0,
                priorityType: 1,
                stepAnime: false,
                through: false,
                trigger: 0,
                walkAnime: true
            }],
            x: options.x !== undefined ? options.x : Math.floor(window.Graphics.width / 2 / 48),
            y: options.y !== undefined ? options.y : Math.floor(window.Graphics.height / 2 / 48)
        };

        // 将事件数据添加到地图数据中
        window.$dataMap.events[eventId] = eventData;
        console.log('事件数据创建完成并添加到 $dataMap:', eventData);

        // 4. 创建 Game_Event 实例
        const event = new window.Game_Event(mapId, eventId);

        console.log('Game_Event 实例创建完成，事件ID:', event._eventId, '地图ID:', event._mapId);

        // 5. 验证事件数据是否正确加载
        const loadedEventData = event.event();
        if (!loadedEventData) {
            throw new Error(`事件数据加载失败，eventId: ${eventId}`);
        }

        console.log('事件数据验证成功:', {
            eventId: loadedEventData.id,
            name: loadedEventData.name,
            x: loadedEventData.x,
            y: loadedEventData.y,
            characterName: loadedEventData.pages[0]?.image?.characterName
        });

        // 6. 预加载事件图像
        if (window.ImageManager && event._characterName) {
            const bitmap = window.ImageManager.loadCharacter(event._characterName);
            console.log('预加载事件图像:', event._characterName, bitmap);

            // 等待图像加载完成
            if (bitmap && typeof bitmap.isReady === 'function' && !bitmap.isReady()) {
                console.log('等待事件图像加载完成...');
                await new Promise<void>((resolve) => {
                    if (typeof bitmap.addLoadListener === 'function') {
                        bitmap.addLoadListener(() => {
                            console.log('事件图像加载完成');
                            resolve();
                        });
                    } else {
                        const checkReady = () => {
                            if (bitmap.isReady && bitmap.isReady()) {
                                console.log('事件图像加载完成（轮询检测）');
                                resolve();
                            } else {
                                setTimeout(checkReady, 100);
                            }
                        };
                        checkReady();
                    }
                });
            }
        }

        console.log('Game_Event 创建完成:', {
            eventId: event._eventId,
            mapId: event._mapId,
            characterName: event._characterName,
            characterIndex: event._characterIndex,
            direction: event._direction,
            x: event._x,
            y: event._y
        });

        // 7. 创建精灵对象
        const sprite = new window.Sprite_Character(event);

        console.log('Sprite_Character (Event) 创建完成:', sprite);

        // 验证锚点是否符合 RPG Maker MZ 源码标准
        if (sprite.anchor) {
            const expectedAnchorX = 0.5;
            const expectedAnchorY = 1;
            console.log('Event 锚点验证:', {
                actualAnchorX: sprite.anchor.x,
                expectedAnchorX: expectedAnchorX,
                anchorXCorrect: sprite.anchor.x === expectedAnchorX,
                actualAnchorY: sprite.anchor.y,
                expectedAnchorY: expectedAnchorY,
                anchorYCorrect: sprite.anchor.y === expectedAnchorY,
                bothAnchorsCorrect: sprite.anchor.x === expectedAnchorX && sprite.anchor.y === expectedAnchorY
            });
        } else {
            console.warn('Event Sprite 没有 anchor 属性！这不符合 RPG Maker MZ 标准');
        }

        // 强制更新精灵
        if (sprite.update) {
            sprite.update();
            console.log('事件精灵已强制更新');
        }

        // 8. 设置显示属性
        sprite.scale.x = 2; // 放大2倍
        sprite.scale.y = 2; // 放大2倍

        // 9. 通过objManage添加到舞台
        objManage.addToStage(sprite);

        // 10. 设置调试属性
        sprite.tint = 0xFFFFFF; // 确保没有色调变化
        console.log('事件精灵创建完成（已放大2倍）');

        // 11. 返回包含显示对象和游戏对象的结构
        // 主要对象是 Sprite（显示对象），游戏对象作为关联数据
        const result = {
            displayObject: sprite,           // 主要对象：Sprite_Character
            gameObject: event,              // 关联的游戏逻辑对象
            type: 'Sprite_Character',       // 对象树中显示的类型
            gameType: 'Game_Event',         // 关联的游戏对象类型
            displayName: `Sprite_Character (Event ${event._eventId || eventId})`
        };

        console.log('Event 创建结果:', result);
        return result;

    } catch (error) {
        console.error('创建 Game_Event 精灵失败:', error);
        throw error;
    }
}

/**
 * 创建测试事件精灵
 * @param characterName 角色图像名称
 * @param characterIndex 角色索引
 * @returns 创建的事件精灵对象
 */
export async function createTestEvent(
    characterName: string = 'People1',
    characterIndex: number = 0
): Promise<any> {
    console.log(`=== 创建测试事件精灵 (${characterName}, ${characterIndex}) ===`);

    try {
        return await createEvent({
            eventId: 1,
            characterName: characterName,
            characterIndex: characterIndex,
            direction: 2
        });
    } catch (error) {
        console.error('创建测试事件失败:', error);
        throw error;
    }
}

/**
 * 创建NPC事件精灵
 * @param npcType NPC类型 ('villager', 'guard', 'merchant', 'child')
 * @returns 创建的NPC事件精灵对象
 */
export async function createNPCEvent(npcType: string = 'villager'): Promise<any> {
    console.log(`=== 创建NPC事件精灵 (${npcType}) ===`);

    let characterName = 'People1';
    let characterIndex = 0;

    // 根据NPC类型选择不同的图像
    switch (npcType) {
        case 'villager':
            characterName = 'People1';
            characterIndex = 0;
            break;
        case 'guard':
            characterName = 'People1';
            characterIndex = 1;
            break;
        case 'merchant':
            characterName = 'People1';
            characterIndex = 2;
            break;
        case 'child':
            characterName = 'People1';
            characterIndex = 3;
            break;
        default:
            characterName = 'People1';
            characterIndex = 0;
            break;
    }

    try {
        return await createEvent({
            eventId: Math.floor(Math.random() * 1000) + 1,
            characterName: characterName,
            characterIndex: characterIndex,
            direction: 2
        });
    } catch (error) {
        console.error(`创建NPC事件 ${npcType} 失败:`, error);
        throw error;
    }
}

/**
 * 创建多个事件精灵
 * @param count 事件数量
 * @returns 创建的事件精灵数组
 */
export async function createMultipleEvents(count: number = 3): Promise<any[]> {
    console.log(`=== 创建 ${count} 个事件精灵 ===`);

    try {
        const events = [];
        const npcTypes = ['villager', 'guard', 'merchant', 'child'];

        for (let i = 0; i < count; i++) {
            const npcType = npcTypes[i % npcTypes.length];
            const event = await createNPCEvent(npcType);
            events.push(event);
        }

        console.log(`成功创建 ${events.length} 个事件精灵`);
        return events;
    } catch (error) {
        console.error('创建多个事件失败:', error);
        throw error;
    }
}
