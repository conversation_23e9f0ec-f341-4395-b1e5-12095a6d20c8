# RPG Maker MZ Scene Creators

这个文件夹包含了所有 RPG Maker MZ 场景的创建器，基于 `src/center/classTree/treeData.ts` 中定义的场景层次结构实现。

## 场景层次结构

```
Scene_Base (场景基类)
├── Scene_Boot (启动场景)
├── Scene_Title (标题场景)
├── Scene_Map (地图场景)
├── Scene_MenuBase (菜单基类)
│   ├── Scene_Menu (主菜单场景)
│   ├── Scene_ItemBase (物品基类场景)
│   │   ├── Scene_Item (物品场景)
│   │   ├── Scene_Skill (技能场景)
│   │   └── Scene_Equip (装备场景)
│   ├── Scene_Status (状态场景)
│   ├── Scene_Options (选项场景)
│   └── Scene_File (文件场景基类)
│       ├── Scene_Save (保存场景)
│       └── Scene_Load (读取场景)
└── Scene_Battle (战斗场景)
```

## 已实现的创建器

### 基础创建器
- **SceneCreator.ts** - 基础场景创建器，提供通用功能

### 具体场景创建器
- **SceneBootCreator.ts** - Scene_Boot 启动场景
- **SceneTitleCreator.ts** - Scene_Title 标题场景
- **SceneMapCreator.ts** - Scene_Map 地图场景
- **SceneBattleCreator.ts** - Scene_Battle 战斗场景
- **SceneMenuBaseCreator.ts** - Scene_MenuBase 菜单基类
- **SceneMenuCreator.ts** - Scene_Menu 主菜单场景
- **SceneItemBaseCreator.ts** - Scene_ItemBase 物品基类场景
- **SceneItemCreator.ts** - Scene_Item 物品场景
- **SceneSkillCreator.ts** - Scene_Skill 技能场景
- **SceneEquipCreator.ts** - Scene_Equip 装备场景
- **SceneStatusCreator.ts** - Scene_Status 状态场景
- **SceneOptionsCreator.ts** - Scene_Options 选项场景
- **SceneFileCreator.ts** - Scene_File 文件场景基类
- **SceneSaveCreator.ts** - Scene_Save 保存场景
- **SceneLoadCreator.ts** - Scene_Load 读取场景

## 使用方法

### 1. 基本使用

```typescript
import { createSceneTitle, createSceneBattle, createSceneMenu } from '../creators';

// 创建标题场景
const titleScene = await createSceneTitle({
    autoStart: false,
    addToStage: true
});

// 创建战斗场景
const battleScene = await createSceneBattle({
    troopId: 1,
    canEscape: true,
    autoStart: false
});

// 创建主菜单场景
const menuScene = await createSceneMenu({
    showStatusWindow: true,
    showGoldWindow: true,
    backgroundType: 1
});
```

### 2. 通过类名创建

```typescript
import { createObjectByClassName } from '../creators';

// 通过类名创建场景
const scene = await createObjectByClassName('Scene_Title', {
    autoStart: false,
    addToStage: true,
    title1Name: 'MyTitle1',
    title2Name: 'MyTitle2'
});
```

### 3. 场景选项

每个场景创建器都支持以下基本选项：

```typescript
interface SceneCreationOptions {
    /** 是否自动启动场景 */
    autoStart?: boolean;
    /** 是否添加到舞台 */
    addToStage?: boolean;
    /** 自定义初始化参数 */
    initParams?: any[];
    /** 场景特定选项 */
    sceneOptions?: any;
}
```

### 4. 特定场景选项

#### 标题场景 (Scene_Title)
```typescript
interface TitleSceneOptions extends SceneCreationOptions {
    title1Name?: string;        // 自定义标题背景1
    title2Name?: string;        // 自定义标题背景2
    showGameTitle?: boolean;    // 是否显示游戏标题
}
```

#### 战斗场景 (Scene_Battle)
```typescript
interface BattleSceneOptions extends SceneCreationOptions {
    troopId?: number;           // 敌群ID
    canEscape?: boolean;        // 是否可以逃跑
    canLose?: boolean;          // 是否可以失败
    battleback1Name?: string;   // 战斗背景1
    battleback2Name?: string;   // 战斗背景2
}
```

#### 地图场景 (Scene_Map)
```typescript
interface MapSceneOptions extends SceneCreationOptions {
    mapId?: number;             // 地图ID
    playerX?: number;           // 玩家起始X坐标
    playerY?: number;           // 玩家起始Y坐标
    playerDirection?: number;   // 玩家起始方向
    menuEnabled?: boolean;      // 是否启用菜单
}
```

## 特性

### 1. 资源管理
- 自动预加载场景所需的资源
- 等待资源加载完成后再创建场景
- 支持异步资源加载

### 2. 生命周期管理
- 遵循 RPG Maker MZ 场景生命周期：initialize → create → start → update → stop → terminate
- 支持场景准备就绪检查
- 自动处理场景状态

### 3. 坐标管理
- **场景对象默认使用 (0, 0) 坐标**，不会被自动居中
- 与角色精灵等其他对象区分，角色精灵会自动居中显示
- 符合 RPG Maker MZ 场景的标准显示行为

### 4. 错误处理
- 完善的错误检查和处理
- 详细的日志输出
- 优雅的降级处理

### 5. 扩展性
- 基于继承的设计，易于扩展
- 支持自定义场景选项
- 模块化的架构

## 注意事项

1. **资源依赖**: 确保 RPG Maker MZ 的核心资源已加载
2. **场景切换**: 使用 SceneManager 进行场景切换，而不是直接创建
3. **内存管理**: 及时清理不需要的场景实例
4. **坐标系统**: 场景对象默认使用 (0, 0) 坐标，不会被自动居中
5. **兼容性**: 基于 RPG Maker MZ v1.8.0 源码实现

## 示例

查看各个创建器文件中的 `createSimple*` 和 `createTest*` 函数获取更多使用示例。
