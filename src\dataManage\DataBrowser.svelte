<script lang="ts">
  // Props
  interface Props {
    onSelect?: (path: string, value: any) => void;
  }

  let { onSelect }: Props = $props();

  // 数据类型映射
  const dataTypes = {
    'actors': '$dataActors',
    'classes': '$dataClasses',
    'skills': '$dataSkills',
    'items': '$dataItems',
    'weapons': '$dataWeapons',
    'armors': '$dataArmors',
    'enemies': '$dataEnemies',
    'troops': '$dataTroops',
    'states': '$dataStates',
    'animations': '$dataAnimations',
    'tilesets': '$dataTilesets',
    'commonEvents': '$dataCommonEvents',
    'system': '$dataSystem',
    'mapInfos': '$dataMapInfos'
  };

  const dataTypeNames = {
    'actors': 'actors',
    'classes': 'classes',
    'skills': 'skills',
    'items': 'items',
    'weapons': 'weapons',
    'armors': 'armors',
    'enemies': 'enemies',
    'troops': 'troops',
    'states': 'states',
    'animations': 'animations',
    'tilesets': 'tilesets',
    'commonEvents': 'commonEvents',
    'system': 'system',
    'mapInfos': 'mapInfos'
  };

  // 当前浏览状态
  let currentPath = $state<string[]>([]);
  let currentData = $state<any>(null);
  let currentItems = $state<any[]>([]);

  // 初始化 - 显示数据类型列表
  $effect(() => {
    if (currentPath.length === 0) {
      currentItems = Object.keys(dataTypes).map(key => ({
        key,
        name: dataTypeNames[key as keyof typeof dataTypeNames],
        type: 'dataType',
        value: null
      }));
    }
  });

  // 获取全局数据
  function getGlobalData(varName: string) {
    try {
      return (window as any)[varName];
    } catch (error) {
      console.warn(`无法获取全局变量: ${varName}`, error);
      return null;
    }
  }

  // 获取值的类型
  function getValueType(value: any): string {
    if (value === null || value === undefined) return 'null';
    if (Array.isArray(value)) return 'array';
    if (typeof value === 'object') return 'object';
    if (typeof value === 'string') return 'string';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'boolean') return 'boolean';
    return 'unknown';
  }

  // 获取显示名称
  function getDisplayName(key: string, value: any): string {
    return key;
  }

  // 获取实际值
  function getActualValue(pathArray: string[]): any {
    if (pathArray.length === 0) return null;

    // 第一个是数据类型
    const dataType = pathArray[0];
    const globalVar = dataTypes[dataType as keyof typeof dataTypes];

    if (!globalVar) return null;

    // 获取全局数据
    let data = getGlobalData(globalVar);
    if (!data) return null;

    // 遍历路径获取值
    for (let i = 1; i < pathArray.length; i++) {
      const key = pathArray[i];

      if (data === null || data === undefined) {
        return null;
      }

      // 如果是数组索引
      if (Array.isArray(data) && /^\d+$/.test(key)) {
        data = data[parseInt(key)];
      } else {
        data = data[key];
      }
    }

    return data;
  }

  // 处理项目点击
  function handleItemClick(item: any) {
    if (item.type === 'dataType') {
      // 点击数据类型，加载对应的数据
      const globalVar = dataTypes[item.key as keyof typeof dataTypes];
      const data = getGlobalData(globalVar);

      if (data) {
        currentPath = [item.key];
        currentData = data;
        loadDataItems(data, item.key);
      }
    } else if (item.type === 'array' || item.type === 'object') {
      // 点击数组或对象，进入下一层
      currentPath = [...currentPath, item.key];
      currentData = item.value;
      loadDataItems(item.value, item.key);
    } else {
      // 点击具体字段，生成绑定表达式
      const pathString = [...currentPath, item.key].join('.');
      const bindingExpression = `{{${pathString}}}`;
      if (onSelect) {
        onSelect(bindingExpression, item.value);
      }
    }
  }

  // 加载数据项
  function loadDataItems(data: any, parentKey: string) {
    const items: any[] = [];

    if (Array.isArray(data)) {
      // 数组：显示索引
      data.forEach((item, index) => {
        if (item !== null && item !== undefined) {
          const type = getValueType(item);
          items.push({
            key: index.toString(),
            name: `[${index}] ${typeof item === 'object' && item.name ? item.name : type}`,
            type,
            value: item
          });
        }
      });
    } else if (typeof data === 'object' && data !== null) {
      // 对象：显示属性
      Object.entries(data).forEach(([key, value]) => {
        const type = getValueType(value);
        const displayName = getDisplayName(key, value);

        items.push({
          key,
          name: displayName,
          type,
          value,
          preview: type === 'string' || type === 'number' || type === 'boolean'
            ? String(value).substring(0, 50)
            : type === 'array'
              ? `[${(value as any[]).length} 项]`
              : type === 'object'
                ? '{对象}'
                : String(value)
        });
      });
    }

    currentItems = items;
  }

  // 返回上一级
  function goBack() {
    if (currentPath.length === 0) return;

    if (currentPath.length === 1) {
      // 返回到数据类型列表
      currentPath = [];
      currentData = null;
      currentItems = Object.keys(dataTypes).map(key => ({
        key,
        name: dataTypeNames[key as keyof typeof dataTypeNames],
        type: 'dataType',
        value: null
      }));
    } else {
      // 返回上一级数据
      currentPath = currentPath.slice(0, -1);

      // 重新加载上级数据
      let data = getGlobalData(dataTypes[currentPath[0] as keyof typeof dataTypes]);
      for (let i = 1; i < currentPath.length; i++) {
        const key = currentPath[i];
        if (Array.isArray(data)) {
          data = data[parseInt(key)];
        } else {
          data = data[key];
        }
      }

      currentData = data;
      loadDataItems(data, currentPath[currentPath.length - 1]);
    }
  }

  // 获取面包屑导航
  function getBreadcrumbs(): string[] {
    if (currentPath.length === 0) return ['数据类型'];

    const breadcrumbs = [dataTypeNames[currentPath[0] as keyof typeof dataTypeNames]];
    for (let i = 1; i < currentPath.length; i++) {
      breadcrumbs.push(currentPath[i]);
    }
    return breadcrumbs;
  }
</script>

<div class="data-browser">
  <!-- 导航栏 -->
  <div class="navigation">
    <div class="breadcrumbs">
      {#each getBreadcrumbs() as crumb, index}
        {#if index > 0}
          <span class="separator">→</span>
        {/if}
        <span class="crumb">{crumb}</span>
      {/each}
    </div>

    {#if currentPath.length > 0}
      <button class="back-button" onclick={goBack}>
        ← 返回
      </button>
    {/if}
  </div>

  <!-- 数据列表 -->
  <div class="data-list">
    {#each currentItems as item}
      <div
        class="data-item"
        class:clickable={item.type === 'dataType' || item.type === 'array' || item.type === 'object'}
        onclick={() => handleItemClick(item)}
      >
        <div class="item-main">
          <span class="item-name">{item.name}</span>
          <span class="item-type">{item.type}</span>
        </div>

        {#if item.preview}
          <div class="item-preview">{item.preview}</div>
        {/if}

        {#if item.type === 'array' || item.type === 'object' || item.type === 'dataType'}
          <div class="item-arrow">→</div>
        {/if}
      </div>
    {/each}

    {#if currentItems.length === 0}
      <div class="empty-state">
        没有可用的数据项
      </div>
    {/if}
  </div>
</div>

<style>
  .data-browser {
    width: 100%;
    height: 400px;
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 6px;
    background: var(--theme-surface-light, #f9fafb);
    display: flex;
    flex-direction: column;
  }

  .navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    background: var(--theme-surface, #ffffff);
  }

  .breadcrumbs {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--theme-text-secondary, #6b7280);
  }

  .separator {
    color: var(--theme-text-secondary, #9ca3af);
  }

  .crumb {
    color: var(--theme-text, #1f2937);
    font-weight: 500;
  }

  .back-button {
    padding: 6px 12px;
    background: var(--theme-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  }

  .back-button:hover {
    background: var(--theme-primary-dark, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  }

  .data-list {
    flex: 1;
    overflow-y: auto;
    padding: 4px;
  }

  .data-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
  }

  .data-item.clickable {
    cursor: pointer;
  }

  .data-item.clickable:hover {
    background: var(--theme-surface, #ffffff);
  }

  .item-main {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
  }

  .item-name {
    font-weight: 600;
    color: var(--theme-text, #1f2937);
  }

  .item-type {
    font-size: 10px;
    color: var(--theme-text-secondary, #4b5563);
    padding: 2px 6px;
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 3px;
    font-weight: 500;
  }

  .item-preview {
    font-size: 12px;
    color: var(--theme-text-secondary, #6b7280);
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-style: italic;
  }

  .item-arrow {
    color: var(--theme-text-secondary, #6b7280);
    font-size: 14px;
    font-weight: bold;
  }

  .empty-state {
    text-align: center;
    color: var(--theme-text-secondary, #6b7280);
    padding: 40px 20px;
    font-style: italic;
    font-size: 14px;
  }

  /* 滚动条样式 */
  .data-list::-webkit-scrollbar {
    width: 8px;
  }

  .data-list::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  .data-list::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    border-radius: 4px;
    border: 1px solid rgba(203, 213, 225, 0.3);
    transition: all 0.2s ease-in-out;
  }

  .data-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
    border-color: rgba(148, 163, 184, 0.5);
    transform: scaleY(1.05);
  }

  .data-list::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  }

  .data-list::-webkit-scrollbar-corner {
    background: transparent;
  }
</style>
