/**
 * 快捷键管理模块 - 响应式版本
 * 处理全局快捷键事件，使用新的响应式状态管理
 */

import { saveOperationRecords } from './filePro';
import {
  sceneModelState,
  getCurrentState,
  clearSelection,
  clearScene
} from './stores/sceneModelStore';
import type { BaseObjectModel } from './type/baseObjectModel.svelte';
import { historyManager } from './historyManager';

/**
 * 快捷键配置接口
 */
interface ShortcutConfig {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  action: () => void;
  description: string;
}

/**
 * 快捷键管理器类
 */
class ShortcutKeyManager {
  private shortcuts: Map<string, ShortcutConfig> = new Map();
  private isInitialized = false;

  /**
   * 初始化快捷键管理器
   */
  init(): void {
    if (this.isInitialized) {
      console.warn('ShortcutKeyManager: 已经初始化过了');
      return;
    }

    console.log('ShortcutKeyManager: 开始初始化快捷键系统');

    // 注册默认快捷键
    this.registerDefaultShortcuts();

    // 添加全局键盘事件监听器
    document.addEventListener('keydown', this.handleKeyDown.bind(this));

    this.isInitialized = true;
    console.log('ShortcutKeyManager: 快捷键系统初始化完成');
  }

  /**
   * 注册默认快捷键
   */
  private registerDefaultShortcuts(): void {
    console.log('ShortcutKeyManager: 注册默认快捷键');

    // Ctrl+S: 收集操作记录
    this.register({
      key: 's',
      ctrl: true,
      action: this.handleGenerateCode.bind(this),
      description: '收集操作记录'
    });



    // Ctrl+Z: 撤销
    this.register({
      key: 'z',
      ctrl: true,
      action: this.handleUndo.bind(this),
      description: '撤销'
    });

    // Ctrl+Y: 重做
    this.register({
      key: 'y',
      ctrl: true,
      action: this.handleRedo.bind(this),
      description: '重做'
    });

    // Ctrl+D: 复制选中对象
    this.register({
      key: 'd',
      ctrl: true,
      action: this.handleCloneSelected.bind(this),
      description: '复制选中对象'
    });

    // Delete: 删除选中对象
    this.register({
      key: 'delete',
      action: this.handleDeleteSelected.bind(this),
      description: '删除选中对象'
    });

    // Escape: 清空选择
    this.register({
      key: 'escape',
      action: this.handleClearSelection.bind(this),
      description: '清空选择'
    });

    // F1: 显示快捷键帮助（这个由页面组件处理，这里只是记录）
    // 注意：F1 键由页面组件直接处理，不在这里注册

    console.log('ShortcutKeyManager: 默认快捷键注册完成');
  }

  /**
   * 注册快捷键
   */
  register(config: ShortcutConfig): void {
    const key = this.getShortcutKey(config);
    this.shortcuts.set(key, config);
    console.log(`ShortcutKeyManager: 注册快捷键 ${key} - ${config.description}`);
  }

  /**
   * 注销快捷键
   */
  unregister(config: Omit<ShortcutConfig, 'action' | 'description'>): void {
    const key = this.getShortcutKey(config);
    if (this.shortcuts.delete(key)) {
      console.log(`ShortcutKeyManager: 注销快捷键 ${key}`);
    }
  }

  /**
   * 生成快捷键标识符
   */
  private getShortcutKey(config: Omit<ShortcutConfig, 'action' | 'description'>): string {
    const parts: string[] = [];

    if (config.ctrl) parts.push('ctrl');
    if (config.alt) parts.push('alt');
    if (config.shift) parts.push('shift');
    parts.push(config.key.toLowerCase());

    return parts.join('+');
  }

  /**
   * 处理键盘按下事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // 检查是否在输入框中
    if (this.isInputElement(event.target as Element)) {
      // 检查是否是历史记录相关的快捷键（Ctrl+Z, Ctrl+Y）
      const isHistoryShortcut = (event.ctrlKey && (event.key.toLowerCase() === 'z' || event.key.toLowerCase() === 'y'));

      if (isHistoryShortcut) {
        // 对于历史记录快捷键，允许在属性面板组件中使用
        console.log('ShortcutKeyManager: 检测到历史记录快捷键，允许在输入元素中执行');
      } else {
        // 其他快捷键在输入框中不处理
        return;
      }
    }

    // 构建当前按键组合
    const currentKey = this.getCurrentKey(event);

    // 查找匹配的快捷键
    const shortcut = this.shortcuts.get(currentKey);
    if (shortcut) {
      console.log(`ShortcutKeyManager: 触发快捷键 ${currentKey} - ${shortcut.description}`);

      // 阻止默认行为
      event.preventDefault();
      event.stopPropagation();

      // 执行快捷键动作
      try {
        shortcut.action();
      } catch (error) {
        console.error(`ShortcutKeyManager: 执行快捷键 ${currentKey} 时出错:`, error);
      }
    } else {
      // 调试：显示未匹配的按键组合（可以在开发时启用）
      // console.log(`ShortcutKeyManager: 未匹配的按键组合: ${currentKey}`);
    }
  }

  /**
   * 获取当前按键组合
   */
  private getCurrentKey(event: KeyboardEvent): string {
    const parts: string[] = [];

    if (event.ctrlKey) parts.push('ctrl');
    if (event.altKey) parts.push('alt');
    if (event.shiftKey) parts.push('shift');
    parts.push(event.key.toLowerCase());

    return parts.join('+');
  }

  /**
   * 检查是否在输入元素中
   */
  private isInputElement(element: Element | null): boolean {
    if (!element) return false;

    const tagName = element.tagName.toLowerCase();

    // 检查基本输入元素
    const isBasicInput = tagName === 'input' || tagName === 'textarea' || tagName === 'select';

    // 检查contenteditable元素
    const isContentEditable = element.getAttribute('contenteditable') === 'true';

    // 🎯 检查是否在属性面板中（属性面板中的组件应该允许历史记录快捷键）
    const isInPropertyPanel = element.closest('.property-panel') !== null ||
                             element.closest('.property-section') !== null ||
                             element.closest('.property-row') !== null ||
                             element.closest('.property-item-horizontal') !== null;

    // 检查是否在各种输入容器中
    const isInInputContainer =
      element.closest('.MuiTextField-root') !== null ||
      element.closest('[contenteditable="true"]') !== null ||
      element.closest('.text-input') !== null ||           // 自定义文本输入框
      element.closest('.prefix-suffix-input') !== null ||  // 前缀后缀输入框
      element.closest('.custom-input') !== null ||         // 自定义输入框
      element.closest('input') !== null ||                 // 任何input元素的子元素
      element.closest('textarea') !== null;                // 任何textarea元素的子元素

    // 检查元素是否有输入相关的类名或属性
    const hasInputClass = element.classList.contains('text-input') ||
                         element.classList.contains('prefix-suffix-input') ||
                         element.classList.contains('custom-input');

    // 检查是否是可编辑的元素
    const isEditable = element.hasAttribute('contenteditable') ||
                      (element as HTMLElement).isContentEditable;

    // 检查input type（排除一些不需要文本输入的类型）
    let isTextInput = true;
    if (tagName === 'input') {
      const inputType = (element as HTMLInputElement).type.toLowerCase();
      const nonTextTypes = ['button', 'submit', 'reset', 'checkbox', 'radio', 'file', 'image', 'range', 'color'];
      isTextInput = !nonTextTypes.includes(inputType);
    }

    // 🎯 如果在属性面板中，只有真正的文本输入才算作输入元素
    if (isInPropertyPanel) {
      const result = (isBasicInput && isTextInput && tagName === 'input' &&
                     ['text', 'number', 'email', 'password', 'search', 'tel', 'url'].includes((element as HTMLInputElement).type)) ||
                     isContentEditable || isEditable;

      if (result) {
        console.log('ShortcutKeyManager: 检测到属性面板中的文本输入元素', {
          tagName,
          inputType: (element as HTMLInputElement).type,
          isInPropertyPanel
        });
      }

      return result;
    }

    // 对于非属性面板的元素，使用原来的逻辑
    const result = (isBasicInput && isTextInput) || isContentEditable || isInInputContainer || hasInputClass || isEditable;

    // 调试日志（可以在开发时启用）
    if (result) {
      console.log('ShortcutKeyManager: 检测到输入元素，跳过快捷键处理', {
        tagName,
        isBasicInput,
        isTextInput,
        isContentEditable,
        isInInputContainer,
        hasInputClass,
        isEditable,
        isInPropertyPanel,
        element
      });
    }

    return result;
  }

  /**
   * 处理生成代码快捷键 (Ctrl+S)
   */
  private async handleGenerateCode(): Promise<void> {
    console.log('ShortcutKeyManager: 执行收集操作记录操作 (Ctrl+S)');

    try {
      // 1. 从响应式状态管理中获取当前场景
      const currentState = getCurrentState();

      if (!currentState.currentScene) {
        console.warn('没有当前场景，无法收集操作记录');
        return;
      }

      console.log('当前场景:', currentState.currentScene);
      console.log('选中对象数量:', currentState.selectedObjects.length);

      console.log('=== 操作记录收集完成 ===');

      // 3. 保存操作记录（使用 filePro 统一处理）
      await saveOperationRecords();

    } catch (error) {
      console.error('ShortcutKeyManager: 收集操作记录时出错:', error);
    }
  }



  /**
   * 处理复制选中对象快捷键 (Ctrl+D)
   */
  private handleCloneSelected(): void {
    console.log('ShortcutKeyManager: 复制选中对象 (Ctrl+D)');

    try {
      const currentState = getCurrentState();

      if (currentState.selectedObjects.length === 0) {
        console.log('没有选中的对象');
        return;
      }

      const selectedObject = currentState.selectedObjects[0]; // 只复制第一个选中的对象
      console.log('准备复制对象:', selectedObject.className);
      console.log('选中对象的父级:', selectedObject.parent?.className || 'null');

      // 🎯 修复：使用带历史记录的复制方法
      if (typeof selectedObject.cloneWithHistory === 'function') {
        const clonedObject = selectedObject.cloneWithHistory();

        if (clonedObject) {
          console.log('🔄 对象复制成功（已记录历史）:', clonedObject.className);

          // 🎯 cloneWithHistory() 已经处理了所有的父子关系和历史记录
          // 不需要再手动添加到父节点，因为 cloneWithHistory 已经做了

          // 选中新复制的对象
          import('./stores/sceneModelStore').then(({ selectObject }) => {
            selectObject(clonedObject);
            console.log('✅ 已选中复制的对象:', clonedObject.className);
          });
        } else {
          console.warn('❌ 对象复制失败');
        }
      } else if (typeof selectedObject.clone === 'function') {
        // 🎯 Fallback：如果没有 cloneWithHistory 方法，使用普通的 clone 方法
        console.warn('⚠️ 对象没有 cloneWithHistory 方法，使用普通 clone（无历史记录）');
        const clonedObject = selectedObject.clone();
        console.log('🔄 对象复制成功（无历史记录）:', clonedObject.className);

        // 手动处理父子关系（因为普通clone不会自动处理）
        const parentModel = selectedObject.parent;
        if (parentModel) {
          parentModel.children.push(clonedObject);
          clonedObject.parent = parentModel;

          // 同步到原始对象
          const parentOriginal = parentModel.getOriginalObject();
          const clonedOriginal = clonedObject.getOriginalObject();
          if (parentOriginal && clonedOriginal && typeof parentOriginal.addChild === 'function') {
            parentOriginal.addChild(clonedOriginal);
          }
        }

        // 选中新复制的对象
        import('./stores/sceneModelStore').then(({ selectObject }) => {
          selectObject(clonedObject);
        });
      } else {
        console.warn('选中的对象没有 clone 或 cloneWithHistory 方法:', selectedObject.className);
      }

    } catch (error) {
      console.error('ShortcutKeyManager: 复制对象时出错:', error);
    }
  }

  /**
   * 处理删除选中对象快捷键 (Delete)
   */
  private handleDeleteSelected(): void {
    console.log('ShortcutKeyManager: 删除选中对象 (Delete)');

    try {
      const currentState = getCurrentState();

      if (currentState.selectedObjects.length === 0) {
        console.log('没有选中的对象');
        return;
      }

      console.log(`准备删除 ${currentState.selectedObjects.length} 个对象`);

      // 删除所有选中的对象
      for (const selectedObject of currentState.selectedObjects) {
        this.deleteObject(selectedObject);
      }

      // 清空选择
      clearSelection();

      console.log('✅ 对象删除操作完成');
    } catch (error) {
      console.error('ShortcutKeyManager: 删除选中对象时出错:', error);
    }
  }

  /**
   * 删除单个对象
   * @param objectToDelete 要删除的对象
   */
  private deleteObject(objectToDelete: BaseObjectModel): void {
    console.log(`🗑️ 删除对象: ${objectToDelete.className}`);

    try {
      // 检查是否为Scene根节点
      if (objectToDelete.className.includes('Scene')) {
        console.warn('⚠️ 不能删除场景根节点');
        return;
      }

      // 🎯 修复：使用带历史记录的删除方法
      if (typeof objectToDelete.destroyWithHistory === 'function') {
        objectToDelete.destroyWithHistory();
        console.log(`✅ 对象删除完成（已记录历史）: ${objectToDelete.className}`);
      } else {
        // Fallback：使用普通的destroy方法
        console.warn('⚠️ 对象没有 destroyWithHistory 方法，使用普通 destroy（无历史记录）');
        objectToDelete.destroy();
        console.log(`✅ 对象删除完成（无历史记录）: ${objectToDelete.className}`);
      }

    } catch (error) {
      console.error(`❌ 删除对象失败: ${objectToDelete.className}`, error);
    }
  }

  /**
   * 处理清空选择快捷键 (Escape)
   */
  private handleClearSelection(): void {
    console.log('ShortcutKeyManager: 清空选择 (Escape)');

    try {
      clearSelection();
      console.log('选择已清空');
    } catch (error) {
      console.error('ShortcutKeyManager: 清空选择时出错:', error);
    }
  }

  /**
   * 获取所有已注册的快捷键
   */
  getRegisteredShortcuts(): Array<{ key: string; description: string }> {
    return Array.from(this.shortcuts.entries()).map(([key, config]) => ({
      key,
      description: config.description
    }));
  }

  /**
   * 处理撤销快捷键 (Ctrl+Z)
   */
  private handleUndo(): void {
    console.log('ShortcutKeyManager: 撤销操作 (Ctrl+Z)');

    try {
      const success = historyManager.undo();

      if (success) {
        const state = historyManager.getState();
        console.log('✅ 撤销成功', {
          canUndo: state.canUndo,
          canRedo: state.canRedo,
          lastOperation: state.lastOperation
        });
      } else {
        console.log('⚠️ 没有可撤销的操作');
      }
    } catch (error) {
      console.error('❌ 撤销操作失败:', error);
    }
  }

  /**
   * 处理重做快捷键 (Ctrl+Y)
   */
  private handleRedo(): void {
    console.log('ShortcutKeyManager: 重做操作 (Ctrl+Y)');

    try {
      const success = historyManager.redo();

      if (success) {
        const state = historyManager.getState();
        console.log('✅ 重做成功', {
          canUndo: state.canUndo,
          canRedo: state.canRedo,
          lastOperation: state.lastOperation
        });
      } else {
        console.log('⚠️ 没有可重做的操作');
      }
    } catch (error) {
      console.error('❌ 重做操作失败:', error);
    }
  }

  /**
   * 销毁快捷键管理器
   */
  destroy(): void {
    if (!this.isInitialized) return;

    console.log('ShortcutKeyManager: 销毁快捷键系统');

    // 移除事件监听器
    document.removeEventListener('keydown', this.handleKeyDown.bind(this));

    // 清空快捷键
    this.shortcuts.clear();

    this.isInitialized = false;
    console.log('ShortcutKeyManager: 快捷键系统已销毁');
  }
}

/**
 * 全局快捷键管理器实例
 */
const shortcutKeyManager = new ShortcutKeyManager();

/**
 * 初始化快捷键系统
 */
export function initShortcutKeys(): void {
  shortcutKeyManager.init();
}

/**
 * 注册快捷键
 */
export function registerShortcut(config: ShortcutConfig): void {
  shortcutKeyManager.register(config);
}

/**
 * 注销快捷键
 */
export function unregisterShortcut(config: Omit<ShortcutConfig, 'action' | 'description'>): void {
  shortcutKeyManager.unregister(config);
}

/**
 * 获取所有已注册的快捷键
 */
export function getRegisteredShortcuts(): Array<{ key: string; description: string }> {
  return shortcutKeyManager.getRegisteredShortcuts();
}

/**
 * 销毁快捷键系统
 */
export function destroyShortcutKeys(): void {
  shortcutKeyManager.destroy();
}

/**
 * 默认导出
 */
export default {
  init: initShortcutKeys,
  register: registerShortcut,
  unregister: unregisterShortcut,
  getRegisteredShortcuts,
  destroy: destroyShortcutKeys
};

// 模块加载时的信息输出
console.log('ShortcutKey 模块已加载 - 响应式版本');
console.log('使用 initShortcutKeys() 来初始化快捷键系统');
console.log('默认快捷键:');
console.log('  Ctrl+S - 收集操作记录');
console.log('  Ctrl+T - 创建测试场景');
console.log('  Ctrl+D - 复制选中对象');
console.log('  Delete - 删除选中对象');
console.log('  Escape - 清空选择');
console.log('  F1     - 显示/隐藏快捷键帮助');