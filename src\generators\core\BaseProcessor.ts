/**
 * 基础处理器抽象类
 * 定义所有处理器的通用接口和基础实现
 */

import { type BaseDisplayProperties } from '../types/base';

/**
 * 基础处理器抽象类
 */
export abstract class BaseProcessor<T extends BaseDisplayProperties> {

  /**
   * 序列化对象为数据属性
   * @param obj 要序列化的对象
   * @returns 序列化后的数据属性
   */
  abstract serialize(obj: any): T;

  /**
   * 反序列化数据属性为对象
   * @param data 数据属性
   * @returns 反序列化后的对象
   */
  abstract deserialize(data: T): any;

  /**
   * 生成对象创建代码
   * @param varName 变量名
   * @param data 数据属性
   * @param indent 缩进字符串
   * @returns 生成的代码字符串
   */
  abstract generateCode(varName: string, data: T, indent: string): string;

  /**
   * 提取基础显示对象属性（通用方法）
   * @param obj 对象
   * @returns 基础属性
   */
  protected extractBaseProperties(obj: any): BaseDisplayProperties {
    return {
      className: obj.constructor.name,
      name: obj.name || '',
      // 位置和变换
      x: obj.x || 0,
      y: obj.y || 0,
      scaleX: obj.scale?.x || 1,
      scaleY: obj.scale?.y || 1,
      skewX: obj.skew?.x || 0,
      skewY: obj.skew?.y || 0,
      rotation: obj.rotation || 0,
      width: obj.width || 0,
      height: obj.height || 0,
      // 显示属性
      alpha: obj.alpha !== undefined ? obj.alpha : 1,
      visible: obj.visible !== undefined ? obj.visible : true,
      // 锚点和轴心
      anchorX: obj.anchor?.x || 0,
      anchorY: obj.anchor?.y || 0,
      pivotX: obj.pivot?.x || 0,
      pivotY: obj.pivot?.y || 0,
    };
  }

  /**
   * 生成基础属性设置代码（通用方法）
   * @param varName 变量名
   * @param data 数据属性
   * @param indent 缩进字符串
   * @returns 基础属性设置代码
   */
  protected generateBasePropertiesCode(varName: string, data: BaseDisplayProperties, indent: string): string {
    const codes: string[] = [];

    // 位置和尺寸
    codes.push(`${indent}${varName}.x = ${data.x || 0};`);
    codes.push(`${indent}${varName}.y = ${data.y || 0};`);
    codes.push(`${indent}${varName}.width = ${data.width || 0};`);
    codes.push(`${indent}${varName}.height = ${data.height || 0};`);

    // 锚点
    codes.push(`${indent}${varName}.anchor.x = ${data.anchorX || 0};`);
    codes.push(`${indent}${varName}.anchor.y = ${data.anchorY || 0};`);

    // 缩放
    codes.push(`${indent}${varName}.scale.x = ${data.scaleX || 1};`);
    codes.push(`${indent}${varName}.scale.y = ${data.scaleY || 1};`);

    // 其他属性
    codes.push(`${indent}${varName}.alpha = ${data.alpha !== undefined ? data.alpha : 1};`);
    codes.push(`${indent}${varName}.visible = ${data.visible !== undefined ? data.visible : true};`);
    codes.push(`${indent}${varName}.rotation = ${data.rotation || 0};`);

    return codes.join('\n');
  }

  /**
   * 创建对象实例（通用方法）
   * @param className 类名
   * @returns 对象实例
   */
  protected createObject(className: string): any {
    // 检查是否在RPG Maker MZ环境中
    if (typeof window !== 'undefined' && (window as any)[className]) {
      return new (window as any)[className]();
    }

    // 模拟环境下返回基础结构
    return this.createMockObject(className);
  }

  /**
   * 创建模拟对象（用于测试环境）
   * @param className 类名
   * @returns 模拟对象
   */
  protected createMockObject(className: string): any {
    return {
      constructor: { name: className },
      x: 0, y: 0,
      scale: { x: 1, y: 1 },
      skew: { x: 0, y: 0 },
      rotation: 0,
      width: 0, height: 0,
      alpha: 1, visible: true,
      anchor: { x: 0, y: 0 },
      pivot: { x: 0, y: 0 },
      name: ''
    };
  }
}
