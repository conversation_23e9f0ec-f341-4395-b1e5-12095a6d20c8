/**
 * 历史记录系统入口文件
 * 提供全局的历史记录管理器实例
 */

import { HistoryManager } from './HistoryManager';
import type { HistoryConfig } from './types';

// 全局历史记录管理器实例
let globalHistoryManager: HistoryManager | null = null;

/**
 * 初始化全局历史记录管理器
 */
export function initializeHistoryManager(config?: Partial<HistoryConfig>): HistoryManager {
  if (globalHistoryManager) {
    console.warn('🕰️ HistoryManager: 全局实例已存在，将重新初始化');
  }

  globalHistoryManager = new HistoryManager(config);

  console.log('🕰️ HistoryManager: 全局实例初始化完成');
  return globalHistoryManager;
}

/**
 * 获取全局历史记录管理器实例
 */
export function getHistoryManager(): HistoryManager {
  if (!globalHistoryManager) {
    console.warn('🕰️ HistoryManager: 全局实例未初始化，使用默认配置创建');
    globalHistoryManager = new HistoryManager();
  }

  return globalHistoryManager;
}

/**
 * 销毁全局历史记录管理器
 */
export function destroyHistoryManager(): void {
  if (globalHistoryManager) {
    globalHistoryManager.clear();
    globalHistoryManager = null;
    console.log('🕰️ HistoryManager: 全局实例已销毁');
  }
}

// 便捷方法：直接操作全局实例
export const historyManager = {
  /**
   * 开始操作组
   */
  startGroup: (name: string, description?: string) => {
    getHistoryManager().startGroup(name, description);
  },

  /**
   * 记录变更
   */
  recordChange: (modelObject: any, fieldName: string, oldValue: any, newValue: any) => {
    getHistoryManager().recordChange(modelObject, fieldName, oldValue, newValue);
  },

  /**
   * 结束操作组
   */
  endGroup: () => {
    getHistoryManager().endGroup();
  },

  /**
   * 撤销
   */
  undo: () => {
    return getHistoryManager().undo();
  },

  /**
   * 重做
   */
  redo: () => {
    return getHistoryManager().redo();
  },

  /**
   * 清空历史记录
   */
  clear: () => {
    getHistoryManager().clear();
  },

  /**
   * 获取状态
   */
  getState: () => {
    return getHistoryManager().getState();
  },

  /**
   * 是否正在记录
   */
  isRecording: () => {
    return getHistoryManager().isRecording();
  },

  /**
   * 暂停记录
   */
  pauseRecording: () => {
    getHistoryManager().pauseRecording();
  },

  /**
   * 恢复记录
   */
  resumeRecording: () => {
    getHistoryManager().resumeRecording();
  },

  /**
   * 添加监听器
   */
  addListener: (listener: any) => {
    getHistoryManager().addListener(listener);
  },

  /**
   * 移除监听器
   */
  removeListener: (listener: any) => {
    getHistoryManager().removeListener(listener);
  },


};

// 导出类型和类
export { HistoryManager } from './HistoryManager';
export type {
  HistoryEntry,
  OperationGroup,
  HistoryConfig,
  HistoryEvent,
  HistoryListener
} from './types';
