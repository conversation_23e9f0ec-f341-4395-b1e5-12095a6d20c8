import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * UIButton模型类 - 使用绑定机制
 */
export class ButtonModel extends BaseObjectModel {
    // 组件类型
    public readonly componentType = 'UIButton';

    // 🔑 子组件绑定属性（所有组件都由外部绑定）
    boundDefaultSprite = $state(null);     // 绑定的默认状态精灵 (UIImage)
    boundHoverSprite = $state(null);       // 绑定的悬停状态精灵 (UIImage)
    boundPressedSprite = $state(null);     // 绑定的按下状态精灵 (UIImage)

    // 状态配置
    enabled = $state(true);

    // 事件代码属性
    onClickCode = $state('');       // 点击事件代码
    onHoverCode = $state('');       // 悬停事件代码
    onHoverOutCode = $state('');    // 离开事件代码
    onPressCode = $state('');       // 按下事件代码
    onReleaseCode = $state('');     // 释放事件代码
    onDoubleClickCode = $state(''); // 双击事件代码

    constructor(button: any = {}) {
        super(button);

        // 初始化绑定属性
        this.boundDefaultSprite = button.boundDefaultSprite || null;
        this.boundHoverSprite = button.boundHoverSprite || null;
        this.boundPressedSprite = button.boundPressedSprite || null;

        // 初始化状态
        this.enabled = button.enabled !== false;

        // 初始化事件代码
        this.onClickCode = button.onClickCode || button._eventCodes?.onClick || '';
        this.onHoverCode = button.onHoverCode || button._eventCodes?.onHover || '';
        this.onHoverOutCode = button.onHoverOutCode || button._eventCodes?.onHoverOut || '';
        this.onPressCode = button.onPressCode || button._eventCodes?.onPress || '';
        this.onReleaseCode = button.onReleaseCode || button._eventCodes?.onRelease || '';
        this.onDoubleClickCode = button.onDoubleClickCode || button._eventCodes?.onDoubleClick || '';

        console.log('🔘 ButtonModel: 创建按钮模型', this);
    }

    /**
     * 设置Button特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Button特有的属性
     */
    protected setupSpecificSync(): void {
        try {
            // 🔧 同步绑定的子组件
            if (this._originalObject.bindDefaultSprite && this.boundDefaultSprite) {
                this._originalObject.bindDefaultSprite(this.boundDefaultSprite);
            }
            if (this._originalObject.bindHoverSprite && this.boundHoverSprite) {
                this._originalObject.bindHoverSprite(this.boundHoverSprite);
            }
            if (this._originalObject.bindPressedSprite && this.boundPressedSprite) {
                this._originalObject.bindPressedSprite(this.boundPressedSprite);
            }

            // 同步状态
            if (this._originalObject.setEnabled && typeof this._originalObject.setEnabled === 'function') {
                this._originalObject.setEnabled(this.enabled);
            } else {
                this._originalObject.enabled = this.enabled;
            }

            // 同步事件代码
            if (this._originalObject.setEventCode && typeof this._originalObject.setEventCode === 'function') {
                this._originalObject.setEventCode('onClick', this.onClickCode);
                this._originalObject.setEventCode('onHover', this.onHoverCode);
                this._originalObject.setEventCode('onHoverOut', this.onHoverOutCode);
                this._originalObject.setEventCode('onPress', this.onPressCode);
                this._originalObject.setEventCode('onRelease', this.onReleaseCode);
                this._originalObject.setEventCode('onDoubleClick', this.onDoubleClickCode);
            }

        } catch (error) {
            console.error('ButtonModel: 同步失败', error);
        }
    }

    /**
     * 获取按钮信息用于显示
     */
    getButtonInfo(): {
        bindings: {
            boundDefaultSprite: any;
            boundHoverSprite: any;
            boundPressedSprite: any;
        };
        enabled: boolean;
        events: {
            onClickCode: string;
            onHoverCode: string;
            onHoverOutCode: string;
            onPressCode: string;
            onReleaseCode: string;
            onDoubleClickCode: string;
        };
        size: { width: number; height: number };
    } {
        return {
            bindings: {
                boundDefaultSprite: this.boundDefaultSprite,
                boundHoverSprite: this.boundHoverSprite,
                boundPressedSprite: this.boundPressedSprite
            },
            enabled: this.enabled,
            events: {
                onClickCode: this.onClickCode,
                onHoverCode: this.onHoverCode,
                onHoverOutCode: this.onHoverOutCode,
                onPressCode: this.onPressCode,
                onReleaseCode: this.onReleaseCode,
                onDoubleClickCode: this.onDoubleClickCode
            },
            size: {
                width: this.width,
                height: this.height
            }
        };
    }



    /**
     * 序列化为JSON
     */
    toJSON(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            alpha: this.alpha,
            visible: this.visible,
            rotation: this.rotation,
            zIndex: this.zIndex,
            scaleX: this.scaleX,
            scaleY: this.scaleY,
            // Button特有属性
            componentType: this.componentType,
            boundDefaultSprite: this.boundDefaultSprite,
            boundHoverSprite: this.boundHoverSprite,
            boundPressedSprite: this.boundPressedSprite,
            enabled: this.enabled,
            _eventCodes: {
                onClick: this.onClickCode,
                onHover: this.onHoverCode,
                onHoverOut: this.onHoverOutCode,
                onPress: this.onPressCode,
                onRelease: this.onReleaseCode,
                onDoubleClick: this.onDoubleClickCode
            }
        };
    }

    /**
     * 从JSON反序列化
     */
    static fromJSON(data: any): ButtonModel {
        return new ButtonModel(data);
    }

    /**
     * 克隆方法（实现抽象方法）- 调用插件的 clone 方法
     */
    clone(): ButtonModel {
        console.log('🔄 ButtonModel: 开始克隆Button对象（调用插件方法）');

        // 1. 调用原始 UIButton 对象的 clone 方法
        const originalUIButton = this.getOriginalObject();
        if (!originalUIButton || typeof originalUIButton.clone !== 'function') {
            console.error('❌ ButtonModel: 原始对象没有 clone 方法');
            throw new Error('UIButton 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUIButton = originalUIButton.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 ButtonModel 包装克隆的对象
        const clonedModel = new ButtonModel(clonedUIButton);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ ButtonModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }





    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const lines: string[] = [];

        // 生成构造函数调用
        lines.push(`${indent}const ${varName} = new UIButton({`);
        lines.push(`${indent}    x: ${this.x},`);
        lines.push(`${indent}    y: ${this.y},`);
        lines.push(`${indent}    width: ${this.width},`);
        lines.push(`${indent}    height: ${this.height},`);
        lines.push(`${indent}    enabled: ${this.enabled}`);
        lines.push(`${indent}});`);

        return lines.join('\n');
    }

    /**
     * 重写特定属性设置代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns Button特定属性设置代码
     */
    protected generateSpecificProperties(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 不在这里生成绑定代码，因为子对象还没创建
        // 绑定代码将在 generateBindingCode() 中生成

        // 事件代码设置
        if (this.onClickCode) {
            codes.push(`${indent}// 点击事件`);
            codes.push(`${indent}${varName}._eventCodes.onClick = '${this.onClickCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onHoverCode) {
            codes.push(`${indent}// 悬停事件`);
            codes.push(`${indent}${varName}._eventCodes.onHover = '${this.onHoverCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onHoverOutCode) {
            codes.push(`${indent}// 离开事件`);
            codes.push(`${indent}${varName}._eventCodes.onHoverOut = '${this.onHoverOutCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onPressCode) {
            codes.push(`${indent}// 按下事件`);
            codes.push(`${indent}${varName}._eventCodes.onPress = '${this.onPressCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onReleaseCode) {
            codes.push(`${indent}// 释放事件`);
            codes.push(`${indent}${varName}._eventCodes.onRelease = '${this.onReleaseCode.replace(/'/g, "\\'")}';`);
        }

        if (this.onDoubleClickCode) {
            codes.push(`${indent}// 双击事件`);
            codes.push(`${indent}${varName}._eventCodes.onDoubleClick = '${this.onDoubleClickCode.replace(/'/g, "\\'")}';`);
        }

        return codes.join('\n');
    }

    /**
     * 重写代码生成方法，确保正确的生成顺序
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 生成的代码字符串
     */
    public generateCreationCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        // 1. 对象创建代码
        codes.push(this.generateObjectCreation(varName, indent));

        // 2. 基础属性设置
        codes.push(this.generateBasicProperties(varName, indent));

        // 3. 特定属性设置（不包含绑定）
        codes.push(this.generateSpecificProperties(varName, indent));

        // 4. 子对象代码生成
        if (this.generateChildrenCode) {
            codes.push(this.generateChildrenCreation(varName, indent));
        }

        // 5. 🔑 绑定代码（在子对象创建之后）
        codes.push(this.generateBindingCode(varName, indent));

        return codes.filter(code => code.trim()).join('\n');
    }

    /**
     * 生成绑定代码（在子对象创建之后调用）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 绑定代码
     */
    protected generateBindingCode(varName: string, indent: string): string {
        const codes: string[] = [];

        // 🔑 辅助函数：查找绑定对象对应的子对象变量名
        const findChildVarName = (boundObject: any): string | null => {
            if (!boundObject) return null;

            // 在子对象中查找匹配的对象
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child.getOriginalObject() === boundObject) {
                    return `${varName}_child${i}`;
                }
            }
            return null;
        };

        // 检查是否有任何绑定，如果有则添加注释
        if (this.boundDefaultSprite || this.boundHoverSprite || this.boundPressedSprite) {
            codes.push(`${indent}// 绑定子组件`);
        }

        // 绑定子组件（如果有的话）
        if (this.boundDefaultSprite) {
            const childVarName = findChildVarName(this.boundDefaultSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定默认状态精灵 (UIImage)`);
                codes.push(`${indent}${varName}.bindDefaultSprite(${childVarName});`);
            }
        }

        if (this.boundHoverSprite) {
            const childVarName = findChildVarName(this.boundHoverSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定悬停状态精灵 (UIImage)`);
                codes.push(`${indent}${varName}.bindHoverSprite(${childVarName});`);
            }
        }

        if (this.boundPressedSprite) {
            const childVarName = findChildVarName(this.boundPressedSprite);
            if (childVarName) {
                codes.push(`${indent}// 绑定按下状态精灵 (UIImage)`);
                codes.push(`${indent}${varName}.bindPressedSprite(${childVarName});`);
            }
        }

        return codes.join('\n');
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UIButton 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UIButton 特有属性
            enabled: this.enabled,

            // 事件代码
            _eventCodes: {
                onClick: this.onClickCode,
                onPress: this.onPressCode,
                onRelease: this.onReleaseCode,
                onHover: this.onHoverCode,
                onHoverOut: this.onHoverOutCode
            }
        };
    }

}

// 注册ButtonModel到基类容器
BaseObjectModel.registerModel('UIButton', ButtonModel);
BaseObjectModel.registerModel('Button', ButtonModel);
