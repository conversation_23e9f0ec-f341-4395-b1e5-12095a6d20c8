# 历史记录系统

基于模型对象、变化字段和历史值的撤销/重做系统。

## 核心特性

- ✅ **简单直接**: 保存模型对象引用、字段名和历史值
- ✅ **响应式兼容**: 直接设置对象属性，自动触发UI更新
- ✅ **操作组支持**: 一次操作涉及多个字段时，统一撤销/重做
- ✅ **内存高效**: 只保存必要信息，不保存完整对象状态
- ✅ **对象生命周期**: 自动检查对象有效性，处理已删除对象
- ✅ **事件系统**: 支持监听撤销/重做事件
- ✅ **智能拖拽检测**: 鼠标按下时延迟记录，抬起时统一提交，避免拖拽过程中产生大量历史记录

## 基本用法

### 1. 初始化

```typescript
import { initializeHistoryManager } from './historyManager';

// 初始化（通常在应用启动时）
initializeHistoryManager({
  maxHistorySize: 100,
  enabled: true,
  autoRecord: true,
  debug: true
});
```

### 2. 记录单个字段变更

```typescript
import { historyManager } from './historyManager';

// 在BaseObjectModel的setter中
set x(value: number) {
  const oldValue = this.#x;

  // 记录历史
  if (historyManager.isRecording()) {
    historyManager.recordChange(this, 'x', oldValue, value);
  }

  this.#x = value;
}
```

### 3. 记录操作组（多个字段）

```typescript
// 移动对象（涉及x和y两个字段）
function moveObject(obj: BaseObjectModel, newX: number, newY: number) {
  historyManager.startGroup('移动对象', `移动${obj.className}到(${newX}, ${newY})`);

  obj.x = newX;  // 自动记录
  obj.y = newY;  // 自动记录

  historyManager.endGroup();
}
```

### 4. 撤销/重做

```typescript
// 撤销
const success = historyManager.undo();
if (success) {
  console.log('撤销成功');
}

// 重做
const success = historyManager.redo();
if (success) {
  console.log('重做成功');
}

// 获取状态
const state = historyManager.getState();
console.log('可撤销:', state.canUndo);
console.log('可重做:', state.canRedo);
```

### 5. 事件监听

```typescript
historyManager.addListener((event) => {
  switch (event.type) {
    case 'undo':
      console.log('执行了撤销:', event.operation?.name);
      break;
    case 'redo':
      console.log('执行了重做:', event.operation?.name);
      break;
    case 'record':
      console.log('记录了操作:', event.operation?.name);
      break;
  }
});
```

### 6. 智能拖拽检测

系统会自动检测鼠标状态，在拖拽过程中优化历史记录：

```typescript
// 拖拽过程示例
obj.x = 10;  // 鼠标按下，记录起始值 0，不立即提交
obj.x = 15;  // 拖拽中，更新当前值，不记录
obj.x = 20;  // 拖拽中，更新当前值，不记录
obj.y = 30;  // 拖拽中，记录y的起始值 0
obj.y = 35;  // 拖拽中，更新y的当前值

// 鼠标抬起时，自动创建操作组：
// "拖拽操作" {
//   x: 0 → 20
//   y: 0 → 35
// }

// 获取鼠标状态
const isMouseDown = historyManager.getMouseState();
const pendingCount = historyManager.getPendingChangesCount();

// 手动刷新待处理变更（特殊情况下使用）
historyManager.flushPendingChanges();
```

## 在BaseObjectModel中集成

```typescript
// 在BaseObjectModel中添加历史记录支持
import { historyManager } from '../historyManager';

class BaseObjectModel {
  // 在每个需要记录的属性setter中添加
  set x(value: number) {
    const oldValue = this.#x;

    if (historyManager.isRecording() && oldValue !== value) {
      historyManager.recordChange(this, 'x', oldValue, value);
    }

    this.#x = value;
  }

  // 批量更新方法
  updatePosition(newX: number, newY: number) {
    historyManager.startGroup('移动对象');
    this.x = newX;
    this.y = newY;
    historyManager.endGroup();
  }

  updateSize(newWidth: number, newHeight: number) {
    historyManager.startGroup('调整大小');
    this.width = newWidth;
    this.height = newHeight;
    historyManager.endGroup();
  }
}
```

## 快捷键集成

```typescript
// 在shortcutKey.ts中添加
this.register({
  key: 'z',
  ctrl: true,
  action: () => {
    const success = historyManager.undo();
    if (success) {
      console.log('撤销成功');
    }
  },
  description: '撤销'
});

this.register({
  key: 'y',
  ctrl: true,
  action: () => {
    const success = historyManager.redo();
    if (success) {
      console.log('重做成功');
    }
  },
  description: '重做'
});
```

## 配置选项

```typescript
interface HistoryConfig {
  maxHistorySize: number;    // 最大历史记录数量（默认100）
  enabled: boolean;          // 是否启用（默认true）
  autoRecord: boolean;       // 是否自动记录（默认true）
  debug: boolean;           // 调试模式（默认false）
}
```

## 注意事项

1. **对象生命周期**: 系统会自动检查对象是否仍然有效
2. **内存管理**: 历史记录有数量限制，超出会自动清理最旧的记录
3. **撤销时暂停记录**: 撤销/重做操作本身不会被记录到历史中
4. **操作组**: 复杂操作建议使用操作组，确保撤销的原子性

## 扩展功能

- 可以添加操作描述，提供更好的用户体验
- 可以添加历史记录UI，显示操作列表
- 可以添加分支历史，支持多条历史线
- 可以添加持久化，保存历史记录到本地存储
