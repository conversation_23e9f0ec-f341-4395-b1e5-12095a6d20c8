/**
 * 路径处理工具类
 * 统一处理所有路径相关的逻辑
 */

/**
 * 路径处理工具
 */
export class PathUtils {
  
  // 预编译正则表达式，提高性能
  private static readonly PATTERNS = {
    IMG_PATH: /img\//,
    WINDOWS_SEPARATOR: /\\/g,
    FILE_EXTENSIONS: /\.(png|jpg|jpeg|gif)$/i
  };
  
  // 目录映射缓存
  private static readonly DIRECTORY_MAPPING = {
    enemy: 'enemies',
    monster: 'enemies',
    face: 'faces',
    icon: 'system',
    title: 'titles1',
    default: 'characters'
  } as const;
  
  /**
   * 转换为相对路径格式 (img/xxx/xxx.png)
   * @param absolutePath 绝对路径或任意路径
   * @returns 相对路径格式
   */
  static convertToRelativePath(absolutePath: string): string {
    if (!absolutePath) {
      return '';
    }

    // 如果已经是相对路径格式（以 img/ 开头），直接返回
    if (absolutePath.startsWith('img/')) {
      return absolutePath;
    }

    // 处理绝对路径，提取 img/ 后面的部分
    if (this.PATTERNS.IMG_PATH.test(absolutePath)) {
      const imgIndex = absolutePath.lastIndexOf('img/');
      return absolutePath.substring(imgIndex);
    }

    // 处理 Windows 路径分隔符
    const normalizedPath = absolutePath.replace(this.PATTERNS.WINDOWS_SEPARATOR, '/');
    if (this.PATTERNS.IMG_PATH.test(normalizedPath)) {
      const imgIndex = normalizedPath.lastIndexOf('img/');
      return normalizedPath.substring(imgIndex);
    }

    // 如果路径中包含项目目录结构，尝试智能提取
    const pathParts = normalizedPath.split('/');
    const imgIndex = pathParts.findIndex(part => part === 'img');
    
    if (imgIndex !== -1) {
      return pathParts.slice(imgIndex).join('/');
    }

    // 如果无法提取 img/ 路径，检查是否是文件名，尝试构造默认路径
    const fileName = normalizedPath.split('/').pop() || '';
    if (fileName && this.PATTERNS.FILE_EXTENSIONS.test(fileName)) {
      const defaultDir = this.guessDirectoryFromFileName(fileName);
      return `img/${defaultDir}/${fileName}`;
    }

    // 最后的回退：返回原路径
    return absolutePath;
  }
  
  /**
   * 根据文件名猜测目录
   * @param fileName 文件名
   * @returns 目录名
   */
  private static guessDirectoryFromFileName(fileName: string): string {
    const lowerFileName = fileName.toLowerCase();
    
    for (const [keyword, directory] of Object.entries(this.DIRECTORY_MAPPING)) {
      if (keyword !== 'default' && lowerFileName.includes(keyword)) {
        return directory;
      }
    }
    
    return this.DIRECTORY_MAPPING.default;
  }
  
  /**
   * 检查路径是否为有效的图片路径
   * @param path 路径
   * @returns 是否为有效图片路径
   */
  static isValidImagePath(path: string): boolean {
    return !!(path && this.PATTERNS.FILE_EXTENSIONS.test(path));
  }
  
  /**
   * 提取文件名（不含扩展名）
   * @param path 路径
   * @returns 文件名
   */
  static getFileNameWithoutExtension(path: string): string {
    const fileName = path.split('/').pop() || '';
    return fileName.replace(/\.[^/.]+$/, '');
  }
  
  /**
   * 提取文件扩展名
   * @param path 路径
   * @returns 扩展名（含点号）
   */
  static getFileExtension(path: string): string {
    const match = path.match(/\.[^/.]+$/);
    return match ? match[0] : '';
  }
}
