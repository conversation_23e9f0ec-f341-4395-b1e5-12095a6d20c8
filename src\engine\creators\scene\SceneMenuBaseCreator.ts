/**
 * Scene_MenuBase 创建器
 * 专门用于创建菜单基类场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';

declare global {
    interface Window {
        Window_Gold: any;
        Window_MenuStatus: any;
        $gameParty: any;
    }
}

/**
 * 菜单基类场景创建选项
 */
export interface MenuBaseSceneOptions extends SceneCreationOptions {
    /** 是否显示角色状态窗口 */
    showActorWindow?: boolean;
    /** 是否显示金钱窗口 */
    showGoldWindow?: boolean;
    /** 背景类型 (0: 窗口, 1: 暗化, 2: 透明) */
    backgroundType?: number;
}

/**
 * 创建菜单基类场景
 * @param options 创建选项
 * @returns 创建的菜单基类场景实例
 */
export async function createSceneMenuBase(options: MenuBaseSceneOptions = {}): Promise<any> {
    console.log('=== 创建菜单基类场景 Scene_MenuBase ===');
    
    try {
        // 预加载菜单场景资源
        await preloadMenuBaseResources(options);
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_MenuBase', defaultOptions);
        
        // Scene_MenuBase 特定的设置
        setupMenuBaseScene(scene, options);
        
        console.log('Scene_MenuBase 创建完成，场景属性:', {
            started: scene._started,
            active: scene._active,
            hasWindowLayer: !!scene._windowLayer,
            hasBackgroundSprite: !!scene._backgroundSprite
        });
        
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_MenuBase 失败:', error);
        throw error;
    }
}

/**
 * 预加载菜单基类场景资源
 * @param options 菜单基类场景选项
 */
async function preloadMenuBaseResources(options: MenuBaseSceneOptions): Promise<void> {
    console.log('预加载菜单基类场景资源...');
    
    if (!window.ImageManager) {
        console.warn('ImageManager 未加载，跳过菜单资源预加载');
        return;
    }
    
    try {
        // 预加载菜单系统图片
        window.ImageManager.loadSystem('Window');
        window.ImageManager.loadSystem('IconSet');
        
        // 预加载角色头像
        if (window.$gameParty && window.$gameParty.allMembers) {
            const members = window.$gameParty.allMembers();
            members.forEach((actor: any) => {
                if (actor && actor.faceName) {
                    window.ImageManager.loadFace(actor.faceName);
                    console.log('预加载角色头像:', actor.faceName);
                }
            });
        }
        
        // 预加载菜单背景
        if (window.$dataSystem && window.$dataSystem.menuBackground) {
            window.ImageManager.loadParallax(window.$dataSystem.menuBackground);
            console.log('预加载菜单背景:', window.$dataSystem.menuBackground);
        }
        
        console.log('菜单基类场景资源预加载完成');
        
    } catch (error) {
        console.error('预加载菜单基类场景资源失败:', error);
        // 不抛出错误，允许场景创建继续
    }
}

/**
 * 设置菜单基类场景
 * @param scene 场景实例
 * @param options 菜单基类场景选项
 */
function setupMenuBaseScene(scene: any, options: MenuBaseSceneOptions): void {
    console.log('设置菜单基类场景...');
    
    try {
        // 设置背景类型
        if (options.backgroundType !== undefined && scene.setBackgroundOpacity) {
            switch (options.backgroundType) {
                case 0: // 窗口背景
                    scene.setBackgroundOpacity(255);
                    break;
                case 1: // 暗化背景
                    scene.setBackgroundOpacity(192);
                    break;
                case 2: // 透明背景
                    scene.setBackgroundOpacity(0);
                    break;
            }
            console.log('设置背景类型:', options.backgroundType);
        }
        
        console.log('菜单基类场景设置完成');
        
    } catch (error) {
        console.error('设置菜单基类场景失败:', error);
    }
}

/**
 * 创建并启动菜单基类场景
 * @param options 创建选项
 * @returns 创建的菜单基类场景实例
 */
export async function createAndStartSceneMenuBase(options: MenuBaseSceneOptions = {}): Promise<any> {
    console.log('=== 创建并启动 Scene_MenuBase ===');
    
    const scene = await createSceneMenuBase({
        ...options,
        autoStart: true
    });
    
    console.log('Scene_MenuBase 已创建并启动');
    return scene;
}

/**
 * 创建简单的菜单基类场景（用于测试）
 * @returns 创建的菜单基类场景实例
 */
export async function createSimpleSceneMenuBase(): Promise<any> {
    console.log('=== 创建简单菜单基类场景 ===');
    
    try {
        const scene = await createSceneMenuBase({
            showActorWindow: true,
            showGoldWindow: true,
            backgroundType: 1, // 暗化背景
            autoStart: false,
            addToStage: true
        });
        
        console.log('简单菜单基类场景创建成功');
        return scene;
        
    } catch (error) {
        console.error('创建简单菜单基类场景失败:', error);
        throw error;
    }
}
