<script lang="ts">
  export let src: string = '';
  export let alt: string = '';
  export let width: string | number = 'auto';
  export let height: string | number = 'auto';
  export let maxWidth: string = '100%';
  export let maxHeight: string = '300px';
  export let fit: 'contain' | 'cover' | 'fill' | 'scale-down' = 'contain';
  export let loading: 'lazy' | 'eager' = 'lazy';
  export let showError: boolean = true;
  export let showLoading: boolean = true;
  export let clickable: boolean = false;
  export let onClick: (() => void) | (() => Promise<void>) | null = null;

  let imageElement: HTMLImageElement;
  let isLoading = true;
  let hasError = false;
  let naturalWidth = 0;
  let naturalHeight = 0;

  // 重置状态当src改变时
  $: if (src) {
    isLoading = true;
    hasError = false;
  }

  function handleLoad() {
    isLoading = false;
    hasError = false;
    if (imageElement) {
      naturalWidth = imageElement.naturalWidth;
      naturalHeight = imageElement.naturalHeight;
    }
  }

  function handleError() {
    isLoading = false;
    hasError = true;
  }

  // 处理点击事件
  async function handleClick() {
    if (clickable && onClick) {
      try {
        await onClick();
      } catch (error) {
        console.error('图片点击处理失败:', error);
      }
    }
  }

  // 处理键盘事件
  async function handleKeydown(event: KeyboardEvent) {
    if (clickable && onClick && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      await handleClick();
    }
  }

  // 计算样式
  $: imageStyle = `
    width: ${typeof width === 'number' ? width + 'px' : width};
    height: ${typeof height === 'number' ? height + 'px' : height};
    max-width: ${maxWidth};
    max-height: ${maxHeight};
    object-fit: ${fit};
  `;
</script>

<!-- svelte-ignore a11y-no-noninteractive-tabindex -->
<!-- svelte-ignore a11y-click-events-have-key-events -->
<!-- svelte-ignore a11y-no-static-element-interactions -->
<div
  class="image-container"
  class:clickable
  on:click={clickable ? handleClick : null}
  on:keydown={clickable ? handleKeydown : null}
  role={clickable ? 'button' : undefined}
  tabindex={clickable ? 0 : undefined}
  aria-label={clickable ? '点击选择图片' : undefined}
>
  {#if src}
    {#if showLoading && isLoading}
      <div class="image-loading">
        <div class="loading-spinner"></div>
        <span class="loading-text">加载中...</span>
      </div>
    {/if}

    {#if showError && hasError}
      <div class="image-error" class:clickable>
        <div class="error-icon">🖼️</div>
        <span class="error-text">图片加载失败</span>
        <span class="error-url">{src}</span>
        {#if clickable}
          <span class="click-hint">点击重新选择图片</span>
        {/if}
      </div>
    {:else}
      <img
        bind:this={imageElement}
        {src}
        {alt}
        {loading}
        style={imageStyle}
        class="image"
        class:loading={isLoading}
        class:clickable
        on:load={handleLoad}
        on:error={handleError}
      />
      {#if clickable && !isLoading}
        <div class="click-overlay">
          <span class="click-hint">点击选择图片</span>
        </div>
      {/if}
    {/if}

  {:else}
    <div class="image-placeholder" class:clickable>
      <div class="placeholder-icon">🖼️</div>
      <span class="placeholder-text">无图片</span>
      {#if clickable}
        <span class="click-hint">点击选择图片</span>
      {/if}
    </div>
  {/if}
</div>

<style>
  .image-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2, 0.5rem);
    padding: var(--spacing-3, 0.75rem);
    border: 1px solid var(--theme-border, rgba(255, 255, 255, 0.2));
    border-radius: var(--border-radius, 4px);
    background: var(--theme-surface-dark, #1a202c);
  }

  .image-container.clickable {
    cursor: pointer;
    transition: all var(--transition-base, 0.2s ease);
  }

  .image-container.clickable:hover {
    border-color: var(--theme-primary, #4a5568);
    background: var(--theme-surface, #2d3748);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .image-container.clickable:focus {
    outline: 2px solid var(--theme-primary, #4a5568);
    outline-offset: 2px;
  }

  .image {
    display: block;
    border-radius: var(--border-radius-small, 2px);
    transition: opacity var(--transition-base, 0.2s ease);
  }

  .image.loading {
    opacity: 0;
  }

  .image-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2, 0.5rem);
    padding: var(--spacing-5, 1.25rem);
    color: var(--theme-text-secondary, rgba(255, 255, 255, 0.8));
  }

  .loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--theme-border, rgba(255, 255, 255, 0.2));
    border-top: 2px solid var(--theme-primary, #4a5568);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    font-size: var(--font-size-sm, 0.875rem);
  }

  .image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-1, 0.25rem);
    padding: var(--spacing-5, 1.25rem);
    color: var(--theme-error, #e53e3e);
    text-align: center;
  }

  .error-icon {
    font-size: var(--font-size-xl, 1.25rem);
    opacity: 0.5;
  }

  .error-text {
    font-size: var(--font-size-sm, 0.875rem);
    font-weight: 500;
  }

  .error-url {
    font-size: var(--font-size-xs, 0.75rem);
    opacity: 0.7;
    word-break: break-all;
    max-width: 200px;
  }

  .image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2, 0.5rem);
    padding: var(--spacing-5, 1.25rem);
    color: var(--theme-text-secondary, rgba(255, 255, 255, 0.8));
  }

  .placeholder-icon {
    font-size: var(--font-size-xl, 1.25rem);
    opacity: 0.5;
  }

  .placeholder-text {
    font-size: var(--font-size-sm, 0.875rem);
  }

  .click-hint {
    font-size: var(--font-size-xs, 0.75rem);
    color: var(--theme-primary, #4a5568);
    font-weight: 500;
    margin-top: var(--spacing-1, 0.25rem);
  }

  .click-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity var(--transition-base, 0.2s ease);
    border-radius: var(--border-radius, 4px);
  }

  .image-container.clickable:hover .click-overlay {
    opacity: 1;
  }

  .click-overlay .click-hint {
    color: white;
    background: rgba(0, 0, 0, 0.8);
    padding: var(--spacing-2, 0.5rem) var(--spacing-3, 0.75rem);
    border-radius: var(--border-radius-small, 2px);
    font-weight: 600;
  }



  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .image-container {
      padding: var(--spacing-2, 0.5rem);
    }

    .error-url {
      max-width: 150px;
    }
  }
</style>
