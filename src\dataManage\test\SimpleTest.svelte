<script lang="ts">
  import SimpleDataSelector from '../components/SimpleDataSelector.svelte';
  import type { DataSelection } from '../types/dataTypes';

  let selection = $state<DataSelection | null>(null);

  function handleSelect(newSelection: DataSelection | null) {
    selection = newSelection;
    console.log('选择结果:', newSelection);
  }
</script>

<div class="test-container">
  <h2>SimpleDataSelector 测试</h2>
  
  <div class="test-section">
    <SimpleDataSelector 
      value={selection}
      onSelect={handleSelect}
      placeholder="选择数据字段"
    />
  </div>

  {#if selection}
    <div class="result-section">
      <h3>选择结果</h3>
      <div class="result-item">
        <strong>数据类型:</strong> {selection.dataType}
      </div>
      <div class="result-item">
        <strong>字段路径:</strong> {selection.fieldPath}
      </div>
      <div class="result-item">
        <strong>显示文本:</strong> {selection.displayText}
      </div>
      <div class="result-item">
        <strong>字段类型:</strong> {selection.fieldInfo.type}
      </div>
      {#if selection.fieldInfo.description}
        <div class="result-item">
          <strong>字段描述:</strong> {selection.fieldInfo.description}
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .test-container {
    max-width: 500px;
    margin: 20px auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .test-section {
    margin-bottom: 20px;
  }

  .result-section {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }

  .result-section h3 {
    margin-top: 0;
    margin-bottom: 12px;
    color: #333;
  }

  .result-item {
    margin-bottom: 8px;
    padding: 4px 0;
  }

  .result-item strong {
    color: #495057;
    margin-right: 8px;
  }
</style>
