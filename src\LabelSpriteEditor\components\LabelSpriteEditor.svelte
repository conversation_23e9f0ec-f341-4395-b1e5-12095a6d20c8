<script lang="ts">
  import { PaneGroup, Pane, PaneResizer } from "paneforge";
  import FontPropertiesPanel from './TopPanel.svelte';
  import ElementsListPanel from './ElementsListPanel.svelte';
  import CanvasDisplayPanel from './CanvasDisplayPanel_Simple.svelte';
  import ElementPropertiesPanel from './ElementPropertiesPanel.svelte';
  import type { bitmapProperties } from '../types';
  import { initializeBitmapObjectState, bitmapModel } from '../stores/bitmapStore';
  import { onMount } from "svelte";

  // 🔧 Svelte 5: Props
  interface Props {
    bitmapData: bitmapProperties;
    onExport?: ((data: bitmapProperties) => void) | undefined;
    onClose?: (() => void) | undefined;
    stageSize?: { width: number; height: number };
    spritePosition?: { x: number; y: number };
  }

  let { bitmapData, onExport, onClose, stageSize, spritePosition }: Props = $props();

  // 🔧 导出功能，直接使用 bitmapModel 的 toPlainObject 方法
  function handleExport() {
    if (!$bitmapModel) {
      console.warn('⚠️ 没有可导出的 bitmap 数据');
      return;
    }

    console.log('🔧 开始导出 bitmap 数据...');

    // 🔧 直接使用 bitmapModel 的导出方法
    const exportData = $bitmapModel.toPlainObject() as bitmapProperties;

    console.log('🔧 导出数据:', exportData);

    // 调用外部回调函数
    if (onExport) {
      onExport(exportData);
      console.log('✅ 数据已通过回调函数传递给外部');
    } else {
      console.warn('⚠️ 没有设置 onExport 回调函数');
    }

    // 🔧 导出后关闭窗口
    if (onClose) {
      console.log('🔧 导出完成，关闭窗口');
      onClose();
    }
  }

  // 关闭功能
  function handleClose() {
    if (onClose) {
      console.log('🔧 用户点击关闭按钮');
      onClose();
    }
  }

  // 🔧 Svelte 5: 使用 $effect 处理初始化
  onMount(() => {
    console.log('LabelSpriteEditor 组件已挂载');
    if (bitmapData) {
      console.log('LabelSpriteEditor 接收到数据，初始化 ReactiveBitmap:', bitmapData);
      try {
        initializeBitmapObjectState(bitmapData);
      } catch (error) {
        console.error('❌ 初始化 ReactiveBitmap 失败:', error);
        // 可以在这里显示错误信息给用户
      }
    }
  });
</script>
<div class="label-sprite-editor">
  <PaneGroup direction="vertical">
    <!-- 顶部：字体属性面板和导出按钮 -->
    <Pane defaultSize={15} minSize={10} maxSize={25}>
      <div class="top-panel-container">
        <FontPropertiesPanel/>
        <div class="header-actions">
          <button
            class="export-btn"
            onclick={handleExport}
            title="导出当前 bitmap 数据并关闭"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="7,10 12,15 17,10"/>
              <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
            导出
          </button>
          {#if onClose}
            <button
              class="action-btn close-btn"
              onclick={handleClose}
              title="关闭"
            >
              ✕
            </button>
          {/if}
        </div>
      </div>
    </Pane>

    <PaneResizer />

    <!-- 主要内容区域 -->
    <Pane defaultSize={85}>
      <PaneGroup direction="horizontal">
        <!-- 左侧：元素列表面板 -->
        <Pane defaultSize={20} minSize={15} maxSize={35}>
          <ElementsListPanel />
        </Pane>

        <PaneResizer />

        <!-- 中间：Canvas显示面板 -->
        <Pane defaultSize={60}>
          <CanvasDisplayPanel
            stageSize={stageSize}
            spritePosition={spritePosition}
          />
        </Pane>

        <PaneResizer />

        <!-- 右侧：元素属性面板 -->
        <Pane defaultSize={20} minSize={15} maxSize={35}>
          <ElementPropertiesPanel/>
        </Pane>
      </PaneGroup>
    </Pane>
  </PaneGroup>
</div>

<style>
  .label-sprite-editor {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-surface, #ffffff);
    overflow: hidden;
  }

  /* 顶部面板容器 */
  .top-panel-container {
    display: flex;
    height: 100%;
    position: relative;
  }

  .top-panel-container > :global(*:first-child) {
    flex: 1;
  }

  /* 头部操作按钮容器 */
  .header-actions {
    position: absolute;
    top: 8px;
    right: 16px;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  /* 导出按钮样式 */
  .export-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: var(--theme-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  }

  .export-btn:hover {
    background: var(--theme-primary-hover, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  }

  .export-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  }

  .export-btn svg {
    flex-shrink: 0;
  }

  /* 关闭按钮样式 - 与 Modal 保持一致 */
  .action-btn {
    background: none;
    border: none;
    font-size: 16px;
    color: var(--theme-text-secondary, #6b7280);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
  }

  .action-btn:hover {
    background: var(--theme-surface-dark, #f3f4f6);
    color: var(--theme-text, #111827);
  }

  .close-btn:hover {
    background: var(--theme-error-light, #fef2f2);
    color: var(--theme-error, #dc2626);
  }

  /* 确保PaneGroup占满整个容器 */
  .label-sprite-editor :global(.pane-group) {
    height: 100%;
  }

  /* 移除面板之间的间距 */
  .label-sprite-editor :global(.pane) {
    background: var(--theme-surface, #ffffff);
  }

  /* 调整分隔线样式 */
  .label-sprite-editor :global(.pane-resizer) {
    background: var(--theme-border, #e5e7eb);
  }

  .label-sprite-editor :global(.pane-resizer:hover) {
    background: var(--theme-primary-light, #93c5fd);
  }
</style>
