/**
 * 历史记录系统类型定义
 */

import type { BaseObjectModel } from '../type/baseObjectModel.svelte';

/**
 * 单个历史记录条目
 */
export interface HistoryEntry {
  /** 时间戳 */
  timestamp: number;
  /** 模型对象引用 */
  modelObject: BaseObjectModel;
  /** 变化的字段名 */
  fieldName: string;
  /** 历史值（撤销时使用） */
  oldValue: any;
  /** 新值（重做时使用） */
  newValue: any;
  /** 对象ID（用于验证对象有效性） */
  objectId: string;
}

/**
 * 操作组（一次操作可能涉及多个字段变更）
 */
export interface OperationGroup {
  /** 操作名称 */
  name: string;
  /** 操作描述 */
  description?: string;
  /** 时间戳 */
  timestamp: number;
  /** 包含的历史记录条目 */
  entries: HistoryEntry[];
}

/**
 * 历史记录配置
 */
export interface HistoryConfig {
  /** 最大历史记录数量 */
  maxHistorySize: number;
  /** 是否启用历史记录 */
  enabled: boolean;
  /** 是否自动记录 */
  autoRecord: boolean;
  /** 调试模式 */
  debug: boolean;
}

/**
 * 历史记录事件
 */
export interface HistoryEvent {
  type: 'undo' | 'redo' | 'record' | 'clear';
  operation?: OperationGroup;
  timestamp: number;
}

/**
 * 历史记录监听器
 */
export type HistoryListener = (event: HistoryEvent) => void;
