/**
 * Scene_Battle 创建器
 * 专门用于创建战斗场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';

declare global {
    interface Window {
        BattleManager: any;
        $gameParty: any;
        $gameTroop: any;
        $dataEnemies: any;
        $dataTroops: any;
        Spriteset_Battle: any;
    }
}

/**
 * 战斗场景创建选项
 */
export interface BattleSceneOptions extends SceneCreationOptions {
    /** 敌群ID */
    troopId?: number;
    /** 是否可以逃跑 */
    canEscape?: boolean;
    /** 是否可以失败 */
    canLose?: boolean;
    /** 战斗背景 */
    battleback1Name?: string;
    battleback2Name?: string;
}

/**
 * 创建战斗场景
 * @param options 创建选项
 * @returns 创建的战斗场景实例
 */
export async function createSceneBattle(options: BattleSceneOptions = {}): Promise<any> {
    console.log('=== 创建战斗场景 Scene_Battle ===');
    
    try {
        // 预加载战斗场景资源
        await preloadBattleResources(options);
        
        // 设置战斗数据（如果提供了敌群ID）
        if (options.troopId !== undefined) {
            setupBattleData(options);
        }
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Battle', defaultOptions);
        
        // Scene_Battle 特定的设置
        console.log('Scene_Battle 创建完成，场景属性:', {
            started: scene._started,
            active: scene._active,
            hasSpriteset: !!scene._spriteset,
            hasStatusWindow: !!scene._statusWindow,
            hasLogWindow: !!scene._logWindow
        });
        
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Battle 失败:', error);
        throw error;
    }
}

/**
 * 预加载战斗场景资源
 * @param options 战斗场景选项
 */
async function preloadBattleResources(options: BattleSceneOptions): Promise<void> {
    console.log('预加载战斗场景资源...');
    
    if (!window.ImageManager) {
        console.warn('ImageManager 未加载，跳过战斗资源预加载');
        return;
    }
    
    try {
        // 预加载战斗系统图片
        window.ImageManager.loadSystem('Window');
        window.ImageManager.loadSystem('IconSet');
        window.ImageManager.loadSystem('Damage');
        window.ImageManager.loadSystem('States');
        window.ImageManager.loadSystem('Weapons1');
        window.ImageManager.loadSystem('Weapons2');
        window.ImageManager.loadSystem('Weapons3');
        
        // 预加载战斗背景
        if (options.battleback1Name) {
            window.ImageManager.loadBattleback1(options.battleback1Name);
            console.log('预加载战斗背景1:', options.battleback1Name);
        }
        
        if (options.battleback2Name) {
            window.ImageManager.loadBattleback2(options.battleback2Name);
            console.log('预加载战斗背景2:', options.battleback2Name);
        }
        
        // 预加载默认战斗背景
        window.ImageManager.loadBattleback1('Grassland');
        window.ImageManager.loadBattleback2('Grassland');
        
        console.log('战斗场景资源预加载完成');
        
    } catch (error) {
        console.error('预加载战斗场景资源失败:', error);
        // 不抛出错误，允许场景创建继续
    }
}

/**
 * 设置战斗数据
 * @param options 战斗场景选项
 */
function setupBattleData(options: BattleSceneOptions): void {
    console.log('设置战斗数据...');
    
    try {
        if (!window.BattleManager) {
            console.warn('BattleManager 未加载，跳过战斗数据设置');
            return;
        }
        
        // 设置敌群
        if (options.troopId !== undefined && window.$dataTroops) {
            const troop = window.$dataTroops[options.troopId];
            if (troop) {
                console.log('设置敌群:', troop.name, '(ID:', options.troopId, ')');
                // 这里可以设置敌群数据，但通常由BattleManager处理
            } else {
                console.warn('敌群ID', options.troopId, '不存在');
            }
        }
        
        // 设置战斗选项
        if (options.canEscape !== undefined) {
            console.log('设置可逃跑:', options.canEscape);
        }
        
        if (options.canLose !== undefined) {
            console.log('设置可失败:', options.canLose);
        }
        
        console.log('战斗数据设置完成');
        
    } catch (error) {
        console.error('设置战斗数据失败:', error);
    }
}

/**
 * 创建并启动战斗场景
 * @param options 创建选项
 * @returns 创建的战斗场景实例
 */
export async function createAndStartSceneBattle(options: BattleSceneOptions = {}): Promise<any> {
    console.log('=== 创建并启动 Scene_Battle ===');
    
    const scene = await createSceneBattle({
        ...options,
        autoStart: true
    });
    
    console.log('Scene_Battle 已创建并启动');
    return scene;
}

/**
 * 创建测试战斗场景
 * @returns 创建的战斗场景实例
 */
export async function createTestSceneBattle(): Promise<any> {
    console.log('=== 创建测试战斗场景 ===');
    
    try {
        const scene = await createSceneBattle({
            troopId: 1, // 使用第一个敌群
            canEscape: true,
            canLose: false,
            battleback1Name: 'Grassland',
            battleback2Name: 'Grassland',
            autoStart: false,
            addToStage: true
        });
        
        console.log('测试战斗场景创建成功');
        return scene;
        
    } catch (error) {
        console.error('创建测试战斗场景失败:', error);
        throw error;
    }
}
