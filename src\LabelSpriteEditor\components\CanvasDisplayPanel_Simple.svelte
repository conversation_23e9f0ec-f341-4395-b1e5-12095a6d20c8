<script lang="ts">
  import { onMount } from "svelte";
  import { bitmapModel, selectedElement } from "../stores/bitmapStore";
  import { CanvasInteractionHandler } from "../lib/canvasInteraction";

  // 🔧 Svelte 5: Props
  interface Props {
    stageSize?: { width: number; height: number };
    spritePosition?: { x: number; y: number };
  }

  let { stageSize, spritePosition }: Props = $props();

  // 🔧 当前的 sprite 对象和 bitmap 实例
  let currentSprite: any = null;
  let currentBitmap: any = null;

  // 🔧 获取当前的 sprite 对象（从 RPG Maker MZ 运行时）
  function getCurrentSprite(): any {
    // 这里需要根据实际的 RPG Maker MZ 环境来获取当前 sprite
    // 可能是从全局变量、场景管理器或其他方式获取
    if (typeof window !== "undefined" && (window as any).SceneManager) {
      const scene = (window as any).SceneManager._scene;
      if (scene && scene._spriteset) {
        // 假设我们要获取的是某个特定的 sprite
        // 这里需要根据实际情况调整
        return scene._spriteset._characterSprites?.[0] || null;
      }
    }
    return null;
  }

  // Canvas相关
  let canvasElement: HTMLCanvasElement;
  let ctx: CanvasRenderingContext2D;
  let interactionHandler: CanvasInteractionHandler | null = null;

  // 初始化Canvas
  onMount(() => {
    if (canvasElement) {
      ctx = canvasElement.getContext("2d")!;

      // 🔧 使用模型内部的 bitmap 并绑定到 sprite
      // 获取模型内部的原始 bitmap
      currentBitmap = $bitmapModel.getOriginalBitmap();

      if (!currentBitmap) {
        console.warn("⚠️ bitmapModel 内部没有 bitmap，可能需要先初始化");
        return;
      }

      // 获取当前 sprite 并赋值 bitmap
      currentSprite = getCurrentSprite();
      if (currentSprite) {
        currentSprite.bitmap = currentBitmap;
      }
      // 🔧 创建交互处理器，直接操作 bitmapModel
      interactionHandler = new CanvasInteractionHandler(canvasElement, ctx, {
        onElementSelected: (index: number) => {
          console.log("选中元素:", index);
          // 使用独立的选中状态
          const element =
            index >= 0 && index < $bitmapModel.elements.length
              ? $bitmapModel.elements[index]
              : null;
          selectedElement.set(element);
        },
        onElementMoved: (index: number, x: number, y: number) => {
          console.log("移动元素:", index, "to", { x, y });
          if (index >= 0 && index < $bitmapModel.elements.length) {
            const element = $bitmapModel.elements[index];
            if (element?.type === "text") {
              $bitmapModel.updateElement(index, { x, y });
            } else if (element?.type === "image") {
              $bitmapModel.updateElement(index, { dx: x, dy: y });
            }
          }
        },
      });

      drawCanvas();
    }

    // 清理函数
    return () => {
      interactionHandler?.destroy();
    };
  });

  // 🔧 统一的更新函数，由 BitmapModel 回调触发
  function handleBitmapUpdate() {
    console.log('🔧 CanvasDisplayPanel: 收到 BitmapModel 更新回调');

    // 确保 currentBitmap 始终指向最新的 bitmap
    const latestBitmap = $bitmapModel?.getOriginalBitmap?.();
    if (latestBitmap && latestBitmap !== currentBitmap) {
      console.log('🔧 检测到 bitmap 引用变化，更新 currentBitmap');
      currentBitmap = latestBitmap;

      // 同时更新 sprite 的 bitmap 引用
      if (currentSprite) {
        currentSprite.bitmap = currentBitmap;
        console.log('🔧 更新 sprite.bitmap 引用');
      }
    }

    // 绘制到预览 canvas
    drawCanvas();
  }

  // 🔧 设置 BitmapModel 的更新回调
  $effect(() => {
    if ($bitmapModel) {
      $bitmapModel.setUpdateCallback(handleBitmapUpdate);
      console.log('🔧 CanvasDisplayPanel: 设置 BitmapModel 更新回调');
    }
  });

  // 🔧 监听选中状态变化（用于更新选择框）
  $effect(() => {
    $selectedElement;
    console.log('🔧 CanvasDisplayPanel: 选中状态变化，重新绘制选择框');

    // 只重新绘制选择框，不重新绘制整个 canvas
    if (interactionHandler && currentBitmap) {
      const selectedIndex = $selectedElement
        ? (currentBitmap.elements || []).findIndex(
            (el: any) => el.id === $selectedElement.id,
          )
        : -1;

      const elementsForHandler = (currentBitmap.elements || []).map(
        (el: any, index: number) => ({
          ...el,
          id: el.id || `element_${index}`,
        }),
      );

      interactionHandler.updateData(
        currentBitmap,
        elementsForHandler,
        selectedIndex,
      );
      interactionHandler.drawSelectionBox();
    }
  });

  // 🔧 使用 currentBitmap._canvas 的内容
  function drawCanvas() {
    if (!ctx) return;

    console.log('🎨 drawCanvas: currentBitmap.elements:', currentBitmap?.elements);
    console.log('🎨 drawCanvas: 第一个元素的 x 坐标:', currentBitmap?.elements?.[0]?.x);

    // 清空整个画布
    ctx.clearRect(0, 0, canvasElement.width, canvasElement.height);

    if (currentBitmap) {
      // 将 bitmap._canvas 的内容绘制到预览 canvas
      if (currentBitmap._canvas) {
        ctx.drawImage(currentBitmap._canvas, 0, 0);
      } else {
        drawLoadingMessage();
      }

      // 🔧 选择框的绘制已移到单独的 $effect 中，这里不再处理
    } else {
      // 显示提示信息
      drawLoadingMessage();
    }
  }

  // 🔧 简化：不再需要自定义绘制函数，直接使用 bitmap._canvas

  // 绘制加载信息
  function drawLoadingMessage() {
    ctx.fillStyle = "#666";
    ctx.font = "16px Arial";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillText(
      "正在初始化编辑器...",
      canvasElement.width / 2,
      canvasElement.height / 2,
    );
  }
</script>

<div class="canvas-display-panel">
  <div class="panel-header">
    <h3>预览画布 (简化版)</h3>
  </div>

  <div class="canvas-container">
    <canvas
      bind:this={canvasElement}
      width={stageSize?.width || 816}
      height={stageSize?.height || 624}
      style="border: 1px solid #ccc; background: #f0f0f0;"
    ></canvas>
  </div>
</div>

<style>
  .canvas-display-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: var(--border-radius, 8px);
    overflow: hidden;
  }

  .panel-header {
    padding: var(--spacing-3, 0.75rem);
    background: var(--theme-surface-light, #f9fafb);
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
  }

  .panel-header h3 {
    margin: 0;
    font-size: var(--font-size-lg, 1.125rem);
    font-weight: 600;
    color: var(--theme-text-primary, #111827);
  }

  .canvas-container {
    flex: 1;
    padding: var(--spacing-4, 1rem);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-4, 1rem);
  }
</style>
