/**
 * Window_Scrollable 创建器
 * 专门用于创建可滚动窗口
 */

import { BaseWindowCreator, type WindowCreationOptions } from './WindowCreator';
import { type BaseWindowOptions } from './WindowBaseCreator';

/**
 * 可滚动窗口创建选项
 */
export interface ScrollableWindowOptions extends BaseWindowOptions {
    /** 滚动速度 */
    scrollSpeed?: number;
    /** 是否启用滚动 */
    scrollEnabled?: boolean;
    /** 初始滚动位置 */
    scrollX?: number;
    scrollY?: number;
}

/**
 * 创建可滚动窗口
 * @param options 创建选项
 * @returns 创建的可滚动窗口实例
 */
export async function createWindowScrollable(options: ScrollableWindowOptions = {}): Promise<any> {
    console.log('=== 创建可滚动窗口 Window_Scrollable ===');
    
    try {
        // 预加载资源
        BaseWindowCreator.preloadWindowResources('Window_Scrollable');
        
        // 设置默认选项
        const defaultOptions: WindowCreationOptions = {
            autoOpen: true,
            addToStage: true,
            rect: options.rect || { x: 0, y: 0, width: 300, height: 200 },
            ...options
        };
        
        // 创建窗口实例
        const window = await BaseWindowCreator.createWindowInstance('Window_Scrollable', defaultOptions);
        
        // Window_Scrollable 特定的设置
        setupScrollableWindow(window, options);
        
        console.log('Window_Scrollable 创建完成，窗口属性:', {
            x: window.x,
            y: window.y,
            width: window.width,
            height: window.height,
            scrollX: window.scrollX,
            scrollY: window.scrollY,
            visible: window.visible
        });
        
        return window;
        
    } catch (error) {
        console.error('创建 Window_Scrollable 失败:', error);
        throw error;
    }
}

/**
 * 设置可滚动窗口属性
 * @param window 窗口实例
 * @param options 可滚动窗口选项
 */
function setupScrollableWindow(window: any, options: ScrollableWindowOptions): void {
    console.log('设置可滚动窗口属性...');
    
    try {
        // 设置滚动位置
        if (options.scrollX !== undefined && window.scrollTo && typeof window.scrollTo === 'function') {
            window.scrollTo(options.scrollX, options.scrollY || 0);
            console.log('设置滚动位置:', { x: options.scrollX, y: options.scrollY || 0 });
        }
        
        // 设置滚动速度（如果窗口支持）
        if (options.scrollSpeed !== undefined && window._scrollSpeed !== undefined) {
            window._scrollSpeed = options.scrollSpeed;
            console.log('设置滚动速度:', options.scrollSpeed);
        }
        
        // 设置滚动启用状态（如果窗口支持）
        if (options.scrollEnabled !== undefined && window._scrollEnabled !== undefined) {
            window._scrollEnabled = options.scrollEnabled;
            console.log('设置滚动启用状态:', options.scrollEnabled);
        }
        
        console.log('可滚动窗口属性设置完成');
        
    } catch (error) {
        console.error('设置可滚动窗口属性失败:', error);
    }
}

/**
 * 创建并打开可滚动窗口
 * @param options 创建选项
 * @returns 创建的可滚动窗口实例
 */
export async function createAndOpenWindowScrollable(options: ScrollableWindowOptions = {}): Promise<any> {
    console.log('=== 创建并打开 Window_Scrollable ===');
    
    const window = await createWindowScrollable({
        ...options,
        autoOpen: true
    });
    
    console.log('Window_Scrollable 已创建并打开');
    return window;
}

/**
 * 创建简单的可滚动窗口（用于测试）
 * @returns 创建的可滚动窗口实例
 */
export async function createSimpleWindowScrollable(): Promise<any> {
    console.log('=== 创建简单可滚动窗口 ===');
    
    try {
        const window = await createWindowScrollable({
            rect: { x: 50, y: 50, width: 400, height: 300 },
            autoOpen: true,
            addToStage: true,
            visible: true,
            scrollSpeed: 4,
            scrollEnabled: true,
            scrollX: 0,
            scrollY: 0
        });
        
        console.log('简单可滚动窗口创建成功');
        return window;
        
    } catch (error) {
        console.error('创建简单可滚动窗口失败:', error);
        throw error;
    }
}
