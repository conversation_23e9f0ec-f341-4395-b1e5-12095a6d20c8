/**
 * 窗口创建器基类
 * 提供窗口创建的通用功能和生命周期管理
 */

import { objManage } from '../../objManage';

declare global {
    interface Window {
        Window_Base: any;
        Window_Scrollable: any;
        Window_Selectable: any;
        Window_Command: any;
        Window_HorzCommand: any;
        Window_MenuCommand: any;
        Window_ItemCategory: any;
        Window_TitleCommand: any;
        Window_PartyCommand: any;
        Window_ItemList: any;
        Window_SavefileList: any;
        Window_Help: any;
        Window_Gold: any;
        Window_StatusBase: any;
        Window_Message: any;
        Rectangle: any;
        Graphics: any;
        ImageManager: any;
        $gameSystem: any;
        ColorManager: any;
    }
}

/**
 * 窗口创建选项
 */
export interface WindowCreationOptions {
    /** 窗口矩形区域 */
    rect?: { x: number; y: number; width: number; height: number };
    /** 是否自动打开窗口 */
    autoOpen?: boolean;
    /** 是否添加到舞台 */
    addToStage?: boolean;
    /** 自定义初始化参数 */
    initParams?: any[];
    /** 窗口特定选项 */
    windowOptions?: any;
}

/**
 * 基础窗口创建器
 */
export class BaseWindowCreator {
    /**
     * 检查窗口类是否可用
     * @param windowClass 窗口类名
     * @returns 是否可用
     */
    protected static checkWindowClass(windowClass: string): boolean {
        if (!window[windowClass]) {
            console.error(`窗口类 ${windowClass} 未加载`);
            return false;
        }
        return true;
    }

    /**
     * 检查必要的资源是否准备就绪
     * @returns 资源状态
     */
    protected static async checkResources(): Promise<boolean> {
        console.log('检查窗口创建所需资源...');
        
        const resourceStatus = objManage.getResourceStatus();
        if (!resourceStatus.all) {
            console.log('等待资源加载完成...', resourceStatus);
            const loaded = await objManage.waitForResources();
            if (!loaded) {
                throw new Error('资源加载超时');
            }
        }
        
        return true;
    }

    /**
     * 创建默认窗口矩形
     * @param width 宽度，默认240
     * @param height 高度，默认120
     * @param x X坐标，默认居中
     * @param y Y坐标，默认居中
     * @returns Rectangle对象
     */
    protected static createDefaultRect(
        width: number = 240,
        height: number = 120,
        x?: number,
        y?: number
    ): any {
        if (!window.Rectangle) {
            throw new Error('Rectangle 类未加载');
        }

        const defaultX = x !== undefined ? x : (window.Graphics.boxWidth - width) / 2;
        const defaultY = y !== undefined ? y : (window.Graphics.boxHeight - height) / 2;

        return new window.Rectangle(defaultX, defaultY, width, height);
    }

    /**
     * 创建窗口实例
     * @param windowClass 窗口类名
     * @param options 创建选项
     * @returns 创建的窗口实例
     */
    protected static async createWindowInstance(
        windowClass: string, 
        options: WindowCreationOptions = {}
    ): Promise<any> {
        console.log(`=== 创建窗口: ${windowClass} ===`);
        console.log('创建选项:', options);

        try {
            // 1. 检查窗口类
            if (!this.checkWindowClass(windowClass)) {
                throw new Error(`窗口类 ${windowClass} 不可用`);
            }

            // 2. 检查资源
            await this.checkResources();

            // 3. 创建窗口矩形
            let rect = options.rect;
            if (!rect) {
                rect = this.createDefaultRect();
                console.log('使用默认窗口矩形:', rect);
            } else {
                rect = new window.Rectangle(rect.x, rect.y, rect.width, rect.height);
                console.log('使用自定义窗口矩形:', rect);
            }

            // 4. 创建窗口实例
            const WindowConstructor = window[windowClass];
            const windowInstance = new WindowConstructor(rect, ...(options.initParams || []));

            console.log(`窗口 ${windowClass} 实例创建成功:`, windowInstance);

            // 5. 设置窗口坐标为 (0, 0)（相对于父容器）
            if (windowInstance.x !== undefined) windowInstance.x = 0;
            if (windowInstance.y !== undefined) windowInstance.y = 0;
            console.log(`窗口 ${windowClass} 坐标已设置为 (0, 0)`);

            // 6. 自动打开窗口（如果需要）
            if (options.autoOpen && windowInstance.open && typeof windowInstance.open === 'function') {
                windowInstance.open();
                console.log(`窗口 ${windowClass} 已自动打开`);
            }

            // 7. 添加到舞台（如果需要）
            if (options.addToStage !== false) {
                objManage.addToStage(windowInstance, false); // 窗口不需要居中，使用 (0, 0) 坐标
                console.log(`窗口 ${windowClass} 已添加到舞台`);
            }

            return windowInstance;

        } catch (error) {
            console.error(`创建窗口 ${windowClass} 失败:`, error);
            throw error;
        }
    }

    /**
     * 预加载窗口所需的资源
     * @param windowClass 窗口类名
     */
    protected static preloadWindowResources(windowClass: string): void {
        console.log(`预加载窗口 ${windowClass} 所需资源...`);
        
        if (!window.ImageManager) {
            console.warn('ImageManager 未加载，跳过资源预加载');
            return;
        }

        // 预加载通用窗口资源
        window.ImageManager.loadSystem('Window');
        window.ImageManager.loadSystem('IconSet');
        
        // 根据窗口类型预加载特定资源
        switch (windowClass) {
            case 'Window_MenuCommand':
            case 'Window_ItemCategory':
            case 'Window_Command':
            case 'Window_HorzCommand':
                // 命令窗口需要图标集
                window.ImageManager.loadSystem('IconSet');
                break;
                
            case 'Window_Help':
                // 帮助窗口可能需要特殊字体
                break;
                
            case 'Window_Gold':
                // 金钱窗口需要货币图标
                window.ImageManager.loadSystem('IconSet');
                break;
                
            default:
                // 其他窗口的通用资源已经预加载
                break;
        }
        
        console.log(`窗口 ${windowClass} 资源预加载请求已发送`);
    }
}

/**
 * 通用窗口创建函数
 * @param windowClass 窗口类名
 * @param options 创建选项
 * @returns 创建的窗口实例
 */
export async function createWindow(
    windowClass: string, 
    options: WindowCreationOptions = {}
): Promise<any> {
    return BaseWindowCreator.createWindowInstance(windowClass, options);
}

/**
 * 预加载所有窗口资源
 */
export function preloadAllWindowResources(): void {
    console.log('预加载所有窗口资源...');
    
    const windowClasses = [
        'Window_Base',
        'Window_Scrollable',
        'Window_Selectable',
        'Window_Command',
        'Window_HorzCommand',
        'Window_MenuCommand',
        'Window_ItemCategory',
        'Window_Help',
        'Window_Gold'
    ];
    
    windowClasses.forEach(windowClass => {
        BaseWindowCreator.preloadWindowResources(windowClass);
    });
    
    console.log('所有窗口资源预加载请求已发送');
}
