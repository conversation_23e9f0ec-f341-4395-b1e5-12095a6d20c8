<script lang="ts">
  import type { ItemModel } from '../../../type/ui/itemModel.svelte';
  import { AccordionPanel } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import Select from '../../../components/Select.svelte';
  import DropTarget from '../../../components/drop/DropTarget.svelte';
  import EventSelectionModal from '../../../modals/EventSelectionModal.svelte';
  import DataBindingModal from '../../../modals/dataModals/DataBindingModal.svelte';
  import type { BaseObjectModel } from '../../../type/baseObjectModel.svelte';

  let { model }: { model: ItemModel } = $props();

  // 状态
  let showEventModal = $state(false);
  let selectedEventType = $state('onClickCode');

  // 🆕 数据绑定模态框状态
  let showDataBindingModal = $state(false);
  let currentBindingTarget: BaseObjectModel | null = $state(null);

  // 事件类型选项
  const eventTypes = [
    { value: 'onClickCode', label: '点击事件' },
    { value: 'onDoubleClickCode', label: '双击事件' },
    { value: 'onHoverCode', label: '悬停事件' }
  ];

  // 绑定项列表 - 使用模型中的状态
  let bindingItems = $derived(model?.bindingItems || []);


  /**
   * 添加新的绑定项
   */
  function addBindingItem() {
    if (!model) return;

    const newBinding = {
      id: `binding_${Date.now()}`,
      targetObject: null,
      fieldName: '',
      dataType: 'saveFiles'
    };
    model.bindingItems.push(newBinding);
    model.bindingItems = [...model.bindingItems]; // 触发响应式更新
  }

  /**
   * 删除绑定项
   */
  function removeBindingItem(id: string) {
    if (!model) return;
    model.bindingItems = model.bindingItems.filter(item => item.id !== id);
  }

  /**
   * 处理拖拽放置 - 使用 DropTarget 组件
   */
  function handleObjectDrop(droppedObject: BaseObjectModel, bindingId: string) {
    console.log('🎯 UIItem: 接收到拖拽对象:', droppedObject.className, '绑定ID:', bindingId);

    try {
      if (!model) return;

      // 找到对应的绑定项并更新
      const bindingIndex = model.bindingItems.findIndex(item => item.id === bindingId);
      if (bindingIndex >= 0) {
        // 直接使用 BaseObjectModel 对象，而不是原始对象
        model.bindingItems[bindingIndex].targetObject = droppedObject;
        model.bindingItems = [...model.bindingItems]; // 触发响应式更新

        console.log('✅ 拖拽对象已设置到绑定项:', bindingId);

        // 如果已经选择了字段，立即建立绑定
        const binding = model.bindingItems[bindingIndex];
        if (binding.fieldName && model) {
          console.log('🔗 立即建立绑定，字段:', binding.fieldName);
          establishBinding(binding);
        } else {
          console.log('⏳ 等待字段选择后建立绑定');
        }
      }
    } catch (error) {
      console.error('处理拖拽数据失败:', error);
    }
  }

  /**
   * 建立数据绑定
   */
  function establishBinding(binding: any) {
    console.log('🔗 开始建立数据绑定:', binding);

    if (!model) {
      console.warn('❌ 建立绑定失败：model 不存在');
      return;
    }

    if (!binding.targetObject) {
      console.warn('❌ 建立绑定失败：targetObject 不存在');
      return;
    }

    if (!binding.fieldName) {
      console.warn('❌ 建立绑定失败：fieldName 不存在');
      return;
    }

    try {
      // 需要找到对应的子组件模型
      console.log('🔍 查找子组件模型，目标对象:', binding.targetObject);
      console.log('📋 当前模型的子组件数量:', model.children?.length || 0);

      const targetModel = findChildModelByObject(binding.targetObject);
      if (targetModel) {
        console.log('✅ 找到目标模型:', targetModel);
        model.addDataBinding(targetModel, binding.fieldName);
        console.log('✅ 建立数据绑定成功:', {
          target: binding.targetObject.name || binding.targetObject.className,
          field: binding.fieldName,
          dataType: binding.dataType
        });

        // 验证绑定是否真的建立了
        const currentBindings = model.getDataBindings();
        console.log('📊 当前模型绑定数量:', currentBindings.size);
        console.log('📊 当前模型绑定详情:', [...currentBindings.entries()]);
      } else {
        console.warn('❌ 未找到对应的子组件模型');
        console.log('🔍 可用的子组件模型:');
        model.children?.forEach((child, index) => {
          console.log(`  ${index}: ${child.className} - ${child.name}`);
          console.log(`      原始对象:`, child.getOriginalObject());
          console.log(`      模型对象:`, child);
        });
        console.log('🎯 目标对象:', binding.targetObject);
        console.log('🎯 目标对象类型:', typeof binding.targetObject);
        console.log('🎯 目标对象是否有 getOriginalObject:', typeof binding.targetObject?.getOriginalObject);
      }
    } catch (error) {
      console.error('❌ 建立数据绑定失败:', error);
    }
  }

  /**
   * 根据模型对象查找对应的子组件模型
   */
  function findChildModelByObject(targetObject: any) {
    if (!model || !model.children) return null;

    // 如果 targetObject 本身就是 BaseObjectModel，直接检查是否在子组件中
    if (targetObject && typeof targetObject.getOriginalObject === 'function') {
      for (const childModel of model.children) {
        if (childModel === targetObject) {
          return childModel;
        }
      }
    }

    // 如果是原始对象，通过原始对象查找
    for (const childModel of model.children) {
      if (childModel.getOriginalObject() === targetObject) {
        return childModel;
      }
    }

    return null;
  }

  /**
   * 清除绑定对象（保留字段选择）
   */
  function clearBindingObject(bindingId: string) {
    if (!model) return;

    const bindingIndex = model.bindingItems.findIndex(item => item.id === bindingId);
    if (bindingIndex >= 0) {
      // 从模型中移除绑定关系
      const binding = model.bindingItems[bindingIndex];
      if (binding.targetObject && binding.fieldName && model) {
        const targetModel = findChildModelByObject(binding.targetObject);
        if (targetModel) {
          model.removeDataBinding(targetModel);
          console.log('🗑️ 已从模型中移除绑定关系');
        }
      }

      // 只清除对象，保留字段选择
      model.bindingItems[bindingIndex].targetObject = null;
      model.bindingItems = [...model.bindingItems]; // 触发响应式更新
      console.log('🗑️ 已清除绑定对象，保留字段选择:', bindingId);
    }
  }



  /**
   * 清除所有绑定
   */
  function clearAllBindings() {
    if (!model) return;

    if (confirm('确定要清除所有数据绑定吗？')) {
      // 清除模型中的绑定
      model.clearDataBindings();

      // 清除界面中的绑定项
      model.bindingItems = [];

      console.log('✅ 已清除所有数据绑定');
    }
  }

  /**
   * 获取当前绑定状态信息
   */
  function getBindingStatusInfo() {
    if (!model) return '模型未加载';

    const modelBindings = model.getDataBindings();
    const uiBindings = bindingItems.filter(item => item.targetObject && item.fieldName);

    return `模型绑定: ${modelBindings.size} 个，界面绑定: ${uiBindings.length} 个`;
  }

  /**
   * 🆕 为绑定项打开数据绑定模态框
   */
  function openDataBindingForBinding(binding: any) {
    if (!binding.targetObject) {
      alert('请先拖拽一个组件到绑定区域');
      return;
    }

    currentBindingTarget = findChildModelByObject(binding.targetObject);
    if (!currentBindingTarget) {
      alert('未找到对应的组件模型');
      return;
    }

    // 保存当前绑定项的引用，用于后续更新字段名
    currentBindingTarget._bindingItem = binding;

    showDataBindingModal = true;
    console.log('📋 为绑定项打开数据绑定模态框:', binding);
  }

  /**
   * 🆕 处理数据绑定确认
   */
  function handleDataBindingConfirm(expression: string) {
    if (!model || !currentBindingTarget) {
      console.warn('⚠️ 无法绑定：模型或目标组件不存在');
      return;
    }

    try {
      // 从表达式中提取字段名（简化处理）
      const fieldName = extractFieldNameFromExpression(expression);

      // 更新绑定项的字段名
      if (currentBindingTarget._bindingItem) {
        currentBindingTarget._bindingItem.fieldName = fieldName;

        // 建立数据绑定
        establishBinding(currentBindingTarget._bindingItem);
      } else {
        // 直接添加数据绑定（兼容旧方式）
        model.addDataBinding(currentBindingTarget, fieldName);
      }

      console.log('✅ 成功绑定字段', fieldName, '到组件', currentBindingTarget.className);

      // 关闭模态框
      showDataBindingModal = false;
      currentBindingTarget = null;

    } catch (error) {
      console.error('❌ 绑定字段失败:', error);
      alert('绑定字段失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * 🆕 从表达式中提取字段名
   */
  function extractFieldNameFromExpression(expression: string): string {
    // 简化处理：从表达式中提取最后一个属性名
    // 例如：window.$dataItems[0].name -> name
    const parts = expression.split('.');
    const lastPart = parts[parts.length - 1];

    // 移除可能的括号和索引
    return lastPart.replace(/\[.*?\]/g, '').trim();
  }



  /**
   * 打开事件选择模态框
   */
  function openEventModal() {
    showEventModal = true;
  }

  /**
   * 关闭事件选择模态框
   */
  function closeEventModal() {
    showEventModal = false;
  }

  /**
   * 处理事件代码选择
   */
  function handleEventCodeSelect(event: CustomEvent) {
    if (!model) return;

    const eventCode = event.detail?.code || '';

    // 根据选择的事件类型设置代码
    switch (selectedEventType) {
      case 'onClickCode':
        model.onClickCode = eventCode;
        break;
      case 'onDoubleClickCode':
        model.onDoubleClickCode = eventCode;
        break;
      case 'onHoverCode':
        model.onHoverCode = eventCode;
        break;
    }

    closeEventModal();
  }


</script>

<AccordionPanel title="UIItem 属性">


  <!-- 数据绑定 -->
  <div class="property-section">
    <h4>数据绑定</h4>

    <div class="binding-section">
      <!-- 绑定状态和操作 -->
      <div class="binding-header">
        <div class="binding-status">
          <Label text={getBindingStatusInfo()} />
        </div>
        <div class="binding-actions">
          <button class="add-binding-btn" onclick={addBindingItem}>
            + 添加绑定
          </button>
          {#if bindingItems.length > 0}
            <button class="clear-all-btn" onclick={clearAllBindings}>
              清除所有
            </button>
          {/if}
        </div>
      </div>

      <!-- 绑定项列表 -->
      {#each bindingItems as binding (binding.id)}
        <div class="binding-item-container">
          <!-- 拖拽目标区域 -->
          {#if binding.targetObject}
            <div class="bound-object-display">
              <div class="dropped-object">
                <span class="object-type">{binding.targetObject.className || 'Object'}</span>
                <span class="object-name">{binding.targetObject.name || '未命名'}</span>
              </div>
              <button
                class="clear-object-btn"
                onclick={() => clearBindingObject(binding.id)}
                title="清除绑定对象"
              >
                🗑️
              </button>
            </div>
          {:else}
            <DropTarget
              onDrop={(object) => handleObjectDrop(object, binding.id)}
              placeholder="拖拽对象到此处"
              targetObject={binding}
              fieldName="targetObject"
              enableHistory={false}
              operationName="绑定数据对象"
            />
          {/if}

          <!-- 字段绑定 -->
          <div class="binding-controls">
            <div class="field-binding-row">
              {#if binding.fieldName}
                <div class="bound-field-display">
                  <span class="field-label">已绑定字段:</span>
                  <span class="field-name">{binding.fieldName}</span>
                </div>
              {:else}
                <span class="no-field-text">未绑定字段</span>
              {/if}
              <button
                class="bind-field-btn"
                onclick={() => openDataBindingForBinding(binding)}
                title="使用数据绑定模态框选择字段"
              >
                🔗 绑定字段
              </button>
            </div>
          </div>

          <!-- 删除整个绑定项按钮 -->
          <div class="binding-actions-row">
            <button
              class="remove-binding-btn"
              onclick={() => removeBindingItem(binding.id)}
              title="删除整个绑定项"
            >
              ❌ 删除绑定
            </button>
          </div>
        </div>
      {/each}

      {#if bindingItems.length === 0}
        <div class="no-bindings">
          暂无数据绑定，点击上方按钮添加
        </div>
      {/if}
    </div>
  </div>



  <!-- 事件代码 -->
  <div class="property-section">
    <h4>事件代码</h4>

    <div class="event-section">
      <div class="event-type-selector">
        <Select
          bind:value={selectedEventType}
          options={eventTypes}
          placeholder="选择事件类型"
        />
        <button class="event-btn" onclick={openEventModal}>
          选择事件
        </button>
      </div>

      <!-- 显示当前选择的事件代码 -->
      {#if selectedEventType === 'onClickCode'}
        <div class="event-code-display">
          <Label text="点击事件代码:" />
          <textarea
            bind:value={model.onClickCode}
            placeholder="// 点击时执行的代码"
            rows="3"
          ></textarea>
        </div>
      {:else if selectedEventType === 'onDoubleClickCode'}
        <div class="event-code-display">
          <Label text="双击事件代码:" />
          <textarea
            bind:value={model.onDoubleClickCode}
            placeholder="// 双击时执行的代码"
            rows="3"
          ></textarea>
        </div>
      {:else if selectedEventType === 'onHoverCode'}
        <div class="event-code-display">
          <Label text="悬停事件代码:" />
          <textarea
            bind:value={model.onHoverCode}
            placeholder="// 悬停时执行的代码"
            rows="3"
          ></textarea>
        </div>
      {/if}
    </div>
  </div>
</AccordionPanel>

{#if showEventModal}
  <EventSelectionModal
    onEventSelected={handleEventCodeSelect}
    onClose={closeEventModal}
  />
{/if}

<!-- 🆕 数据绑定模态框 -->
<DataBindingModal
  bind:isOpen={showDataBindingModal}
  onConfirm={handleDataBindingConfirm}
  mode="field"
/>

<style>
  .property-section {
    margin-bottom: 16px;
    padding: 8px;
    border: 1px solid #444;
    border-radius: 4px;
    background: #2a2a2a;
  }

  .property-section h4 {
    margin: 0 0 8px 0;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
  }

  .binding-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  /* 新的数据绑定样式 */
  .binding-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;
  }

  .binding-status {
    flex: 1;
    color: #ccc;
    font-size: 12px;
  }

  .binding-actions {
    display: flex;
    gap: 6px;
  }

  .add-binding-btn {
    background: #4CAF50;
    border: none;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    white-space: nowrap;
  }

  .add-binding-btn:hover {
    background: #45a049;
  }

  .clear-all-btn {
    background: #ff9800;
    border: none;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    white-space: nowrap;
  }

  .clear-all-btn:hover {
    background: #f57c00;
  }

  /* 🆕 字段绑定行样式 */
  .field-binding-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 8px;
    background: var(--theme-surface-variant, #2a2a2a);
    border-radius: 4px;
  }

  .bound-field-display {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
  }

  .field-label {
    font-size: 11px;
    color: var(--theme-text-secondary, #6b7280);
  }

  .field-name {
    font-size: 11px;
    font-weight: 500;
    color: var(--theme-primary, #3b82f6);
    background: var(--theme-primary-dark, #1e3a8a);
    padding: 2px 6px;
    border-radius: 3px;
  }

  .no-field-text {
    font-size: 11px;
    color: var(--theme-text-secondary, #6b7280);
    font-style: italic;
  }

  .bind-field-btn {
    padding: 4px 8px;
    background: var(--theme-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 10px;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .bind-field-btn:hover {
    background: var(--theme-primary-dark, #2563eb);
  }



  .binding-item-container {
    border: 1px solid #444;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 12px;
    background: #2a2a2a;
    position: relative;
  }

  .bound-object-display {
    border: 1px solid #4CAF50;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 12px;
    background: rgba(76, 175, 80, 0.1);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dropped-object {
    display: flex;
    flex-direction: column;
    gap: 4px;
    color: #4CAF50;
    text-align: center;
  }

  .object-type {
    font-weight: bold;
    font-size: 12px;
  }

  .object-name {
    font-size: 14px;
  }

  .clear-object-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    background: #ff9800;
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .clear-object-btn:hover {
    background: #f57c00;
  }

  .binding-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 8px;
  }



  .binding-actions-row {
    display: flex;
    justify-content: center;
    margin-top: 8px;
  }

  .remove-binding-btn {
    background: #f44336;
    border: none;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .remove-binding-btn:hover {
    background: #d32f2f;
  }

  .no-bindings {
    text-align: center;
    color: #888;
    font-style: italic;
    padding: 20px;
  }

  .event-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .event-type-selector {
    display: flex;
    gap: 6px;
    align-items: center;
  }

  .event-type-selector :global(.select) {
    flex: 1;
  }

  .event-btn {
    background: #2196F3;
    border: none;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    white-space: nowrap;
  }

  .event-btn:hover {
    background: #1976D2;
  }

  .event-code-display {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .event-code-display textarea {
    width: 100%;
    background: #333;
    border: 1px solid #555;
    border-radius: 4px;
    color: white;
    padding: 6px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
    resize: vertical;
    min-height: 50px;
  }

  .event-code-display textarea:focus {
    outline: none;
    border-color: #2196F3;
  }

  .event-code-display textarea::placeholder {
    color: #888;
  }
</style>
