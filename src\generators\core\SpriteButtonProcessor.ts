/**
 * SpriteButton 处理器
 * 处理 SpriteButton 对象的序列化、反序列化和代码生成
 */

import { SpriteProcessor } from './SpriteProcessor';
import { type SpriteButtonProperties, type ButtonState, type ButtonType } from '../types/spriteButton';

/**
 * SpriteButton 处理器
 */
export class SpriteButtonProcessor extends SpriteProcessor {

  /**
   * 序列化 SpriteButton 对象
   * @param obj SpriteButton 对象
   * @returns 序列化后的属性
   */
  serialize(obj: any): SpriteButtonProperties {
    // 先获取基础 Sprite 属性
    const spriteProperties = super.serialize(obj);

    // 添加 SpriteButton 特有属性（适应新的简化实现）
    return {
      ...spriteProperties,
      // 按钮特有属性
      buttonType: obj._buttonType || 'normal' as ButtonType,
      currentState: obj._currentState || 'normal' as ButtonState,
      isPressed: obj._isPressed !== undefined ? obj._isPressed : false,
      isEnabled: obj._isEnabled !== undefined ? obj._isEnabled : true,

      // 按钮文本
      buttonText: obj._buttonText || obj.getText?.() || 'Button',

      // 事件处理代码（字符串形式）
      onClick: obj._onClickCode || obj._clickHandler?.toString() || undefined,
      onHover: obj._onHoverCode || obj._hoverHandler?.toString() || undefined,
      onPress: obj._onPressCode || obj._pressHandler?.toString() || undefined,
      onRelease: obj._onReleaseCode || obj._releaseHandler?.toString() || undefined,

      // 按钮组
      buttonGroup: obj._buttonGroup || undefined
    };
  }

  /**
   * 反序列化为 SpriteButton 对象
   * @param data SpriteButton 属性
   * @returns SpriteButton 对象
   */
  deserialize(data: SpriteButtonProperties): any {
    // 创建新的 SpriteButton（不传递 bitmap 参数，让它自动创建）
    const spriteButton = new (globalThis as any).SpriteButton();

    // 设置基础 Sprite 属性
    spriteButton.x = data.x || 0;
    spriteButton.y = data.y || 0;
    spriteButton.visible = data.visible !== undefined ? data.visible : true;
    spriteButton.alpha = data.alpha !== undefined ? data.alpha : 1;

    // 设置 SpriteButton 特有属性
    spriteButton._buttonType = data.buttonType;
    spriteButton._currentState = data.currentState;
    spriteButton._isPressed = data.isPressed;
    spriteButton._isEnabled = data.isEnabled;

    // 设置按钮文本
    if (data.buttonText && spriteButton.setText) {
      spriteButton.setText(data.buttonText);
    }

    // 设置事件处理代码
    if (data.onClick) spriteButton._onClickCode = data.onClick;
    if (data.onHover) spriteButton._onHoverCode = data.onHover;
    if (data.onPress) spriteButton._onPressCode = data.onPress;
    if (data.onRelease) spriteButton._onReleaseCode = data.onRelease;

    // 设置按钮组
    if (data.buttonGroup) spriteButton._buttonGroup = data.buttonGroup;

    return spriteButton;
  }

  /**
   * 生成 SpriteButton 创建代码
   * @param varName 变量名
   * @param data SpriteButton 属性
   * @param indent 缩进字符串
   * @returns 生成的代码字符串
   */
  generateCode(varName: string, data: SpriteButtonProperties, indent: string): string {
    const codes: string[] = [];

    // 1. 先生成基础 Sprite 代码（包含 bitmap 数据对象）
    const spriteCode = super.generateCode(varName, data, indent);
    codes.push(spriteCode);

    // 2. 设置按钮文本
    if (data.buttonText && data.buttonText !== 'Button') {
      codes.push(`${indent}// 设置按钮文本`);
      codes.push(`${indent}${varName}.setText('${data.buttonText}');`);
    }

    // 3. 设置按钮类型和状态
    if (data.buttonType && data.buttonType !== 'normal') {
      codes.push(`${indent}// 设置按钮类型`);
      codes.push(`${indent}${varName}.setButtonType('${data.buttonType}');`);
    }
    if (data.isEnabled !== undefined && !data.isEnabled) {
      codes.push(`${indent}// 设置启用状态`);
      codes.push(`${indent}${varName}.setEnabled(${data.isEnabled});`);
    }

    // 4. 设置事件处理
    if (data.onClick) {
      codes.push(`${indent}// 设置点击事件`);
      codes.push(`${indent}${varName}.setClickHandler(() => {`);
      codes.push(`${indent}  ${data.onClick}`);
      codes.push(`${indent}});`);
    }

    // 5. 设置按钮组（仅对 radio 类型）
    if (data.buttonGroup && data.buttonType === 'radio') {
      codes.push(`${indent}// 设置按钮组`);
      codes.push(`${indent}${varName}._buttonGroup = '${data.buttonGroup}';`);
    }

    return codes.join('\n');
  }
}
