/**
 * 选中对象管理器 - 管理选中对象的状态和信息
 */

import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';
import type {
  SelectedObjectInfo,
  ArrowConfig,
  ArrowType,
  HitTestResult,
  ToolConfig,
  ToolEventCallbacks,
  ToolMode
} from './types';
import { DrawingUtils } from './DrawingUtils';

export class SelectionManager {
  private selectedObject: BaseObjectModel | null = null;
  private selectedObjectInfo: SelectedObjectInfo | null = null;
  private arrowConfigs: ArrowConfig[] = [];
  private hoveredArrow: ArrowType | null = null;
  private currentMode: ToolMode = 'transform' as ToolMode;

  private config: ToolConfig;
  private callbacks: ToolEventCallbacks;

  constructor(config: ToolConfig, callbacks: ToolEventCallbacks = {}) {
    this.config = config;
    this.callbacks = callbacks;
  }

  /**
   * 设置选中对象
   */
  setSelectedObject(object: BaseObjectModel | null): void {
    if (this.selectedObject === object) {
      return;
    }

    const previousObject = this.selectedObject;
    this.selectedObject = object;

    if (object) {
      this.updateSelectedObjectInfo();
      this.updateArrowConfigs();
      console.log('🎯 选中对象:', object.className, {
        position: { x: object.x, y: object.y },
        size: { width: object.width, height: object.height }
      });
    } else {
      this.selectedObjectInfo = null;
      this.arrowConfigs = [];
      console.log('🎯 取消选中');
    }

    this.callbacks.onSelectionChange?.(object);
  }

  /**
   * 获取当前选中对象
   */
  getSelectedObject(): BaseObjectModel | null {
    return this.selectedObject;
  }

  /**
   * 获取选中对象信息
   */
  getSelectedObjectInfo(): SelectedObjectInfo | null {
    return this.selectedObjectInfo;
  }

  /**
   * 获取箭头配置
   */
  getArrowConfigs(): ArrowConfig[] {
    return this.arrowConfigs;
  }

  /**
   * 计算对象的全局坐标（相对于游戏Canvas）
   */
  private getGlobalPosition(obj: BaseObjectModel): { x: number; y: number } {
    // 通过 _originalObject 获取实际的 Sprite 对象
    const sprite = (obj as any)._originalObject;

    if (sprite) {
      // 方法1：使用 PIXI 的 toGlobal 方法
      try {
        const globalPos = sprite.toGlobal({ x: 0, y: 0 });

        console.log('🌍 PIXI全局坐标转换:', {
          localPos: { x: obj.x, y: obj.y },
          spritePos: { x: sprite.x, y: sprite.y },
          globalPos: { x: globalPos.x, y: globalPos.y },
          hasParent: !!sprite.parent,
          parentInfo: sprite.parent ? {
            x: sprite.parent.x,
            y: sprite.parent.y,
            scale: sprite.parent.scale ? { x: sprite.parent.scale.x, y: sprite.parent.scale.y } : 'N/A'
          } : null
        });

        return { x: globalPos.x, y: globalPos.y };
      } catch (error) {
        console.warn('🌍 PIXI toGlobal 失败，使用手动计算:', error);
      }
    }

    // 方法2：手动计算全局坐标（备用方案）
    let globalX = obj.x;
    let globalY = obj.y;

    if (sprite && sprite.parent) {
      // 简单的父级坐标累加（不考虑复杂变换）
      let currentParent = sprite.parent;
      while (currentParent) {
        globalX += currentParent.x || 0;
        globalY += currentParent.y || 0;
        currentParent = currentParent.parent;
      }

      console.log('🌍 手动坐标计算:', {
        localPos: { x: obj.x, y: obj.y },
        globalPos: { x: globalX, y: globalY }
      });
    } else {
      console.log('🌍 使用局部坐标（无父级）:', { x: globalX, y: globalY });
    }

    return { x: globalX, y: globalY };
  }

  /**
   * 更新选中对象信息
   */
  private updateSelectedObjectInfo(): void {
    if (!this.selectedObject) {
      this.selectedObjectInfo = null;
      return;
    }

    const obj = this.selectedObject;

    // 获取全局坐标
    const globalPos = this.getGlobalPosition(obj);

    // 计算对象边界（使用全局坐标，考虑锚点）
    const bounds = {
      x: globalPos.x - obj.anchorX * obj.width,
      y: globalPos.y - obj.anchorY * obj.height,
      width: obj.width,
      height: obj.height
    };

    // 计算中心点
    const center = {
      x: bounds.x + bounds.width / 2,
      y: bounds.y + bounds.height / 2
    };

    this.selectedObjectInfo = {
      object: obj,
      bounds,
      center
    };
  }

  /**
   * 更新箭头配置
   */
  private updateArrowConfigs(): void {
    if (!this.selectedObjectInfo) {
      this.arrowConfigs = [];
      return;
    }

    this.arrowConfigs = DrawingUtils.createArrowConfigs(
      this.selectedObjectInfo,
      this.config.arrowSize,
      this.config.arrowOffset,
      this.config.arrowColors,
      this.currentMode
    );
  }

  /**
   * 设置工具模式
   */
  setToolMode(mode: ToolMode): void {
    if (this.currentMode !== mode) {
      this.currentMode = mode;
      // 重新生成箭头配置
      this.updateArrowConfigs();
    }
  }

  /**
   * 获取当前工具模式
   */
  getToolMode(): ToolMode {
    return this.currentMode;
  }

  /**
   * 更新对象位置后刷新信息
   */
  refreshObjectInfo(): void {
    if (this.selectedObject) {
      this.updateSelectedObjectInfo();
      this.updateArrowConfigs();
    }
  }

  /**
   * 设置悬停的箭头
   */
  setHoveredArrow(arrowType: ArrowType | null): void {
    if (this.hoveredArrow !== arrowType) {
      this.hoveredArrow = arrowType;
    }
  }

  /**
   * 获取悬停的箭头
   */
  getHoveredArrow(): ArrowType | null {
    return this.hoveredArrow;
  }

  /**
   * 碰撞检测 - 检查鼠标位置是否在选中对象的UI元素上
   */
  hitTest(mouseX: number, mouseY: number): HitTestResult {
    if (!this.selectedObjectInfo) {
      return { hit: false, type: 'none' };
    }

    // 检测箭头
    for (const arrowConfig of this.arrowConfigs) {
      if (DrawingUtils.hitTestArrow(mouseX, mouseY, arrowConfig)) {
        return {
          hit: true,
          type: 'arrow',
          arrowType: arrowConfig.type,
          distance: Math.sqrt(
            Math.pow(mouseX - arrowConfig.x, 2) +
            Math.pow(mouseY - arrowConfig.y, 2)
          )
        };
      }
    }

    // 检测包围盒
    if (DrawingUtils.hitTestBoundingBox(mouseX, mouseY, this.selectedObjectInfo.bounds)) {
      return { hit: true, type: 'boundingBox' };
    }

    return { hit: false, type: 'none' };
  }

  /**
   * 检查是否有选中对象
   */
  hasSelection(): boolean {
    return this.selectedObject !== null;
  }

  /**
   * 清除选择
   */
  clearSelection(): void {
    this.setSelectedObject(null);
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ToolConfig>): void {
    this.config = { ...this.config, ...config };
    if (this.selectedObject) {
      this.updateArrowConfigs();
    }
  }

  /**
   * 更新回调
   */
  updateCallbacks(callbacks: Partial<ToolEventCallbacks>): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * 获取选中对象的边界框（用于渲染）
   */
  getSelectionBounds(): { x: number; y: number; width: number; height: number } | null {
    return this.selectedObjectInfo?.bounds || null;
  }

  /**
   * 检查对象是否在视口内
   */
  isObjectInViewport(
    viewportX: number,
    viewportY: number,
    viewportWidth: number,
    viewportHeight: number
  ): boolean {
    if (!this.selectedObjectInfo) {
      return false;
    }

    const bounds = this.selectedObjectInfo.bounds;
    return !(
      bounds.x + bounds.width < viewportX ||
      bounds.x > viewportX + viewportWidth ||
      bounds.y + bounds.height < viewportY ||
      bounds.y > viewportY + viewportHeight
    );
  }

  /**
   * 获取对象在屏幕上的位置（考虑视口偏移和缩放）
   */
  getScreenPosition(
    viewportX: number = 0,
    viewportY: number = 0,
    scale: number = 1
  ): { x: number; y: number; width: number; height: number } | null {
    if (!this.selectedObjectInfo) {
      return null;
    }

    const bounds = this.selectedObjectInfo.bounds;
    return {
      x: (bounds.x - viewportX) * scale,
      y: (bounds.y - viewportY) * scale,
      width: bounds.width * scale,
      height: bounds.height * scale
    };
  }
}
