/**
 * 全局拖拽状态管理
 */
import { writable } from 'svelte/store';
import type { BaseObjectModel } from '../type/baseObjectModel.svelte';

interface DragState {
  isDragging: boolean;
  draggedObject: BaseObjectModel | null;
}

const initialState: DragState = {
  isDragging: false,
  draggedObject: null
};

function createDragStore() {
  const { subscribe, set, update } = writable<DragState>(initialState);

  return {
    subscribe,
    
    // 开始拖拽
    startDrag: (object: BaseObjectModel) => {
      update(state => ({
        isDragging: true,
        draggedObject: object
      }));
      console.log('🎯 开始拖拽:', object.className);
    },
    
    // 结束拖拽
    endDrag: () => {
      update(state => ({
        isDragging: false,
        draggedObject: null
      }));
      console.log('🎯 结束拖拽');
    },
    
    // 重置状态
    reset: () => set(initialState)
  };
}

export const dragStore = createDragStore();
