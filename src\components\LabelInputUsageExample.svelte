<script lang="ts">
  /**
   * LabelInput 使用示例
   * 展示如何正确使用新的历史记录功能
   */

  import LabelInput from './LabelInput.svelte';
  import { historyManager } from '../historyManager';

  // 模拟一个模型对象
  let gameObject = {
    className: 'GameObject',
    x: 100,
    y: 200,
    width: 64,
    height: 64,
    scale: 1.0,
    rotation: 0
  };

  // 响应式状态
  let objectX = $state(gameObject.x);
  let objectY = $state(gameObject.y);
  let objectWidth = $state(gameObject.width);
  let objectHeight = $state(gameObject.height);
  let objectScale = $state(gameObject.scale);
  let objectRotation = $state(gameObject.rotation);

  // 处理变更的函数
  function handleXChange(newValue: string | number) {
    const numValue = typeof newValue === 'number' ? newValue : parseFloat(newValue.toString());
    gameObject.x = numValue;
    objectX = numValue;
    console.log('X坐标变更:', numValue);
  }

  function handleYChange(newValue: string | number) {
    const numValue = typeof newValue === 'number' ? newValue : parseFloat(newValue.toString());
    gameObject.y = numValue;
    objectY = numValue;
    console.log('Y坐标变更:', numValue);
  }

  function handleWidthChange(newValue: string | number) {
    const numValue = typeof newValue === 'number' ? newValue : parseFloat(newValue.toString());
    gameObject.width = numValue;
    objectWidth = numValue;
    console.log('宽度变更:', numValue);
  }

  function handleHeightChange(newValue: string | number) {
    const numValue = typeof newValue === 'number' ? newValue : parseFloat(newValue.toString());
    gameObject.height = numValue;
    objectHeight = numValue;
    console.log('高度变更:', numValue);
  }

  function handleScaleChange(newValue: string | number) {
    const numValue = typeof newValue === 'number' ? newValue : parseFloat(newValue.toString());
    gameObject.scale = numValue;
    objectScale = numValue;
    console.log('缩放变更:', numValue);
  }

  function handleRotationChange(newValue: string | number) {
    const numValue = typeof newValue === 'number' ? newValue : parseFloat(newValue.toString());
    gameObject.rotation = numValue;
    objectRotation = numValue;
    console.log('旋转变更:', numValue);
  }

  // 历史记录操作
  function undo() {
    const success = historyManager.undo();
    if (success) {
      // 同步UI状态
      objectX = gameObject.x;
      objectY = gameObject.y;
      objectWidth = gameObject.width;
      objectHeight = gameObject.height;
      objectScale = gameObject.scale;
      objectRotation = gameObject.rotation;
      console.log('撤销成功');
    }
  }

  function redo() {
    const success = historyManager.redo();
    if (success) {
      // 同步UI状态
      objectX = gameObject.x;
      objectY = gameObject.y;
      objectWidth = gameObject.width;
      objectHeight = gameObject.height;
      objectScale = gameObject.scale;
      objectRotation = gameObject.rotation;
      console.log('重做成功');
    }
  }

  function clearHistory() {
    historyManager.clear();
    console.log('历史记录已清空');
  }
</script>

<div class="usage-example">
  <h2>LabelInput 使用示例</h2>

  <div class="example-section">
    <h3>✅ 正确用法（支持历史记录）</h3>

    <div class="property-grid">
      <!-- 位置属性 -->
      <div class="property-group">
        <h4>📍 位置</h4>
        <div class="input-row">
          <label>X坐标:</label>
          <LabelInput
            bind:value={objectX}
            type="number"
            step={1}
            onChange={handleXChange}
            targetObject={gameObject}
            fieldName="x"
            enableHistory={true}
            name="X坐标"
          />
        </div>
        <div class="input-row">
          <label>Y坐标:</label>
          <LabelInput
            bind:value={objectY}
            type="number"
            step={1}
            onChange={handleYChange}
            targetObject={gameObject}
            fieldName="y"
            enableHistory={true}
            name="Y坐标"
          />
        </div>
      </div>

      <!-- 尺寸属性 -->
      <div class="property-group">
        <h4>📏 尺寸</h4>
        <div class="input-row">
          <label>宽度:</label>
          <LabelInput
            bind:value={objectWidth}
            type="number"
            step={1}
            min={1}
            onChange={handleWidthChange}
            targetObject={gameObject}
            fieldName="width"
            enableHistory={true}
            name="宽度"
          />
        </div>
        <div class="input-row">
          <label>高度:</label>
          <LabelInput
            bind:value={objectHeight}
            type="number"
            step={1}
            min={1}
            onChange={handleHeightChange}
            targetObject={gameObject}
            fieldName="height"
            enableHistory={true}
            name="高度"
          />
        </div>
      </div>

      <!-- 变换属性 -->
      <div class="property-group">
        <h4>🔄 变换</h4>
        <div class="input-row">
          <label>缩放:</label>
          <LabelInput
            bind:value={objectScale}
            type="number"
            step={0.1}
            min={0.1}
            max={5.0}
            precision={2}
            onChange={handleScaleChange}
            targetObject={gameObject}
            fieldName="scale"
            enableHistory={true}
            name="缩放"
          />
        </div>
        <div class="input-row">
          <label>旋转:</label>
          <LabelInput
            bind:value={objectRotation}
            type="number"
            step={1}
            min={0}
            max={360}
            onChange={handleRotationChange}
            targetObject={gameObject}
            fieldName="rotation"
            enableHistory={true}
            name="旋转"
          />
        </div>
      </div>
    </div>
  </div>

  <div class="example-section">
    <h3>🎮 历史记录控制</h3>
    <div class="button-row">
      <button onclick={undo}>↶ 撤销 (Ctrl+Z)</button>
      <button onclick={redo}>↷ 重做 (Ctrl+Y)</button>
      <button onclick={clearHistory}>🗑️ 清空历史</button>
    </div>
  </div>

  <div class="example-section">
    <h3>📊 当前对象状态</h3>
    <div class="object-state">
      <pre>{JSON.stringify(gameObject, null, 2)}</pre>
    </div>
  </div>

  <div class="example-section">
    <h3>📝 使用说明</h3>
    <ul>
      <li><strong>拖拽模式</strong>：在标签上按住鼠标左键并拖拽来调整数值</li>
      <li><strong>输入模式</strong>：双击标签进入编辑模式，直接输入数值，按Enter确认或Escape取消</li>
      <li><strong>历史记录</strong>：两种模式都只会产生一条历史记录</li>
      <li><strong>撤销/重做</strong>：使用按钮或快捷键来测试历史记录功能</li>
      <li><strong>必需参数</strong>：<code>targetObject</code> 和 <code>fieldName</code> 是必需的</li>
      <li><strong>可选参数</strong>：<code>enableHistory</code> 默认为 true</li>
    </ul>

    <div class="test-modes">
      <h4>🧪 测试两种模式：</h4>
      <ol>
        <li><strong>拖拽测试</strong>：拖拽任意数值，然后按Ctrl+Z撤销</li>
        <li><strong>输入测试</strong>：双击数值进入编辑模式，输入新值，按Enter确认，然后按Ctrl+Z撤销</li>
        <li><strong>取消测试</strong>：双击数值进入编辑模式，输入新值，按Escape取消（不应产生历史记录）</li>
      </ol>
    </div>
  </div>
</div>

<style>
  .usage-example {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
    font-family: system-ui, sans-serif;
  }

  .example-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
  }

  .example-section h3 {
    margin-top: 0;
    color: #333;
  }

  .property-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .property-group {
    padding: 15px;
    border: 1px solid #ccc;
    border-radius: 6px;
    background: white;
  }

  .property-group h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #555;
  }

  .input-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
  }

  .input-row label {
    min-width: 60px;
    font-weight: 500;
    font-size: 14px;
  }

  .button-row {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  button {
    padding: 10px 16px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }

  button:hover {
    background: #f0f0f0;
    border-color: #999;
  }

  button:active {
    background: #e0e0e0;
  }

  .object-state {
    background: #2d2d2d;
    color: #f8f8f2;
    padding: 15px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    overflow-x: auto;
  }

  ul {
    margin: 0;
    padding-left: 20px;
  }

  li {
    margin-bottom: 8px;
    line-height: 1.4;
  }

  code {
    background: #f0f0f0;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
  }

  .test-modes {
    margin-top: 15px;
    padding: 15px;
    background: #e8f4fd;
    border-radius: 6px;
    border-left: 4px solid #2196f3;
  }

  .test-modes h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #1976d2;
  }

  .test-modes ol {
    margin: 0;
    padding-left: 20px;
  }

  .test-modes li {
    margin-bottom: 8px;
    line-height: 1.4;
  }
</style>
