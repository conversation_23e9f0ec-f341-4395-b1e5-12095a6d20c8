<script lang="ts">
  import { historyManager } from '../historyManager';

  // Props
  export let value: string = '#ffffff';
  export let label: string = '';
  export let disabled: boolean = false;
  export let onChange: (color: string) => void = () => {};

  // 历史记录相关
  export let targetObject: any = undefined;
  export let fieldName: string | undefined = undefined;
  export let enableHistory: boolean = true;
  export let name: string = '';
  export let id: string = '';

  // 内部状态
  let initialValue: string | null = null;

  // 处理颜色变化开始（记录初始值）
  function handleColorChangeStart(_event: Event) {
    if (enableHistory && historyManager.isRecording()) {
      initialValue = value;
      console.log("🎨 ColorPicker: 记录初始颜色:", initialValue);
    }
  }

  // 处理颜色变化结束（记录历史）
  function handleColorChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const newValue = target.value;

    console.log("🎨 ColorPicker: 颜色变化:", {
      from: value,
      to: newValue,
      enableHistory,
      targetObject: targetObject?.className || 'none',
      fieldName: fieldName || 'none'
    });

    // 更新值
    value = newValue;
    onChange(newValue);

    // 🎯 处理历史记录
    if (enableHistory && initialValue !== null && initialValue !== newValue && historyManager.isRecording()) {
      if (targetObject && fieldName) {
        // 记录到模型对象
        historyManager.recordChange(targetObject, fieldName, initialValue, newValue);
        console.log("📝 HistoryManager: 已记录颜色变更");
      } else {
        // 如果没有指定目标对象，创建一个虚拟对象来记录
        const virtualObject = {
          className: `ColorPicker_${name || id || 'unnamed'}`,
          [fieldName || 'color']: newValue
        };
        historyManager.recordChange(virtualObject, fieldName || 'color', initialValue, newValue);
        console.log("📝 HistoryManager: 已记录颜色虚拟对象变更");
      }
    }

    // 清理初始值
    initialValue = null;
  }

  // 处理文本输入变化 - 暂时不使用
  /* function handleTextChange(event: Event) {
    const target = event.target as HTMLInputElement;
    let newValue = target.value;

    // 确保以#开头
    if (newValue && !newValue.startsWith('#')) {
      newValue = '#' + newValue;
    }

    // 验证颜色格式
    if (/^#[0-9A-Fa-f]{6}$/.test(newValue) || /^#[0-9A-Fa-f]{3}$/.test(newValue)) {
      value = newValue;
      onChange(value);
    }
  } */


</script>

<div class="color-picker">
  {#if label}
    <label class="color-label">{label}</label>
  {/if}

  <!-- 颜色选择器 -->
  <input
    type="color"
    bind:value
    onmousedown={handleColorChangeStart}
    oninput={handleColorChange}
    {disabled}
    class="color-input"
    title="选择颜色"
  />

  <!-- 文本输入框 - 暂时隐藏 -->
  <!-- <input
    type="text"
    bind:value
    on:input={handleTextChange}
    {disabled}
    class="color-text"
    placeholder="#ffffff"
    maxlength="7"
  /> -->
</div>

<style>
  .color-picker {
    display: flex;
    align-items: center;
    gap: var(--spacing-1, 0.25rem);
  }

  .color-label {
    font-size: var(--font-size-xs, 0.75rem);
    font-weight: 500;
    color: var(--theme-text, #111827);
    min-width: fit-content;
  }

  .color-input {
    width: 40px;
    height: 20px;
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 4px;
    cursor: pointer;
    background: none;
    padding: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  /* 确保颜色区域可以点击 */
  .color-input::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: 4px;
  }

  .color-input::-webkit-color-swatch {
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .color-input::-moz-color-swatch {
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .color-input:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  /* 文本输入框样式 - 暂时不使用
  .color-text {
    width: 50px;
    padding: 2px 4px;
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 3px;
    font-size: var(--font-size-xs, 0.75rem);
    font-family: monospace;
    background: var(--theme-surface, #ffffff);
  }

  .color-text:focus {
    outline: none;
    border-color: var(--theme-primary, #3b82f6);
    box-shadow: 0 0 0 3px var(--theme-primary-light, #dbeafe);
  }

  .color-text:disabled {
    background: var(--theme-surface-light, #f9fafb);
    color: var(--theme-text-secondary, #6b7280);
    cursor: not-allowed;
  }
  */
</style>
