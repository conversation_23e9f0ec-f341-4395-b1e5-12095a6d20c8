/*:
 * @target MZ
 * @plugindesc 跟踪并记录Bitmap上的文本和图像绘制操作
 * <AUTHOR> Agent
 * @help
 * 这个插件重写了Bitmap的drawText和blt方法，
 * 记录所有绘制的文本和图像信息到elements数组中。
 *
 * 特点:
 * 1. 只记录参数信息，不记录上下文状态
 * 2. 支持区域性重绘，只更新需要的部分
 * 3. 可以选择性地更新特定文本或图像
 * 4. 支持根据文本内容匹配和更新
 * 5. 使用统一的elements数组存储所有元素，通过type属性区分类型
 *
 * 使用方法:
 * - bitmap.elements 数组包含所有绘制的元素信息，每个元素有type属性("text"或"image")
 * - bitmap.getTextElements() 获取所有文本元素
 * - bitmap.getImageElements() 获取所有图像元素
 * - bitmap.redrawElementByContent(content) 根据文本内容重绘特定元素
 * - bitmap.updateElementByContent(content, newText) 根据内容更新文本元素
 * - bitmap.redrawElement(index) 重新绘制特定元素
 * - bitmap.redrawRect(x, y, width, height) 重新绘制指定区域内的所有内容
 * - bitmap.redrawAll() 重新绘制所有内容
 *
 * 示例:
 * // 根据内容更新文本
 * bitmap.updateElementByContent("原始文本", "新文本");
 *
 * // 根据内容重绘文本
 * bitmap.redrawElementByContent("某个文本");
 */

(function () {
    'use strict';

    //=============================================================================
    // 保存原始方法
    //=============================================================================
    const _Bitmap_initialize = Bitmap.prototype.initialize;
    const _Bitmap_drawText = Bitmap.prototype.drawText;
    const _Bitmap_blt = Bitmap.prototype.blt;
    const _Bitmap_clear = Bitmap.prototype.clear;

    //=============================================================================
    // 重写初始化方法，添加elements数组
    //=============================================================================
    Bitmap.prototype.initialize = function (width, height) {
        _Bitmap_initialize.call(this, width, height);

    };

    // 获取所有文本元素
    Bitmap.prototype.getTextElements = function () {
        return this.elements.filter(element => element.type === 'text');
    };

    // 获取所有图像元素
    Bitmap.prototype.getImageElements = function () {
        return this.elements.filter(element => element.type === 'image');
    };

    // 表达式解析方法
    Bitmap.prototype.evaluateExpression = function(expression) {
        try {
            // 解析表达式，如 "actors.0.name" 或 "system.gameTitle"
            const parts = expression.split('.');
            if (parts.length === 0) return '';

            // 构建全局变量名
            const dataType = parts[0];
            const globalVarName = '$data' + dataType.charAt(0).toUpperCase() + dataType.slice(1);

            // 获取全局数据
            let value = window[globalVarName];
            if (!value) {
                // 全局变量不存在，返回空字符串（不显示任何内容）
                return '';
            }

            // 遍历路径获取值
            for (let i = 1; i < parts.length; i++) {
                const part = parts[i];

                // 如果当前值为null或undefined，直接返回空字符串
                if (value === null || value === undefined) {
                    return '';
                }

                // 处理数组索引
                if (/^\d+$/.test(part)) {
                    // 纯数字，作为数组索引
                    if (Array.isArray(value)) {
                        const index = parseInt(part);
                        // 检查数组索引是否有效
                        if (index >= 0 && index < value.length) {
                            value = value[index];
                        } else {
                            // 数组索引超出范围，返回空字符串
                            return '';
                        }
                    } else {
                        // 不是数组但尝试用数字索引访问，返回空字符串
                        return '';
                    }
                } else {
                    // 普通属性
                    if (typeof value === 'object' && value !== null && part in value) {
                        value = value[part];
                    } else {
                        // 属性不存在，返回空字符串
                        return '';
                    }
                }
            }

            // 最终值检查：只有当值存在且不为空时才返回字符串
            if (value === null || value === undefined || value === '') {
                return '';
            }

            // 对于数字0和布尔值false，仍然显示
            return String(value);

        } catch (error) {
            // 解析出错，返回空字符串（不显示任何内容）
            return '';
        }
    };

    //=============================================================================
    // 重写drawText方法，支持数据绑定表达式
    //=============================================================================
    Bitmap.prototype.drawText = function (text, x, y, maxWidth, lineHeight, align) {
        if (!this.elements) this.elements = [];

        // 检查是否为绑定表达式
        let displayText = text;
        let bindingExpression = null;

        if (text && typeof text === 'string' && text.startsWith('{{') && text.endsWith('}}')) {
            bindingExpression = text.slice(2, -2); // 移除 {{ }}
            const evaluatedValue = this.evaluateExpression(bindingExpression);
            // 如果表达式解析结果为空字符串，则不显示任何内容（包括原始表达式）
            displayText = evaluatedValue;
        }

        // 调用原始方法绘制实际显示的文本
        _Bitmap_drawText.call(this, displayText, x, y, maxWidth, lineHeight, align);

        // 记录参数信息（包含绑定信息）
        const textInfo = {
            type: 'text',  // 添加类型标识
            text: text,                    // 原始文本（可能是绑定表达式）
            displayText: displayText,      // 实际显示的文本
            bindingExpression: bindingExpression, // 绑定表达式（如果有）
            x: x,
            y: y,
            maxWidth: maxWidth || 0xffffffff,
            lineHeight: lineHeight || 36,
            align: align || "left"
        };

        // 计算文本的大致边界框，用于区域重绘
        const width = maxWidth || this.measureTextWidth(text) + 4;
        const height = lineHeight || 36;
        textInfo.bounds = {
            x: x,
            y: y,
            width: width,
            height: height
        };

        // 检查是否已存在相同位置的文本，如果是则更新
        const existingIndex = this.elements.findIndex(e =>
            e.type === 'text' && e.x === x && e.y === y &&
            e.maxWidth === maxWidth && e.lineHeight === lineHeight &&
            e.align === align
        );

        if (existingIndex >= 0) {
            this.elements[existingIndex] = textInfo;
        } else {
            this.elements.push(textInfo);
        }

        return textInfo;
    };

    //=============================================================================
    // 重写blt方法，记录图像信息并添加边界框
    //=============================================================================
    Bitmap.prototype.blt = function (source, sx, sy, sw, sh, dx, dy, dw, dh) {
        if (!this.elements) this.elements = [];
        // 设置默认值
        dw = dw || sw;
        dh = dh || sh;

        // 调用原始方法绘制图像
        _Bitmap_blt.call(this, source, sx, sy, sw, sh, dx, dy, dw, dh);

        // 在source上创建regions属性（如果不存在）
        if (source && !source.regions) {
            source.regions = [];
        }

        // 如果source存在，检查是否已有相同的区域，如果没有则添加
        if (source && source.regions) {
            // 检查是否已存在相同的区域
            const existingRegion = source.regions.find(region =>
                region.sx === sx &&
                region.sy === sy &&
                region.sw === sw &&
                region.sh === sh
            );

            // 如果不存在相同区域，则添加新区域
            if (!existingRegion) {
                const regionInfo = {
                    id: `region_${Date.now()}`,
                    label: `区域 ${source.regions.length + 1}`,
                    sx: sx,
                    sy: sy,
                    sw: sw,
                    sh: sh
                };
                source.regions.push(regionInfo);
                console.log('添加新区域到source.regions:', regionInfo);
            }
        }

        // 记录图像信息
        const imgInfo = {
            type: 'image',  // 添加类型标识
            source: source,
            sx: sx,
            sy: sy,
            sw: sw,
            sh: sh,
            dx: dx,
            dy: dy,
            dw: dw,
            dh: dh,
            // 添加边界框信息，用于区域重绘
            bounds: {
                x: dx,
                y: dy,
                width: dw,
                height: dh
            }
        };

        // 检查是否已存在相同位置的图像，如果是则更新
        const existingIndex = this.elements.findIndex(e =>
            e.type === 'image' && e.dx === dx && e.dy === dy &&
            e.dw === dw && e.dh === dh
        );

        if (existingIndex >= 0) {
            this.elements[existingIndex] = imgInfo;
        } else {
            this.elements.push(imgInfo);
        }

        return imgInfo;
    };

    Bitmap.prototype.redrawing = function () {
        // 临时禁用记录，避免无限循环
        const originalDrawText = Bitmap.prototype.drawText;
        const originalBlt = Bitmap.prototype.blt;
        Bitmap.prototype.drawText = _Bitmap_drawText;
        Bitmap.prototype.blt = _Bitmap_blt;

        // 保存当前内容
        const savedElements = [...this.elements];

        // 清除位图和记录
        this.clear();

        // 先绘制所有图像元素
        const imageElements = savedElements.filter(e => e.type === 'image');
        console.log('重绘图像元素:', imageElements.length);

        for (const imgItem of imageElements) {
            console.log('重绘图像:', imgItem);

            try {
                // 检查是否为序列化后的图像数据（包含 sourceUrl 而不是 source）
                if (imgItem.sourceUrl && !imgItem.source) {
                    console.log("检测到序列化图像数据，开始加载:", imgItem.sourceUrl);

                    // 使用 CustomResourcePath.js 插件的 ImageManager.loadBitmapFromUrl 方法
                    const bitmap = ImageManager.loadBitmapFromUrl(imgItem.sourceUrl);
                    imgItem.source = bitmap;

                    // 如果图像还在加载中，等待加载完成后重绘
                    if (bitmap._loadingState !== "loaded") {
                        console.log("图像正在加载中，等待完成后重绘");

                        // 添加加载监听器
                        if (typeof bitmap.addLoadListener === 'function') {
                            bitmap.addLoadListener(() => {
                                console.log("序列化图像加载完成，重新绘制");
                                this.redrawing();
                            });
                        }

                        // 绘制加载中的占位符
                        this.fillRect(
                            imgItem.dx,
                            imgItem.dy,
                            imgItem.dw,
                            imgItem.dh,
                            'rgba(255, 255, 0, 0.5)' // 黄色表示加载中
                        );
                        continue;
                    }
                }

                // 检查源图像是否有效
                if (!imgItem.source || !imgItem.source._image) {
                    console.error("源图像无效:", imgItem.source);

                    // 如果源图像无效，绘制一个红色矩形作为占位符
                    this.fillRect(
                        imgItem.dx,
                        imgItem.dy,
                        imgItem.dw,
                        imgItem.dh,
                        imgItem.color || 'rgba(255, 0, 0, 0.5)'
                    );

                    continue;
                }

                console.log("重绘图像源:", imgItem.source);

                // 确保源图像已加载
                if (imgItem.source._loadingState !== "loaded") {
                    console.log("源图像未加载完成");

                    // 如果图像未加载，绘制一个占位符
                    this.fillRect(
                        imgItem.dx,
                        imgItem.dy,
                        imgItem.dw,
                        imgItem.dh,
                        imgItem.color || 'rgba(0, 0, 255, 0.5)'
                    );

                    // 不再绘制边框和标签

                    continue;
                }

                // 调用原始方法绘制图像
                _Bitmap_blt.call(
                    this,
                    imgItem.source,
                    imgItem.sx,
                    imgItem.sy,
                    imgItem.sw,
                    imgItem.sh,
                    imgItem.dx,
                    imgItem.dy,
                    imgItem.dw,
                    imgItem.dh
                );

                // 不再绘制边框

                // 不再绘制标签

                console.log("图像重绘完成");
            } catch (error) {
                console.error("重绘图像元素时出错:", error, imgItem);

                // 如果绘制失败，绘制一个红色矩形作为占位符
                try {
                    this.fillRect(
                        imgItem.dx,
                        imgItem.dy,
                        imgItem.dw,
                        imgItem.dh,
                        imgItem.color || 'rgba(255, 0, 0, 0.5)'
                    );

                    // 不再绘制边框和标签
                } catch (e) {
                    console.error("绘制占位符失败:", e);
                }
            }
        }

        // 再绘制所有文本元素，支持数据绑定
        const textElements = savedElements.filter(e => e.type === 'text');
        for (const textItem of textElements) {
            let displayText = textItem.text;

            // 如果有绑定表达式，重新解析
            if (textItem.bindingExpression) {
                const newValue = this.evaluateExpression(textItem.bindingExpression);
                // 如果解析结果为空字符串，则不显示任何内容
                displayText = newValue;
            } else if (textItem.text && typeof textItem.text === 'string' &&
                      textItem.text.startsWith('{{') && textItem.text.endsWith('}}')) {
                // 兼容处理：如果text是绑定表达式但没有bindingExpression字段
                const expression = textItem.text.slice(2, -2);
                const newValue = this.evaluateExpression(expression);
                // 如果解析结果为空字符串，则不显示任何内容
                displayText = newValue;
            }

            _Bitmap_drawText.call(
                this,
                displayText,  // 使用解析后的文本
                textItem.x,
                textItem.y,
                textItem.maxWidth,
                textItem.lineHeight,
                textItem.align
            );
        }

        // 恢复记录功能
        Bitmap.prototype.drawText = originalDrawText;
        Bitmap.prototype.blt = originalBlt;

        // 恢复保存的内容记录
        this.elements = savedElements;

        // 更新纹理
        this._baseTexture.update();
    };

    // 根据内容查找文本元素
    Bitmap.prototype.findElementByContent = function (content) {
        return this.elements.find(e => e.type === 'text' && e.text === content);
    };

    // 根据内容更新文本元素
    Bitmap.prototype.updateElementByContent = function (content, newText) {
        const element = this.findElementByContent(content);
        if (element) {
            element.text = newText;
            this.redrawing();
            return true;
        }
        return false;
    };

    // 根据索引重绘元素
    Bitmap.prototype.redrawElement = function (index) {
        if (index >= 0 && index < this.elements.length) {
            const element = this.elements[index];

            // 临时禁用记录
            const originalDrawText = Bitmap.prototype.drawText;
            const originalBlt = Bitmap.prototype.blt;
            Bitmap.prototype.drawText = _Bitmap_drawText;
            Bitmap.prototype.blt = _Bitmap_blt;

            // 根据元素类型调用相应的绘制方法
            if (element.type === 'text') {
                _Bitmap_drawText.call(
                    this,
                    element.text,
                    element.x,
                    element.y,
                    element.maxWidth,
                    element.lineHeight,
                    element.align
                );
            } else if (element.type === 'image') {
                _Bitmap_blt.call(
                    this,
                    element.source,
                    element.sx,
                    element.sy,
                    element.sw,
                    element.sh,
                    element.dx,
                    element.dy,
                    element.dw,
                    element.dh
                );
            }

            // 恢复记录功能
            Bitmap.prototype.drawText = originalDrawText;
            Bitmap.prototype.blt = originalBlt;

            // 更新纹理
            this._baseTexture.update();
            return true;
        }
        return false;
    };

    // 重绘指定区域内的所有元素
    Bitmap.prototype.redrawRect = function (x, y, width, height) {
        // 找出与指定区域相交的所有元素
        const elementsInRect = this.elements.filter(e => {
            const bounds = e.bounds;
            return (
                bounds.x < x + width &&
                bounds.x + bounds.width > x &&
                bounds.y < y + height &&
                bounds.y + bounds.height > y
            );
        });

        if (elementsInRect.length > 0) {
            // 临时禁用记录
            const originalDrawText = Bitmap.prototype.drawText;
            const originalBlt = Bitmap.prototype.blt;
            Bitmap.prototype.drawText = _Bitmap_drawText;
            Bitmap.prototype.blt = _Bitmap_blt;

            // 清除指定区域
            this.clearRect(x, y, width, height);

            // 重绘区域内的所有元素
            for (const element of elementsInRect) {
                if (element.type === 'text') {
                    _Bitmap_drawText.call(
                        this,
                        element.text,
                        element.x,
                        element.y,
                        element.maxWidth,
                        element.lineHeight,
                        element.align
                    );
                } else if (element.type === 'image') {
                    _Bitmap_blt.call(
                        this,
                        element.source,
                        element.sx,
                        element.sy,
                        element.sw,
                        element.sh,
                        element.dx,
                        element.dy,
                        element.dw,
                        element.dh
                    );
                }
            }

            // 恢复记录功能
            Bitmap.prototype.drawText = originalDrawText;
            Bitmap.prototype.blt = originalBlt;

            // 更新纹理
            this._baseTexture.update();
            return true;
        }
        return false;
    };

    // 重绘所有内容
    Bitmap.prototype.redrawAll = function () {
        this.redrawing();
        return true;
    };



    console.log("RPGEditor_BitmapTracker 插件已加载 - 使用统一的elements数组，包含背景图坐标修正");
})();
