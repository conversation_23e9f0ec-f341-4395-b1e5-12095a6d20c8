/**
 * Sprite 相关类型定义
 */

import { type BaseDisplayProperties } from './base';

/**
 * Sprite 事件属性
 */
export interface SpriteEventsProperties {
  onClick?: string;        // 点击事件代码
  onHover?: string;        // 悬停进入事件代码
  onHoverOut?: string;     // 悬停离开事件代码
  onPress?: string;        // 按下事件代码
  onRelease?: string;      // 释放事件代码
  onDoubleClick?: string;  // 双击事件代码
}

/**
 * Bitmap 属性
 */
export interface BitmapProperties {
  fontBold: boolean;
  fontFace: string;
  fontItalic: boolean;
  fontSize: number;
  outlineColor: string;
  outlineWidth: number;
  textColor: string;
  _paintOpacity: number;
  _smooth: boolean;
  elements?: any[];
  url?: string;
  regions?: {
    id?: string;
    label?: string;
    sx: number;
    sy: number;
    sw: number;
    sh: number;
  }[];
}

/**
 * Sprite 属性
 */
export interface SpriteProperties extends BaseDisplayProperties {
  // 混合模式
  blendMode: number;
  // 遮罩和滤镜 (复杂对象，需要特殊处理)
  mask?: any;
  filters?: any[];
  // 新增：层级信息
  zIndex?: number;                 // Z轴层级
  bitmap: BitmapProperties;
  // 新增：事件处理
  events?: SpriteEventsProperties; // 事件属性
}
