<script lang="ts">
  /**
   * 简化的 Checkbox 组件
   * 使用原生 HTML checkbox 样式，支持历史记录
   */

  import { historyManager } from '../historyManager';

  // Props using $props()
  let {
    checked = $bindable(false),
    disabled = false,
    label = '',
    onChange = () => {},
    // 历史记录相关
    targetObject = undefined,
    fieldName = undefined,
    enableHistory = true,
    name = '',
    id = '',
    // 🔑 新增：自动推断历史记录
    autoHistory = true
  }: {
    checked?: boolean;
    disabled?: boolean;
    label?: string;
    onChange?: (checked: boolean, event: Event) => void;
    // 历史记录相关
    targetObject?: any;
    fieldName?: string;
    enableHistory?: boolean;
    name?: string;
    id?: string;
    // 自动推断历史记录
    autoHistory?: boolean;
  } = $props();

  // 内部状态，用于跟踪复选框的实际状态
  let internalChecked = $state(checked);

  // 监听外部 checked 值的变化（用于撤销/重做等外部修改）
  $effect(() => {
    if (checked !== internalChecked) {
      internalChecked = checked;
      console.log("✅ Checkbox: 外部值变化，同步内部状态:", checked);
    }
  });

  // 处理变化事件
  function handleChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const newChecked = target.checked;
    const oldChecked = checked;

    console.log("✅ Checkbox: 状态变化:", {
      from: oldChecked,
      to: newChecked,
      enableHistory,
      targetObject: targetObject?.className || 'none',
      fieldName: fieldName || 'none'
    });

    // 🎯 处理历史记录（在更新值之前记录）
    if (enableHistory && oldChecked !== newChecked && historyManager.isRecording()) {
      if (targetObject && fieldName) {
        // 记录到模型对象
        historyManager.recordChange(targetObject, fieldName, oldChecked, newChecked);
        console.log("📝 HistoryManager: 已记录复选框变更");
      } else {
        // 如果没有指定目标对象，创建一个虚拟对象来记录
        const virtualObject = {
          className: `Checkbox_${name || id || 'unnamed'}`,
          [fieldName || 'checked']: newChecked
        };
        historyManager.recordChange(virtualObject, fieldName || 'checked', oldChecked, newChecked);
        console.log("📝 HistoryManager: 已记录复选框虚拟对象变更");
      }
    }

    // 🔑 直接更新绑定的值（支持双向绑定）
    checked = newChecked;
    internalChecked = newChecked;

    // 同时调用 onChange 回调（如果有的话）
    onChange(newChecked, event);

    // 🎯 操作完成后主动失焦，确保快捷键能正常工作
    setTimeout(() => {
      if (event.target) {
        (event.target as HTMLElement).blur();
        console.log("📝 Checkbox: 操作完成后主动失焦");
      }
    }, 100);
  }
</script>

<label class="component-checkbox-base">
  <input
    type="checkbox"
    checked={internalChecked}
    {disabled}
    onchange={handleChange}
  />
  {#if label}
    <span class="checkbox-label">{label}</span>
  {/if}
</label>

<style>
  @import './shared-styles.css';

  .checkbox-label {
    font-size: var(--component-font-size);
    color: var(--component-text);
  }

  .component-checkbox-base:hover .checkbox-label {
    color: var(--component-border-focus);
  }

  .component-checkbox-base input[type="checkbox"] {
    width: 14px;
    height: 14px;
  }
</style>
