/**
 * 全局主题配置文件
 * 统一管理应用程序的主题色彩和样式
 */

/**
 * 主题色彩配置接口
 */
export interface ThemeColors {
  // 主色调
  primary: string;
  primaryDark: string;
  primaryLight: string;

  // 次要色调
  secondary: string;
  secondaryDark: string;
  secondaryLight: string;

  // 背景色
  background: string;
  backgroundDark: string;
  backgroundLight: string;

  // 表面色
  surface: string;
  surfaceDark: string;
  surfaceLight: string;
  surfaceHover: string;

  // 文本色
  text: string;
  textSecondary: string;
  textInverse: string;

  // 边框色
  border: string;
  borderLight: string;
  borderDark: string;

  // 状态色
  success: string;
  warning: string;
  error: string;
  info: string;

  // 阴影
  shadow: string;
  shadowLight: string;
  shadowDark: string;
}

/**
 * 默认主题色彩配置（基于系统菜单栏的深色主题）
 */
export const defaultTheme: ThemeColors = {
  // 主色调 - 基于菜单栏的渐变色
  primary: '#4a5568',        // 菜单栏主色
  primaryDark: '#2d3748',    // 菜单栏深色
  primaryLight: '#718096',   // 菜单栏浅色

  // 次要色调
  secondary: '#667eea',      // 紫蓝色
  secondaryDark: '#5a67d8',
  secondaryLight: '#7c3aed',

  // 背景色
  background: '#1a202c',     // 深色背景
  backgroundDark: '#171923', // 更深背景
  backgroundLight: '#2d3748', // 浅色背景

  // 表面色
  surface: '#3a4553',        // 表面色 (调深一点)
  surfaceDark: '#2d3748',    // 深色表面
  surfaceLight: '#4a5568',   // 浅色表面
  surfaceHover: '#4a5568',   // 悬停表面色

  // 文本色
  text: '#ffffff',           // 主文本色
  textSecondary: 'rgba(255, 255, 255, 0.8)', // 次要文本色
  textInverse: '#1a202c',    // 反色文本

  // 边框色
  border: 'rgba(255, 255, 255, 0.2)',      // 边框色
  borderLight: 'rgba(255, 255, 255, 0.1)', // 浅边框
  borderDark: 'rgba(255, 255, 255, 0.3)',  // 深边框

  // 状态色
  success: '#22c55e',        // 成功色
  warning: '#fbbf24',        // 警告色
  error: '#e53e3e',          // 错误色
  info: '#3b82f6',           // 信息色

  // 阴影
  shadow: 'rgba(0, 0, 0, 0.3)',       // 阴影色
  shadowLight: 'rgba(0, 0, 0, 0.1)',  // 浅阴影
  shadowDark: 'rgba(0, 0, 0, 0.5)',   // 深阴影
};

/**
 * 浅色主题配置
 */
export const lightTheme: ThemeColors = {
  // 主色调
  primary: '#4a5568',
  primaryDark: '#2d3748',
  primaryLight: '#718096',

  // 次要色调
  secondary: '#667eea',
  secondaryDark: '#5a67d8',
  secondaryLight: '#7c3aed',

  // 背景色
  background: '#ffffff',
  backgroundDark: '#f7fafc',
  backgroundLight: '#edf2f7',

  // 表面色
  surface: '#f0f2f5',
  surfaceDark: '#e8eaed',
  surfaceLight: '#edf2f7',
  surfaceHover: '#f8f9fa',

  // 文本色
  text: '#1a202c',
  textSecondary: 'rgba(26, 32, 44, 0.8)',
  textInverse: '#ffffff',

  // 边框色
  border: 'rgba(26, 32, 44, 0.2)',
  borderLight: 'rgba(26, 32, 44, 0.1)',
  borderDark: 'rgba(26, 32, 44, 0.3)',

  // 状态色
  success: '#22c55e',
  warning: '#fbbf24',
  error: '#e53e3e',
  info: '#3b82f6',

  // 阴影
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowLight: 'rgba(0, 0, 0, 0.05)',
  shadowDark: 'rgba(0, 0, 0, 0.2)',
};

/**
 * 主题类型枚举
 */
export enum ThemeType {
  DARK = 'dark',
  LIGHT = 'light',
  AUTO = 'auto'
}

/**
 * 主题管理类
 */
export class ThemeManager {
  private static instance: ThemeManager;
  private currentTheme: ThemeType = ThemeType.DARK;
  private currentColors: ThemeColors = defaultTheme;

  private constructor() {
    this.loadThemeFromStorage();
    this.applySystemThemeListener();
  }

  /**
   * 获取主题管理器实例（单例模式）
   */
  static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager();
    }
    return ThemeManager.instance;
  }

  /**
   * 设置主题
   */
  setTheme(theme: ThemeType): void {
    this.currentTheme = theme;
    this.updateColors();
    this.saveThemeToStorage();
    this.applyCSSVariables();
  }

  /**
   * 获取当前主题
   */
  getCurrentTheme(): ThemeType {
    return this.currentTheme;
  }

  /**
   * 获取当前主题色彩
   */
  getCurrentColors(): ThemeColors {
    return this.currentColors;
  }

  /**
   * 更新颜色配置
   */
  private updateColors(): void {
    if (this.currentTheme === ThemeType.AUTO) {
      // 自动模式：根据系统偏好设置
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      this.currentColors = prefersDark ? defaultTheme : lightTheme;
    } else if (this.currentTheme === ThemeType.LIGHT) {
      this.currentColors = lightTheme;
    } else {
      this.currentColors = defaultTheme;
    }
  }

  /**
   * 应用CSS变量
   */
  private applyCSSVariables(): void {
    const root = document.documentElement;
    const colors = this.currentColors;

    // 设置CSS自定义属性
    root.style.setProperty('--theme-primary', colors.primary);
    root.style.setProperty('--theme-primary-dark', colors.primaryDark);
    root.style.setProperty('--theme-primary-light', colors.primaryLight);

    root.style.setProperty('--theme-secondary', colors.secondary);
    root.style.setProperty('--theme-secondary-dark', colors.secondaryDark);
    root.style.setProperty('--theme-secondary-light', colors.secondaryLight);

    root.style.setProperty('--theme-background', colors.background);
    root.style.setProperty('--theme-background-dark', colors.backgroundDark);
    root.style.setProperty('--theme-background-light', colors.backgroundLight);

    root.style.setProperty('--theme-surface', colors.surface);
    root.style.setProperty('--theme-surface-dark', colors.surfaceDark);
    root.style.setProperty('--theme-surface-light', colors.surfaceLight);
    root.style.setProperty('--theme-surface-hover', colors.surfaceHover);

    root.style.setProperty('--theme-text', colors.text);
    root.style.setProperty('--theme-text-secondary', colors.textSecondary);
    root.style.setProperty('--theme-text-inverse', colors.textInverse);

    root.style.setProperty('--theme-border', colors.border);
    root.style.setProperty('--theme-border-light', colors.borderLight);
    root.style.setProperty('--theme-border-dark', colors.borderDark);

    root.style.setProperty('--theme-success', colors.success);
    root.style.setProperty('--theme-warning', colors.warning);
    root.style.setProperty('--theme-error', colors.error);
    root.style.setProperty('--theme-info', colors.info);

    root.style.setProperty('--theme-shadow', colors.shadow);
    root.style.setProperty('--theme-shadow-light', colors.shadowLight);
    root.style.setProperty('--theme-shadow-dark', colors.shadowDark);

    // 更新body类名以支持Skeleton UI
    document.body.className = this.currentTheme === ThemeType.LIGHT ? 'light' : 'dark';
  }

  /**
   * 从本地存储加载主题
   */
  private loadThemeFromStorage(): void {
    const savedTheme = localStorage.getItem('app-theme') as ThemeType;
    if (savedTheme && Object.values(ThemeType).includes(savedTheme)) {
      this.currentTheme = savedTheme;
    }
    this.updateColors();
    this.applyCSSVariables();
  }

  /**
   * 保存主题到本地存储
   */
  private saveThemeToStorage(): void {
    localStorage.setItem('app-theme', this.currentTheme);
  }

  /**
   * 监听系统主题变化
   */
  private applySystemThemeListener(): void {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', () => {
      if (this.currentTheme === ThemeType.AUTO) {
        this.updateColors();
        this.applyCSSVariables();
      }
    });
  }
}

// 导出主题管理器实例
export const themeManager = ThemeManager.getInstance();

// 默认导出
export default themeManager;
