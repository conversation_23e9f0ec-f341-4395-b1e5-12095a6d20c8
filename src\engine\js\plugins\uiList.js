/**
 * UIList - 重构版列表组件
 * 纯容器设计，支持 UIItem 模板和 RPG Maker MZ 数据绑定
 */

(() => {
    'use strict';

    // 确保 PIXI 可用
    if (typeof PIXI === 'undefined') {
        console.error('UIList: PIXI 未找到');
        return;
    }

    /**
     * UIList - 列表容器组件，支持数据绑定和 UIItem 模板
     */
    class UIList extends PIXI.Container {
        constructor(properties = {}) {
            super();

            console.log('📋 UIList: 创建列表组件（重构版）', properties);
            // ⚠️ 重要：在RPG Maker MZ中，PIXI的interactive属性无效
            // 因为RPG Maker MZ使用自己的TouchInput系统，不使用PIXI的interaction manager
            // 所以UIList容器本身不应该设置为interactive，避免干扰子元素的事件检测
            this.interactive = false;
            this.interactiveChildren = true; // 允许子元素交互
            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'UIList';

            // 基础属性 - 使用 width/height 保持兼容性
            this.width = properties.width || 200;
            this.height = properties.height || 300;
            this.enabled = properties.enabled !== false;

            // 列表特有属性
            this.dataSource = properties.dataSource || null;  // 数据源（RPG Maker MZ 数据）
            this.itemTemplate = null;                  // UIItem 模板
            this.itemInstances = [];                   // 实例化的 UIItem 数组
            this.itemSpacing = properties.itemSpacing || 5;
            this.itemHeight = properties.itemHeight || 32;    // 🔑 添加项目高度属性
            this.virtualScrolling = properties.virtualScrolling !== false; // 🔑 添加虚拟滚动属性
            this.scrollEnabled = properties.scrollEnabled !== false;

            // 选择状态
            this.selectionMode = properties.selectionMode || 'single'; // single, multiple, none
            this.selectedIndices = new Set();
            this.selectedItems = [];

            // 滚动状态
            this.scrollTop = 0;
            this.maxScrollTop = 0;

            // 事件代码
            this._eventCodes = {
                onSelectionChange: properties._eventCodes?.onSelectionChange || '',
                onItemClick: properties._eventCodes?.onItemClick || '',
                onItemDoubleClick: properties._eventCodes?.onItemDoubleClick || '',
                onItemHover: properties._eventCodes?.onItemHover || ''
            };

            // 初始化组件
            this.setupContainer();
            // 🔧 暂时移除遮罩功能，因为它阻止了内容显示
             this.setupMask();

            // 设置滚轮滚动
            this.setupScrolling();

            console.log('✅ UIList: 列表组件创建完成');
        }



        /**
         * 设置容器
         */
        setupContainer() {
            console.log('🎨 UIList: 设置容器');

            // 1. 🆕 默认创建空的UIItem模板供用户编辑
            this.createDefaultItemTemplate();

            // 2. 列表项容器（用于存放数据实例）
            this.itemContainer = new PIXI.Container();
            // 设置容器位置在模板下方
            if (this.itemTemplate) {
                this.itemContainer.y = this.itemTemplate.height + 20;
            } else {
                this.itemContainer.y = 60; // 默认位置
            }
            this.addChild(this.itemContainer);
        }

        /**
         * 创建默认的UIItem模板
         */
        createDefaultItemTemplate() {
            console.log('📋 UIList: 创建默认UIItem模板');

            // 确保UIItem类可用
            if (typeof UIItem === 'undefined') {
                console.warn('⚠️ UIList: UIItem类未找到，跳过模板创建');
                return;
            }

            // 创建空的UIItem模板
            this.itemTemplate = new UIItem({
                width: this.width - 20, // 稍微小一点，留出边距
                height: 40,
                enabled: true
            });

            // 设置模板位置（在列表顶部显示）
            this.itemTemplate.x = 10;
            this.itemTemplate.y = 10;

            // 添加到UIList作为子对象（用于编辑）
            this.addChild(this.itemTemplate);

            console.log('✅ UIList: 默认UIItem模板创建完成');
        }

        /**
         * 设置滚轮滚动（使用RPG Maker MZ的TouchInput系统）
         */
        setupScrolling() {
            console.log('🖱️ UIList: 设置滚轮滚动');



            // 🔑 参考UIButton的实现：重写update方法来处理滚轮事件
            if (!this._originalUpdate) {
                this._originalUpdate = this.update;
            }

            // 重写 update 方法来处理滚轮事件
            this.update = function() {
                // 调用原始的 update 方法
                if (this._originalUpdate) {
                    this._originalUpdate.call(this);
                }

                // 处理滚轮滚动
                this.processWheelScroll();
            };

            console.log('✅ UIList: 滚轮滚动设置完成');
        }

        /**
         * 处理滚轮滚动（在update中调用，使用RPG Maker MZ的TouchInput）
         */
        processWheelScroll() {
            if (!this.scrollEnabled || !this.itemContainer) return;
            if (typeof TouchInput === 'undefined') return;

            // 检查是否在UIList区域内
            if (!this.isTouchedInsideFrame()) return;

            // 使用RPG Maker MZ的滚轮检测
            const threshold = 20;
            if (TouchInput.wheelY >= threshold) {
                // 向下滚动
                this.scrollDown();
                console.log('🖱️ UIList: 向下滚动');
            }
            if (TouchInput.wheelY <= -threshold) {
                // 向上滚动
                this.scrollUp();
                console.log('🖱️ UIList: 向上滚动');
            }
        }

        /**
         * 检查UIList是否阻挡了子元素的事件检测
         * 这是一个调试方法，用于诊断事件传播问题
         */
        debugEventBlocking() {
            if (typeof TouchInput === 'undefined') return;

            const touchX = TouchInput.x;
            const touchY = TouchInput.y;
            const listBounds = this.getBounds();

            console.log('🔍 UIList事件阻挡诊断:', {
                UIList: {
                    bounds: listBounds,
                    interactive: this.interactive,
                    interactiveChildren: this.interactiveChildren,
                    visible: this.visible,
                    worldVisible: this.worldVisible
                },
                Touch: {
                    x: touchX,
                    y: touchY,
                    insideList: touchX >= listBounds.x && touchX <= listBounds.x + listBounds.width &&
                               touchY >= listBounds.y && touchY <= listBounds.y + listBounds.height
                },
                Children: this.children.map(child => ({
                    name: child.name || child.constructor.name,
                    bounds: child.getBounds ? child.getBounds() : 'no bounds',
                    interactive: child.interactive,
                    visible: child.visible,
                    worldVisible: child.worldVisible
                }))
            });
        }

        /**
         * 检查鼠标是否在UIList区域内
         */
        isTouchedInsideFrame() {
            if (typeof TouchInput === 'undefined') return false;

            // 获取鼠标在世界坐标中的位置
            const touchPos = { x: TouchInput.x, y: TouchInput.y };

            // 转换为UIList的本地坐标
            const localPos = this.worldTransform.applyInverse(touchPos);

            // 检查是否在UIList的边界内
            return localPos.x >= 0 && localPos.x <= this.width &&
                   localPos.y >= 0 && localPos.y <= this.height;
        }

        /**
         * 向下滚动
         */
        scrollDown() {
            const scrollSpeed = 40;
            this.scrollTop = Math.min(this.scrollTop + scrollSpeed, this.maxScrollTop);
            this.updateScrollPosition();
        }

        /**
         * 向上滚动
         */
        scrollUp() {
            const scrollSpeed = 40;
            this.scrollTop = Math.max(this.scrollTop - scrollSpeed, 0);
            this.updateScrollPosition();
        }

        /**
         * 更新滚动位置
         */
        updateScrollPosition() {
            if (!this.itemContainer) return;

            // 应用滚动位置到itemContainer
            const baseY = this.itemTemplate?.height + 20 || 60;
            this.itemContainer.y = baseY - this.scrollTop;

            // 更新遮罩
            this.updateItemContainerMask();

            console.log('🖱️ UIList: 滚动位置更新', {
                scrollTop: this.scrollTop,
                maxScrollTop: this.maxScrollTop,
                containerY: this.itemContainer.y
            });
        }

        /**
         * 设置遮罩
         */
        setupMask() {
            // 🔍 调试：检查UIList和itemContainer的尺寸
            console.log('🎭 UIList: 设置遮罩', {
                listWidth: this.width,
                listHeight: this.height,
                itemContainerY: this.itemContainer?.y || 0
            });

            // 确保UIList有足够的高度来容纳itemContainer
            const minHeight = (this.itemContainer?.y || 0) + 200; // 至少200像素的内容区域
            if (this.height < minHeight) {
                console.log(`🔧 UIList: 调整高度从 ${this.height} 到 ${minHeight}`);
                this.height = minHeight;
            }

            // 🔑 修复：创建基于内容计算尺寸的遮罩
            this.listMask = new PIXI.Graphics();
            this.listMask.beginFill(0xffffff);

            // 🔑 基于内容计算遮罩尺寸
            const templateWidth = this.itemTemplate?.width || 200;
            const templateHeight = this.itemTemplate?.height || 40;
            const containerY = this.itemContainer?.y || 0;

            // 计算列表的实际尺寸
            const listWidth = Math.max(templateWidth + 40, 200); // 模板宽度 + 边距，最小200
            const visibleItemCount = 8; // 可见项目数量
            const listHeight = containerY + (templateHeight + this.itemSpacing) * visibleItemCount;

            // 确保UIList有正确的尺寸
            if (this.width <= 0) this.width = listWidth;
            if (this.height <= 0) this.height = listHeight;

            console.log('🎭 UIList: 计算遮罩尺寸', {
                templateWidth,
                templateHeight,
                containerY,
                listWidth,
                listHeight,
                finalWidth: this.width,
                finalHeight: this.height
            });

            this.listMask.drawRect(0, 0, this.width, this.height);
            this.listMask.endFill();

            // 🔑 重要：遮罩必须添加到UIList中
             this.addChild(this.listMask);

            // 🔑 设置itemContainer的遮罩
            if (this.itemContainer) {
                this.itemContainer.mask = this.listMask;
            }

            console.log('✅ UIList: 遮罩设置完成', {
                maskWidth: this.width,
                maskHeight: this.height,
                itemContainerY: containerY
            });
        }

        /**
         * 绑定数据源
         * @param {Array} dataSource RPG Maker MZ 数据数组
         */
        bindDataSource(dataSource) {
            console.log('📊 UIList: 绑定数据源', dataSource);
            this.dataSource = dataSource;
            this.refreshItems();
        }

        /**
         * 绑定 UIItem 模板
         * @param {UIItem} itemTemplate UIItem 模板对象
         */
        bindItemTemplate(itemTemplate) {
            console.log('📋 UIList: 绑定 UIItem 模板', itemTemplate);

            // 如果已有模板，先移除
            if (this.itemTemplate && this.itemTemplate.parent === this) {
                this.removeChild(this.itemTemplate);
            }

            this.itemTemplate = itemTemplate;

            // 将新模板添加为子对象（如果不是已经添加的话）
            if (itemTemplate && itemTemplate.parent !== this) {
                this.addChild(itemTemplate);
            }

            this.refreshItems();
        }

        /**
         * 刷新列表项
         */
        refreshItems() {
            if (!this.dataSource || !this.itemTemplate) {
                console.log('⚠️ UIList: 数据源或模板未设置，跳过刷新');
                return;
            }

            console.log('🔄 UIList: 刷新列表项', {
                dataCount: this.dataSource.length,
                hasTemplate: !!this.itemTemplate,
                dataSource: this.dataSource,
                templateBindings: this.itemTemplate.dataBindings.size,
                templateChildren: this.itemTemplate.children.length
            });

            // 清除现有实例（只清除itemContainer中的实例，保留模板）
            this.clearItems();

            // 为每个数据项创建 UIItem 实例
            this.dataSource.forEach((dataItem, index) => {
                const itemInstance = this.itemTemplate.clone();

                // 绑定数据到 UIItem
                if (typeof itemInstance.bindData === 'function') {
                    console.log(`📊 UIList: 为第${index}个UIItem绑定数据:`, {
                        dataItem: dataItem,
                        itemBindings: itemInstance.dataBindings.size,
                        itemChildren: itemInstance.children.length
                    });

                    itemInstance.bindData(dataItem);

                    // 🔍 验证绑定后的状态
                    console.log(`✅ UIList: 第${index}个UIItem绑定完成:`, {
                        currentData: itemInstance.currentData,
                        bindingsCount: itemInstance.dataBindings.size
                    });
                } else {
                    console.warn(`⚠️ UIList: 第${index}个UIItem没有bindData方法`);
                }

                // 设置位置（在itemContainer内部的相对位置）
                itemInstance.y = index * (itemInstance.height + this.itemSpacing);

                // 🔍 调试信息：检查添加前的状态
                console.log(`🔍 添加第${index}个UIItem前:`);
                console.log('  - itemContainer存在:', !!this.itemContainer);
                console.log('  - itemContainer位置:', this.itemContainer.x, this.itemContainer.y);
                console.log('  - itemContainer父对象:', this.itemContainer.parent === this ? 'UIList' : 'other');
                console.log('  - UIList子对象数量:', this.children.length);
                console.log('  - itemContainer子对象数量:', this.itemContainer.children.length);

                // 添加到数据实例容器
                this.itemContainer.addChild(itemInstance);
                this.itemInstances.push(itemInstance);

                // 🔍 调试信息：检查添加后的状态
                console.log(`✅ 添加第${index}个UIItem后:`);
                console.log('  - itemInstance父对象:', itemInstance.parent === this.itemContainer ? 'itemContainer' : 'other');
                console.log('  - itemInstance位置:', itemInstance.x, itemInstance.y);
                console.log('  - itemContainer子对象数量:', this.itemContainer.children.length);
                console.log('  - UIList子对象数量:', this.children.length);
            });

            // 更新滚动范围
            this.updateScrollRange();

            // 🔑 更新itemContainer的遮罩（因为内容变化了）
            this.updateItemContainerMask();

            // 强制更新显示
            this.forceDisplayUpdate();

            // 🔍 调试：检查UIItem和其子组件的状态
            this.itemInstances.forEach((item, index) => {
                console.log(`🔍 UIItem ${index + 1} 状态:`, {
                    visible: item.visible,
                    alpha: item.alpha,
                    x: item.x,
                    y: item.y,
                    width: item.width,
                    height: item.height,
                    childrenCount: item.children?.length || 0,
                    parent: item.parent?.constructor?.name || 'null'
                });

                // 检查UIItem的子组件（UILabel等）
                if (item.children && item.children.length > 0) {
                    item.children.forEach((child, childIndex) => {
                        console.log(`  🔍 子组件 ${childIndex + 1}:`, {
                            type: child.constructor.name,
                            visible: child.visible,
                            alpha: child.alpha,
                            x: child.x,
                            y: child.y,
                            width: child.width,
                            height: child.height,
                            text: child.text || 'N/A'
                        });
                    });
                }
            });
            console.log('✅ UIList: 列表项刷新完成，共', this.itemInstances.length, '项');
        }

        /**
         * 清除所有列表项（只清除数据实例，保留模板）
         */
        clearItems() {
            console.log('🧹 UIList: 清除列表项实例，保留模板');

            this.itemInstances.forEach(item => {
                if (item.parent) {
                    item.parent.removeChild(item);
                }
                if (typeof item.destroy === 'function') {
                    item.destroy();
                }
            });
            this.itemInstances = [];
            this.selectedIndices.clear();
            this.selectedItems = [];

            // 🔑 更新itemContainer的遮罩（因为内容被清除了）
            this.updateItemContainerMask();

            console.log('✅ UIList: 列表项实例清除完成');
        }

        /**
         * 更新滚动范围
         */
        updateScrollRange() {
            if (this.itemInstances.length === 0) {
                this.maxScrollTop = 0;
                return;
            }

            const totalHeight = this.itemInstances.length * 
                (this.itemInstances[0].height + this.itemSpacing) - this.itemSpacing;
            this.maxScrollTop = Math.max(0, totalHeight - this.height);
        }

        /**
         * 获取选中的数据项
         * @returns {Array} 选中的数据项数组
         */
        getSelectedItems() {
            return this.selectedItems;
        }

        /**
         * 设置选中项
         * @param {Array} indices 要选中的索引数组
         */
        setSelectedIndices(indices) {
            this.selectedIndices.clear();
            this.selectedItems = [];

            indices.forEach(index => {
                if (index >= 0 && index < this.dataSource.length) {
                    this.selectedIndices.add(index);
                    this.selectedItems.push(this.dataSource[index]);
                }
            });

            this.updateSelectionDisplay();
            this.triggerSelectionChange();
        }

        /**
         * 更新选择显示
         */
        updateSelectionDisplay() {
            // 这里可以添加选择高亮的视觉效果
            // 暂时简化实现
        }

        /**
         * 触发选择变化事件
         */
        triggerSelectionChange() {
            if (this._eventCodes.onSelectionChange) {
                try {
                    eval(this._eventCodes.onSelectionChange);
                } catch (error) {
                    console.error('UIList: 选择变化事件执行失败', error);
                }
            }
        }

        /**
         * 滚动到指定位置
         * @param {number} scrollTop 滚动位置
         */
        scrollTo(scrollTop) {
            this.scrollTop = Math.max(0, Math.min(scrollTop, this.maxScrollTop));
            this.itemContainer.y = -this.scrollTop;
        }

        /**
         * 更新itemContainer的遮罩
         */
        updateItemContainerMask() {
            if (!this.listMask || !this.itemContainer) return;

            console.log('🎭 UIList: 更新itemContainer遮罩');

            // 🔑 修复：遮罩应该保持固定位置，不跟随itemContainer移动
            const templateWidth = this.itemTemplate?.width || 200;
            const templateHeight = this.itemTemplate?.height || 40;

            // 🔑 遮罩的固定显示区域（不随滚动变化）
            const maskX = 0; // 从UIList左边开始
            const maskY = (this.itemTemplate?.height + 20) || 60; // 固定在模板下方
            const maskWidth = Math.max(templateWidth + 40, this.width || 200);
            const visibleItemCount = 8;
            const maskHeight = (templateHeight + this.itemSpacing) * visibleItemCount;

            // 清除并重新绘制遮罩
            this.listMask.clear();
            this.listMask.beginFill(0xffffff);
            this.listMask.drawRect(maskX, maskY, maskWidth, maskHeight);
            this.listMask.endFill();

            console.log('🎭 UIList: itemContainer遮罩已更新', {
                containerY: this.itemContainer.y, // 仅用于调试
                maskX,
                maskY, // 固定位置
                maskWidth,
                maskHeight
            });
        }

        /**
         * 更新布局
         */
        updateLayout() {
            console.log('📐 UIList: 更新布局');

            // 🔑 重新计算所有item的位置（基于新的itemSpacing）
            if (this.itemInstances && this.itemInstances.length > 0) {
                console.log('📐 UIList: 重新计算item位置，间距:', this.itemSpacing);

                this.itemInstances.forEach((item, index) => {
                    if (item) {
                        const newY = index * (item.height + this.itemSpacing);
                        if (item.y !== newY) {
                            item.y = newY;
                            console.log(`📐 UIList: 更新第${index}个item位置: y=${newY}`);
                        }
                    }
                });

                // 更新滚动范围
                this.updateScrollRange();

                // 🔑 更新itemContainer的遮罩
                this.updateItemContainerMask();

                // 强制更新显示
                this.forceDisplayUpdate();
            }
        }

        /**
         * 克隆当前 UIList 对象
         * @param {Object} options 克隆选项
         * @returns {UIList} 克隆的 UIList 对象
         */
        clone(options = {}) {
            console.log('🔄 UIList: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = {
                width: this.width,
                height: this.height,
                enabled: this.enabled,
                itemSpacing: this.itemSpacing,
                scrollEnabled: this.scrollEnabled,
                selectionMode: this.selectionMode,
                _eventCodes: { ...this._eventCodes }
            };

            // 2. 创建克隆对象
            const clonedList = new UIList(cloneProperties);

            // 3. 设置位置和变换属性
            clonedList.x = this.x + (offsetPosition ? offsetX : 0);
            clonedList.y = this.y + (offsetPosition ? offsetY : 0);
            clonedList.scale.x = this.scale.x;
            clonedList.scale.y = this.scale.y;
            clonedList.rotation = this.rotation;
            clonedList.alpha = this.alpha;
            clonedList.zIndex = this.zIndex;

            // 4. 克隆所有子对象（特殊处理itemTemplate和itemContainer）
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false });

                    // 🔑 特殊处理：如果是itemTemplate，设置为克隆对象的模板
                    if (child === this.itemTemplate) {
                        clonedList.itemTemplate = clonedChild;
                        console.log('🔄 UIList: 设置克隆的itemTemplate');
                    }

                    // 🔑 特殊处理：如果是itemContainer，设置为克隆对象的容器
                    if (child === this.itemContainer) {
                        clonedList.itemContainer = clonedChild;
                        console.log('🔄 UIList: 设置克隆的itemContainer');
                    }

                    clonedList.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                } else if (child === this.itemContainer) {
                    // 🔑 如果itemContainer没有clone方法，创建新的Container
                    const newContainer = new PIXI.Container();
                    newContainer.x = child.x;
                    newContainer.y = child.y;
                    clonedList.itemContainer = newContainer;
                    clonedList.addChild(newContainer);
                    clonedChildren.push(newContainer);
                    console.log('🔄 UIList: 创建新的itemContainer');
                }
            }

            // 🔑 复制数据源和重要状态
            if (this.dataSource && this.dataSource.length > 0) {
                clonedList.dataSource = [...this.dataSource]; // 复制数据源
                console.log('🔄 UIList: 复制数据源，包含', this.dataSource.length, '项数据');
            }

            // 🔑 复制其他重要状态
            clonedList.itemHeight = this.itemHeight;
            clonedList.virtualScrolling = this.virtualScrolling;
            clonedList.selectedIndices = new Set(this.selectedIndices);
            clonedList.selectedItems = [...this.selectedItems];

            console.log('✅ UIList: 克隆完成，包含', clonedChildren.length, '个子对象');
            return clonedList;
        }

        /**
         * 强制更新显示
         */
        forceDisplayUpdate() {
            // 强制更新 PIXI 容器的显示
            if (this.itemContainer) {
                try {
                    console.log('🔄 UIList: 开始强制更新显示，当前子项数量:', this.itemContainer.children.length);

                    // 方法1: 重新设置容器的可见性来触发重绘
                    const wasVisible = this.itemContainer.visible;
                    this.itemContainer.visible = false;
                    this.itemContainer.visible = wasVisible;

                    // 方法2: 更新所有子项的位置来触发重绘
                    this.itemInstances.forEach((item) => {
                        if (item) {
                            // 微调位置然后恢复，触发重绘
                            const originalY = item.y;
                            item.y = originalY + 0.001;
                            item.y = originalY;
                        }
                    });

                    // 方法3: 在编辑器模式下，强制重新排列子项
                    if (window.EDITOR_MODE && typeof this.arrangeItems === 'function') {
                        this.arrangeItems();
                    }

                    // 方法4: 如果有渲染器，请求重新渲染
                    if (window.Graphics && window.Graphics._renderer) {
                        try {
                            window.Graphics._renderer.render(this);
                        } catch (renderError) {
                            console.warn('渲染器更新失败:', renderError);
                        }
                    }

                    // 方法5: 触发父容器的更新
                    if (this.parent && typeof this.parent.updateTransform === 'function') {
                        try {
                            this.parent.updateTransform();
                        } catch (parentError) {
                            console.warn('父容器更新失败:', parentError);
                        }
                    }

                    console.log('🔄 UIList: 强制更新显示完成，容器子项:', this.itemContainer.children.length);
                } catch (error) {
                    console.warn('⚠️ UIList: 强制更新显示时出错:', error);
                }
            }
        }

        /**
         * 销毁组件
         */
        destroy() {
            this.clearItems();
            super.destroy();
            console.log('💥 UIList: 组件已销毁');
        }
    }

    // 导出到全局
    window.UIList = UIList;

    console.log('✅ UIList 插件加载完成');
})();
