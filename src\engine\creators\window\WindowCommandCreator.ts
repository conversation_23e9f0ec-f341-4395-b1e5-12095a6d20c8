/**
 * Window_Command 创建器
 * 专门用于创建命令窗口
 */

import { BaseWindowCreator, type WindowCreationOptions } from './WindowCreator';
import { type SelectableWindowOptions } from './WindowSelectableCreator';

/**
 * 命令项目接口
 */
export interface CommandItem {
    /** 命令名称 */
    name: string;
    /** 命令符号 */
    symbol: string;
    /** 是否启用 */
    enabled?: boolean;
    /** 扩展数据 */
    ext?: any;
}

/**
 * 命令窗口创建选项
 */
export interface CommandWindowOptions extends SelectableWindowOptions {
    /** 命令列表 */
    commands?: CommandItem[];
    /** 是否自动刷新 */
    autoRefresh?: boolean;
}

/**
 * 创建命令窗口
 * @param options 创建选项
 * @returns 创建的命令窗口实例
 */
export async function createWindowCommand(options: CommandWindowOptions = {}): Promise<any> {
    console.log('=== 创建命令窗口 Window_Command ===');
    
    try {
        // 预加载资源
        BaseWindowCreator.preloadWindowResources('Window_Command');
        
        // 设置默认选项
        const defaultOptions: WindowCreationOptions = {
            autoOpen: true,
            addToStage: true,
            rect: options.rect || { x: 0, y: 0, width: 200, height: 150 },
            ...options
        };
        
        // 创建窗口实例
        const window = await BaseWindowCreator.createWindowInstance('Window_Command', defaultOptions);
        
        // Window_Command 特定的设置
        setupCommandWindow(window, options);
        
        console.log('Window_Command 创建完成，窗口属性:', {
            x: window.x,
            y: window.y,
            width: window.width,
            height: window.height,
            maxItems: window.maxItems(),
            index: window.index(),
            active: window.active,
            visible: window.visible
        });
        
        return window;
        
    } catch (error) {
        console.error('创建 Window_Command 失败:', error);
        throw error;
    }
}

/**
 * 设置命令窗口属性
 * @param window 窗口实例
 * @param options 命令窗口选项
 */
function setupCommandWindow(window: any, options: CommandWindowOptions): void {
    console.log('设置命令窗口属性...');
    
    try {
        // 添加命令项目
        if (options.commands && options.commands.length > 0) {
            // 清除现有命令列表
            if (window.clearCommandList && typeof window.clearCommandList === 'function') {
                window.clearCommandList();
            }
            
            // 添加新命令
            options.commands.forEach((command, index) => {
                if (window.addCommand && typeof window.addCommand === 'function') {
                    window.addCommand(
                        command.name,
                        command.symbol,
                        command.enabled !== false,
                        command.ext || null
                    );
                    console.log(`添加命令 ${index + 1}:`, command);
                }
            });
            
            // 刷新窗口
            if (options.autoRefresh !== false && window.refresh && typeof window.refresh === 'function') {
                window.refresh();
                console.log('命令窗口已刷新');
            }
        }
        
        // 设置初始选中索引
        if (options.initialIndex !== undefined && window.select && typeof window.select === 'function') {
            window.select(options.initialIndex);
            console.log('设置初始选中索引:', options.initialIndex);
        }
        
        // 设置激活状态
        if (options.activate !== undefined) {
            if (options.activate && window.activate && typeof window.activate === 'function') {
                window.activate();
                console.log('命令窗口已激活');
            } else if (!options.activate && window.deactivate && typeof window.deactivate === 'function') {
                window.deactivate();
                console.log('命令窗口已取消激活');
            }
        }
        
        console.log('命令窗口属性设置完成');
        
    } catch (error) {
        console.error('设置命令窗口属性失败:', error);
    }
}

/**
 * 创建并激活命令窗口
 * @param options 创建选项
 * @returns 创建的命令窗口实例
 */
export async function createAndActivateWindowCommand(options: CommandWindowOptions = {}): Promise<any> {
    console.log('=== 创建并激活 Window_Command ===');
    
    const window = await createWindowCommand({
        ...options,
        autoOpen: true,
        activate: true
    });
    
    console.log('Window_Command 已创建并激活');
    return window;
}

/**
 * 创建简单的命令窗口（用于测试）
 * @returns 创建的命令窗口实例
 */
export async function createSimpleWindowCommand(): Promise<any> {
    console.log('=== 创建简单命令窗口 ===');
    
    try {
        const window = await createWindowCommand({
            rect: { x: 150, y: 150, width: 200, height: 200 },
            autoOpen: true,
            addToStage: true,
            visible: true,
            initialIndex: 0,
            activate: true,
            commands: [
                { name: '选项1', symbol: 'option1', enabled: true },
                { name: '选项2', symbol: 'option2', enabled: true },
                { name: '选项3', symbol: 'option3', enabled: false },
                { name: '取消', symbol: 'cancel', enabled: true }
            ],
            autoRefresh: true
        });
        
        console.log('简单命令窗口创建成功');
        return window;
        
    } catch (error) {
        console.error('创建简单命令窗口失败:', error);
        throw error;
    }
}
