/**
 * 事件分类数据 - 三级结构：大类 > 子类 > 具体事件
 */

export interface EventItem {
  id: string;
  name: string;
  code: string;
  description: string;
}

export interface Subcategory {
  id: string;
  name: string;
  icon: string;
  events: EventItem[];
}

export interface MainCategory {
  id: string;
  name: string;
  icon: string;
  subcategories: Subcategory[];
}

export const eventCategories: MainCategory[] = [
  {
    id: 'scene',
    name: '场景',
    icon: '🎬',
    subcategories: [
      {
        id: 'scene_switch',
        name: '场景切换',
        icon: '🔄',
        events: [
          {
            id: 'goto_map',
            name: '切换到地图',
            code: 'SceneManager.goto(Scene_Map);',
            description: '切换到地图场景'
          },
          {
            id: 'goto_title',
            name: '切换到标题',
            code: 'SceneManager.goto(Scene_Title);',
            description: '切换到标题场景'
          },
          {
            id: 'goto_menu',
            name: '切换到菜单',
            code: 'SceneManager.push(Scene_Menu);',
            description: '打开主菜单'
          },
          {
            id: 'goto_battle',
            name: '切换到战斗',
            code: 'SceneManager.goto(Scene_Battle);',
            description: '切换到战斗场景'
          },
          {
            id: 'goto_gameover',
            name: '切换到游戏结束',
            code: 'SceneManager.goto(Scene_Gameover);',
            description: '切换到游戏结束场景'
          }
        ]
      },
      {
        id: 'scene_ui',
        name: '界面场景',
        icon: '📋',
        events: [
          {
            id: 'goto_save',
            name: '切换到存档',
            code: 'SceneManager.push(Scene_Save);',
            description: '打开存档界面'
          },
          {
            id: 'goto_load',
            name: '切换到读档',
            code: 'SceneManager.push(Scene_Load);',
            description: '打开读档界面'
          },
          {
            id: 'goto_options',
            name: '切换到选项',
            code: 'SceneManager.push(Scene_Options);',
            description: '打开选项界面'
          },
          {
            id: 'goto_shop',
            name: '切换到商店',
            code: 'SceneManager.push(Scene_Shop);',
            description: '打开商店界面'
          },
          {
            id: 'goto_name',
            name: '切换到命名',
            code: 'SceneManager.push(Scene_Name);',
            description: '打开命名界面'
          }
        ]
      }
    ]
  },
  {
    id: 'system',
    name: '系统',
    icon: '⚙️',
    subcategories: [
      {
        id: 'audio',
        name: '音频设置',
        icon: '🔊',
        events: [
          {
            id: 'master_volume',
            name: '主音量调节',
            code: 'AudioManager.masterVolume = value; // value: 0-100',
            description: '调整游戏整体音量 (0-100%)'
          },
          {
            id: 'bgm_volume',
            name: 'BGM音量调节',
            code: 'AudioManager.bgmVolume = value; // value: 0-100',
            description: '调整背景音乐音量'
          },
          {
            id: 'bgs_volume',
            name: 'BGS音量调节',
            code: 'AudioManager.bgsVolume = value; // value: 0-100',
            description: '调整背景音效音量'
          },
          {
            id: 'me_volume',
            name: 'ME音量调节',
            code: 'AudioManager.meVolume = value; // value: 0-100',
            description: '调整音乐效果音量'
          },
          {
            id: 'se_volume',
            name: 'SE音量调节',
            code: 'AudioManager.seVolume = value; // value: 0-100',
            description: '调整音效音量'
          },
          {
            id: 'mute_toggle',
            name: '静音切换',
            code: 'AudioManager._masterVolume = AudioManager._masterVolume > 0 ? 0 : 100;',
            description: '一键静音/取消静音'
          },
          {
            id: 'audio_test',
            name: '音频测试',
            code: 'SoundManager.playCursor();',
            description: '播放测试音效'
          }
        ]
      },
      {
        id: 'display',
        name: '显示设置',
        icon: '🖥️',
        events: [
          {
            id: 'fullscreen_toggle',
            name: '全屏切换',
            code: 'Graphics._switchFullScreen();',
            description: '切换全屏/窗口模式'
          },
          {
            id: 'resolution_adjust',
            name: '分辨率调整',
            code: 'Graphics.resize(width, height); // 设置宽度和高度',
            description: '调整游戏分辨率'
          },
          {
            id: 'quality_setting',
            name: '画质设置',
            code: 'Graphics._renderer.resolution = value; // value: 0.5/1.0/2.0',
            description: '调整画面质量 (低/中/高)'
          },
          {
            id: 'fps_limit',
            name: '帧率限制',
            code: 'Graphics._app.ticker.maxFPS = value; // value: 30/60/120',
            description: '设置帧率上限'
          },
          {
            id: 'brightness_adjust',
            name: '亮度调节',
            code: 'Graphics._app.stage.filters = [new PIXI.filters.ColorMatrixFilter()]; Graphics._app.stage.filters[0].brightness(value);',
            description: '调整屏幕亮度'
          }
        ]
      },
      {
        id: 'gameplay',
        name: '游戏设置',
        icon: '🎮',
        events: [
          {
            id: 'always_dash',
            name: '始终跑步切换',
            code: '$dataSystem.optAlwaysDash = !$dataSystem.optAlwaysDash; ConfigManager.save();',
            description: '开启/关闭始终跑步模式'
          },
          {
            id: 'command_remember',
            name: '指令记忆切换',
            code: '$dataSystem.optCommandRemember = !$dataSystem.optCommandRemember; ConfigManager.save();',
            description: '开启/关闭战斗指令记忆'
          },
          {
            id: 'message_speed',
            name: '消息速度调节',
            code: 'ConfigManager.textSpeed = value; ConfigManager.save(); // value: 1-5',
            description: '调整文本显示速度 (1-5级)'
          },
          {
            id: 'auto_save_toggle',
            name: '自动保存切换',
            code: 'ConfigManager.autosave = !ConfigManager.autosave; ConfigManager.save();',
            description: '开启/关闭自动保存功能'
          }
        ]
      },
      {
        id: 'controls',
        name: '控制设置',
        icon: '⌨️',
        events: [
          {
            id: 'key_config',
            name: '按键配置',
            code: 'SceneManager.push(Scene_KeyConfig);',
            description: '打开按键配置界面'
          },
          {
            id: 'gamepad_setting',
            name: '手柄设置',
            code: 'Input.gamepadSupport = !Input.gamepadSupport;',
            description: '开启/关闭手柄支持'
          },
          {
            id: 'touch_control',
            name: '触摸控制',
            code: 'ConfigManager.touchUI = !ConfigManager.touchUI; ConfigManager.save();',
            description: '开启/关闭触摸控制 (移动端)'
          }
        ]
      },
      {
        id: 'save',
        name: '存档设置',
        icon: '💾',
        events: [
          {
            id: 'quick_save',
            name: '快速保存',
            code: '$gameSystem.onBeforeSave(); DataManager.saveGame(1);',
            description: '执行快速保存'
          },
          {
            id: 'quick_load',
            name: '快速读取',
            code: 'DataManager.loadGame(1); $gamePlayer.refresh();',
            description: '执行快速读取'
          },
          {
            id: 'auto_save_interval',
            name: '自动保存间隔',
            code: 'ConfigManager.autosaveInterval = value * 60; // value: 分钟数',
            description: '设置自动保存时间间隔'
          }
        ]
      },
      {
        id: 'advanced',
        name: '高级设置',
        icon: '🔧',
        events: [
          {
            id: 'debug_mode',
            name: '调试模式',
            code: '$dataSystem.testBattlers = !$dataSystem.testBattlers; $testEvent = !$testEvent;',
            description: '开启/关闭调试模式'
          },
          {
            id: 'performance_monitor',
            name: '性能监控',
            code: 'Graphics.showFps = !Graphics.showFps;',
            description: '显示/隐藏性能信息'
          },
          {
            id: 'reset_settings',
            name: '重置设置',
            code: 'ConfigManager.makeEmpty(); ConfigManager.save(); location.reload();',
            description: '恢复默认设置'
          }
        ]
      }
    ]
  }
];
