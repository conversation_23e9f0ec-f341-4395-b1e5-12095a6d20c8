/**
 * 场景创建器基类
 * 提供场景创建的通用功能和生命周期管理
 */

import { objManage } from '../../objManage';

declare global {
    interface Window {
        Scene_Base: any;
        Scene_Boot: any;
        Scene_Title: any;
        Scene_Map: any;
        Scene_MenuBase: any;
        Scene_Battle: any;
        Scene_Menu: any;
        Scene_ItemBase: any;
        Scene_Item: any;
        Scene_Skill: any;
        Scene_Equip: any;
        Scene_Status: any;
        Scene_Options: any;
        Scene_File: any;
        Scene_Save: any;
        Scene_Load: any;
        Graphics: any;
        ImageManager: any;
        DataManager: any;
        SceneManager: any;
        $dataSystem: any;
    }
}

/**
 * 场景创建选项
 */
export interface SceneCreationOptions {
    /** 是否自动启动场景 */
    autoStart?: boolean;
    /** 是否添加到舞台 */
    addToStage?: boolean;
    /** 自定义初始化参数 */
    initParams?: any[];
    /** 场景特定选项 */
    sceneOptions?: any;
}

/**
 * 基础场景创建器
 */
export class BaseSceneCreator {
    /**
     * 检查场景类是否可用
     * @param sceneClass 场景类名
     * @returns 是否可用
     */
    protected static checkSceneClass(sceneClass: string): boolean {
        if (!window[sceneClass]) {
            console.error(`场景类 ${sceneClass} 未加载`);
            return false;
        }
        return true;
    }

    /**
     * 检查必要的资源是否准备就绪
     * @returns 资源状态
     */
    protected static async checkResources(): Promise<boolean> {
        console.log('检查场景创建所需资源...');

        const resourceStatus = objManage.getResourceStatus();
        if (!resourceStatus.all) {
            console.log('等待资源加载完成...', resourceStatus);
            const loaded = await objManage.waitForResources();
            if (!loaded) {
                throw new Error('资源加载超时');
            }
        }

        return true;
    }

    /**
     * 创建场景实例
     * @param sceneClass 场景类名
     * @param options 创建选项
     * @returns 创建的场景实例
     */
    protected static async createSceneInstance(
        sceneClass: string,
        options: SceneCreationOptions = {}
    ): Promise<any> {
        console.log(`=== 创建场景: ${sceneClass} ===`);
        console.log('创建选项:', options);

        try {
            // 1. 检查场景类
            if (!this.checkSceneClass(sceneClass)) {
                throw new Error(`场景类 ${sceneClass} 不可用`);
            }

            // 2. 检查资源
            await this.checkResources();

            // 3. 创建场景实例
            const SceneConstructor = window[sceneClass];
            const scene = new SceneConstructor(...(options.initParams || []));

            console.log(`场景 ${sceneClass} 实例创建成功:`, scene);

            // 4. 初始化场景
            if (scene.initialize && typeof scene.initialize === 'function') {
                scene.initialize(...(options.initParams || []));
                console.log(`场景 ${sceneClass} 初始化完成`);
            }

            // 5. 创建场景内容
            if (scene.create && typeof scene.create === 'function') {
                scene.create();
                console.log(`场景 ${sceneClass} 内容创建完成`);
            }

            // 6. 等待场景准备就绪
            if (scene.isReady && typeof scene.isReady === 'function') {
                let readyCheckCount = 0;
                const maxReadyChecks = 100; // 最多检查10秒

                while (!scene.isReady() && readyCheckCount < maxReadyChecks) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    readyCheckCount++;
                }

                if (readyCheckCount >= maxReadyChecks) {
                    console.warn(`场景 ${sceneClass} 准备就绪检查超时`);
                } else {
                    console.log(`场景 ${sceneClass} 已准备就绪`);
                }
            }

            // 7. 自动启动场景（如果需要）
            if (options.autoStart && scene.start && typeof scene.start === 'function') {
                scene.start();
                console.log(`场景 ${sceneClass} 已启动`);
            }

            // 8. 设置场景坐标为 (0, 0)
            if (scene.x !== undefined) scene.x = 0;
            if (scene.y !== undefined) scene.y = 0;
            console.log(`场景 ${sceneClass} 坐标已设置为 (0, 0)`);

            // 9. 添加到舞台（如果需要）
            if (options.addToStage !== false) {
                objManage.addToStage(scene, false); // 场景不需要居中，使用 (0, 0) 坐标
                console.log(`场景 ${sceneClass} 已添加到舞台`);
            }

            return scene;

        } catch (error) {
            console.error(`创建场景 ${sceneClass} 失败:`, error);
            throw error;
        }
    }

    /**
     * 预加载场景所需的资源
     * @param sceneClass 场景类名
     */
    protected static preloadSceneResources(sceneClass: string): void {
        console.log(`预加载场景 ${sceneClass} 所需资源...`);

        if (!window.ImageManager) {
            console.warn('ImageManager 未加载，跳过资源预加载');
            return;
        }

        // 预加载通用场景资源
        window.ImageManager.loadSystem('Window');

        // 根据场景类型预加载特定资源
        switch (sceneClass) {
            case 'Scene_Title':
                if (window.$dataSystem) {
                    window.ImageManager.loadTitle1(window.$dataSystem.title1Name);
                    window.ImageManager.loadTitle2(window.$dataSystem.title2Name);
                }
                break;

            case 'Scene_Battle':
                window.ImageManager.loadSystem('Shadow1');
                window.ImageManager.loadSystem('Damage');
                break;

            case 'Scene_Map':
                // 地图场景的资源会在地图加载时处理
                break;

            default:
                // 菜单类场景的通用资源
                window.ImageManager.loadSystem('IconSet');
                break;
        }

        console.log(`场景 ${sceneClass} 资源预加载请求已发送`);
    }
}

/**
 * 通用场景创建函数
 * @param sceneClass 场景类名
 * @param options 创建选项
 * @returns 创建的场景实例
 */
export async function createScene(
    sceneClass: string,
    options: SceneCreationOptions = {}
): Promise<any> {
    return BaseSceneCreator.createSceneInstance(sceneClass, options);
}

/**
 * 预加载所有场景资源
 */
export function preloadAllSceneResources(): void {
    console.log('预加载所有场景资源...');

    const sceneClasses = [
        'Scene_Title',
        'Scene_Battle',
        'Scene_Map',
        'Scene_Menu',
        'Scene_Item',
        'Scene_Skill',
        'Scene_Equip',
        'Scene_Status',
        'Scene_Options',
        'Scene_Save',
        'Scene_Load'
    ];

    sceneClasses.forEach(sceneClass => {
        BaseSceneCreator.preloadSceneResources(sceneClass);
    });

    console.log('所有场景资源预加载请求已发送');
}
