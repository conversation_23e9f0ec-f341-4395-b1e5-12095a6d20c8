/**
 * 项目状态管理 Store
 * 使用 Svelte 官方的 stores 来管理全局项目状态
 */

import { writable, derived } from 'svelte/store';
import type { ScriptInfo } from '../lib/tauriAPI';

/**
 * 项目状态接口
 */
export interface ProjectState {
  projectPath: string | null;
  projectName: string | null;
  projectFilePath: string | null;
  scripts: ScriptInfo[];
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;
}

/**
 * 初始项目状态
 */
const initialState: ProjectState = {
  projectPath: null,
  projectName: null,
  projectFilePath: null,
  scripts: [],
  isLoaded: false,
  isLoading: false,
  error: null
};

/**
 * 项目状态 store
 */
export const projectStore = writable<ProjectState>(initialState);

/**
 * 派生状态：是否有项目加载
 */
export const hasProject = derived(
  projectStore,
  ($projectStore) => $projectStore.isLoaded && $projectStore.projectPath !== null
);

/**
 * 派生状态：项目基础路径（用于资源加载）
 */
export const projectBasePath = derived(
  projectStore,
  ($projectStore) => $projectStore.projectPath
);

/**
 * 派生状态：JS 文件夹路径
 */
export const jsPath = derived(
  projectStore,
  ($projectStore) => $projectStore.projectPath ? `${$projectStore.projectPath}/js` : null
);

/**
 * 项目状态操作函数
 */
export const projectActions = {
  /**
   * 开始加载项目
   */
  startLoading() {
    projectStore.update(state => ({
      ...state,
      isLoading: true,
      error: null
    }));
  },

  /**
   * 设置项目信息
   */
  setProject(projectPath: string, projectFilePath: string, scripts: ScriptInfo[]) {
    const projectName = projectPath.split(/[/\\]/).pop() || 'Unknown Project';
    
    projectStore.update(state => ({
      ...state,
      projectPath,
      projectName,
      projectFilePath,
      scripts,
      isLoaded: true,
      isLoading: false,
      error: null
    }));

    console.log('项目状态已更新:', {
      projectPath,
      projectName,
      projectFilePath,
      scriptsCount: scripts.length
    });
  },

  /**
   * 设置加载错误
   */
  setError(error: string) {
    projectStore.update(state => ({
      ...state,
      isLoading: false,
      error
    }));
  },

  /**
   * 清除项目
   */
  clearProject() {
    projectStore.set(initialState);
    console.log('项目状态已清除');
  },

  /**
   * 更新脚本列表
   */
  updateScripts(scripts: ScriptInfo[]) {
    projectStore.update(state => ({
      ...state,
      scripts
    }));
  }
};

/**
 * 便捷的获取当前状态函数
 */
export function getCurrentProjectState(): ProjectState {
  let currentState: ProjectState;
  projectStore.subscribe(state => {
    currentState = state;
  })();
  return currentState!;
}

/**
 * 便捷的订阅项目路径变化
 */
export function subscribeToProjectPath(callback: (path: string | null) => void) {
  return projectStore.subscribe(state => {
    callback(state.projectPath);
  });
}

/**
 * 便捷的订阅项目加载状态
 */
export function subscribeToProjectLoaded(callback: (isLoaded: boolean) => void) {
  return projectStore.subscribe(state => {
    callback(state.isLoaded);
  });
}
