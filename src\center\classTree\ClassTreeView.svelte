<script lang="ts">
  /**
   * 类继承关系图可视化组件
   */
  
  import type { ClassTreeNode, ConnectionLine } from './tree';
  import { buildClassTree, calculateTreeLayout, DEFAULT_LAYOUT } from './tree';
  import { RPG_MAKER_CLASSES } from './treeData';
  import ClassNode from './ClassNode.svelte';
  import ClassConnection from './ClassConnection.svelte';

  // Props using $props()
  interface Props {
    rootClassName?: string;
    onnodeclick?: (event: { className: string; node: ClassTreeNode }) => void;
  }

  let {
    rootClassName = '',
    onnodeclick
  }: Props = $props();

  // 状态
  let highlightedNodes = $state(new Set<string>());
  let hoveredNode = $state<string | null>(null);

  // 直接使用 $derived 计算所有数据
  let treeData = $derived.by(() => {
    if (!rootClassName) {
      console.log('没有根类名，返回空数据');
      return {
        tree: null,
        connections: [],
        svgWidth: 800,
        svgHeight: 600
      };
    }

    // 使用固定的合理尺寸，避免异常值
    const safeContainerWidth = 1000;
    const safeContainerHeight = 800;

    console.log('构建继承关系树:', rootClassName, `使用固定容器尺寸: ${safeContainerWidth}x${safeContainerHeight}`);

    // 构建树结构
    const builtTree = buildClassTree(RPG_MAKER_CLASSES, rootClassName);

    if (builtTree) {
      // 计算布局，传递安全的容器尺寸
      const layout = calculateTreeLayout(
        builtTree,
        {
          ...DEFAULT_LAYOUT,
          nodeWidth: 120,
          nodeHeight: 40
        },
        safeContainerWidth,
        safeContainerHeight
      );

      const result = {
        tree: layout.tree,
        connections: layout.connections,
        svgWidth: layout.width,
        svgHeight: layout.height
      };

      console.log('树结构构建完成:', {
        rootNode: layout.tree.name,
        nodes: countNodes(layout.tree),
        connections: layout.connections.length,
        size: { width: result.svgWidth, height: result.svgHeight },
        firstNodeCoords: layout.tree.x !== undefined ? `(${layout.tree.x}, ${layout.tree.y})` : 'no coords'
      });

      return result;
    } else {
      console.warn('无法构建树结构，根类不存在:', rootClassName);
      return {
        tree: null,
        connections: [],
        svgWidth: 800,
        svgHeight: 600
      };
    }
  });

  // 提取计算后的数据
  let tree = $derived(treeData.tree);
  let connections = $derived(treeData.connections);
  let svgWidth = $derived(treeData.svgWidth);
  let svgHeight = $derived(treeData.svgHeight);

  /**
   * 计算树中节点总数
   */
  function countNodes(node: ClassTreeNode): number {
    let count = 1;
    for (const child of node.children) {
      count += countNodes(child);
    }
    return count;
  }

  /**
   * 递归渲染树节点
   */
  function renderTreeNodes(node: ClassTreeNode): ClassTreeNode[] {
    const nodes = [node];
    for (const child of node.children) {
      nodes.push(...renderTreeNodes(child));
    }
    return nodes;
  }

  /**
   * 处理节点点击
   */
  function handleNodeClick(event: { className: string; node: ClassTreeNode }) {
    const { className, node } = event;
    console.log(`点击了类节点: ${className}`);
    onnodeclick?.({ className, node });
  }

  /**
   * 处理节点悬停
   */
  function handleNodeHover(event: { className: string; node: ClassTreeNode; isEnter: boolean }) {
    const { className, isEnter } = event;

    if (isEnter) {
      hoveredNode = className;
      highlightInheritancePath(className);
    } else {
      hoveredNode = null;
      highlightedNodes.clear();
      highlightedNodes = new Set(highlightedNodes);
    }
  }

  /**
   * 高亮继承路径
   */
  function highlightInheritancePath(className: string) {
    highlightedNodes.clear();
    
    if (tree) {
      // 向上查找父类
      findParentPath(tree, className, highlightedNodes);
      // 向下查找子类
      findChildrenPath(tree, className, highlightedNodes);
    }
    
    highlightedNodes = new Set(highlightedNodes);
  }

  /**
   * 查找父类路径
   */
  function findParentPath(node: ClassTreeNode, targetClass: string, path: Set<string>): boolean {
    if (node.name === targetClass) {
      path.add(node.name);
      return true;
    }
    
    for (const child of node.children) {
      if (findParentPath(child, targetClass, path)) {
        path.add(node.name);
        return true;
      }
    }
    
    return false;
  }

  /**
   * 查找子类路径
   */
  function findChildrenPath(node: ClassTreeNode, targetClass: string, path: Set<string>) {
    if (node.name === targetClass) {
      addAllChildren(node, path);
    } else {
      for (const child of node.children) {
        findChildrenPath(child, targetClass, path);
      }
    }
  }

  /**
   * 添加所有子节点到路径
   */
  function addAllChildren(node: ClassTreeNode, path: Set<string>) {
    path.add(node.name);
    for (const child of node.children) {
      addAllChildren(child, path);
    }
  }

  /**
   * 检查连接线是否应该高亮
   */
  function isConnectionHighlighted(connection: ConnectionLine): boolean {
    return highlightedNodes.has(connection.fromClass) && highlightedNodes.has(connection.toClass);
  }

  // 获取所有节点用于渲染
  let allNodes = $derived(tree ? renderTreeNodes(tree) : []);
</script>

<div class="tree-view-container">
  {#if tree}
    <svg 
      width={svgWidth} 
      height={svgHeight}
      viewBox="0 0 {svgWidth} {svgHeight}"
      class="tree-svg"
    >
      <!-- 定义渐变和滤镜 -->
      <defs>
        <linearGradient id="nodeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.1" />
          <stop offset="100%" style="stop-color:#000000;stop-opacity:0.1" />
        </linearGradient>
        
        <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
          <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
        </filter>
      </defs>
      
      <!-- 渲染连接线 -->
      <g class="connections-layer">
        {#each connections as connection}
          <ClassConnection 
            {connection}
            isHighlighted={isConnectionHighlighted(connection)}
            strokeColor="var(--theme-border)"
          />
        {/each}
      </g>
      
      <!-- 渲染节点 -->
      <g class="nodes-layer">
        {#each allNodes as node (node.name)}
          <ClassNode
            {node}
            nodeHeight={40}
            onnodeclick={handleNodeClick}
            onnodehover={handleNodeHover}
          />
        {/each}
      </g>

      <!-- 调试信息 -->
      {#if allNodes.length > 0}
        <text x="10" y="20" fill="var(--theme-text)" font-size="12" opacity="0.7">
          节点数: {allNodes.length}
        </text>
      {/if}
    </svg>
  {:else}
    <div class="empty-state">
      <p>请选择一个基类型来查看继承关系图</p>
    </div>
  {/if}
</div>

<style>
  .tree-view-container {
    width: 100%;
    height: 100%;
    overflow: auto;
    background: var(--theme-surface);
    border-radius: var(--border-radius);
    border: 1px solid var(--theme-border);
  }

  .tree-svg {
    display: block;
    background: var(--theme-background);
    min-width: 100%;
    min-height: 100%;
  }

  .connections-layer {
    z-index: 1;
  }

  .nodes-layer {
    z-index: 2;
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--theme-text-muted);
    font-style: italic;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .tree-view-container {
      font-size: 0.9rem;
    }
  }
</style>
