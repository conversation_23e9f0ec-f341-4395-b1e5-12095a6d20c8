<script lang="ts">
  import SimpleDataSelector from './SimpleDataSelector.svelte';
  import type { DataSelection } from '../types/dataTypes';
  import { dataManager } from '../stores/dataStore';

  // Props
  interface Props {
    value?: string;
    onChange?: (value: string) => void;
    placeholder?: string;
    disabled?: boolean;
    showPreview?: boolean;
    itemIndex?: number; // 用于数组数据的索引
  }

  let {
    value = '',
    onChange,
    placeholder = "输入文本或选择数据绑定",
    disabled = false,
    showPreview = true,
    itemIndex = 1
  }: Props = $props();

  // 本地状态
  let dataSelection = $state<DataSelection | null>(null);
  let previewValue = $state<string>('');

  // 处理文本输入变化
  function handleTextChange(event: Event) {
    const target = event.target as HTMLTextAreaElement;
    const newValue = target.value;
    if (onChange) {
      onChange(newValue);
    }
    updatePreview(newValue);
  }

  // 处理数据选择
  function handleDataSelect(selection: DataSelection | null) {
    dataSelection = selection;
  }

  // 应用数据绑定
  function applyDataBinding() {
    if (!dataSelection) return;

    const bindingExpression = `{{${dataSelection.dataType}.${dataSelection.fieldPath}}}`;
    if (onChange) {
      onChange(bindingExpression);
    }
    updatePreview(bindingExpression);
  }

  // 更新预览值
  function updatePreview(text: string) {
    if (!showPreview) return;

    // 解析绑定表达式并获取实际值
    const bindingRegex = /\{\{(\w+)\.([^}]+)\}\}/g;
    let previewText = text;
    let match;

    while ((match = bindingRegex.exec(text)) !== null) {
      const [fullMatch, dataType, fieldPath] = match;
      try {
        const actualValue = dataManager.getFieldValue(dataType as any, fieldPath, itemIndex);
        if (actualValue !== null && actualValue !== undefined) {
          previewText = previewText.replace(fullMatch, String(actualValue));
        }
      } catch (error) {
        console.warn('预览数据绑定失败:', error);
      }
    }

    previewValue = previewText;
  }

  // 监听value变化，更新预览
  $effect(() => {
    updatePreview(value);
  });
</script>

<div class="data-binding-input" class:disabled>
  <!-- 数据选择器 -->
  <div class="selector-section">
    <label class="section-label">数据绑定:</label>
    <SimpleDataSelector
      value={dataSelection}
      onSelect={handleDataSelect}
      placeholder="选择数据字段"
      compact={true}
      disabled={disabled}
    />
    {#if dataSelection}
      <button
        class="apply-button"
        onclick={applyDataBinding}
        disabled={disabled}
        title="应用数据绑定到文本"
      >
        应用绑定
      </button>
    {/if}
  </div>

  <!-- 文本输入 -->
  <div class="input-section">
    <label class="section-label">文本内容:</label>
    <textarea
      {value}
      oninput={handleTextChange}
      {placeholder}
      {disabled}
      rows="3"
      class="text-input"
    ></textarea>
  </div>

  <!-- 预览 -->
  {#if showPreview && previewValue && previewValue !== value}
    <div class="preview-section">
      <label class="section-label">预览:</label>
      <div class="preview-content">
        {previewValue}
      </div>
    </div>
  {/if}

  <!-- 绑定信息 -->
  {#if dataSelection}
    <div class="binding-info">
      <span class="binding-text">
        选中: {dataSelection.displayText}
      </span>
    </div>
  {/if}
</div>

<style>
  .data-binding-input {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .data-binding-input.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  .selector-section,
  .input-section,
  .preview-section {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .section-label {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    margin: 0;
  }

  .selector-section {
    position: relative;
  }

  .apply-button {
    position: absolute;
    top: 24px;
    right: 4px;
    padding: 4px 8px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    transition: background-color 0.2s;
    z-index: 10;
  }

  .apply-button:hover:not(:disabled) {
    background: #2563eb;
  }

  .apply-button:disabled {
    background: #9ca3af;
    cursor: not-allowed;
  }

  .text-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    min-height: 60px;
    background: white;
    transition: border-color 0.2s;
  }

  .text-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .text-input:disabled {
    background: #f9fafb;
    color: #6b7280;
  }

  .preview-section {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 8px;
  }

  .preview-content {
    font-size: 14px;
    color: #1f2937;
    min-height: 20px;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .binding-info {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    background: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 4px;
  }

  .binding-text {
    font-size: 12px;
    color: #1e40af;
    font-weight: 500;
  }

  /* 响应式设计 */
  @media (max-width: 480px) {
    .apply-button {
      position: static;
      margin-top: 6px;
      align-self: flex-start;
    }
  }
</style>
