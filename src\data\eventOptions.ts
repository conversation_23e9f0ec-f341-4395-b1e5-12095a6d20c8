/**
 * 事件选择数据配置
 * 按照 分类-子分类-具体选项 的三级结构组织
 */

export interface EventOption {
  id: string;
  label: string;
  code: string;
  description?: string;
}

export interface EventSubCategory {
  id: string;
  label: string;
  options: EventOption[];
}

export interface EventCategory {
  id: string;
  label: string;
  icon: string;
  subCategories: EventSubCategory[];
}

/**
 * 事件选择数据
 */
export const eventData: EventCategory[] = [
  // ==================== 场景管理 ====================
  {
    id: 'scene',
    label: '场景',
    icon: '🎬',
    subCategories: [
      {
        id: 'scene-transition',
        label: '切换场景',
        options: [
          {
            id: 'goto-title',
            label: '返回标题画面',
            code: 'SceneManager.goto(Scene_Title);',
            description: '返回游戏标题画面'
          },
          {
            id: 'goto-map',
            label: '返回地图',
            code: 'SceneManager.goto(Scene_Map);',
            description: '返回地图场景'
          },
          {
            id: 'goto-menu',
            label: '打开主菜单',
            code: 'SceneManager.push(Scene_Menu);',
            description: '打开游戏主菜单'
          },
          {
            id: 'goto-save',
            label: '打开存档画面',
            code: 'SceneManager.push(Scene_Save);',
            description: '打开存档选择画面'
          },
          {
            id: 'goto-load',
            label: '打开读档画面',
            code: 'SceneManager.push(Scene_Load);',
            description: '打开读档选择画面'
          },
          {
            id: 'goto-options',
            label: '打开设置画面',
            code: 'SceneManager.push(Scene_Options);',
            description: '打开游戏设置画面'
          },
          {
            id: 'goto-gameover',
            label: '游戏结束',
            code: 'SceneManager.goto(Scene_Gameover);',
            description: '跳转到游戏结束画面'
          }
        ]
      },
      {
        id: 'scene-control',
        label: '场景控制',
        options: [
          {
            id: 'scene-pop',
            label: '返回上一场景',
            code: 'SceneManager.pop();',
            description: '返回到上一个场景'
          },
          {
            id: 'scene-exit',
            label: '退出游戏',
            code: 'SceneManager.exit();',
            description: '退出游戏程序'
          }
        ]
      }
    ]
  },

  // ==================== 系统设置 ====================
  {
    id: 'system',
    label: '系统',
    icon: '⚙️',
    subCategories: [
      {
        id: 'audio',
        label: '音频设置',
        options: [
          {
            id: 'bgm-volume',
            label: 'BGM音量调节',
            code: 'AudioManager.bgmVolume = value; // value: 0-100',
            description: '调整背景音乐音量'
          },
          {
            id: 'bgs-volume',
            label: 'BGS音量调节',
            code: 'AudioManager.bgsVolume = value; // value: 0-100',
            description: '调整背景音效音量'
          },
          {
            id: 'me-volume',
            label: 'ME音量调节',
            code: 'AudioManager.meVolume = value; // value: 0-100',
            description: '调整音乐效果音量'
          },
          {
            id: 'se-volume',
            label: 'SE音量调节',
            code: 'AudioManager.seVolume = value; // value: 0-100',
            description: '调整音效音量'
          },
          {
            id: 'mute-toggle',
            label: '静音切换',
            code: 'AudioManager._masterVolume = AudioManager._masterVolume > 0 ? 0 : 100;',
            description: '一键静音/取消静音'
          },
          {
            id: 'play-se-cursor',
            label: '播放光标音效',
            code: 'SoundManager.playCursor();',
            description: '播放光标移动音效'
          },
          {
            id: 'play-se-ok',
            label: '播放确认音效',
            code: 'SoundManager.playOk();',
            description: '播放确认选择音效'
          },
          {
            id: 'play-se-cancel',
            label: '播放取消音效',
            code: 'SoundManager.playCancel();',
            description: '播放取消操作音效'
          },
          {
            id: 'play-se-buzzer',
            label: '播放错误音效',
            code: 'SoundManager.playBuzzer();',
            description: '播放错误提示音效'
          },
          {
            id: 'play-se-equip',
            label: '播放装备音效',
            code: 'SoundManager.playEquip();',
            description: '播放装备道具音效'
          },
          {
            id: 'play-se-save',
            label: '播放保存音效',
            code: 'SoundManager.playSave();',
            description: '播放保存游戏音效'
          },
          {
            id: 'play-se-load',
            label: '播放读取音效',
            code: 'SoundManager.playLoad();',
            description: '播放读取存档音效'
          }
        ]
      },
      {
        id: 'system-message',
        label: '消息',
        options: [
          {
            id: 'show-message',
            label: '显示消息',
            code: '$gameMessage.add("这是一条消息");',
            description: '在游戏中显示一条消息'
          },
          {
            id: 'clear-message',
            label: '清除消息',
            code: '$gameMessage.clear();',
            description: '清除当前显示的消息'
          }
        ]
      },
      {
        id: 'display',
        label: '显示设置',
        options: [
          {
            id: 'fullscreen-toggle',
            label: '全屏切换',
            code: 'Graphics._switchFullScreen();',
            description: '切换全屏/窗口模式'
          },
          {
            id: 'resolution-adjust',
            label: '分辨率调整',
            code: 'Graphics.resize(width, height); // 设置宽度和高度',
            description: '调整游戏分辨率'
          }
        ]
      },
      {
        id: 'gameplay',
        label: '游戏设置',
        options: [
          {
            id: 'always-dash',
            label: '始终跑步切换',
            code: '$dataSystem.optAlwaysDash = !$dataSystem.optAlwaysDash; ConfigManager.save();',
            description: '开启/关闭始终跑步模式'
          },
          {
            id: 'command-remember',
            label: '指令记忆切换',
            code: '$dataSystem.optCommandRemember = !$dataSystem.optCommandRemember; ConfigManager.save();',
            description: '开启/关闭战斗指令记忆'
          },
          {
            id: 'message-speed',
            label: '消息速度调节',
            code: 'ConfigManager.textSpeed = value; ConfigManager.save(); // value: 1-5',
            description: '调整文本显示速度 (1-5级)'
          }
        ]
      },
      {
        id: 'controls',
        label: '控制设置',
        options: [
          {
            id: 'touch-control',
            label: '触摸控制切换',
            code: 'ConfigManager.touchUI = !ConfigManager.touchUI; ConfigManager.save();',
            description: '开启/关闭触摸控制 (移动端)'
          }
        ]
      },
      {
        id: 'system-message',
        label: '消息',
        options: [
          {
            id: 'show-message',
            label: '显示消息',
            code: '$gameMessage.add("这是一条消息");',
            description: '在游戏中显示一条消息'
          },
          {
            id: 'clear-message',
            label: '清除消息',
            code: '$gameMessage.clear();',
            description: '清除当前显示的消息'
          }
        ]
      },
      {
        id: 'system-variable',
        label: '变量开关',
        options: [
          {
            id: 'toggle-switch',
            label: '切换开关1',
            code: '$gameSwitches.setValue(1, !$gameSwitches.value(1));',
            description: '切换1号开关的状态'
          },
          {
            id: 'set-variable',
            label: '设置变量1',
            code: '$gameVariables.setValue(1, value); // value: 数值',
            description: '将1号变量设置为指定数值'
          }
        ]
      }
    ]
  },

  // ==================== 游戏控制 ====================
  {
    id: 'game',
    label: '游戏',
    icon: '🎮',
    subCategories: [
      {
        id: 'game-party',
        label: '队伍',
        options: [
          {
            id: 'add-actor',
            label: '加入角色1',
            code: '$gameParty.addActor(1);',
            description: '将1号角色加入队伍'
          },
          {
            id: 'remove-actor',
            label: '移除角色1',
            code: '$gameParty.removeActor(1);',
            description: '将1号角色从队伍移除'
          }
        ]
      },
      {
        id: 'game-gold',
        label: '金钱',
        options: [
          {
            id: 'gain-gold',
            label: '获得金钱',
            code: '$gameParty.gainGold(1000);',
            description: '获得1000金钱'
          },
          {
            id: 'lose-gold',
            label: '失去金钱',
            code: '$gameParty.loseGold(500);',
            description: '失去500金钱'
          }
        ]
      }
    ]
  }
];

/**
 * 根据ID查找事件选项
 */
export function findEventOption(optionId: string): EventOption | null {
  for (const category of eventData) {
    for (const subCategory of category.subCategories) {
      const option = subCategory.options.find(opt => opt.id === optionId);
      if (option) {
        return option;
      }
    }
  }
  return null;
}

/**
 * 获取所有事件选项的扁平列表
 */
export function getAllEventOptions(): EventOption[] {
  const allOptions: EventOption[] = [];
  for (const category of eventData) {
    for (const subCategory of category.subCategories) {
      allOptions.push(...subCategory.options);
    }
  }
  return allOptions;
}
