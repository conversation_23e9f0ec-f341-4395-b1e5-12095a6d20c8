<script lang="ts">
  import { eventData, type EventCategory, type EventSubCategory, type EventOption } from '../data/eventOptions';

  // Props
  let { onEventSelected, onClose }: {
    onEventSelected: (event: CustomEvent) => void;
    onClose: () => void;
  } = $props();

  // 状态管理
  let selectedCategory: EventCategory | null = $state(null);
  let selectedSubCategory: EventSubCategory | null = $state(null);
  let selectedOption: EventOption | null = $state(null);

  // 搜索功能
  let searchTerm = $state('');
  let filteredOptions = $derived(() => {
    if (!searchTerm.trim()) return [];

    const term = searchTerm.toLowerCase();
    const results: EventOption[] = [];

    for (const category of eventData) {
      for (const subCategory of category.subCategories) {
        for (const option of subCategory.options) {
          if (
            option.label.toLowerCase().includes(term) ||
            option.description?.toLowerCase().includes(term) ||
            category.label.toLowerCase().includes(term) ||
            subCategory.label.toLowerCase().includes(term)
          ) {
            results.push(option);
          }
        }
      }
    }

    return results;
  });

  // 重置选择状态
  function resetSelection() {
    selectedCategory = null;
    selectedSubCategory = null;
    selectedOption = null;
  }

  // 选择分类
  function selectCategory(category: EventCategory) {
    selectedCategory = category;
    selectedSubCategory = null;
    selectedOption = null;
  }

  // 选择子分类
  function selectSubCategory(subCategory: EventSubCategory) {
    selectedSubCategory = subCategory;
    selectedOption = null;
  }

  // 选择具体选项
  function selectOption(option: EventOption) {
    selectedOption = option;
  }

  // 确认选择
  function confirmSelection() {
    if (selectedOption) {
      const event = new CustomEvent('eventSelected', {
        detail: selectedOption
      });
      onEventSelected(event);
    }
  }

  // 处理搜索结果选择
  function selectFromSearch(option: EventOption) {
    selectedOption = option;
    searchTerm = ''; // 清除搜索
  }

  // 关闭模态框
  function handleClose() {
    onClose();
  }

  // 键盘事件处理
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      handleClose();
    } else if (event.key === 'Enter' && selectedOption) {
      confirmSelection();
    }
  }
</script>

<svelte:window on:keydown={handleKeydown} />

<!-- 模态框遮罩 -->
<div class="modal-overlay" onclick={handleClose} role="button" tabindex="0">
  <!-- 模态框内容 -->
  <div class="modal-content" onclick={(e) => e.stopPropagation()} role="dialog">
    <!-- 头部 -->
    <div class="modal-header">
      <h3>🎯 选择事件</h3>
      <button class="close-btn" onclick={handleClose}>✕</button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-section">
      <div class="search-input-wrapper">
        <input
          type="text"
          placeholder="🔍 搜索事件..."
          bind:value={searchTerm}
          class="search-input"
        />
        {#if searchTerm}
          <button class="clear-search" onclick={() => searchTerm = ''}>✕</button>
        {/if}
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="modal-body">
      {#if searchTerm && filteredOptions.length > 0}
        <!-- 搜索结果 -->
        <div class="search-results">
          <h4>🔍 搜索结果 ({filteredOptions.length})</h4>
          <div class="options-list">
            {#each filteredOptions as option}
              <div
                class="option-item"
                class:selected={selectedOption?.id === option.id}
                onclick={() => selectFromSearch(option)}
                role="button"
                tabindex="0"
              >
                <div class="option-label">{option.label}</div>
                <div class="option-description">{option.description || ''}</div>
                <div class="option-code">{option.code}</div>
              </div>
            {/each}
          </div>
        </div>
      {:else if searchTerm}
        <!-- 无搜索结果 -->
        <div class="no-results">
          <p>😔 没有找到匹配的事件</p>
          <button class="clear-search-btn" onclick={() => searchTerm = ''}>清除搜索</button>
        </div>
      {:else}
        <!-- 三栏布局：左中右 -->
        <div class="three-column-layout">
          <!-- 左栏：一级分类菜单 -->
          <div class="left-panel">
            <div class="panel-header">
              <h4>📂 分类</h4>
            </div>
            <div class="categories-list">
              {#each eventData as category}
                <div
                  class="category-item"
                  class:active={selectedCategory?.id === category.id}
                  onclick={() => selectCategory(category)}
                  role="button"
                  tabindex="0"
                >
                  <div class="category-icon">{category.icon}</div>
                  <div class="category-info">
                    <div class="category-label">{category.label}</div>
                    <div class="category-count">
                      {category.subCategories.reduce((sum, sub) => sum + sub.options.length, 0)} 个选项
                    </div>
                  </div>
                </div>
              {/each}
            </div>
          </div>

          <!-- 中栏：二级子分类菜单 -->
          <div class="middle-panel">
            <div class="panel-header">
              <h4>
                {#if selectedCategory}
                  {selectedCategory.icon} {selectedCategory.label}
                {:else}
                  📋 子分类
                {/if}
              </h4>
            </div>
            <div class="subcategories-list">
              {#if selectedCategory}
                {#each selectedCategory.subCategories as subCategory}
                  <div
                    class="subcategory-item"
                    class:active={selectedSubCategory?.id === subCategory.id}
                    onclick={() => selectSubCategory(subCategory)}
                    role="button"
                    tabindex="0"
                  >
                    <div class="subcategory-info">
                      <div class="subcategory-label">📂 {subCategory.label}</div>
                      <div class="subcategory-count">{subCategory.options.length} 个选项</div>
                    </div>
                  </div>
                {/each}
              {:else}
                <div class="empty-state">
                  <p>👈 请先选择左侧的分类</p>
                </div>
              {/if}
            </div>
          </div>

          <!-- 右栏：具体选项列表 -->
          <div class="right-panel">
            <div class="panel-header">
              <h4>
                {#if selectedSubCategory}
                  📋 {selectedSubCategory.label}
                {:else}
                  📝 选项列表
                {/if}
              </h4>
            </div>
            <div class="options-list">
              {#if selectedSubCategory}
                {#each selectedSubCategory.options as option}
                  <div
                    class="option-item"
                    class:selected={selectedOption?.id === option.id}
                    onclick={() => selectOption(option)}
                    role="button"
                    tabindex="0"
                  >
                    <div class="option-label">{option.label}</div>
                    <div class="option-description">{option.description || ''}</div>
                    <div class="option-code">{option.code}</div>
                  </div>
                {/each}
              {:else}
                <div class="empty-state">
                  <p>👈 请先选择中间的子分类</p>
                </div>
              {/if}
            </div>
          </div>
        </div>
      {/if}
    </div>

    <!-- 底部按钮 -->
    <div class="modal-footer">
      <button class="btn secondary" onclick={handleClose}>取消</button>
      <button
        class="btn primary"
        disabled={!selectedOption}
        onclick={confirmSelection}
      >
        确认选择
      </button>
    </div>
  </div>
</div>

<style>
  /* 模态框样式 */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: var(--bg-primary, #2a2a2a);
    border-radius: 8px;
    border: 1px solid var(--border-color, #444);
    width: 90vw;
    max-width: 800px;
    height: 80vh;
    max-height: 600px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  /* 头部样式 */
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color, #444);
    background: var(--bg-secondary, #333);
    border-radius: 8px 8px 0 0;
  }

  .modal-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--text-primary, #fff);
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-secondary, #ccc);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .close-btn:hover {
    background: var(--bg-hover, #444);
    color: var(--text-primary, #fff);
  }

  /* 搜索栏样式 */
  .search-section {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color, #444);
    background: var(--bg-secondary, #333);
  }

  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-input {
    flex: 1;
    padding: 8px 12px;
    background: var(--bg-primary, #2a2a2a);
    border: 1px solid var(--border-color, #444);
    border-radius: 4px;
    color: var(--text-primary, #fff);
    font-size: 14px;
  }

  .search-input:focus {
    outline: none;
    border-color: var(--primary-color, #4CAF50);
  }

  .clear-search {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: var(--text-secondary, #ccc);
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
  }

  .clear-search:hover {
    background: var(--bg-hover, #444);
    color: var(--text-primary, #fff);
  }

  /* 主体内容样式 */
  .modal-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  /* 三栏布局 */
  .three-column-layout {
    display: flex;
    height: 100%;
    overflow: hidden;
  }

  /* 左栏：一级分类 */
  .left-panel {
    width: 200px;
    border-right: 1px solid var(--border-color, #444);
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary, #333);
  }

  /* 中栏：二级分类 */
  .middle-panel {
    width: 220px;
    border-right: 1px solid var(--border-color, #444);
    display: flex;
    flex-direction: column;
    background: var(--bg-primary, #2a2a2a);
  }

  /* 右栏：具体选项 */
  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary, #2a2a2a);
  }

  /* 面板头部 */
  .panel-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color, #444);
    background: var(--bg-secondary, #333);
  }

  .panel-header h4 {
    margin: 0;
    font-size: 13px;
    color: var(--text-primary, #fff);
    font-weight: 600;
  }

  /* 分类列表（左栏） */
  .categories-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .category-item {
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 4px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
  }

  .category-item:hover {
    background: var(--bg-hover, #3a3a3a);
    border-color: var(--border-color, #444);
  }

  .category-item.active {
    background: var(--primary-color-light, rgba(76, 175, 80, 0.1));
    border-color: var(--primary-color, #4CAF50);
  }

  .category-item .category-icon {
    font-size: 20px;
    margin-right: 12px;
    width: 24px;
    text-align: center;
  }

  .category-info {
    flex: 1;
  }

  .category-item .category-label {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary, #fff);
    margin-bottom: 2px;
  }

  .category-item .category-count {
    font-size: 11px;
    color: var(--text-secondary, #ccc);
  }

  /* 子分类列表（中栏） */
  .subcategories-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .subcategory-item {
    padding: 12px;
    margin-bottom: 4px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
  }

  .subcategory-item:hover {
    background: var(--bg-hover, #3a3a3a);
    border-color: var(--border-color, #444);
  }

  .subcategory-item.active {
    background: var(--primary-color-light, rgba(76, 175, 80, 0.1));
    border-color: var(--primary-color, #4CAF50);
  }

  .subcategory-info {
    display: flex;
    flex-direction: column;
  }

  .subcategory-label {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary, #fff);
    margin-bottom: 2px;
  }

  .subcategory-count {
    font-size: 11px;
    color: var(--text-secondary, #ccc);
  }

  /* 空状态 */
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    color: var(--text-secondary, #ccc);
    font-size: 12px;
    text-align: center;
  }

  /* 选项列表（右栏） */
  .options-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .option-item {
    background: var(--bg-secondary, #333);
    border: 1px solid var(--border-color, #444);
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .option-item:hover {
    border-color: var(--primary-color, #4CAF50);
    background: var(--bg-hover, #3a3a3a);
  }

  .option-item.selected {
    border-color: var(--primary-color, #4CAF50);
    background: var(--primary-color-light, rgba(76, 175, 80, 0.1));
  }

  .option-label {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary, #fff);
    margin-bottom: 4px;
  }

  .option-description {
    font-size: 11px;
    color: var(--text-secondary, #ccc);
    margin-bottom: 8px;
    line-height: 1.4;
  }

  .option-code {
    font-family: 'Courier New', monospace;
    font-size: 10px;
    color: var(--primary-color, #4CAF50);
    background: var(--bg-primary, #2a2a2a);
    padding: 6px 8px;
    border-radius: 4px;
    border: 1px solid var(--border-color, #444);
    word-break: break-all;
    line-height: 1.3;
  }

  /* 搜索结果 */
  .search-results h4 {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: var(--text-primary, #fff);
    padding: 0 20px;
  }

  .no-results {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary, #ccc);
  }

  .clear-search-btn {
    background: var(--primary-color, #4CAF50);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 12px;
    transition: all 0.2s;
  }

  .clear-search-btn:hover {
    background: var(--primary-color-dark, #45a049);
  }

  /* 底部按钮 */
  .modal-footer {
    display: flex;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid var(--border-color, #444);
    background: var(--bg-secondary, #333);
    border-radius: 0 0 8px 8px;
  }

  .btn {
    flex: 1;
    padding: 10px 20px;
    border: 1px solid var(--border-color, #444);
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }

  .btn.secondary {
    background: var(--bg-primary, #2a2a2a);
    color: var(--text-primary, #fff);
  }

  .btn.secondary:hover {
    background: var(--bg-hover, #3a3a3a);
  }

  .btn.primary {
    background: var(--primary-color, #4CAF50);
    color: white;
    border-color: var(--primary-color, #4CAF50);
  }

  .btn.primary:hover {
    background: var(--primary-color-dark, #45a049);
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn:disabled:hover {
    background: var(--primary-color, #4CAF50);
  }
</style>