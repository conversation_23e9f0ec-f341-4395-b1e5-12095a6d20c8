<script lang="ts">
  import { bitmapModel, selectedElement } from '../stores/bitmapStore';
  import ElementItem from './ElementItem.svelte';

  // 生成唯一ID
  function generateId(): string {
    return `element_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // 🔧 处理元素选择，使用独立的选中状态
  function handleSelect(index: number) {
    console.log('ElementsListPanel handleSelect:', index);
    if ($bitmapModel && index >= 0 && index < $bitmapModel.elements.length) {
      selectedElement.set($bitmapModel.elements[index]);
    }
  }

  // 🔧 处理添加元素，直接操作 bitmapModel
  function handleAddText() {
    if (!$bitmapModel) return;

    $bitmapModel.addTextElement('新文本', 100, 100, {
      id: generateId(),
      maxWidth: 60,
      lineHeight: 36,
      align: 'left'
    });
    console.log('添加文本元素到 bitmapModel');
  }

  function handleAddImage() {
    if (!$bitmapModel) return;

    $bitmapModel.addImageElement(
      null, // source
      0, 0, 100, 100, // sx, sy, sw, sh
      100, 100, 100, 100, // dx, dy, dw, dh
      { id: generateId() }
    );
    console.log('添加图片元素到 bitmapModel');
  }

  // 🔧 处理删除元素，直接操作 bitmapModel
  function handleRemove(index: number) {
    if (!$bitmapModel) return;
    $bitmapModel.removeElement(index);
  }

  // 🔧 处理移动元素，直接操作 bitmapModel
  function handleMoveUp(index: number) {
    if (!$bitmapModel || index <= 0) return;

    const elements = $bitmapModel.elements;
    if (index >= elements.length) return;

    // 交换元素位置
    [elements[index], elements[index - 1]] = [elements[index - 1], elements[index]];

    // 更新选中状态
    if ($selectedElement === elements[index]) {
      selectedElement.set(elements[index - 1]);
    } else if ($selectedElement === elements[index - 1]) {
      selectedElement.set(elements[index]);
    }
  }

  function handleMoveDown(index: number) {
    if (!$bitmapModel) return;

    const elements = $bitmapModel.elements;
    if (index < 0 || index >= elements.length - 1) return;

    // 交换元素位置
    [elements[index], elements[index + 1]] = [elements[index + 1], elements[index]];

    // 更新选中状态
    if ($selectedElement === elements[index]) {
      selectedElement.set(elements[index + 1]);
    } else if ($selectedElement === elements[index + 1]) {
      selectedElement.set(elements[index]);
    }
  }

  // 🔧 处理复制元素，直接操作 bitmapModel
  function handleCopy(element: any) {
    if (!bitmapModel) return;

    // 🔧 手动深拷贝元素，避免共享引用导致裁切信息混乱
    const newElement: any = {
      type: element.type,
      id: generateId()
    };

    console.log('🔧 复制元素 - 原始元素:', element);

    // 根据元素类型进行深拷贝
    if (element.type === 'text') {
      // 文本元素的深拷贝
      newElement.text = element.text;
      newElement.x = element.x + 20;  // 调整位置避免重叠
      newElement.y = element.y + 20;
      newElement.maxWidth = element.maxWidth;
      newElement.lineHeight = element.lineHeight;
      newElement.align = element.align;
    } else if (element.type === 'image') {
      // 图像元素的深拷贝
      newElement.dx = element.dx + 20;  // 调整位置避免重叠
      newElement.dy = element.dy + 20;
      newElement.dw = element.dw;
      newElement.dh = element.dh;
      newElement.sx = element.sx;
      newElement.sy = element.sy;
      newElement.sw = element.sw;
      newElement.sh = element.sh;

      // 🔧 source 对象保持引用（因为是 Bitmap 对象，不能深拷贝）
      newElement.source = element.source;

      console.log('🔧 复制图像元素 - 裁切信息:', {
        原始: { sx: element.sx, sy: element.sy, sw: element.sw, sh: element.sh },
        新的: { sx: newElement.sx, sy: newElement.sy, sw: newElement.sw, sh: newElement.sh }
      });
    }

    // 直接添加到 elements 数组
    $bitmapModel.elements.push(newElement);
    console.log('🔧 复制完成 - 新元素:', newElement);
  }
</script>

<div class="elements-list-panel">
  <div class="panel-header">
    <h3>元素列表</h3>
    <div class="add-buttons">
      <button class="add-btn text-btn" onclick={handleAddText} title="添加文本">
        📝 文本
      </button>
      <button class="add-btn image-btn" onclick={handleAddImage} title="添加图片">
        🖼️ 图片
      </button>
    </div>
  </div>

  <div class="elements-container">
    {#if !$bitmapModel || $bitmapModel.elements.length === 0}
      <div class="empty-state">
        <p>暂无元素</p>
        <p class="hint">点击上方按钮添加文本或图片元素 bitmapModel.elements.length: {$bitmapModel?.elements.length}</p>
      </div>
    {:else}
      {#each $bitmapModel.elements as element, index (index)}
        {@const isSelected = $selectedElement === element}
        <ElementItem
          {element}
          {index}
          totalElements={$bitmapModel.elements.length}
          {isSelected}
          onSelect={handleSelect}
          onRemove={handleRemove}
          onMoveUp={handleMoveUp}
          onMoveDown={handleMoveDown}
          onCopy={handleCopy}
        />
      {/each}
    {/if}
  </div>
</div>

<style>
  .elements-list-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-surface, #3a2f2f);
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 6px;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    background: var(--theme-surface-light, #f9fafb);
  }

  .panel-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--theme-text, #111827);
  }

  .add-buttons {
    display: flex;
    gap: 8px;
  }

  .add-btn {
    padding: 6px 12px;
    font-size: 12px;
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 4px;
    background: var(--theme-surface, #ffffff);
    cursor: pointer;
    transition: all 0.15s ease;
  }

  .add-btn:hover {
    background: var(--theme-surface-hover, #f3f4f6);
    border-color: var(--theme-border-hover, #d1d5db);
  }

  .text-btn:hover {
    background: var(--theme-success-light, #f0fdf4);
    border-color: var(--theme-success, #22c55e);
    color: var(--theme-success, #22c55e);
  }

  .image-btn:hover {
    background: var(--theme-info-light, #eff6ff);
    border-color: var(--theme-info, #3b82f6);
    color: var(--theme-info, #3b82f6);
  }

  .elements-container {
    flex: 1;
    padding: 8px;
    overflow-y: auto;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--theme-text-secondary, #6b7280);
    text-align: center;
  }

  .empty-state p {
    margin: 4px 0;
  }

  .hint {
    font-size: 12px;
    color: var(--theme-text-tertiary, #9ca3af);
  }
</style>
