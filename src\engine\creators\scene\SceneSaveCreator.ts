/**
 * Scene_Save 创建器
 * 专门用于创建保存场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';
import { type FileSceneOptions } from './SceneFileCreator';

/**
 * 保存场景创建选项
 */
export interface SaveSceneOptions extends FileSceneOptions {
    /** 是否自动保存到第一个槽位 */
    autoSaveToFirstSlot?: boolean;
}

/**
 * 创建保存场景
 * @param options 创建选项
 * @returns 创建的保存场景实例
 */
export async function createSceneSave(options: SaveSceneOptions = {}): Promise<any> {
    console.log('=== 创建保存场景 Scene_Save ===');
    
    try {
        // 预加载保存场景资源
        BaseSceneCreator.preloadSceneResources('Scene_Save');
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Save', defaultOptions);
        
        // Scene_Save 特定的设置
        if (options.autoSaveToFirstSlot && scene._listWindow) {
            scene._listWindow.select(1); // 选择第一个保存槽位（索引1，因为0通常是自动保存）
            console.log('设置自动保存到第一个槽位');
        }
        
        console.log('Scene_Save 创建完成');
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Save 失败:', error);
        throw error;
    }
}

/**
 * 创建并启动保存场景
 * @param options 创建选项
 * @returns 创建的保存场景实例
 */
export async function createAndStartSceneSave(options: SaveSceneOptions = {}): Promise<any> {
    return createSceneSave({ ...options, autoStart: true });
}

/**
 * 创建简单的保存场景（用于测试）
 * @returns 创建的保存场景实例
 */
export async function createSimpleSceneSave(): Promise<any> {
    return createSceneSave({
        initialFileIndex: 1,
        mode: 'save',
        backgroundType: 1,
        autoStart: false,
        addToStage: true
    });
}
