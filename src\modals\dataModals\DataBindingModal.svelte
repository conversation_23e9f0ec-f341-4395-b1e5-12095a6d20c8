<script lang="ts">
  import Modal from '../Modal.svelte';
  import { getDataObjects, getListDataSources, staticDataObjects, type DataObject, type DataField } from './commonExpressions';
  import SafeInput from '../../components/SafeInput.svelte';

  let {
    isOpen = $bindable(false),
    onConfirm = () => {},
    mode = 'field' // 'field' 或 'list'
  }: {
    isOpen: boolean;
    onConfirm: (expression: string) => void;
    mode?: 'field' | 'list';
  } = $props();

  let selectedExpression = $state('');

  // 获取数据对象，如果失败则使用静态数据
  let dataObjects = $state<DataObject[]>(staticDataObjects);
  let selectedObject = $state<DataObject | null>(staticDataObjects[0] || null);

  // 初始化数据对象
  function initializeData() {
    try {
      let objects: DataObject[];

      if (mode === 'list') {
        // 列表模式：获取列表数据源
        objects = getListDataSources();
      } else {
        // 字段模式：获取字段数据源
        objects = getDataObjects();
      }

      if (objects.length > 0) {
        dataObjects = objects;
        selectedObject = objects[0];
      }
    } catch (error) {
      console.warn('Failed to get dynamic data objects, using static ones:', error);
      dataObjects = staticDataObjects;
      selectedObject = staticDataObjects[0] || null;
    }
  }

  // 监听模态框打开状态
  $effect(() => {
    if (isOpen) {
      initializeData();
    }
  });

  function selectObject(obj: DataObject) {
    selectedObject = obj;
  }

  function selectField(field: DataField) {
    selectedExpression = field.value;
  }

  function confirm() {
    if (selectedExpression) {
      onConfirm(selectedExpression);
      resetModal();
    }
  }

  function cancel() {
    resetModal();
  }

  function resetModal() {
    isOpen = false;
    selectedExpression = '';
    selectedObject = dataObjects[0] || null;
  }
</script>

<Modal bind:isOpen>
  {#snippet children()}
    <div class="data-modal">
      <h3>{mode === 'list' ? '选择列表数据源' : '选择数据绑定'}</h3>

      <div class="data-browser">
        <!-- 左侧：对象列表 -->
        <div class="object-list">
          <h4>数据对象</h4>
          {#each dataObjects as obj}
            <button
              class="object-item"
              class:selected={selectedObject === obj}
              onclick={() => selectObject(obj)}
            >
              <span class="object-name">{obj.name}</span>
              <span class="object-label">{obj.label}</span>
            </button>
          {/each}
        </div>

        <!-- 右侧：字段列表 -->
        <div class="field-list">
          <h4>字段</h4>
          {#if selectedObject}
            {#each selectedObject.fields as field}
              <button
                class="field-item"
                class:selected={selectedExpression === field.value}
                onclick={() => selectField(field)}
              >
                <span class="field-name">{field.name}</span>
                <span class="field-label">{field.label}</span>
                <code class="field-value">{field.value}</code>
              </button>
            {/each}
          {:else}
            <div class="no-selection">请选择一个数据对象</div>
          {/if}
        </div>
      </div>

      <div class="custom-section">
        <label>自定义表达式:</label>
        <SafeInput
          bind:value={selectedExpression}
          placeholder={mode === 'list' ? 'window.$dataItems.slice(1).filter(item => item)' : 'window.$gameParty.gold()'}
          class="custom-input"
        />
      </div>

      <div class="actions">
        <button onclick={cancel}>取消</button>
        <button onclick={confirm} disabled={!selectedExpression}>确认</button>
      </div>
    </div>
  {/snippet}
</Modal>

<style>
  .data-modal {
    width: 700px;
    max-height: 600px;
    padding: 20px;
  }

  .data-browser {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    height: 400px;
  }

  .object-list, .field-list {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    overflow-y: auto;
  }

  .object-list h4, .field-list h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
  }

  .object-item, .field-item {
    display: block;
    width: 100%;
    padding: 8px;
    margin-bottom: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    text-align: left;
  }

  .object-item:hover, .field-item:hover {
    background: #f5f5f5;
  }

  .object-item.selected, .field-item.selected {
    background: #e3f2fd;
    border-color: #2196f3;
  }

  .object-name, .field-name {
    display: block;
    font-size: 12px;
    font-weight: bold;
    color: #333;
  }

  .object-label, .field-label {
    display: block;
    font-size: 11px;
    color: #666;
    margin-top: 2px;
  }

  .field-value {
    display: block;
    font-size: 10px;
    color: #888;
    font-family: monospace;
    margin-top: 4px;
    background: #f8f8f8;
    padding: 2px 4px;
    border-radius: 2px;
  }

  .no-selection {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 20px;
  }

  .custom-section {
    margin-bottom: 16px;
  }

  .custom-section label {
    display: block;
    margin-bottom: 4px;
    font-size: 12px;
  }

  :global(.custom-input) {
    width: 100%;
    font-family: monospace;
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  .actions button {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
  }

  .actions button:last-child {
    background: #2196f3;
    color: white;
    border-color: #2196f3;
  }

  .actions button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
</style>
