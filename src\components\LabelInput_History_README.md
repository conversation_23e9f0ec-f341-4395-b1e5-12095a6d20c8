# LabelInput 历史记录功能

LabelInput组件现在支持精确的拖拽历史记录控制，解决了拖拽过程中产生多条历史记录的问题。

## 🎯 解决的问题

### **之前的问题**
```
用户拖拽数值输入框 → 产生多条历史记录
撤销时需要按多次 Ctrl+Z → 用户体验差
```

### **现在的解决方案**
```
用户拖拽数值输入框 → 只产生一条历史记录
撤销时只需按一次 Ctrl+Z → 完美的用户体验
```

## 🔧 新增的Props

```typescript
interface LabelInputProps {
  // ... 原有props

  // 历史记录相关
  targetObject?: any;        // 对应的模型对象
  fieldName?: string;        // 对应的字段名
  enableHistory?: boolean;   // 是否启用历史记录（默认true）
}
```

## 📝 使用方法

### **1. 基本用法（推荐）**
```svelte
<script>
  import LabelInput from './LabelInput.svelte';

  let myObject = {
    className: 'MyObject',
    x: 100
  };

  function handleChange(newValue) {
    myObject.x = newValue;
  }
</script>

<LabelInput
  bind:value={myObject.x}
  type="number"
  onChange={handleChange}
  targetObject={myObject}
  fieldName="x"
  enableHistory={true}
  name="x坐标"
/>
```

### **2. 不指定目标对象（会创建虚拟对象）**
```svelte
<LabelInput
  bind:value={someValue}
  type="number"
  onChange={handleChange}
  fieldName="value"
  enableHistory={true}
  name="测试值"
/>
```

### **3. 禁用历史记录**
```svelte
<LabelInput
  bind:value={someValue}
  type="number"
  onChange={handleChange}
  enableHistory={false}
/>
```

## 🔍 工作原理

### **🖱️ 拖拽模式**

#### **拖拽开始时**
1. 检查是否启用历史记录
2. 开始历史记录操作组
3. 记录初始值

#### **拖拽过程中**
1. 正常更新数值
2. 不产生历史记录（延迟到结束时统一记录）

#### **拖拽结束时**
1. 比较初始值和最终值
2. 如果值发生变化，记录一条历史记录
3. 结束操作组

### **⌨️ 输入模式**

#### **进入编辑模式时**
1. 双击标签进入编辑模式
2. 记录初始值

#### **编辑过程中**
1. 正常输入数值
2. 不产生历史记录

#### **退出编辑模式时**
1. **Enter确认**：比较初始值和最终值，如果变化则记录历史
2. **Escape取消**：恢复初始值，不记录历史
3. **失焦退出**：比较初始值和最终值，如果变化则记录历史

## 🧪 测试

使用测试组件验证功能：

```svelte
<script>
  import LabelInputHistoryTest from './LabelInputHistoryTest.svelte';
</script>

<LabelInputHistoryTest />
```

### **测试步骤**

#### **🖱️ 拖拽模式测试**
1. 拖拽数值输入框改变值
2. 按 Ctrl+Z 撤销
3. 按 Ctrl+Y 重做
4. 验证每次拖拽只产生一条历史记录

#### **⌨️ 输入模式测试**
1. 双击数值输入框进入编辑模式
2. 输入新的数值
3. 按 Enter 确认（或点击其他地方失焦）
4. 按 Ctrl+Z 撤销
5. 验证输入操作只产生一条历史记录

#### **🚫 取消操作测试**
1. 双击数值输入框进入编辑模式
2. 输入新的数值
3. 按 Escape 取消
4. 验证值恢复到原来的值，且不产生历史记录

## 📊 调试信息

组件会在控制台输出详细的调试信息：

```
📝 HistoryManager: 开始操作组: 拖拽调整x坐标
📝 HistoryManager: 已记录模型对象变更
📝 HistoryManager: 操作组已结束
```

## ⚠️ 注意事项

1. **targetObject**: 建议总是提供，这样历史记录会直接关联到模型对象
2. **fieldName**: 应该与模型对象的实际字段名一致
3. **错误处理**: 组件有完整的错误处理，确保历史记录操作正确结束
4. **性能**: 拖拽过程中不会产生历史记录，性能优秀

## 🔄 与其他组件的集成

这个模式可以应用到其他拖拽组件：

```typescript
// 拖拽开始
if (enableHistory) {
  historyManager.startGroup('操作名称');
  historyManager.pauseRecording();
}

// 拖拽结束
if (enableHistory && initialValue !== finalValue) {
  historyManager.recordChange(targetObject, fieldName, initialValue, finalValue);
  historyManager.endGroup();
  historyManager.resumeRecording();
}
```

## 🎉 优势总结

- ✅ **精确控制**: 每次拖拽只产生一条历史记录
- ✅ **用户友好**: 撤销行为符合用户期望
- ✅ **性能优秀**: 拖拽过程中不产生历史记录
- ✅ **错误安全**: 完整的错误处理机制
- ✅ **易于使用**: 简单的API，向后兼容
- ✅ **调试友好**: 详细的日志输出
