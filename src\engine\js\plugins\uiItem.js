/**
 * UIItem - 列表项模板组件
 * 支持数据绑定和子组件管理
 */

(() => {
    'use strict';

    // 确保 PIXI 可用
    if (typeof PIXI === 'undefined') {
        console.error('UIItem: PIXI 未找到');
        return;
    }

    /**
     * UIItem - 列表项模板组件
     */
    class UIItem extends PIXI.Container {
        constructor(properties = {}) {
            super();

            console.log('📄 UIItem: 创建列表项组件', properties);

            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'UIItem';

            // 基础属性
            this.width = properties.width || 180;
            this.height = properties.height || 40;
            this.enabled = properties.enabled !== false;
            this.name = properties.name || ''; // 🔑 添加name属性

            // 数据绑定映射 - 子组件 → 字段名映射
            this.dataBindings = new Map();
            this.currentData = null;

            // 🔑 UI绑定项状态（用于属性面板显示）
            this.bindingItems = properties.bindingItems || [];

            // 事件代码
            this._eventCodes = {
                onClick: properties._eventCodes?.onClick || '',
                onDoubleClick: properties._eventCodes?.onDoubleClick || '',
                onHover: properties._eventCodes?.onHover || ''
            };

            // 🔑 设置事件监听器（参考UIButton实现）
            this.setupEventListeners();

            console.log('✅ UIItem: 列表项组件创建完成');
        }

        /**
         * 设置事件监听器（参考UIButton实现）
         */
        setupEventListeners() {
            console.log('🖱️ UIItem: 设置事件监听器');

            // 使PIXI对象可交互
            this.interactive = true;
            this.interactiveChildren = true;

            // 初始化状态
            this._wasPressed = false;
            this._wasHovered = false;

            // 双击检测相关
            this._doubleClickCount = 0;
            this._doubleClickTimer = null;

            // 🔑 参考UIButton：重写update方法来处理触摸事件
            if (!this._originalUpdate) {
                this._originalUpdate = this.update;
            }

            // 重写 update 方法来处理触摸事件
            this.update = function() {
                // 调用原始的 update 方法
                if (this._originalUpdate) {
                    this._originalUpdate.call(this);
                }

                // 处理触摸事件
                this.processItemTouch();
            };

            console.log('✅ UIItem: 事件监听器设置完成');
        }

        /**
         * 处理UIItem的触摸事件（参考UIButton实现）
         */
        processItemTouch() {
            if (!this.enabled || typeof TouchInput === 'undefined') return;

            // 检查是否在UIItem区域内
            const isInside = this.isTouchedInsideFrame();

            // 处理悬停状态
            if (isInside && !this._wasHovered) {
                this._wasHovered = true;
                this.onItemHover();
            } else if (!isInside && this._wasHovered) {
                this._wasHovered = false;
                this.onItemHoverOut();
            }

            // 处理点击事件
            if (isInside && TouchInput.isPressed() && !this._wasPressed) {
                this._wasPressed = true;
                this.onItemPress();
            } else if (this._wasPressed && !TouchInput.isPressed()) {
                this._wasPressed = false;
                if (isInside) {
                    this.handleItemClick();
                }
                this.onItemRelease();
            }
        }

        /**
         * 检查鼠标是否在UIItem区域内
         */
        isTouchedInsideFrame() {
            if (typeof TouchInput === 'undefined') return false;

            // 获取鼠标在世界坐标中的位置
            const touchPos = { x: TouchInput.x, y: TouchInput.y };

            // 转换为UIItem的本地坐标
            const localPos = this.worldTransform.applyInverse(touchPos);

            // 检查是否在UIItem的边界内
            return localPos.x >= 0 && localPos.x <= this.width &&
                   localPos.y >= 0 && localPos.y <= this.height;
        }

        /**
         * UIItem悬停事件
         */
        onItemHover() {
            console.log('🔄 UIItem: 鼠标进入');
            this.executeEvent('onHover');
        }

        /**
         * UIItem离开事件
         */
        onItemHoverOut() {
            console.log('🔄 UIItem: 鼠标离开');
        }

        /**
         * UIItem按下事件
         */
        onItemPress() {
            console.log('👇 UIItem: 按下');
        }

        /**
         * UIItem释放事件
         */
        onItemRelease() {
            console.log('👆 UIItem: 释放');
        }

        /**
         * 处理UIItem点击事件（包括双击检测）
         */
        handleItemClick() {
            console.log('🎯 UIItem: handleItemClick 被调用');

            // 检查是否有双击事件代码
            const hasDoubleClickEvent = this._eventCodes['onDoubleClick'] &&
                                      this._eventCodes['onDoubleClick'].trim() !== '';

            if (hasDoubleClickEvent) {
                // 有双击事件，需要检测双击
                this._doubleClickCount++;

                if (this._doubleClickCount === 1) {
                    // 第一次点击，设置定时器
                    this._doubleClickTimer = setTimeout(() => {
                        this._doubleClickCount = 0;
                        // 单击事件
                        this.executeEvent('onClick');
                    }, 300); // 300ms 内的第二次点击算作双击
                } else if (this._doubleClickCount === 2) {
                    // 第二次点击，清除定时器并执行双击事件
                    clearTimeout(this._doubleClickTimer);
                    this._doubleClickCount = 0;
                    this.executeEvent('onDoubleClick');
                    return; // 不执行单击事件
                }
            } else {
                // 没有双击事件，直接执行单击
                this.executeEvent('onClick');
            }
        }

        /**
         * 执行事件（参考UIButton实现）
         */
        executeEvent(eventType) {
            console.log('🎯 UIItem: executeEvent被调用', eventType);

            // 🔑 编辑器模式检查
            if (window.EDITOR_MODE === true) {
                console.log('🚫 UIItem: 编辑器模式，跳过事件执行', eventType);
                return;
            }

            // 🔑 确保 _eventCodes 对象存在
            if (!this._eventCodes) {
                this._eventCodes = {};
            }

            // 获取事件代码
            const eventCode = this._eventCodes && this._eventCodes[eventType];

            if (!eventCode || typeof eventCode !== 'string') {
                console.log('❌ UIItem: 没有找到事件代码');
                return;
            }

            console.log('⚡ UIItem: 执行事件', eventType, eventCode);

            try {
                const eventFunction = new Function(eventCode);
                eventFunction.call(this);
            } catch (error) {
                console.error(`UIItem ${eventType} 事件执行失败:`, error);
            }
        }

        /**
         * 添加数据绑定
         * @param {Object} childComponent 子组件对象
         * @param {string} fieldName 要绑定的字段名
         */
        addDataBinding(childComponent, fieldName) {
            console.log('🔗 UIItem: 添加数据绑定', {
                component: childComponent.uiComponentType || childComponent.constructor.name,
                field: fieldName
            });

            this.dataBindings.set(childComponent, fieldName);
        }

        /**
         * 移除数据绑定
         * @param {Object} childComponent 子组件对象
         */
        removeDataBinding(childComponent) {
            console.log('🔗 UIItem: 移除数据绑定', {
                component: childComponent.uiComponentType || childComponent.constructor.name
            });

            this.dataBindings.delete(childComponent);
        }

        /**
         * 绑定数据对象
         * @param {Object} dataObject 数据对象
         */
        bindData(dataObject) {
            console.log('📊 UIItem: 绑定数据对象', dataObject);

            this.currentData = dataObject;

            // 遍历所有绑定，更新子组件
            this.dataBindings.forEach((fieldName, childComponent) => {
                const value = this.getFieldValue(dataObject, fieldName);
                this.updateChildComponent(childComponent, value);
            });
        }

        /**
         * 获取字段值（支持嵌套字段和简单表达式）
         * @param {Object} dataObject 数据对象
         * @param {string} fieldName 字段名（支持 "a.b.c" 格式和 "field || 'default'" 表达式）
         * @returns {any} 字段值
         */
        getFieldValue(dataObject, fieldName) {
            if (!dataObject || !fieldName) return null;

            console.log('🔍 UIItem: 获取字段值', { fieldName, dataObject });

            // 🔑 支持简单的 || 表达式（如 "title || '空存档'"）
            if (fieldName.includes('||')) {
                const parts = fieldName.split('||').map(part => part.trim());
                const actualFieldName = parts[0];
                const defaultValue = parts[1] ? parts[1].replace(/['"]/g, '') : null; // 移除引号

                console.log('🔍 UIItem: 解析表达式', { actualFieldName, defaultValue });

                // 获取实际字段值
                const actualValue = this.getSimpleFieldValue(dataObject, actualFieldName);
                const result = actualValue || defaultValue;

                console.log('🔍 UIItem: 表达式结果', { actualValue, result });
                return result;
            }

            // 普通字段访问
            return this.getSimpleFieldValue(dataObject, fieldName);
        }

        /**
         * 获取简单字段值（支持嵌套字段）
         * @param {Object} dataObject 数据对象
         * @param {string} fieldName 字段名（支持 "a.b.c" 格式）
         * @returns {any} 字段值
         */
        getSimpleFieldValue(dataObject, fieldName) {
            if (!dataObject || !fieldName) return null;

            // 支持嵌套字段访问
            const fields = fieldName.split('.');
            let value = dataObject;

            for (const field of fields) {
                if (value && typeof value === 'object' && field in value) {
                    value = value[field];
                } else {
                    return null;
                }
            }

            return value;
        }

        /**
         * 更新子组件的值
         * @param {Object} childComponent 子组件
         * @param {any} value 新值
         */
        updateChildComponent(childComponent, value) {
            if (!childComponent) return;

            const componentType = childComponent.uiComponentType || childComponent.constructor.name;

            try {
                switch (componentType) {
                    case 'UILabel':
                        if (typeof childComponent.setText === 'function') {
                            childComponent.setText(String(value || ''));
                        } else if (childComponent.text !== undefined) {
                            childComponent.text = String(value || '');
                        }
                        break;

                    case 'UIImage':
                        if (typeof childComponent.setImagePath === 'function') {
                            childComponent.setImagePath(String(value || ''));
                        } else if (childComponent.imagePath !== undefined) {
                            childComponent.imagePath = String(value || '');
                        }
                        break;

                    case 'UIButton':
                        if (typeof childComponent.setText === 'function') {
                            childComponent.setText(String(value || ''));
                        }
                        break;

                    case 'UISlider':
                        if (typeof value === 'number' && typeof childComponent.setValue === 'function') {
                            childComponent.setValue(value);
                        } else if (childComponent.value !== undefined) {
                            childComponent.value = Number(value) || 0;
                        }
                        break;

                    case 'UISwitch':
                        if (typeof value === 'boolean' && typeof childComponent.setOn === 'function') {
                            childComponent.setOn(value);
                        } else if (childComponent.isOn !== undefined) {
                            childComponent.isOn = Boolean(value);
                        }
                        break;

                    default:
                        console.warn('UIItem: 未知的组件类型', componentType);
                        break;
                }

                console.log('📊 UIItem: 更新组件值', {
                    component: componentType,
                    value: value
                });

            } catch (error) {
                console.error('UIItem: 更新组件值失败', error);
            }
        }

        /**
         * 获取所有数据绑定
         * @returns {Map} 数据绑定映射
         */
        getDataBindings() {
            return new Map(this.dataBindings);
        }

        /**
         * 清除所有数据绑定
         */
        clearDataBindings() {
            console.log('🔗 UIItem: 清除所有数据绑定');
            this.dataBindings.clear();
        }

        /**
         * 克隆当前 UIItem 对象
         * @param {Object} options 克隆选项
         * @returns {UIItem} 克隆的 UIItem 对象
         */
        clone(options = {}) {
            console.log('🔄 UIItem: 开始克隆对象', {
                originalChildren: this.children.length,
                originalBindings: this.dataBindings.size,
                originalCurrentData: this.currentData,
                originalBindingItems: this.bindingItems?.length || 0
            });

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = {
                width: this.width,
                height: this.height,
                enabled: this.enabled,
                _eventCodes: { ...this._eventCodes }
            };

            // 2. 创建克隆对象
            const clonedItem = new UIItem(cloneProperties);

            // 🔑 复制关键数据
            clonedItem.name = this.name || '';
            clonedItem.currentData = this.currentData; // 复制当前数据
            clonedItem.bindingItems = this.bindingItems ? [...this.bindingItems] : []; // 复制绑定项状态

            // 3. 设置位置和变换属性
            clonedItem.x = this.x + (offsetPosition ? offsetX : 0);
            clonedItem.y = this.y + (offsetPosition ? offsetY : 0);
            clonedItem.scale.x = this.scale.x;
            clonedItem.scale.y = this.scale.y;
            clonedItem.rotation = this.rotation;
            clonedItem.alpha = this.alpha;
            clonedItem.zIndex = this.zIndex;

            // 4. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false });
                    clonedItem.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                }
            }

            // 5. 重建数据绑定关系
            this.rebuildDataBindingsForClone(clonedItem, clonedChildren);

            // 🔍 验证克隆结果
            console.log('✅ UIItem: 克隆完成', {
                clonedChildren: clonedChildren.length,
                clonedBindings: clonedItem.dataBindings.size,
                clonedCurrentData: clonedItem.currentData,
                clonedBindingItems: clonedItem.bindingItems?.length || 0,
                clonedName: clonedItem.name
            });

            return clonedItem;
        }

        /**
         * 为克隆对象重建数据绑定关系
         * @param {UIItem} clonedItem 克隆的 UIItem 对象
         * @param {Array} clonedChildren 克隆的子对象数组
         */
        rebuildDataBindingsForClone(clonedItem, clonedChildren) {
            console.log('🔗 UIItem: 重建克隆对象的数据绑定关系');

            // 找到原始绑定对象在子对象数组中的索引
            const findChildIndex = (boundObject) => {
                if (!boundObject) return -1;
                for (let i = 0; i < this.children.length; i++) {
                    if (this.children[i] === boundObject) {
                        return i;
                    }
                }
                return -1;
            };

            // 重建所有数据绑定
            this.dataBindings.forEach((fieldName, originalChild) => {
                const index = findChildIndex(originalChild);
                if (index >= 0 && index < clonedChildren.length) {
                    clonedItem.addDataBinding(clonedChildren[index], fieldName);
                    console.log('🔗 重新绑定数据字段:', fieldName);
                }
            });
        }

        /**
         * 销毁组件
         */
        destroy() {
            this.clearDataBindings();
            super.destroy();
            console.log('💥 UIItem: 组件已销毁');
        }
    }

    // 导出到全局
    window.UIItem = UIItem;

    console.log('✅ UIItem 插件加载完成');
})();
