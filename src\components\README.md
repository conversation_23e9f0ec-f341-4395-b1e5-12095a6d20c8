# 统一组件样式系统

## 概述

为了确保所有组件的样式一致性，我们创建了一个统一的样式系统，以 `SafeInput` 组件为标准。

## 核心文件

- `shared-styles.css` - 统一样式定义文件

## 设计标准

### 字体规范
- **字体大小**: 11px
- **字体族**: inherit
- **行高**: 1.2

### 边框规范
- **边框**: 1px solid var(--theme-border, #e2e8f0)
- **圆角**: 4px
- **焦点边框**: var(--theme-primary, #3b82f6)

### 内边距规范
- **默认**: 4px 8px
- **小尺寸**: 2px 6px
- **大尺寸**: 6px 12px

### 颜色规范
- **背景色**: var(--theme-surface, #ffffff)
- **文字色**: var(--theme-text, #1a202c)
- **禁用背景**: var(--theme-surface-disabled, #f5f5f5)
- **禁用文字**: var(--theme-text-disabled, #999)

## 基础样式类

### 输入框样式
```css
.component-input-base
```

### 按钮样式
```css
.component-button-base
```

### 选择框样式
```css
.component-select-base
```

### 下拉框样式
```css
.component-dropdown-base
.component-dropdown-options
.component-dropdown-option
```

### 复选框样式
```css
.component-checkbox-base
.component-checkbox-input
```

## 尺寸变体

### 小尺寸
```css
.component-sm
```

### 大尺寸
```css
.component-lg
```

## 状态变体

### 错误状态
```css
.component-error
```

### 成功状态
```css
.component-success
```

### 警告状态
```css
.component-warning
```

## 工具类

### 全宽
```css
.component-full-width
```

### 无边框
```css
.component-no-border
```

### 无内边距
```css
.component-no-padding
```

### 文本对齐
```css
.component-text-center
.component-text-right
```

## 使用方法

### 1. 导入统一样式

在组件的 `<style>` 标签中导入：

```svelte
<style>
  @import './shared-styles.css';

  /* 组件特定样式 */
  .custom-style {
    /* ... */
  }
</style>
```

### 2. 使用基础样式类

```svelte
<!-- 输入框 -->
<input class="component-input-base" />

<!-- 按钮 -->
<button class="component-button-base">按钮</button>

<!-- 选择框触发器 -->
<div class="component-select-base">
  <span>选择项</span>
</div>

<!-- 复选框 -->
<label class="component-checkbox-base">
  <input type="checkbox" class="component-checkbox-input" />
  <span>标签</span>
</label>
```

### 3. 添加尺寸和状态变体

```svelte
<!-- 小尺寸输入框 -->
<input class="component-input-base component-sm" />

<!-- 错误状态按钮 -->
<button class="component-button-base component-error">错误</button>

<!-- 全宽选择框 -->
<div class="component-select-base component-full-width">
  <span>全宽选择</span>
</div>
```

## 已更新的组件

- ✅ `SafeInput.svelte` - 使用 `component-input-base`
- ✅ `Select.svelte` - 使用统一样式变量和类
- ✅ `Checkbox.svelte` - 使用 `component-checkbox-base`
- ✅ `Button.svelte` - 使用 `component-button-base` 和统一样式变量
- ✅ `Switch.svelte` - 使用统一样式变量和尺寸类
- ✅ `Radio.svelte` - 使用统一样式变量和尺寸类
- ✅ `Slider.svelte` - 使用统一样式变量和尺寸类
- ✅ `LabelInput.svelte` - 使用统一样式变量

## 组件更新完成 🎉

所有主要组件都已更新为使用统一样式系统！

## 注意事项

1. **CSS变量优先级**: 统一样式使用CSS变量，确保主题一致性
2. **类名命名**: 使用 `component-` 前缀避免冲突
3. **渐进式更新**: 可以逐步将现有组件迁移到统一样式
4. **自定义样式**: 组件特定样式仍可在组件内定义，但应遵循统一的设计标准

## 示例

完整的组件示例：

```svelte
<script>
  let value = '';
  let checked = false;
</script>

<div class="form-container">
  <input
    class="component-input-base component-sm"
    bind:value
    placeholder="输入内容"
  />

  <label class="component-checkbox-base">
    <input
      type="checkbox"
      class="component-checkbox-input"
      bind:checked
    />
    <span>同意条款</span>
  </label>

  <button class="component-button-base component-success">
    提交
  </button>
</div>

<style>
  @import './shared-styles.css';

  .form-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
</style>
```
