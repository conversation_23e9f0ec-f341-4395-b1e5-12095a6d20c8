<script lang="ts">
  /**
   * 拖拽源组件 - 使用自定义拖拽实现
   */
  import { dragStore } from '../../stores/dragStore';
  import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';

  interface Props {
    object: BaseObjectModel;
    children?: any;
    ondragstart?: (object: BaseObjectModel) => void;
  }

  let { object, children, ondragstart }: Props = $props();

  let dragElement: HTMLDivElement;
  let isDragging = $state(false);
  let dragPreview: HTMLDivElement | null = null;
  let isMouseDown = $state(false);
  let dragStarted = $state(false);
  let startPosition = { x: 0, y: 0 };

  // 拖拽阈值 - 鼠标移动超过这个距离才开始拖拽
  const DRAG_THRESHOLD = 5;

  // 创建拖拽预览元素
  function createDragPreview(sourceElement: HTMLDivElement, startX: number, startY: number) {
    // 克隆原始元素
    const preview = sourceElement.cloneNode(true) as HTMLDivElement;

    // 设置预览样式
    preview.style.position = 'fixed';
    preview.style.left = `${startX + 10}px`; // 稍微偏移避免遮挡鼠标
    preview.style.top = `${startY + 10}px`;
    preview.style.opacity = '0.7';
    preview.style.pointerEvents = 'none';
    preview.style.zIndex = '9999';
    // 移除旋转效果，保持原始方向
    preview.style.boxShadow = '0 4px 12px rgba(0,0,0,0.3)';
    preview.classList.add('drag-preview');

    // 添加到 body
    document.body.appendChild(preview);
    return preview;
  }

  // 更新拖拽预览位置
  function updateDragPreview(x: number, y: number) {
    if (dragPreview) {
      dragPreview.style.left = `${x + 10}px`;
      dragPreview.style.top = `${y + 10}px`;
    }
  }

  // 移除拖拽预览
  function removeDragPreview() {
    if (dragPreview && document.body.contains(dragPreview)) {
      try {
        document.body.removeChild(dragPreview);
      } catch (error) {
        console.warn('移除拖拽预览时出错:', error);
      }
      dragPreview = null;
    }
  }

  // 组件卸载时清理
  $effect(() => {
    return () => {
      removeDragPreview();
    };
  });

  // 鼠标按下 - 准备拖拽但不立即开始
  function handleMouseDown(event: MouseEvent) {
    // 只处理左键
    if (event.button !== 0) return;

    isMouseDown = true;
    dragStarted = false;
    startPosition = { x: event.clientX, y: event.clientY };

    console.log('🎯 鼠标按下，准备拖拽:', object.className);

    // 阻止默认行为
    event.preventDefault();

    // 鼠标移动处理
    const handleMouseMove = (e: MouseEvent) => {
      if (!isMouseDown) return;

      // 计算移动距离
      const deltaX = e.clientX - startPosition.x;
      const deltaY = e.clientY - startPosition.y;
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // 只有移动距离超过阈值才开始真正的拖拽
      if (!dragStarted && distance > DRAG_THRESHOLD) {
        startActualDrag(e);
      }

      // 如果已经开始拖拽，更新预览位置
      if (dragStarted && dragPreview) {
        updateDragPreview(e.clientX, e.clientY);
      }
    };

    // 鼠标释放处理
    const handleMouseUp = (e: MouseEvent) => {
      const wasDragging = dragStarted;

      // 重置状态
      isMouseDown = false;

      if (wasDragging) {
        console.log('🎯 拖拽结束');
        finishDrag(e);
      } else {
        console.log('🎯 点击事件（未达到拖拽阈值）');
        // 这里可以触发点击事件，但不影响原有的点击处理
      }

      // 清理事件监听器
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    // 添加全局事件监听器
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }

  // 开始真正的拖拽
  function startActualDrag(event: MouseEvent) {
    dragStarted = true;
    isDragging = true;

    console.log('🎯 开始实际拖拽:', object.className);

    dragStore.startDrag(object);
    ondragstart?.(object);

    // 现在才创建拖拽预览
    dragPreview = createDragPreview(dragElement, event.clientX, event.clientY);

    // 设置鼠标样式
    document.body.style.cursor = 'grabbing';
  }

  // 完成拖拽
  function finishDrag(event: MouseEvent) {
    isDragging = false;
    dragStarted = false;
    document.body.style.cursor = '';

    // 移除拖拽预览
    removeDragPreview();

    // 检查是否在拖拽目标上释放
    const elementBelow = document.elementFromPoint(event.clientX, event.clientY);
    const dropTarget = elementBelow?.closest('[data-drop-target]');

    if (dropTarget) {
      // 触发自定义拖拽事件
      const dropEvent = new CustomEvent('customdrop', {
        detail: { object, clientX: event.clientX, clientY: event.clientY }
      });
      dropTarget.dispatchEvent(dropEvent);
    }

    dragStore.endDrag();
  }
</script>

<div
  bind:this={dragElement}
  onmousedown={handleMouseDown}
  class="drag-source"
  class:dragging={isDragging}
  role="button"
  aria-label="可拖拽项目"
  tabindex="0"
>
  {@render children?.()}
</div>

<style>
  .drag-source {
    cursor: grab;
    user-select: none;
    transition: opacity 0.2s ease;
  }

  .drag-source:hover {
    opacity: 0.8;
  }

  .drag-source.dragging {
    opacity: 0.5;
    cursor: grabbing;
  }

  /* 全局拖拽预览样式 */
  :global(.drag-preview) {
    transition: transform 0.1s ease-out;
    border: 2px dashed #2196F3;
    background: rgba(33, 150, 243, 0.1);
  }

  :global(.drag-preview *) {
    pointer-events: none;
  }
</style>
