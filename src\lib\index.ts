/**
 * 库文件统一导出入口
 */

// 导出Tauri API
export { default as TauriAPI, WindowAPI, AppAPI, FileAPI, ProjectAPI, OperationRecordsAPI } from './tauriAPI';
export type { WindowInfo, ApiResponse, ProjectConfig, CurrentProjectInfo, PluginSaveResult } from './tauriAPI';

// // 导出主题相关
// export { default as themeManager, ThemeManager, ThemeType } from '../theme/theme';
// export type { ThemeColors } from '../theme/theme';

// // 导出组件
// export { default as ThemeProvider } from '../theme/ThemeProvider.svelte';

// 这里可以添加其他库文件的导出
// export { default as Utils } from './utils';
// export { default as Constants } from './constants';
