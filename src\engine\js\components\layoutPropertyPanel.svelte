<script>
    import { onMount, onDestroy } from 'svelte';
    import PropertyContainer from './propertyContainer.svelte';
    import Select from './select.svelte';
    import SafeInput from './safeInput.svelte';
    import Checkbox from './checkbox.svelte';

    export let layoutModel = null;

    // 布局类型选项
    const layoutTypeOptions = [
        { value: 'vertical', label: '垂直布局' },
        { value: 'horizontal', label: '水平布局' },
        { value: 'grid', label: '网格布局' }
    ];

    // 对齐方式选项
    const alignmentOptions = [
        { value: 'start', label: '开始' },
        { value: 'center', label: '居中' },
        { value: 'end', label: '结束' },
        { value: 'space-between', label: '两端对齐' },
        { value: 'space-around', label: '环绕对齐' },
        { value: 'stretch', label: '拉伸' }
    ];

    // 响应式变量
    $: layoutType = layoutModel?.layoutType || 'vertical';
    $: spacing = layoutModel?.spacing || 5;
    $: horizontalSpacing = layoutModel?.horizontalSpacing || 5;
    $: verticalSpacing = layoutModel?.verticalSpacing || 5;
    $: padding = layoutModel?.padding || 0;
    $: columns = layoutModel?.columns || 2;
    $: rows = layoutModel?.rows || 0;
    $: mainAxisAlignment = layoutModel?.mainAxisAlignment || 'start';
    $: crossAxisAlignment = layoutModel?.crossAxisAlignment || 'start';
    $: containerWidth = layoutModel?.containerWidth || 0;
    $: containerHeight = layoutModel?.containerHeight || 0;
    $: autoUpdate = layoutModel?.autoUpdate || true;

    // 是否显示网格特定选项
    $: showGridOptions = $layoutType === 'grid';
    
    // 是否显示高级间距选项
    let showAdvancedSpacing = false;

    function handleLayoutTypeChange(event) {
        if (layoutModel?.layoutType?.set) {
            layoutModel.layoutType.set(event.detail.value);
            console.log('📐 LayoutPropertyPanel: 布局类型变更为', event.detail.value);
        }
    }

    function handleSpacingChange(event) {
        const value = parseInt(event.detail.value) || 0;
        if (layoutModel?.spacing?.set) {
            layoutModel.spacing.set(value);
            console.log('📐 LayoutPropertyPanel: 间距变更为', value);
        }
    }

    function handleHorizontalSpacingChange(event) {
        const value = parseInt(event.detail.value) || 0;
        if (layoutModel?.horizontalSpacing?.set) {
            layoutModel.horizontalSpacing.set(value);
            console.log('📐 LayoutPropertyPanel: 水平间距变更为', value);
        }
    }

    function handleVerticalSpacingChange(event) {
        const value = parseInt(event.detail.value) || 0;
        if (layoutModel?.verticalSpacing?.set) {
            layoutModel.verticalSpacing.set(value);
            console.log('📐 LayoutPropertyPanel: 垂直间距变更为', value);
        }
    }

    function handlePaddingChange(event) {
        const value = parseInt(event.detail.value) || 0;
        if (layoutModel?.padding?.set) {
            layoutModel.padding.set(value);
            console.log('📐 LayoutPropertyPanel: 内边距变更为', value);
        }
    }

    function handleColumnsChange(event) {
        const value = parseInt(event.detail.value) || 1;
        if (layoutModel?.columns?.set) {
            layoutModel.columns.set(Math.max(1, value));
            console.log('📐 LayoutPropertyPanel: 网格列数变更为', value);
        }
    }

    function handleRowsChange(event) {
        const value = parseInt(event.detail.value) || 0;
        if (layoutModel?.rows?.set) {
            layoutModel.rows.set(Math.max(0, value));
            console.log('📐 LayoutPropertyPanel: 网格行数变更为', value);
        }
    }

    function handleMainAxisAlignmentChange(event) {
        if (layoutModel?.mainAxisAlignment?.set) {
            layoutModel.mainAxisAlignment.set(event.detail.value);
            console.log('📐 LayoutPropertyPanel: 主轴对齐变更为', event.detail.value);
        }
    }

    function handleCrossAxisAlignmentChange(event) {
        if (layoutModel?.crossAxisAlignment?.set) {
            layoutModel.crossAxisAlignment.set(event.detail.value);
            console.log('📐 LayoutPropertyPanel: 交叉轴对齐变更为', event.detail.value);
        }
    }

    function handleContainerWidthChange(event) {
        const value = parseInt(event.detail.value) || 0;
        if (layoutModel?.containerWidth?.set) {
            layoutModel.containerWidth.set(Math.max(0, value));
            console.log('📐 LayoutPropertyPanel: 容器宽度变更为', value);
        }
    }

    function handleContainerHeightChange(event) {
        const value = parseInt(event.detail.value) || 0;
        if (layoutModel?.containerHeight?.set) {
            layoutModel.containerHeight.set(Math.max(0, value));
            console.log('📐 LayoutPropertyPanel: 容器高度变更为', value);
        }
    }

    function handleAutoUpdateChange(event) {
        if (layoutModel?.autoUpdate?.set) {
            layoutModel.autoUpdate.set(event.detail.checked);
            console.log('📐 LayoutPropertyPanel: 自动更新变更为', event.detail.checked);
        }
    }

    function toggleAdvancedSpacing() {
        showAdvancedSpacing = !showAdvancedSpacing;
        console.log('📐 LayoutPropertyPanel: 切换高级间距选项', showAdvancedSpacing);
    }

    function resetLayout() {
        if (!layoutModel) return;
        
        console.log('📐 LayoutPropertyPanel: 重置布局设置');
        
        if (layoutModel.layoutType?.set) layoutModel.layoutType.set('vertical');
        if (layoutModel.spacing?.set) layoutModel.spacing.set(5);
        if (layoutModel.padding?.set) layoutModel.padding.set(0);
        if (layoutModel.columns?.set) layoutModel.columns.set(2);
        if (layoutModel.rows?.set) layoutModel.rows.set(0);
        if (layoutModel.mainAxisAlignment?.set) layoutModel.mainAxisAlignment.set('start');
        if (layoutModel.crossAxisAlignment?.set) layoutModel.crossAxisAlignment.set('start');
        if (layoutModel.containerWidth?.set) layoutModel.containerWidth.set(0);
        if (layoutModel.containerHeight?.set) layoutModel.containerHeight.set(0);
        if (layoutModel.autoUpdate?.set) layoutModel.autoUpdate.set(true);
    }

    onMount(() => {
        console.log('📐 LayoutPropertyPanel: 组件挂载', layoutModel?.constructor.name);
    });

    onDestroy(() => {
        console.log('📐 LayoutPropertyPanel: 组件销毁');
    });
</script>

<div class="layout-property-panel">
    <h3>布局设置</h3>
    
    {#if layoutModel}
        <!-- 基础布局设置 -->
        <PropertyContainer label="布局类型">
            <Select 
                options={layoutTypeOptions}
                value={$layoutType}
                on:change={handleLayoutTypeChange}
            />
        </PropertyContainer>

        <!-- 间距设置 -->
        <PropertyContainer label="间距">
            <SafeInput 
                type="number"
                value={$spacing}
                min="0"
                max="100"
                on:change={handleSpacingChange}
            />
            <button 
                class="toggle-advanced" 
                on:click={toggleAdvancedSpacing}
                title="切换高级间距选项"
            >
                {showAdvancedSpacing ? '简单' : '高级'}
            </button>
        </PropertyContainer>

        {#if showAdvancedSpacing}
            <PropertyContainer label="水平间距">
                <SafeInput 
                    type="number"
                    value={$horizontalSpacing}
                    min="0"
                    max="100"
                    on:change={handleHorizontalSpacingChange}
                />
            </PropertyContainer>

            <PropertyContainer label="垂直间距">
                <SafeInput 
                    type="number"
                    value={$verticalSpacing}
                    min="0"
                    max="100"
                    on:change={handleVerticalSpacingChange}
                />
            </PropertyContainer>
        {/if}

        <PropertyContainer label="内边距">
            <SafeInput 
                type="number"
                value={$padding}
                min="0"
                max="50"
                on:change={handlePaddingChange}
            />
        </PropertyContainer>

        <!-- 网格布局特定设置 -->
        {#if showGridOptions}
            <PropertyContainer label="网格列数">
                <SafeInput 
                    type="number"
                    value={$columns}
                    min="1"
                    max="10"
                    on:change={handleColumnsChange}
                />
            </PropertyContainer>

            <PropertyContainer label="网格行数">
                <SafeInput 
                    type="number"
                    value={$rows}
                    min="0"
                    max="10"
                    placeholder="0=自动"
                    on:change={handleRowsChange}
                />
            </PropertyContainer>
        {/if}

        <!-- 对齐设置 -->
        <PropertyContainer label="主轴对齐">
            <Select 
                options={alignmentOptions.filter(opt => 
                    $layoutType === 'grid' ? 
                    ['start', 'center', 'end'].includes(opt.value) : 
                    true
                )}
                value={$mainAxisAlignment}
                on:change={handleMainAxisAlignmentChange}
            />
        </PropertyContainer>

        <PropertyContainer label="交叉轴对齐">
            <Select 
                options={alignmentOptions.filter(opt => 
                    ['start', 'center', 'end', 'stretch'].includes(opt.value)
                )}
                value={$crossAxisAlignment}
                on:change={handleCrossAxisAlignmentChange}
            />
        </PropertyContainer>

        <!-- 容器尺寸设置 -->
        <PropertyContainer label="容器宽度">
            <SafeInput 
                type="number"
                value={$containerWidth}
                min="0"
                max="1000"
                placeholder="0=自动"
                on:change={handleContainerWidthChange}
            />
        </PropertyContainer>

        <PropertyContainer label="容器高度">
            <SafeInput 
                type="number"
                value={$containerHeight}
                min="0"
                max="1000"
                placeholder="0=自动"
                on:change={handleContainerHeightChange}
            />
        </PropertyContainer>

        <!-- 高级设置 -->
        <PropertyContainer label="自动更新">
            <Checkbox 
                checked={$autoUpdate}
                on:change={handleAutoUpdateChange}
            />
        </PropertyContainer>

        <!-- 操作按钮 -->
        <div class="layout-actions">
            <button class="reset-button" on:click={resetLayout}>
                重置布局
            </button>
        </div>
    {:else}
        <p class="no-model">请选择一个布局对象</p>
    {/if}
</div>

<style>
    .layout-property-panel {
        padding: 10px;
        background: #2a2a2a;
        border-radius: 4px;
        color: white;
        font-size: 12px;
    }

    h3 {
        margin: 0 0 15px 0;
        color: #ffffff;
        font-size: 14px;
        font-weight: bold;
        border-bottom: 1px solid #444;
        padding-bottom: 5px;
    }

    .toggle-advanced {
        background: #444;
        color: white;
        border: 1px solid #666;
        border-radius: 3px;
        padding: 2px 6px;
        font-size: 10px;
        cursor: pointer;
        margin-left: 5px;
    }

    .toggle-advanced:hover {
        background: #555;
    }

    .layout-actions {
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px solid #444;
    }

    .reset-button {
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 6px 12px;
        font-size: 11px;
        cursor: pointer;
        width: 100%;
    }

    .reset-button:hover {
        background: #c0392b;
    }

    .no-model {
        color: #888;
        font-style: italic;
        text-align: center;
        padding: 20px;
    }
</style>
