<script lang="ts">
  import { onMount } from 'svelte';
  import type { ImageElement } from '../../type/bitmap.svelte';

  // 🔧 Props
  interface Props {
    element?: ImageElement | null;
    onCropCompleted?: (event: CustomEvent) => void;
    onCropReset?: (event: CustomEvent) => void;
  }

  let {
    element,
    onCropCompleted,
    onCropReset
  }: Props = $props();

  // 🔧 本地状态
  let gridRows = $state(2);
  let gridCols = $state(2);
  let selectedGridX = $state(0);
  let selectedGridY = $state(0);
  let imageWidth = $state(0);
  let imageHeight = $state(0);
  let imageUrl = $state('');
  let imageLoaded = $state(false);
  let currentBitmap = $state<any>(null);
  let isImporting = $state(false);


  // 🔧 图片加载功能
  async function handleOpenFileSelector() {
    try {
      isImporting = true;

      // 使用 Tauri 对话框 API
      const { open } = await import('@tauri-apps/plugin-dialog');
      const result = await open({
        directory: false,
        multiple: false,
        title: '选择图片文件',
        filters: [
          {
            name: 'PNG Images',
            extensions: ['png']
          },
          {
            name: 'JPEG Images',
            extensions: ['jpg', 'jpeg']
          },
          {
            name: 'GIF Images',
            extensions: ['gif']
          },
          {
            name: 'BMP Images',
            extensions: ['bmp']
          },
          {
            name: 'All Images',
            extensions: ['png', 'jpg', 'jpeg', 'gif', 'bmp']
          }
        ]
      });

      if (!result || typeof result !== 'string') {
        console.log('用户取消了文件选择');
        isImporting = false;
        return;
      }

      console.log('选择的图片文件:', result);

      // 读取文件并加载图片
      await loadImageFromPath(result);

    } catch (error) {
      console.error('打开文件选择器失败:', error);
      alert('打开文件选择器失败: ' + (error instanceof Error ? error.message : String(error)));
      isImporting = false;
    }
  }

  // 从文件路径加载图片
  async function loadImageFromPath(filePath: string) {
    try {
      console.log('开始加载图片:', filePath);

      // 使用 ImageManager 加载图片
      const bitmap = await window.ImageManager.loadBitmapFromUrl(filePath);

      if (!bitmap) {
        throw new Error('图片加载失败');
      }

      // 等待图片完全加载
      let checkInterval = setInterval(() => {
        if (bitmap.isReady() && bitmap._image) {
          // 图片加载成功
          clearInterval(checkInterval);
          console.log('图片加载完成:', {
            width: bitmap.width,
            height: bitmap.height,
            url: bitmap._url,
            ready: bitmap.isReady()
          });

          // 🔧 设置图片尺寸
          imageWidth = bitmap.width;
          imageHeight = bitmap.height;
          imageLoaded = true;
          currentBitmap = bitmap;
          imageUrl = filePath;

          // 保存到 element.source
          if (element) {
            if (!element.source) {
              element.source = bitmap;
            } else {
              // 更新现有的 source
              Object.assign(element.source, bitmap);
            }
            console.log('已保存图片到 element.source');
          }

          isImporting = false;
        }
      }, 50);

      // 超时处理
      setTimeout(() => {
        if (!bitmap.isReady()) {
          clearInterval(checkInterval);
          console.error('图片加载超时');
          alert('图片加载超时，请重试');
          isImporting = false;
        }
      }, 10000);

    } catch (error) {
      console.error('加载图片失败:', error);
      alert('加载图片失败: ' + (error instanceof Error ? error.message : String(error)));
      isImporting = false;
    }
  }

  // Canvas 相关
  let canvasRef = $state() as HTMLCanvasElement;
  let displayWidth = $state(400);
  let displayHeight = $state(300);

  // 🔧 显示模式现在从 props 传入，不需要本地状态
  // showCropMode: false = canvas模式，true = 裁切图片模式







  // 🔧 生成网格数据
  function regenerateValidRegions(rows: number, cols: number) {
    if (!imageWidth || !imageHeight || rows <= 0 || cols <= 0) return [];

    const cellWidth = imageWidth / cols;
    const cellHeight = imageHeight / rows;
    const validRegions = [];

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const sx = Math.round(col * cellWidth);
        const sy = Math.round(row * cellHeight);
        const sw = Math.round(cellWidth);
        const sh = Math.round(cellHeight);

        validRegions.push({
          sx, sy, sw, sh,
          gridIndex: row * cols + col,
          label: `区域 ${col + 1}-${row + 1}`
        });
      }
    }

    return validRegions;
  }

  // 🔧 裁切按钮：生成裁切数据并切换到图片模式
  function handleCropButton() {
    console.log('🔧 点击裁切按钮，当前网格设置:', { gridRows, gridCols });

    if (!element?.source || !imageWidth || !imageHeight) {
      console.warn('⚠️ 缺少必要数据，无法生成裁切数据');
      return;
    }

    if (gridRows <= 0 || gridCols <= 0) {
      console.warn('⚠️ 网格设置无效，请先设置行数和列数');
      alert('请先设置有效的行数和列数（大于0）');
      return;
    }

    // 根据当前网格设置生成裁切数据
    const newRegions = regenerateValidRegions(gridRows, gridCols);
    element.source.regions = newRegions;

    console.log('🔧 已生成裁切数据:', {
      regions: newRegions.length,
      expectedRegions: gridRows * gridCols
    });

    // 🔧 通知父组件裁切完成，让父组件处理显示
    if (onCropCompleted) {
      const event = new CustomEvent('cropCompleted', {
        detail: { regions: newRegions }
      });
      onCropCompleted(event);
    }
  }

  // 🔧 重置按钮：删除裁切数据，回到 canvas 模式
  function handleResetButton() {
    // 删除 bitmap.regions
    if (element?.source) {
      element.source.regions = [];
      console.log('🔧 已删除 bitmap.regions');
    }

    console.log('🔧 重置完成');

    // 🔧 通知父组件重置完成
    if (onCropReset) {
      const event = new CustomEvent('cropReset', {
        detail: {}
      });
      onCropReset(event);
    }
  }

  // 🔧 Canvas 绘制功能
  function drawCanvas() {
    if (!canvasRef || !currentBitmap?._image) {
      console.log('❌ drawCanvas: 缺少必要元素', {
        canvasRef: !!canvasRef,
        currentBitmap: !!currentBitmap,
        image: !!currentBitmap?._image
      });
      return;
    }

    const ctx = canvasRef.getContext('2d');
    if (!ctx) {
      console.log('❌ drawCanvas: 无法获取 canvas context');
      return;
    }

    // 🔧 使用图片的实际尺寸或传入的尺寸
    const actualImageWidth = imageWidth || currentBitmap._image.width || currentBitmap._image.naturalWidth;
    const actualImageHeight = imageHeight || currentBitmap._image.height || currentBitmap._image.naturalHeight;

    console.log('🎨 drawCanvas: 图片尺寸', {
      传入尺寸: { imageWidth, imageHeight },
      实际尺寸: { actualImageWidth, actualImageHeight }
    });

    if (!actualImageWidth || !actualImageHeight) {
      console.log('❌ drawCanvas: 无法获取有效的图片尺寸');
      return;
    }

    // 计算显示尺寸
    const maxWidth = 400;
    const maxHeight = 300;
    const aspectRatio = actualImageWidth / actualImageHeight;

    if (aspectRatio > maxWidth / maxHeight) {
      displayWidth = maxWidth;
      displayHeight = maxWidth / aspectRatio;
    } else {
      displayHeight = maxHeight;
      displayWidth = maxHeight * aspectRatio;
    }

    console.log('🎨 drawCanvas: 计算显示尺寸', { displayWidth, displayHeight, aspectRatio });

    // 设置 canvas 尺寸
    canvasRef.width = displayWidth;
    canvasRef.height = displayHeight;
    canvasRef.style.width = displayWidth + 'px';
    canvasRef.style.height = displayHeight + 'px';

    // 清空 canvas
    ctx.clearRect(0, 0, displayWidth, displayHeight);

    // 绘制图片
    ctx.drawImage(currentBitmap._image, 0, 0, displayWidth, displayHeight);
    console.log('✅ drawCanvas: 图片绘制完成');

    // 绘制网格线
    drawGrid(ctx);

    // 绘制选中区域
    drawSelectedArea(ctx);
  }

  function drawGrid(ctx: CanvasRenderingContext2D) {
    // 🔧 检查是否有 regions 数据，如果没有则不绘制网格线
    const bitmapRegions = element?.source?.regions;
    if (!bitmapRegions || bitmapRegions.length === 0) {
      console.log('🔧 没有 regions 数据，不绘制网格线');
      return;
    }

    // 🔧 检查网格设置，如果是 1x1 且没有有效 regions，不绘制网格线
    if (gridRows <= 1 && gridCols <= 1) {
      console.log('🔧 网格设置为 1x1，不绘制网格线');
      return;
    }

    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 1;

    const cellWidth = displayWidth / gridCols;
    const cellHeight = displayHeight / gridRows;

    console.log('🎨 绘制网格线:', { gridRows, gridCols, cellWidth, cellHeight });

    // 绘制垂直线
    for (let i = 1; i < gridCols; i++) {
      const x = i * cellWidth;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, displayHeight);
      ctx.stroke();
    }

    // 绘制水平线
    for (let i = 1; i < gridRows; i++) {
      const y = i * cellHeight;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(displayWidth, y);
      ctx.stroke();
    }
  }

  function drawSelectedArea(ctx: CanvasRenderingContext2D) {
    // 🔧 检查是否有 regions 数据，如果没有则不绘制选中区域
    const bitmapRegions = element?.source?.regions;
    if (!bitmapRegions || bitmapRegions.length === 0) {
      console.log('🔧 没有 regions 数据，不绘制选中区域');
      return;
    }

    // 🔧 检查网格设置，如果是 1x1 且没有有效 regions，不绘制选中区域
    if (gridRows <= 1 && gridCols <= 1) {
      console.log('🔧 网格设置为 1x1，不绘制选中区域');
      return;
    }

    const cellWidth = displayWidth / gridCols;
    const cellHeight = displayHeight / gridRows;

    const x = selectedGridX * cellWidth;
    const y = selectedGridY * cellHeight;

    console.log('🎨 绘制选中区域:', { selectedGridX, selectedGridY, x, y, cellWidth, cellHeight });

    // 绘制选中区域的边框
    ctx.strokeStyle = '#ff0000';
    ctx.lineWidth = 2;
    ctx.strokeRect(x, y, cellWidth, cellHeight);

    // 绘制半透明覆盖
    ctx.fillStyle = 'rgba(255, 0, 0, 0.2)';
    ctx.fillRect(x, y, cellWidth, cellHeight);
  }

  // 🔧 处理 Canvas 点击
  function handleCanvasClick(event: MouseEvent) {
    if (!canvasRef) return;

    const rect = canvasRef.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const cellWidth = displayWidth / gridCols;
    const cellHeight = displayHeight / gridRows;

    selectedGridX = Math.floor(x / cellWidth);
    selectedGridY = Math.floor(y / cellHeight);

    // 确保不超出边界
    selectedGridX = Math.max(0, Math.min(selectedGridX, gridCols - 1));
    selectedGridY = Math.max(0, Math.min(selectedGridY, gridRows - 1));

    console.log('🖱️ Canvas 点击:', { selectedGridX, selectedGridY });

    // 重新绘制
    drawCanvas();
  }

  // 🔧 显示模式现在由父组件控制，不需要本地监听

  // 🔧 监听图片变化，重新绘制 Canvas
  $effect(() => {
    console.log('🔍 监听图片变化:', {
      currentBitmap: !!currentBitmap,
      image: !!currentBitmap?._image,
      imageWidth,
      imageHeight,
      canvasRef: !!canvasRef
    });

    if (currentBitmap?._image && canvasRef) {
      console.log('🎨 准备绘制 Canvas');
      // 使用 requestAnimationFrame 确保 DOM 已更新
      requestAnimationFrame(() => {
        drawCanvas();
      });
    }
  });

  // 🔧 监听网格设置变化，重新绘制 Canvas
  $effect(() => {
    console.log('🔍 监听网格设置变化:', { gridRows, gridCols, selectedGridX, selectedGridY });

    if (currentBitmap?._image && canvasRef) {
      console.log('🎨 网格设置变化，重新绘制 Canvas');
      // 使用 requestAnimationFrame 确保 DOM 已更新
      requestAnimationFrame(() => {
        drawCanvas();
      });
    }
  });

  // 🔧 组件挂载后尝试绘制
  onMount(() => {
    console.log('🔍 ImageCropComponent 挂载完成');

    // 🔧 强制初始化 Canvas 尺寸
    if (canvasRef) {
      canvasRef.width = 400;
      canvasRef.height = 300;
      canvasRef.style.width = '400px';
      canvasRef.style.height = '300px';
      console.log('🎨 Canvas 初始化尺寸设置完成');
    }

    if (currentBitmap?._image) {
      console.log('🎨 onMount: 准备绘制 Canvas');
      setTimeout(() => drawCanvas(), 50);
    }
  });
</script>

<div class="image-crop-component">
  {#if !imageLoaded}
    <!-- 🔧 图片加载区域 -->
    <div class="image-load-section">
      <h4>加载图片</h4>
      <button
        onclick={handleOpenFileSelector}
        disabled={isImporting}
        class="import-btn"
      >
        {isImporting ? '导入中...' : '📁 选择图片'}
      </button>
      <p class="hint">请先选择要裁切的图片</p>
    </div>
  {:else}
    <!-- 🔧 图片已加载，显示裁切控制 -->
    <div class="crop-controls-header">
      <div class="grid-settings">
        <label>行数:
          <input
            type="number"
            bind:value={gridRows}
            min="1"
            max="10"
            class="grid-input"
          />
        </label>
        <label>列数:
          <input
            type="number"
            bind:value={gridCols}
            min="1"
            max="10"
            class="grid-input"
          />
        </label>
      </div>

      <div class="action-buttons">
        <button
          type="button"
          onclick={handleCropButton}
          disabled={!element?.source || !imageWidth || gridRows <= 0 || gridCols <= 0}
          class="crop-btn"
        >
          ✂️ 裁切
        </button>

        <button
          type="button"
          onclick={handleResetButton}
          class="reset-btn"
        >
          🔄 重置
        </button>
      </div>

      <div class="image-info">
        <p>图片尺寸: {imageWidth} x {imageHeight}</p>
        <p>网格: {gridRows} x {gridCols} = {gridRows * gridCols} 个区域</p>
      </div>
    </div>
  {/if}

  <!-- 🔧 只显示 Canvas 模式 -->
  <div class="crop-display-area">
    <div class="canvas-container">
      <canvas
        bind:this={canvasRef}
        class="crop-canvas"
        onclick={handleCanvasClick}
      ></canvas>
      <!-- 🔧 调试信息 -->
      <div class="debug-info" style="font-size: 12px; color: #666; margin-top: 8px;">
        Canvas调试: canvasRef={!!canvasRef}, currentBitmap={!!currentBitmap},
        image={!!currentBitmap?._image}, imageLoaded={imageLoaded}
      </div>
    </div>
  </div>
</div>

<style>
  .image-crop-component {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
  }

  .crop-controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
  }

  .grid-settings {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .grid-settings label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
  }

  .grid-input {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }

  .crop-btn, .reset-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
  }

  .crop-btn {
    background: #10b981;
    color: white;
  }

  .crop-btn:hover:not(:disabled) {
    background: #059669;
  }

  .crop-btn:disabled {
    background: #d1d5db;
    color: #9ca3af;
    cursor: not-allowed;
  }

  .reset-btn {
    background: #f59e0b;
    color: white;
  }

  .reset-btn:hover {
    background: #d97706;
  }

  .crop-display-area {
    min-height: 300px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
  }



  .canvas-container {
    padding: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .crop-canvas {
    border: 1px solid #ccc;
    cursor: pointer;
    max-width: 100%;
    max-height: 400px;
    min-width: 200px;
    min-height: 150px;
    background: #f0f0f0;
  }

  /* 🔧 图片加载区域样式 */
  .image-load-section {
    text-align: center;
    padding: 40px 20px;
    background: white;
    border-radius: 8px;
    border: 2px dashed #ccc;
  }

  .image-load-section h4 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 18px;
  }

  .import-btn {
    padding: 12px 24px;
    background: var(--theme-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
  }

  .import-btn:hover:not(:disabled) {
    background: var(--theme-primary-hover, #2563eb);
    transform: translateY(-1px);
  }

  .import-btn:disabled {
    background: var(--theme-surface-disabled, #f3f4f6);
    color: var(--theme-text-disabled, #9ca3af);
    cursor: not-allowed;
    transform: none;
  }

  .hint {
    margin: 16px 0 0 0;
    color: #666;
    font-size: 14px;
  }

  /* 🔧 图片信息样式 */
  .image-info {
    margin-top: 12px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    font-size: 12px;
    color: #666;
  }

  .image-info p {
    margin: 2px 0;
  }
</style>
