<script lang="ts">
  /**
   * 主题提供者组件
   * 负责初始化和管理全局主题
   */
  
  import { onMount } from 'svelte';
  import { themeManager, ThemeType } from '../theme';
  
  // 主题状态
  let currentTheme = $state(themeManager.getCurrentTheme());
  let isInitialized = $state(false);
  
  /**
   * 切换主题
   */
  export function setTheme(theme: ThemeType) {
    themeManager.setTheme(theme);
    currentTheme = theme;
  }
  
  /**
   * 获取当前主题
   */
  export function getCurrentTheme() {
    return currentTheme;
  }
  
  /**
   * 切换深色/浅色主题
   */
  export function toggleTheme() {
    const newTheme = currentTheme === ThemeType.DARK ? ThemeType.LIGHT : ThemeType.DARK;
    setTheme(newTheme);
  }
  
  // 组件挂载时初始化主题
  onMount(() => {
    // 确保主题管理器已初始化
    themeManager.setTheme(themeManager.getCurrentTheme());
    isInitialized = true;
    
    console.log('主题系统已初始化:', {
      currentTheme: themeManager.getCurrentTheme(),
      colors: themeManager.getCurrentColors()
    });
  });
</script>

<!-- 主题提供者不渲染任何内容，只负责主题管理 -->
{#if isInitialized}
  <slot />
{:else}
  <!-- 主题加载中的占位符 -->
  <div class="theme-loading">
    <div class="loading-spinner"></div>
    <p>正在加载主题...</p>
  </div>
{/if}

<style>
  .theme-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: var(--theme-background);
    color: var(--theme-text);
    font-family: var(--font-family-base);
  }
  
  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--theme-border);
    border-top: 3px solid var(--theme-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-4);
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .theme-loading p {
    margin: 0;
    font-size: var(--font-size-sm);
    opacity: 0.8;
  }
</style>
