<script lang="ts">
  /**
   * 响应式滑动条属性面板 - 简化版本
   * 直接使用响应式模型对象，无需额外的函数
   */

  import {
    sceneModelState
  } from '../../../stores/sceneModelStore';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import LabelInput from '../../../components/LabelInput.svelte';
  import DropTarget from '../../../components/drop/DropTarget.svelte';
  import EventSelectionModal from '../../../modals/EventSelectionModal.svelte';
  import type { SliderModel } from '../../../type/ui/sliderModel.svelte';
  import type { BaseObjectModel } from '../../../type/baseObjectModel.svelte';

  // 监听选中的对象 - 使用第一个选中的对象
  let currentState = $derived($sceneModelState);
  let slider = $derived(currentState.selectedObjects[0] as SliderModel | null);

  // 面板展开状态
  let isExpanded = $state(true);

  // 方向选项已删除

  // 事件类型选项
  const eventTypes = [
    { value: 'onChange', label: '值改变事件' },
    { value: 'onDragStart', label: '开始拖拽事件' },
    { value: 'onDragEnd', label: '拖拽结束事件' }
  ];

  // 事件选择相关状态
  let showEventModal = $state(false);
  let selectedEventType = $state('onChange');



  // 计算进度百分比
  let progressPercent = $derived(() => {
    if (!slider || slider.maxValue === slider.minValue) return 0;
    return Math.round(((slider.value - slider.minValue) / (slider.maxValue - slider.minValue)) * 100);
  });

  // 获取绑定状态信息
  let bindingInfo = $derived(() => {
    if (!slider) return null;
    return slider.getBindingInfo();
  });

  // 处理拖拽绑定 - handleTrackDrop已删除

  function handleRangeDrop(object: BaseObjectModel) {
    if (!slider) return;
    console.log('🎯 绑定范围精灵:', object.className);
    const originalObject = object.getOriginalObject();

    // 🔑 UI更新模型：只更新 SliderModel 的响应式属性
    slider.boundRangeSprite = originalObject;

    // 模型会通过 setupSpecificSync() 自动同步到 JavaScript 对象
    console.log('✅ 范围精灵绑定完成，模型已更新');
  }

  function handleThumbDrop(object: BaseObjectModel) {
    if (!slider) return;
    console.log('🎯 绑定滑块精灵:', object.className);
    const originalObject = object.getOriginalObject();

    // 🔑 UI更新模型：只更新 SliderModel 的响应式属性
    slider.boundThumbSprite = originalObject;

    // 模型会通过 setupSpecificSync() 自动同步到 JavaScript 对象
    console.log('✅ 滑块精灵绑定完成，模型已更新');
  }

  function handleProgressDrop(object: BaseObjectModel) {
    if (!slider) return;
    console.log('🎯 绑定进度精灵:', object.className);
    const originalObject = object.getOriginalObject();

    // 🔑 UI更新模型：只更新 SliderModel 的响应式属性
    slider.boundProgressSprite = originalObject;

    // 模型会通过 setupSpecificSync() 自动同步到 JavaScript 对象
    console.log('✅ 进度精灵绑定完成，模型已更新');
  }

  function handleLabelDrop(object: BaseObjectModel) {
    if (!slider) return;
    console.log('🎯 绑定文本精灵:', object.className);
    const originalObject = object.getOriginalObject();

    // 🔑 UI更新模型：只更新 SliderModel 的响应式属性
    slider.boundLabelSprite = originalObject;

    // 模型会通过 setupSpecificSync() 自动同步到 JavaScript 对象
    console.log('✅ 文本精灵绑定完成，模型已更新');
  }

  // 解除绑定 - unbindTrack已删除

  function unbindRange() {
    if (!slider) return;
    slider.boundRangeSprite = null;
    console.log('🔓 范围精灵绑定已解除，模型已更新');
  }

  function unbindThumb() {
    if (!slider) return;
    slider.boundThumbSprite = null;
    console.log('🔓 滑块精灵绑定已解除，模型已更新');
  }

  function unbindProgress() {
    if (!slider) return;
    slider.boundProgressSprite = null;
    console.log('🔓 进度精灵绑定已解除，模型已更新');
  }

  function unbindLabel() {
    if (!slider) return;
    slider.boundLabelSprite = null;
    console.log('🔓 文本精灵绑定已解除，模型已更新');
  }

  // 事件处理函数
  function openEventModal(eventType: string) {
    selectedEventType = eventType;
    showEventModal = true;
  }

  // 关闭事件选择弹窗
  function closeEventModal() {
    showEventModal = false;
  }

  // 处理事件选择
  function handleEventSelected(event: CustomEvent) {
    const selectedEvent = event.detail;
    if (selectedEvent && selectedEvent.code && slider) {
      // 根据当前选择的事件类型设置对应的事件代码
      switch (selectedEventType) {
        case 'onChange':
          slider.onChangeCode = selectedEvent.code;
          break;
        case 'onDragStart':
          slider.onDragStartCode = selectedEvent.code;
          break;
        case 'onDragEnd':
          slider.onDragEndCode = selectedEvent.code;
          break;
      }
    }
    closeEventModal();
  }

  // 获取当前事件代码
  function getCurrentEventCode(eventType: string): string {
    if (!slider) return '';
    switch (eventType) {
      case 'onChange':
        return slider.onChangeCode || '';
      case 'onDragStart':
        return slider.onDragStartCode || '';
      case 'onDragEnd':
        return slider.onDragEndCode || '';
      default:
        return '';
    }
  }

  console.log('SliderPanel 组件已加载');
</script>

{#if slider}
  <AccordionPanel
    title="滑动条属性"
    icon="🎚️"
    badge="Slider"
    badgeVariant="info"
    bind:expanded={isExpanded}
  >
    <!-- 数值设置 -->
    <PropertyContainer>
        <Label text="当前值:" />
        <LabelInput
          bind:value={slider.value}
          type="number"
          min={slider.minValue}
          max={slider.maxValue}
          step={slider.step}
          targetObject={slider}
          fieldName="value"
          name="当前值"
        />
      </PropertyContainer>

      <PropertyContainer>
        <Label text="最小值:" />
        <LabelInput
          bind:value={slider.minValue}
          type="number"
          targetObject={slider}
          fieldName="minValue"
          name="最小值"
        />
      </PropertyContainer>

      <PropertyContainer>
        <Label text="最大值:" />
        <LabelInput
          bind:value={slider.maxValue}
          type="number"
          targetObject={slider}
          fieldName="maxValue"
          name="最大值"
        />
      </PropertyContainer>

      <PropertyContainer>
        <Label text="步长:" />
        <LabelInput
          bind:value={slider.step}
          type="number"
          min={0.1}
          step={0.1}
          targetObject={slider}
          fieldName="step"
          name="步长"
        />
      </PropertyContainer>

    <!-- 🔑 子组件绑定状态 -->
    <div class="property-section">
      <h4>🔗 子组件绑定</h4>

      <!-- 轨道精灵绑定已删除 -->

      <!-- 范围精灵绑定 ⭐ 新增 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">范围精灵 (UIImage) - 定义有效范围:</span>
          <span class="binding-status-badge {bindingInfo()?.hasRange ? 'bound' : 'unbound'}">
            {bindingInfo()?.hasRange ? '已绑定' : '未绑定'}
          </span>
          {#if bindingInfo()?.hasRange}
            <button class="unbind-btn" onclick={unbindRange} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if bindingInfo()?.hasRange}
          <div class="bound-object-info">
            已绑定: {bindingInfo()?.rangeSprite?.className || '未知对象'}
            <div class="range-info">
              🔑 此精灵的长度决定滑动条的总长度
            </div>
          </div>
        {:else}
          <DropTarget
            onDrop={handleRangeDrop}
            placeholder="拖拽 UIImage 对象到此处定义滑动范围"
            targetObject={slider}
            fieldName="boundRangeSprite"
            enableHistory={true}
            operationName="绑定滑动条范围精灵"
          />
        {/if}
      </div>

      <!-- 滑块精灵绑定 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">滑块精灵 (UIImage):</span>
          <span class="binding-status-badge {bindingInfo()?.hasThumb ? 'bound' : 'unbound'}">
            {bindingInfo()?.hasThumb ? '已绑定' : '未绑定'}
          </span>
          {#if bindingInfo()?.hasThumb}
            <button class="unbind-btn" onclick={unbindThumb} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if bindingInfo()?.hasThumb}
          <div class="bound-object-info">
            已绑定: {bindingInfo()?.thumbSprite?.className || '未知对象'}
          </div>
        {:else}
          <DropTarget
            onDrop={handleThumbDrop}
            placeholder="拖拽 UIImage 对象到此处绑定为滑块"
            targetObject={slider}
            fieldName="boundThumbSprite"
            enableHistory={true}
            operationName="绑定滑动条滑块精灵"
          />
        {/if}
      </div>

      <!-- 进度精灵绑定 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">进度精灵 (UIImage):</span>
          <span class="binding-status-badge {bindingInfo()?.hasProgress ? 'bound' : 'unbound'}">
            {bindingInfo()?.hasProgress ? '已绑定' : '未绑定'}
          </span>
          {#if bindingInfo()?.hasProgress}
            <button class="unbind-btn" onclick={unbindProgress} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if bindingInfo()?.hasProgress}
          <div class="bound-object-info">
            已绑定: {bindingInfo()?.progressSprite?.className || '未知对象'}
          </div>
        {:else}
          <DropTarget
            onDrop={handleProgressDrop}
            placeholder="拖拽 UIImage 对象到此处绑定为进度条"
            targetObject={slider}
            fieldName="boundProgressSprite"
            enableHistory={true}
            operationName="绑定滑动条进度精灵"
          />
        {/if}
      </div>

      <!-- 文本精灵绑定 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">文本精灵 (UILabel):</span>
          <span class="binding-status-badge {bindingInfo()?.hasLabel ? 'bound' : 'unbound'}">
            {bindingInfo()?.hasLabel ? '已绑定' : '未绑定'}
          </span>
          {#if bindingInfo()?.hasLabel}
            <button class="unbind-btn" onclick={unbindLabel} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if bindingInfo()?.hasLabel}
          <div class="bound-object-info">
            已绑定: {bindingInfo()?.labelSprite?.className || '未知对象'}
          </div>
        {:else}
          <DropTarget
            onDrop={handleLabelDrop}
            placeholder="拖拽 UILabel 对象到此处绑定为标签"
            targetObject={slider}
            fieldName="boundLabelSprite"
            enableHistory={true}
            operationName="绑定滑动条标签精灵"
          />
        {/if}
      </div>

      <div class="binding-info">
        <p class="binding-hint">
          💡 从左侧对象树拖拽精灵对象到上方区域进行绑定
        </p>
      </div>
    </div>



    <!-- 行为设置 -->
    <div class="property-section">
      <h4>⚙️ 行为设置</h4>

      <div class="property-row">
        <span class="info-label">启用状态:</span>
        <div class="property-item-horizontal">
          <label class="checkbox-label">
            <input
              type="checkbox"
              bind:checked={slider.enabled}
            />
            启用滑动条
          </label>
        </div>
      </div>

      <!-- 方向选择已删除 -->
    </div>

    <!-- 事件设置 -->
    <div class="property-section">
      <h4>⚡ 事件设置</h4>

      {#each eventTypes as eventType}
        <div class="event-item">
          <div class="event-header">
            <span class="event-label">{eventType.label}:</span>
            <button
              class="event-select-btn"
              onclick={() => openEventModal(eventType.value)}
            >
              📝 选择事件
            </button>
          </div>

          {#if getCurrentEventCode(eventType.value)}
            <div class="event-code-display">
              <code>{getCurrentEventCode(eventType.value)}</code>
            </div>
          {:else}
            <div class="event-code-empty">
              未设置事件
            </div>
          {/if}
        </div>
      {/each}
    </div>

    <!-- 预览信息 -->
    <div class="property-section">
      <h4>👁️ 预览信息</h4>
      <div class="preview-info">
        <div class="preview-row">
          <span>当前值:</span>
          <span class="preview-value">{slider.value}</span>
        </div>
        <div class="preview-row">
          <span>范围:</span>
          <span class="preview-value">{slider.minValue} - {slider.maxValue}</span>
        </div>
        <div class="preview-row">
          <span>进度:</span>
          <span class="preview-value">{progressPercent()}%</span>
        </div>
      </div>
    </div>

  </AccordionPanel>
{:else}
  <div class="no-selection">
    <p>请选择一个滑动条对象来查看其属性</p>
  </div>
{/if}

<!-- 事件选择弹窗 -->
{#if showEventModal}
  <EventSelectionModal
    onClose={closeEventModal}
    onEventSelected={handleEventSelected}
  />
{/if}

<style>
  /* 基础样式 */
  .property-section {
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 4px;
    padding: 8px;
    border: 1px solid var(--theme-border-light, #e9ecef);
    margin-bottom: 8px;
  }

  .property-section h4 {
    margin: 0 0 8px 0;
    font-size: 11px;
    font-weight: 600;
    color: var(--theme-text, #1a202c);
  }

  .property-row {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 6px;
  }

  .property-row:last-child {
    margin-bottom: 0;
  }

  .property-item-horizontal {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
  }

  .property-item-full-width {
    flex: 1;
  }

  .info-label {
    font-size: 10px;
    font-weight: 500;
    color: var(--theme-text-secondary, #718096);
    white-space: nowrap;
    min-width: 60px;
    text-align: right;
  }

  /* 输入框样式 */
  .number-input {
    width: 100%;
    max-width: 80px;
    padding: 4px 6px;
    font-size: 10px;
    border: 1px solid var(--theme-border, #e2e8f0);
    border-radius: 3px;
    background: var(--theme-surface, #ffffff);
    color: var(--theme-text, #1a202c);
    transition: border-color 0.15s ease;
  }

  .select-input {
    width: 100%;
    padding: 4px 6px;
    font-size: 10px;
    border: 1px solid var(--theme-border, #e2e8f0);
    border-radius: 3px;
    background: var(--theme-surface, #ffffff);
    color: var(--theme-text, #1a202c);
    transition: border-color 0.15s ease;
  }

  .number-input:focus, .select-input:focus {
    outline: none;
    border-color: var(--theme-primary, #3182ce);
    box-shadow: 0 0 0 1px rgba(49, 130, 206, 0.1);
  }

  /* 🔑 绑定状态样式 */
  .binding-item {
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
  }

  .binding-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    font-size: 10px;
  }

  .bound-object-info {
    font-size: 9px;
    color: var(--theme-text-secondary, #666);
    padding: 4px 6px;
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 3px;
    border: 1px solid var(--theme-border-light, #e9ecef);
  }

  .unbind-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 2px;
    padding: 2px 4px;
    font-size: 8px;
    cursor: pointer;
    transition: background-color 0.15s ease;
  }

  .unbind-btn:hover {
    background: #c82333;
  }

  .binding-label {
    font-weight: 500;
    color: var(--theme-text, #1a202c);
  }

  .binding-status-badge {
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 9px;
    font-weight: 500;
    text-transform: uppercase;
  }

  .binding-status-badge.bound {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .binding-status-badge.unbound {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  .binding-info {
    margin-top: 8px;
  }

  .binding-hint {
    font-size: 9px;
    color: var(--theme-text-secondary, #718096);
    margin: 0;
    line-height: 1.4;
    font-style: italic;
  }

  /* 范围精灵特殊信息样式 */
  .range-info {
    font-size: 9px;
    color: var(--theme-accent, #3182ce);
    margin-top: 4px;
    padding: 2px 6px;
    background: var(--theme-accent-light, #ebf8ff);
    border-radius: 2px;
    border-left: 2px solid var(--theme-accent, #3182ce);
    font-weight: 500;
  }

  /* 复选框样式 */
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 10px;
    color: var(--theme-text, #1a202c);
    cursor: pointer;
  }

  .checkbox-label input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
  }

  /* 预览信息样式 */
  .preview-info {
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 3px;
    padding: 6px;
  }

  .preview-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 9px;
    margin-bottom: 3px;
  }

  .preview-row:last-child {
    margin-bottom: 0;
  }

  .preview-value {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    color: var(--theme-primary, #3182ce);
    font-weight: 500;
  }

  .no-selection {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 16px;
    text-align: center;
    color: var(--theme-text-secondary, #718096);
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 6px;
    border: 2px dashed var(--theme-border, #e2e8f0);
  }

  .no-selection p {
    margin: 0;
    font-size: 12px;
  }

  /* 事件设置样式 */
  .event-item {
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
  }

  .event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
  }

  .event-label {
    font-size: 10px;
    font-weight: 500;
    color: var(--theme-text, #1a202c);
  }

  .event-select-btn {
    background: var(--theme-primary, #3182ce);
    color: white;
    border: none;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 9px;
    cursor: pointer;
    transition: background-color 0.15s ease;
  }

  .event-select-btn:hover {
    background: var(--theme-primary-dark, #2c5aa0);
  }

  .event-code-display {
    background: var(--theme-surface-light, #f8f9fa);
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 3px;
    padding: 6px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 9px;
    color: var(--theme-text, #1a202c);
    word-break: break-all;
    line-height: 1.4;
  }

  .event-code-display code {
    background: none;
    padding: 0;
    color: var(--theme-primary, #3182ce);
  }

  .event-code-empty {
    font-size: 9px;
    color: var(--theme-text-secondary, #718096);
    font-style: italic;
    padding: 6px;
    text-align: center;
    background: var(--theme-surface-light, #f8f9fa);
    border: 1px dashed var(--theme-border, #e2e8f0);
    border-radius: 3px;
  }
</style>
