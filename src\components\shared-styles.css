/*
 * 统一组件样式文件
 * 以 SafeInput 为标准，统一所有组件的字体、边框、尺寸等样式
 */

/* ===== 基础变量 ===== */
:root {
  /* 字体 */
  --component-font-size: 11px;
  --component-font-family: inherit;
  --component-line-height: 1.2;

  /* 边框 */
  --component-border: 1px solid var(--theme-border, #e2e8f0);
  --component-border-radius: 4px;
  --component-border-focus: var(--theme-primary, #3b82f6);

  /* 内边距 */
  --component-padding: 4px 8px;
  --component-padding-sm: 2px 6px;
  --component-padding-lg: 6px 12px;

  /* 背景色 */
  --component-bg: var(--theme-surface, #f0f2f5);
  --component-bg-disabled: var(--theme-surface-dark, #e8eaed);
  --component-bg-readonly: var(--theme-surface, #f0f2f5);
  --component-bg-hover: var(--theme-surface-hover, #f8f9fa);

  /* 文字颜色 */
  --component-text: var(--theme-text, #1a202c);
  --component-text-disabled: var(--theme-text-disabled, #999);
  --component-text-placeholder: var(--theme-text-placeholder, #999);
  --component-text-secondary: var(--theme-text-secondary, #718096);

  /* 阴影 */
  --component-shadow-focus: 0 0 0 2px rgba(59, 130, 246, 0.1);
  --component-shadow-dropdown: 0 4px 12px rgba(0, 0, 0, 0.15);

  /* 过渡动画 */
  --component-transition: border-color 0.2s ease, box-shadow 0.2s ease;

  /* 状态颜色 */
  --component-error: var(--theme-error, #ef4444);
  --component-success: var(--theme-success, #10b981);
  --component-warning: var(--theme-warning, #f59e0b);
}

/* ===== 基础输入框样式 ===== */
.component-input-base {
  padding: var(--component-padding);
  border: var(--component-border);
  border-radius: var(--component-border-radius);
  background: var(--component-bg);
  color: var(--component-text);
  font-size: var(--component-font-size);
  font-family: var(--component-font-family);
  line-height: var(--component-line-height);
  outline: none;
  transition: var(--component-transition);
  box-sizing: border-box;
}

.component-input-base:focus {
  border-color: var(--component-border-focus);
  box-shadow: var(--component-shadow-focus);
}

.component-input-base:disabled {
  background-color: var(--component-bg-disabled);
  color: var(--component-text-disabled);
  cursor: not-allowed;
}

.component-input-base:readonly {
  background-color: var(--component-bg-readonly);
  cursor: default;
}

.component-input-base::placeholder {
  color: var(--component-text-placeholder);
}

/* ===== 基础按钮样式 ===== */
.component-button-base {
  padding: var(--component-padding);
  border: var(--component-border);
  border-radius: var(--component-border-radius);
  background: var(--component-bg);
  color: var(--component-text);
  font-size: var(--component-font-size);
  font-family: var(--component-font-family);
  line-height: var(--component-line-height);
  cursor: pointer;
  transition: var(--component-transition);
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.component-button-base:hover {
  background-color: var(--component-bg-hover);
}

.component-button-base:focus {
  border-color: var(--component-border-focus);
  box-shadow: var(--component-shadow-focus);
  outline: none;
}

.component-button-base:disabled {
  background-color: var(--component-bg-disabled);
  color: var(--component-text-disabled);
  cursor: not-allowed;
}

/* ===== 基础选择框样式 ===== */
.component-select-base {
  padding: var(--component-padding);
  border: var(--component-border);
  border-radius: var(--component-border-radius);
  background: var(--component-bg);
  color: var(--component-text);
  font-size: var(--component-font-size);
  font-family: var(--component-font-family);
  line-height: var(--component-line-height);
  cursor: pointer;
  transition: var(--component-transition);
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.component-select-base:focus {
  border-color: var(--component-border-focus);
  box-shadow: var(--component-shadow-focus);
  outline: none;
}

.component-select-base:disabled {
  background-color: var(--component-bg-disabled);
  color: var(--component-text-disabled);
  cursor: not-allowed;
}

/* ===== 下拉框样式 ===== */
.component-dropdown-base {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 99999;
  background-color: var(--component-bg);
  border: var(--component-border);
  border-radius: var(--component-border-radius);
  box-shadow: var(--component-shadow-dropdown);
  margin-top: 2px;
  max-height: 200px;
  overflow: visible;
}

.component-dropdown-options {
  max-height: 180px;
  overflow-y: auto;
  overflow-x: hidden;
}

.component-dropdown-option {
  padding: var(--component-padding);
  font-size: var(--component-font-size);
  font-family: var(--component-font-family);
  line-height: var(--component-line-height);
  color: var(--component-text);
  cursor: pointer;
  transition: background-color 0.15s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.component-dropdown-option:hover {
  background-color: var(--component-bg-hover);
}

.component-dropdown-option.selected {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--component-border-focus);
}

.component-dropdown-option.disabled {
  color: var(--component-text-disabled);
  cursor: not-allowed;
}

/* ===== 复选框样式 ===== */
.component-checkbox-base {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: var(--component-font-size);
  font-family: var(--component-font-family);
  line-height: var(--component-line-height);
  color: var(--component-text);
  cursor: pointer;
}

.component-checkbox-input {
  width: 14px;
  height: 14px;
  border: var(--component-border);
  border-radius: 2px;
  background: var(--component-bg);
  cursor: pointer;
}

.component-checkbox-input:checked {
  background-color: var(--component-border-focus);
  border-color: var(--component-border-focus);
}

.component-checkbox-input:disabled {
  background-color: var(--component-bg-disabled);
  cursor: not-allowed;
}

/* ===== 尺寸变体 ===== */
.component-sm {
  padding: var(--component-padding-sm) !important;
  font-size: 10px !important;
}

.component-lg {
  padding: var(--component-padding-lg) !important;
  font-size: 12px !important;
}

/* ===== Select组件特殊样式 ===== */
.select-container.component-sm {
  min-width: 70px !important;
}

.select-container {
  min-width: 90px;
}

/* ===== 状态变体 ===== */
.component-error {
  border-color: var(--component-error) !important;
}

.component-success {
  border-color: var(--component-success) !important;
}

.component-warning {
  border-color: var(--component-warning) !important;
}

/* ===== 工具类 ===== */
.component-full-width {
  width: 100%;
}

.component-no-border {
  border: none !important;
}

.component-no-padding {
  padding: 0 !important;
}

.component-text-center {
  text-align: center;
}

.component-text-right {
  text-align: right;
}
