/**
 * Scene_Skill 创建器
 * 专门用于创建技能场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';
import { type ItemBaseSceneOptions } from './SceneItemBaseCreator';

/**
 * 技能场景创建选项
 */
export interface SkillSceneOptions extends ItemBaseSceneOptions {
    /** 角色ID */
    actorId?: number;
    /** 技能类型ID */
    skillTypeId?: number;
    /** 初始选中的技能索引 */
    initialSkillIndex?: number;
}

/**
 * 创建技能场景
 * @param options 创建选项
 * @returns 创建的技能场景实例
 */
export async function createSceneSkill(options: SkillSceneOptions = {}): Promise<any> {
    console.log('=== 创建技能场景 Scene_Skill ===');
    
    try {
        // 预加载技能场景资源
        BaseSceneCreator.preloadSceneResources('Scene_Skill');
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Skill', defaultOptions);
        
        // Scene_Skill 特定的设置
        if (options.actorId !== undefined && scene._actor) {
            // 设置角色
            console.log('设置技能场景角色ID:', options.actorId);
        }
        
        if (options.skillTypeId !== undefined && scene._itemWindow) {
            // 设置技能类型
            console.log('设置技能类型ID:', options.skillTypeId);
        }
        
        if (options.initialSkillIndex !== undefined && scene._itemWindow) {
            scene._itemWindow.select(options.initialSkillIndex);
            console.log('设置初始技能索引:', options.initialSkillIndex);
        }
        
        console.log('Scene_Skill 创建完成');
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Skill 失败:', error);
        throw error;
    }
}

/**
 * 创建并启动技能场景
 * @param options 创建选项
 * @returns 创建的技能场景实例
 */
export async function createAndStartSceneSkill(options: SkillSceneOptions = {}): Promise<any> {
    return createSceneSkill({ ...options, autoStart: true });
}

/**
 * 创建简单的技能场景（用于测试）
 * @returns 创建的技能场景实例
 */
export async function createSimpleSceneSkill(): Promise<any> {
    return createSceneSkill({
        actorId: 1,
        skillTypeId: 1,
        showHelpWindow: true,
        initialSkillIndex: 0,
        backgroundType: 1,
        autoStart: false,
        addToStage: true
    });
}
