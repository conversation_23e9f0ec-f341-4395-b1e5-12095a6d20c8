/**
 * 玩家对象复制器
 */

import type { PlayerCopyOptions, CopyResult } from './types';
import { DEFAULT_COPY_OPTIONS } from './types';
import { CopyUtils } from './CopyUtils';

/**
 * 玩家复制器类
 */
export class PlayerCopier {
  
  /**
   * 复制玩家对象
   * @param sourcePlayer 源玩家对象
   * @param options 复制选项
   * @returns 复制结果
   */
  static async copy(sourcePlayer: any, options: PlayerCopyOptions = {}): Promise<CopyResult> {
    console.log('=== PlayerCopier: 开始复制玩家对象 ===');
    console.log('源对象:', sourcePlayer);
    console.log('复制选项:', options);

    try {
      // 合并默认选项
      const copyOptions = { ...DEFAULT_COPY_OPTIONS, ...options };

      // 等待资源加载
      const resourcesReady = await CopyUtils.waitForResources();
      if (!resourcesReady) {
        throw new Error('资源加载超时');
      }

      // 解析源对象结构
      const structure = CopyUtils.parseObjectStructure(sourcePlayer);
      console.log('源对象结构:', structure);

      if (!structure.gameObject) {
        throw new Error('无法找到有效的游戏对象');
      }

      const sourceGameObject = structure.gameObject;
      const sourceSprite = structure.displayObject;

      // 验证是否为玩家对象
      if (sourceGameObject.constructor?.name !== 'Game_Player') {
        throw new Error(`期望 Game_Player 对象，实际得到: ${sourceGameObject.constructor?.name}`);
      }

      console.log('源玩家信息:', {
        characterName: sourceGameObject._characterName,
        characterIndex: sourceGameObject._characterIndex,
        direction: sourceGameObject._direction,
        x: sourceGameObject._x,
        y: sourceGameObject._y
      });

      // 动态导入创建器
      const { createPlayer } = await import('../../creators/character/PlayerCreator');

      // 创建新的玩家对象
      const newPlayer = await createPlayer({
        actorId: options.newActorId,
        characterName: sourceGameObject._characterName,
        characterIndex: sourceGameObject._characterIndex,
        direction: sourceGameObject._direction,
        x: sourceGameObject._x,
        y: sourceGameObject._y
      });

      if (!newPlayer) {
        throw new Error('创建新玩家对象失败');
      }

      // 应用位置偏移
      if (newPlayer.gameObject && copyOptions.positionOffset) {
        CopyUtils.applyPositionOffset(newPlayer.gameObject, copyOptions.positionOffset);
      }

      // 复制显示属性
      if (newPlayer.displayObject && sourceSprite) {
        CopyUtils.copyDisplayProperties(sourceSprite, newPlayer.displayObject);
      }

      // 更新显示名称
      CopyUtils.updateDisplayName(newPlayer, copyOptions.nameSuffix);

      console.log('玩家对象复制成功:', newPlayer);

      return {
        success: true,
        copiedObject: newPlayer,
        sourceType: CopyUtils.detectObjectType(sourcePlayer),
        targetType: CopyUtils.detectObjectType(newPlayer)
      };

    } catch (error) {
      console.error('PlayerCopier: 复制玩家对象失败:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        sourceType: CopyUtils.detectObjectType(sourcePlayer)
      };
    }
  }

  /**
   * 批量复制玩家对象
   * @param sourcePlayers 源玩家对象数组
   * @param options 复制选项
   * @returns 复制结果数组
   */
  static async copyMultiple(
    sourcePlayers: any[], 
    options: PlayerCopyOptions = {}
  ): Promise<CopyResult[]> {
    console.log(`=== PlayerCopier: 批量复制 ${sourcePlayers.length} 个玩家对象 ===`);

    const results: CopyResult[] = [];

    for (let i = 0; i < sourcePlayers.length; i++) {
      const sourcePlayer = sourcePlayers[i];
      
      // 为每个对象生成不同的偏移
      const playerOptions = {
        ...options,
        positionOffset: {
          x: (options.positionOffset?.x || 1) + i,
          y: (options.positionOffset?.y || 1) + i
        }
      };

      const result = await this.copy(sourcePlayer, playerOptions);
      results.push(result);

      // 如果复制失败，记录但继续处理其他对象
      if (!result.success) {
        console.warn(`第 ${i + 1} 个玩家对象复制失败:`, result.error);
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`批量复制完成: ${successCount}/${sourcePlayers.length} 成功`);

    return results;
  }

  /**
   * 验证玩家对象是否可以复制
   * @param sourcePlayer 源玩家对象
   * @returns 是否可以复制
   */
  static canCopy(sourcePlayer: any): boolean {
    try {
      const structure = CopyUtils.parseObjectStructure(sourcePlayer);
      
      if (!structure.gameObject) {
        return false;
      }

      // 检查是否为玩家对象
      const gameObjectType = structure.gameObject.constructor?.name;
      if (gameObjectType !== 'Game_Player') {
        return false;
      }

      // 检查必要属性
      const gameObject = structure.gameObject;
      return !!(
        gameObject._characterName &&
        gameObject._characterIndex !== undefined &&
        gameObject._direction !== undefined
      );

    } catch (error) {
      console.warn('PlayerCopier: 验证对象时出错:', error);
      return false;
    }
  }

  /**
   * 获取玩家对象信息
   * @param sourcePlayer 源玩家对象
   * @returns 玩家信息
   */
  static getPlayerInfo(sourcePlayer: any): any {
    try {
      const structure = CopyUtils.parseObjectStructure(sourcePlayer);
      
      if (!structure.gameObject) {
        return null;
      }

      const gameObject = structure.gameObject;
      
      return {
        type: 'Game_Player',
        characterName: gameObject._characterName,
        characterIndex: gameObject._characterIndex,
        direction: gameObject._direction,
        position: { x: gameObject._x, y: gameObject._y },
        isWrapper: structure.isWrapper,
        displayName: structure.displayName
      };

    } catch (error) {
      console.warn('PlayerCopier: 获取玩家信息时出错:', error);
      return null;
    }
  }
}
