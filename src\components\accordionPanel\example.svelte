<script lang="ts">
  import { AccordionPanel, PropertyContainer, PropertyPanel } from './index';
  import SafeInput from '../SafeInput.svelte';
  import Select from '../Select.svelte';
  import type { PanelConfig } from './index';

  // 示例数据模型
  let model = {
    text: '示例文本',
    prefix: '',
    suffix: '',
    textAlign: 'left',
    verticalAlign: 'top',
    fontSize: 14,
    fontWeight: 'normal'
  };

  // 示例选项
  const textAlignOptions = [
    { value: 'left', label: '左对齐' },
    { value: 'center', label: '居中' },
    { value: 'right', label: '右对齐' }
  ];

  const verticalAlignOptions = [
    { value: 'top', label: '顶部' },
    { value: 'middle', label: '中间' },
    { value: 'bottom', label: '底部' }
  ];

  const fontWeightOptions = [
    { value: 'normal', label: '正常' },
    { value: 'bold', label: '粗体' },
    { value: 'lighter', label: '细体' }
  ];

  // 方式1：使用配置对象
  const textConfig: PanelConfig = {
    title: "📝 文本内容",
    properties: [
      {
        label: "文本",
        component: SafeInput,
        props: {
          value: model.text,
          placeholder: "输入文本内容",
          targetObject: model,
          fieldName: "text"
        },
        width: 'full'
      }
    ]
  };

  const prefixSuffixConfig: PanelConfig = {
    title: "🔤 前缀后缀",
    properties: [
      {
        label: "前缀",
        component: SafeInput,
        props: {
          value: model.prefix,
          placeholder: "前缀",
          targetObject: model,
          fieldName: "prefix"
        }
      },
      {
        label: "后缀",
        component: SafeInput,
        props: {
          value: model.suffix,
          placeholder: "后缀",
          targetObject: model,
          fieldName: "suffix"
        }
      }
    ]
  };

  const alignConfig: PanelConfig = {
    title: "📐 对齐设置",
    properties: [
      {
        label: "水平对齐",
        component: Select,
        props: {
          value: model.textAlign,
          options: textAlignOptions,
          placeholder: "选择水平对齐",
          size: "sm",
          searchable: false,
          clearable: false,
          targetObject: model,
          fieldName: "textAlign"
        }
      },
      {
        label: "垂直对齐",
        component: Select,
        props: {
          value: model.verticalAlign,
          options: verticalAlignOptions,
          placeholder: "选择垂直对齐",
          size: "sm",
          searchable: false,
          clearable: false,
          targetObject: model,
          fieldName: "verticalAlign"
        }
      }
    ]
  };

  const fontConfig: PanelConfig = {
    title: "🎨 字体样式",
    properties: [
      {
        label: "字体大小",
        component: SafeInput,
        props: {
          value: model.fontSize.toString(),
          placeholder: "字体大小",
          type: "number",
          targetObject: model,
          fieldName: "fontSize"
        }
      },
      {
        label: "字体粗细",
        component: Select,
        props: {
          value: model.fontWeight,
          options: fontWeightOptions,
          placeholder: "选择字体粗细",
          size: "sm",
          searchable: false,
          clearable: false,
          targetObject: model,
          fieldName: "fontWeight"
        }
      }
    ]
  };
</script>

<div class="example-container">
  <h3>属性面板系统使用示例</h3>

  <div class="panels-container">
    <!-- 方式1：使用配置对象 -->
    <h4>方式1：配置对象方式</h4>
    <PropertyPanel config={textConfig} />
    <PropertyPanel config={prefixSuffixConfig} />
    <PropertyPanel config={alignConfig} />
    <PropertyPanel config={fontConfig} />

    <!-- 方式2：使用组合组件 -->
    <h4>方式2：组合组件方式</h4>
    <AccordionPanel title="🔤 前缀后缀（组合方式）">
      <PropertyContainer columns={2}>
        <div class="property-item">
          <label>前缀:</label>
          <SafeInput
            bind:value={model.prefix}
            placeholder="前缀"
          />
        </div>

        <div class="property-item">
          <label>后缀:</label>
          <SafeInput
            bind:value={model.suffix}
            placeholder="后缀"
          />
        </div>
      </PropertyContainer>
    </AccordionPanel>
  </div>

  <div class="model-display">
    <h4>当前模型数据：</h4>
    <pre>{JSON.stringify(model, null, 2)}</pre>
  </div>
</div>

<style>
  .example-container {
    padding: 16px;
    max-width: 800px;
    margin: 0 auto;
  }

  .panels-container {
    margin-bottom: 24px;
  }

  .model-display {
    background: var(--theme-surface-light, #f8f9fa);
    padding: 16px;
    border-radius: 4px;
    border: 1px solid var(--theme-border, #e2e8f0);
  }

  .model-display pre {
    margin: 0;
    font-size: 12px;
    color: var(--theme-text, #1a202c);
  }

  h3, h4 {
    color: var(--theme-text, #1a202c);
    margin-bottom: 16px;
  }

  .property-item {
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 28px;
  }

  .property-item label {
    font-size: var(--component-font-size, 11px);
    font-family: var(--component-font-family, inherit);
    color: var(--component-text, #1a202c);
    font-weight: 500;
    flex-shrink: 0;
    min-width: 60px;
  }
</style>
