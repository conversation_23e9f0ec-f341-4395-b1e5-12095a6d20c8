/**
 * Window_HorzCommand 创建器
 * 专门用于创建水平命令窗口
 */

import { BaseWindowCreator, type WindowCreationOptions } from './WindowCreator';
import { type CommandWindowOptions } from './WindowCommandCreator';

/**
 * 水平命令窗口创建选项
 */
export interface HorzCommandWindowOptions extends CommandWindowOptions {
    /** 每行显示的命令数 */
    maxCols?: number;
}

/**
 * 创建水平命令窗口
 * @param options 创建选项
 * @returns 创建的水平命令窗口实例
 */
export async function createWindowHorzCommand(options: HorzCommandWindowOptions = {}): Promise<any> {
    console.log('=== 创建水平命令窗口 Window_HorzCommand ===');
    
    try {
        // 预加载资源
        BaseWindowCreator.preloadWindowResources('Window_HorzCommand');
        
        // 设置默认选项
        const defaultOptions: WindowCreationOptions = {
            autoOpen: true,
            addToStage: true,
            rect: options.rect || { x: 0, y: 0, width: 400, height: 80 },
            ...options
        };
        
        // 创建窗口实例
        const window = await BaseWindowCreator.createWindowInstance('Window_HorzCommand', defaultOptions);
        
        // Window_HorzCommand 特定的设置
        setupHorzCommandWindow(window, options);
        
        console.log('Window_HorzCommand 创建完成，窗口属性:', {
            x: window.x,
            y: window.y,
            width: window.width,
            height: window.height,
            maxItems: window.maxItems(),
            maxCols: window.maxCols(),
            index: window.index(),
            active: window.active,
            visible: window.visible
        });
        
        return window;
        
    } catch (error) {
        console.error('创建 Window_HorzCommand 失败:', error);
        throw error;
    }
}

/**
 * 设置水平命令窗口属性
 * @param window 窗口实例
 * @param options 水平命令窗口选项
 */
function setupHorzCommandWindow(window: any, options: HorzCommandWindowOptions): void {
    console.log('设置水平命令窗口属性...');
    
    try {
        // 添加命令项目
        if (options.commands && options.commands.length > 0) {
            // 清除现有命令列表
            if (window.clearCommandList && typeof window.clearCommandList === 'function') {
                window.clearCommandList();
            }
            
            // 添加新命令
            options.commands.forEach((command, index) => {
                if (window.addCommand && typeof window.addCommand === 'function') {
                    window.addCommand(
                        command.name,
                        command.symbol,
                        command.enabled !== false,
                        command.ext || null
                    );
                    console.log(`添加水平命令 ${index + 1}:`, command);
                }
            });
            
            // 刷新窗口
            if (options.autoRefresh !== false && window.refresh && typeof window.refresh === 'function') {
                window.refresh();
                console.log('水平命令窗口已刷新');
            }
        }
        
        // 设置最大列数（水平命令窗口的特色）
        if (options.maxCols !== undefined) {
            // 通常需要重写 maxCols 方法
            console.log('设置水平命令窗口最大列数:', options.maxCols);
        }
        
        // 设置初始选中索引
        if (options.initialIndex !== undefined && window.select && typeof window.select === 'function') {
            window.select(options.initialIndex);
            console.log('设置初始选中索引:', options.initialIndex);
        }
        
        // 设置激活状态
        if (options.activate !== undefined) {
            if (options.activate && window.activate && typeof window.activate === 'function') {
                window.activate();
                console.log('水平命令窗口已激活');
            } else if (!options.activate && window.deactivate && typeof window.deactivate === 'function') {
                window.deactivate();
                console.log('水平命令窗口已取消激活');
            }
        }
        
        console.log('水平命令窗口属性设置完成');
        
    } catch (error) {
        console.error('设置水平命令窗口属性失败:', error);
    }
}

/**
 * 创建并激活水平命令窗口
 * @param options 创建选项
 * @returns 创建的水平命令窗口实例
 */
export async function createAndActivateWindowHorzCommand(options: HorzCommandWindowOptions = {}): Promise<any> {
    console.log('=== 创建并激活 Window_HorzCommand ===');
    
    const window = await createWindowHorzCommand({
        ...options,
        autoOpen: true,
        activate: true
    });
    
    console.log('Window_HorzCommand 已创建并激活');
    return window;
}

/**
 * 创建简单的水平命令窗口（用于测试）
 * @returns 创建的水平命令窗口实例
 */
export async function createSimpleWindowHorzCommand(): Promise<any> {
    console.log('=== 创建简单水平命令窗口 ===');
    
    try {
        const window = await createWindowHorzCommand({
            rect: { x: 100, y: 50, width: 500, height: 80 },
            autoOpen: true,
            addToStage: true,
            visible: true,
            initialIndex: 0,
            activate: true,
            maxCols: 4,
            commands: [
                { name: '物品', symbol: 'item', enabled: true },
                { name: '技能', symbol: 'skill', enabled: true },
                { name: '装备', symbol: 'equip', enabled: true },
                { name: '状态', symbol: 'status', enabled: true }
            ],
            autoRefresh: true
        });
        
        console.log('简单水平命令窗口创建成功');
        return window;
        
    } catch (error) {
        console.error('创建简单水平命令窗口失败:', error);
        throw error;
    }
}
