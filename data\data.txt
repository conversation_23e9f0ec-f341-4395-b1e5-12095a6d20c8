// 数据文件加载后存储在全局变量中
window.$dataActors     // 角色数据
window.$dataClasses    // 职业数据
window.$dataSkills     // 技能数据
window.$dataItems      // 物品数据
window.$dataWeapons    // 武器数据
window.$dataArmors     // 防具数据
window.$dataEnemies    // 敌人数据
window.$dataTroops     // 敌群数据
window.$dataStates     // 状态数据
window.$dataAnimations // 动画数据
window.$dataTilesets   // 图块集数据
window.$dataCommonEvents // 公共事件数据
window.$dataSystem     // 系统数据
window.$dataMapInfos   // 地图信息数据
window.$dataMap        // 当前地图数据


// 直接访问全局变量
console.log($dataActors[1]);        // 获取ID为1的角色数据
console.log($dataSystem.gameTitle); // 获取游戏标题
console.log($dataMap.events);       // 获取当前地图的事件

--------------------------------------------------------------------------------- 

ImageManager._cache = {};   // 普通图片缓存
ImageManager._system = {};  // 系统图片缓存（UI相关）

// 加载图片（返回 Bitmap 对象）
const characterBitmap = ImageManager.loadCharacter("Actor1");
const faceBitmap = ImageManager.loadFace("Actor1");
const systemBitmap = ImageManager.loadSystem("Window");

// 检查图片是否加载完成
if (characterBitmap.isReady()) {
    // 图片已加载完成，可以使用
    const sprite = new Sprite(characterBitmap);
}

// 添加加载完成监听器
characterBitmap.addLoadListener(() => {
    console.log("图片加载完成");
    // 在这里使用图片
});

--------------------------------------------------------------------------------- 
AudioManager._bgmBuffer = null;      // BGM缓冲区
AudioManager._bgsBuffer = null;      // BGS缓冲区
AudioManager._meBuffer = null;       // ME缓冲区
AudioManager._seBuffers = [];        // SE缓冲区数组
AudioManager._staticBuffers = [];    // 静态SE缓冲区数组

// 播放BGM
AudioManager.playBgm({name: "Theme1", volume: 90, pitch: 100, pan: 0});

// 播放SE
AudioManager.playSe({name: "Cursor1", volume: 90, pitch: 100, pan: 0});

// 创建音频缓冲区
const buffer = AudioManager.createBuffer("bgm/", "Theme1");

EffectManager._cache = {};  // 特效缓存
// 加载特效
const effect = EffectManager.load("Skill01");

// 检查特效是否准备就绪
if (EffectManager.isReady()) {
    // 所有特效已加载完成
}

--------------------------------------------------------------------------------- 

window.$gameTemp      // 临时数据
window.$gameSystem    // 系统数据
window.$gameScreen    // 画面数据
window.$gameTimer     // 计时器
window.$gameMessage   // 消息
window.$gameSwitches  // 开关
window.$gameVariables // 变量
window.$gameSelfSwitches // 独立开关
window.$gameActors    // 角色
window.$gameParty     // 队伍
window.$gameTroop     // 敌群
window.$gameMap       // 地图
window.$gamePlayer    // 玩家

// 1. 检查资源是否加载完成
function checkResourcesReady() {
    return DataManager.isDatabaseLoaded() && 
           ImageManager.isReady() && 
           EffectManager.isReady();
}

// 2. 创建角色精灵
function createActorSprite(actorId) {
    const actor = $dataActors[actorId];
    const characterName = actor.characterName;
    const bitmap = ImageManager.loadCharacter(characterName);
    
    const sprite = new Sprite(bitmap);
    if (bitmap.isReady()) {
        // 立即设置
        setupSprite(sprite, actor);
    } else {
        // 等待加载完成
        bitmap.addLoadListener(() => {
            setupSprite(sprite, actor);
        });
    }
    return sprite;
}

// 3. 播放技能动画
function playSkillAnimation(skillId, target) {
    const skill = $dataSkills[skillId];
    const animationId = skill.animationId;
    
    if (animationId > 0) {
        const animation = $dataAnimations[animationId];
        // 播放动画逻辑
        target.startAnimation(animation);
    }
}

// 4. 访问地图数据
function getMapEvent(eventId) {
    if ($dataMap && $dataMap.events) {
        return $dataMap.events[eventId];
    }
    return null;
}

// 1. 检查资源是否加载完成
function checkResourcesReady() {
    return DataManager.isDatabaseLoaded() && 
           ImageManager.isReady() && 
           EffectManager.isReady();
}

// 2. 创建角色精灵
function createActorSprite(actorId) {
    const actor = $dataActors[actorId];
    const characterName = actor.characterName;
    const bitmap = ImageManager.loadCharacter(characterName);
    
    const sprite = new Sprite(bitmap);
    if (bitmap.isReady()) {
        // 立即设置
        setupSprite(sprite, actor);
    } else {
        // 等待加载完成
        bitmap.addLoadListener(() => {
            setupSprite(sprite, actor);
        });
    }
    return sprite;
}

// 3. 播放技能动画
function playSkillAnimation(skillId, target) {
    const skill = $dataSkills[skillId];
    const animationId = skill.animationId;
    
    if (animationId > 0) {
        const animation = $dataAnimations[animationId];
        // 播放动画逻辑
        target.startAnimation(animation);
    }
}

// 4. 访问地图数据
function getMapEvent(eventId) {
    if ($dataMap && $dataMap.events) {
        return $dataMap.events[eventId];
    }
    return null;
}