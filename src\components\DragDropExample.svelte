<script lang="ts">
  /**
   * 拖拽示例组件 - 演示如何使用
   */
  import SimpleObjectTreeNode from '../left/objectTree/SimpleObjectTreeNode.svelte';
  import ObjectDropTarget from './ObjectDropTarget.svelte';
  import DragDropTest from './drop/DragDropTest.svelte';
  import type { BaseObjectModel } from '../type/baseObjectModel.svelte';

  // 示例数据
  let mockObjects: BaseObjectModel[] = [
    { className: 'Sprite', name: 'Player', x: 100, y: 200, children: [] },
    { className: 'Container', name: 'UI', x: 50, y: 50, children: [] },
    { className: 'Bitmap', name: 'Background', x: 0, y: 0, children: [] }
  ];

  let droppedObject = $state<BaseObjectModel | null>(null);

  // 处理拖拽开始
  function handleDragStart(node: BaseObjectModel) {
    console.log('开始拖拽:', node.className);
  }

  // 处理对象放置
  function handleObjectDrop(object: BaseObjectModel) {
    droppedObject = object;
    console.log('接收到对象:', object);
  }
</script>

<div class="demo-container">
  <h3>拖拽演示</h3>

  <!-- 简单测试 -->
  <DragDropTest />

  <div class="demo-layout">
    <!-- 左侧：对象树 -->
    <div class="object-tree">
      <h4>对象列表（可拖拽）</h4>
      {#each mockObjects as object}
        <SimpleObjectTreeNode
          node={object}
          ondragstart={handleDragStart}
        />
      {/each}
    </div>

    <!-- 右侧：拖拽目标 -->
    <div class="drop-area">
      <h4>拖拽目标区域</h4>
      <ObjectDropTarget
        onDrop={handleObjectDrop}
        placeholder="将左侧对象拖拽到此处"
      />

      {#if droppedObject}
        <div class="result">
          <h5>接收到的对象:</h5>
          <p><strong>类型:</strong> {droppedObject.className}</p>
          <p><strong>名称:</strong> {droppedObject.name || '无'}</p>
          <p><strong>位置:</strong> ({droppedObject.x}, {droppedObject.y})</p>
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .demo-container {
    padding: 20px;
    max-width: 800px;
  }

  .demo-layout {
    display: flex;
    gap: 20px;
    margin-top: 10px;
  }

  .object-tree {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
  }

  .drop-area {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
  }

  h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
  }

  h5 {
    margin: 15px 0 5px 0;
    color: #333;
    font-size: 12px;
  }

  .result {
    margin-top: 15px;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 4px;
    font-size: 12px;
  }

  .result p {
    margin: 2px 0;
  }
</style>
