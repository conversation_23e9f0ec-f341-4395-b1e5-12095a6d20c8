<script lang="ts">
  // 底部面板组件
  let activeBottomTab = $state('status');
  
  const bottomTabs = [
    { id: 'status', name: '状态栏', icon: '📊' },
    { id: 'output', name: '输出', icon: '📤' },
    { id: 'terminal', name: '终端', icon: '💻' }
  ];

  const statusItems = [
    { label: '行', value: '1' },
    { label: '列', value: '1' },
    { label: '选择', value: '0' },
    { label: '编码', value: 'UTF-8' },
    { label: '语言', value: 'JavaScript' }
  ];

  function switchBottomTab(tabId: string) {
    activeBottomTab = tabId;
  }
</script>

<div class="bottom-panel">
  <div class="header">
    <div class="tabs">
      {#each bottomTabs as tab}
        <button 
          class="tab" 
          class:active={activeBottomTab === tab.id}
          onclick={() => switchBottomTab(tab.id)}
        >
          <span class="icon">{tab.icon}</span>
          <span>{tab.name}</span>
        </button>
      {/each}
    </div>
  </div>
  
  <div class="content">
    {#if activeBottomTab === 'status'}
      <div class="status-area">
        <div class="status-items">
          {#each statusItems as item}
            <div class="status-item">
              <span class="label">{item.label}:</span>
              <span class="value">{item.value}</span>
            </div>
          {/each}
        </div>
        <div class="status-right">
          <span class="ready-indicator">● 就绪</span>
        </div>
      </div>
    {:else if activeBottomTab === 'output'}
      <div class="output-area">
        <div class="output-content">
          <div class="output-line">[2024-01-20 10:30:15] 构建开始...</div>
          <div class="output-line">[2024-01-20 10:30:16] 编译文件: main.js</div>
          <div class="output-line">[2024-01-20 10:30:17] 构建完成 ✓</div>
          <div class="output-line success">[2024-01-20 10:30:17] 构建成功！</div>
        </div>
      </div>
    {:else if activeBottomTab === 'terminal'}
      <div class="terminal-area">
        <div class="terminal-content">
          <div class="terminal-line">$ npm run dev</div>
          <div class="terminal-line">  Local:   http://localhost:5173/</div>
          <div class="terminal-line">  Network: use --host to expose</div>
          <div class="terminal-line">  ready in 234ms.</div>
          <div class="terminal-line current">$ <span class="cursor">|</span></div>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .bottom-panel {
    height: 100%;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
    display: flex;
    flex-direction: column;
  }

  .header {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .tabs {
    display: flex;
    gap: 0.5rem;
  }

  .tab {
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .tab:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  .tab.active {
    background: rgba(255, 255, 255, 0.7);
    border-bottom-color: transparent;
  }

  .content {
    flex: 1;
    padding: 1rem;
    overflow: auto;
  }

  .status-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .status-items {
    display: flex;
    gap: 1rem;
  }

  .status-item {
    display: flex;
    gap: 0.25rem;
    font-size: 0.9rem;
  }

  .label {
    font-weight: 500;
    opacity: 0.7;
  }

  .value {
    font-weight: 600;
  }

  .ready-indicator {
    color: #22c55e;
    font-weight: 600;
    font-size: 0.9rem;
  }

  .output-content,
  .terminal-content {
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .output-line,
  .terminal-line {
    margin-bottom: 0.25rem;
    opacity: 0.8;
  }

  .output-line.success {
    color: #22c55e;
    font-weight: 600;
  }

  .terminal-line.current {
    opacity: 1;
    font-weight: 600;
  }

  .cursor {
    animation: blink 1s infinite;
  }

  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
</style>
