# LabelSpriteEditor 复制粘贴功能

## 功能概述

LabelSpriteEditor 现在支持对左侧元素列表中的文本和图片元素进行复制粘贴操作。

## 使用方法

### 1. 键盘快捷键

- **Ctrl+C**: 复制当前选中的元素
- **Ctrl+V**: 粘贴已复制的元素

### 2. 鼠标操作

#### 复制按钮
- 每个元素右侧都有一个 📋 复制按钮
- 点击即可复制该元素

#### 粘贴按钮
- 面板底部有一个 "📋 粘贴" 按钮
- 只有在有已复制元素时才可用
- 显示已复制元素的类型（文本/图片）

#### 右键菜单
- 右键点击任意元素可打开上下文菜单
- 菜单包含"复制"和"粘贴"选项
- 粘贴选项只有在有已复制元素时才可用

### 3. 视觉反馈

- 当有元素被复制到剪贴板时，元素列表会显示绿色左边框
- 粘贴按钮会显示已复制元素的类型信息
- 面板底部显示快捷键提示

## 功能特点

### 智能位置调整
- 粘贴的元素会自动偏移位置（+20px），避免与原元素重叠
- 文本元素调整 x, y 坐标
- 图片元素调整 dx, dy 坐标

### 唯一ID生成
- 每个粘贴的元素都会获得新的唯一ID
- 避免ID冲突问题

### 跨类型支持
- 支持复制粘贴文本元素
- 支持复制粘贴图片元素
- 保持元素的所有属性

## 技术实现

### Store 集成
- 使用 bitmapActions.addElement() 添加新元素
- 与现有的状态管理系统完全集成
- 支持撤销/重做（如果实现）

### 事件处理
- 全局键盘事件监听
- 防止事件冲突和冒泡
- 正确的组件生命周期管理

### 无障碍支持
- 键盘快捷键支持
- 适当的 ARIA 标签
- 清晰的视觉反馈

## 使用示例

1. **复制文本元素**:
   - 选中一个文本元素
   - 按 Ctrl+C 或点击复制按钮
   - 按 Ctrl+V 或点击粘贴按钮
   - 新的文本元素会出现在稍微偏移的位置

2. **复制图片元素**:
   - 选中一个图片元素
   - 使用相同的复制粘贴操作
   - 图片的所有属性（源、裁剪区域等）都会被保留

3. **批量复制**:
   - 可以多次粘贴同一个已复制的元素
   - 每次粘贴都会创建新的独立元素

## 注意事项

- 剪贴板内容在组件重新加载时会丢失
- 复制的是元素的深拷贝，修改原元素不会影响已复制的内容
- 粘贴操作会自动选中新创建的元素
