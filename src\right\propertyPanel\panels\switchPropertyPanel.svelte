<script lang="ts">
  /**
   * 响应式开关属性面板 - Switch组件专用
   * 直接使用响应式模型对象，无需额外的函数
   */

  import {
    sceneModelState
  } from '../../../stores/sceneModelStore';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import DropTarget from '../../../components/drop/DropTarget.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import LabelInput from '../../../components/LabelInput.svelte';
  import EventSelectionModal from '../../../modals/EventSelectionModal.svelte';
  import type { SwitchModel } from '../../../type/ui/switchModel.svelte';
  import type { BaseObjectModel } from '../../../type/baseObjectModel.svelte';

  // 监听选中的对象 - 使用第一个选中的对象
  let currentState = $derived($sceneModelState);
  let switchObj = $derived(currentState.selectedObjects[0] as SwitchModel | null);

  // 面板展开状态
  let isExpanded = $state(true);

  // 事件配置状态
  let selectedEventType = $state('onChange'); // 当前选择的事件类型
  let showEventModal = $state(false); // 是否显示事件选择弹窗

  // 事件类型选项
  const eventTypes = [
    { value: 'onChange', label: '状态变化事件' },
    { value: 'onToggle', label: '切换事件' }
  ];

  // 获取绑定信息
  let bindingInfo = $derived(() => {
    if (!switchObj) return null;
    return switchObj.getBindingInfo();
  });

  // ==================== 🔗 组件绑定处理 ====================

  /**
   * 处理背景轨道拖拽绑定
   */
  function handleBackgroundDrop(droppedObject: BaseObjectModel) {
    if (!switchObj) return;

    console.log('🔘 Switch面板: 尝试绑定背景轨道', droppedObject.className);

    // 检查是否为UIImage类型
    if (droppedObject.className !== 'UIImage') {
      console.warn('🚨 Switch面板: 背景轨道只能绑定UIImage对象');
      return;
    }

    // 获取原始对象进行绑定
    const originalObject = droppedObject.getOriginalObject();
    switchObj.boundBackgroundSprite = originalObject;

    console.log('✅ Switch面板: 背景轨道绑定成功');
  }

  /**
   * 处理滑块按钮拖拽绑定
   */
  function handleKnobDrop(droppedObject: BaseObjectModel) {
    if (!switchObj) return;

    console.log('🔘 Switch面板: 尝试绑定滑块按钮', droppedObject.className);

    // 检查是否为UIImage类型
    if (droppedObject.className !== 'UIImage') {
      console.warn('🚨 Switch面板: 滑块按钮只能绑定UIImage对象');
      return;
    }

    // 获取原始对象进行绑定
    const originalObject = droppedObject.getOriginalObject();
    switchObj.boundKnobSprite = originalObject;

    console.log('✅ Switch面板: 滑块按钮绑定成功');
  }

  // ==================== 🔓 解除绑定处理 ====================

  /**
   * 解除背景轨道绑定
   */
  function unbindBackground() {
    if (!switchObj) return;
    switchObj.boundBackgroundSprite = null;
    console.log('🔘 Switch面板: 背景轨道绑定已解除');
  }

  /**
   * 解除滑块按钮绑定
   */
  function unbindKnob() {
    if (!switchObj) return;
    switchObj.boundKnobSprite = null;
    console.log('🔘 Switch面板: 滑块按钮绑定已解除');
  }

  // ==================== ⚡ 事件管理 ====================

  /**
   * 打开事件选择弹窗
   */
  function openEventModal() {
    showEventModal = true;
  }

  /**
   * 关闭事件选择弹窗
   */
  function closeEventModal() {
    showEventModal = false;
  }

  /**
   * 处理事件选择
   */
  function handleEventSelected(event: CustomEvent) {
    if (!switchObj) return;

    const selectedEvent = event.detail;
    if (selectedEvent && selectedEvent.code) {
      // 根据当前选择的事件类型设置对应的事件代码
      switch (selectedEventType) {
        case 'onChange':
          switchObj.onChangeCode = selectedEvent.code;
          break;
        case 'onToggle':
          switchObj.onToggleCode = selectedEvent.code;
          break;
      }
      console.log('🔘 Switch面板: 设置事件代码', {
        eventType: selectedEventType,
        code: selectedEvent.code
      });
    }
    closeEventModal();
  }

  /**
   * 获取当前选择事件类型的代码
   */
  function getCurrentEventCode(): string {
    if (!switchObj) return '';

    switch (selectedEventType) {
      case 'onChange': return switchObj.onChangeCode || '';
      case 'onToggle': return switchObj.onToggleCode || '';
      default: return '';
    }
  }


</script>

{#if switchObj}
  <AccordionPanel
    title="开关属性"
    icon="🔘"
    badge="Switch"
    badgeVariant="info"
    bind:expanded={isExpanded}
  >
    <!-- 状态设置 -->
    <PropertyContainer>
        <Label text="启用状态:" />
        <Checkbox
          checked={switchObj.enabled}
          targetObject={switchObj}
          fieldName="enabled"
          enableHistory={true}
          name="启用状态"
          onChange={(checked) => {
            console.log("🔧 Switch面板: 启用状态变化", {from: switchObj.enabled, to: checked});
            switchObj.enabled = checked;
          }}
        />
        <Label text={switchObj.enabled ? '启用' : '禁用'} size="sm" variant="secondary" />
      </PropertyContainer>

      <PropertyContainer>
        <Label text="动画时长:" />
        <LabelInput
          bind:value={switchObj.animationDuration}
          type="number"
          min={0}
          max={2000}
          step={50}
          targetObject={switchObj}
          fieldName="animationDuration"
          enableHistory={true}
          name="动画时长"
        />
        <Label text="ms" size="sm" variant="secondary" />
      </PropertyContainer>

    <!-- 🔑 子组件绑定状态 -->
    <div class="property-section">
      <h4>🔗 子组件绑定</h4>

      <!-- 背景轨道绑定 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">背景轨道 (UIImage) - 开关外观:</span>
          <span class="binding-status-badge {bindingInfo()?.hasBackground ? 'bound' : 'unbound'}">
            {bindingInfo()?.hasBackground ? '已绑定' : '未绑定'}
          </span>
          {#if bindingInfo()?.hasBackground}
            <button class="unbind-btn" onclick={unbindBackground} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if bindingInfo()?.hasBackground}
          <div class="bound-object-info">
            已绑定: {bindingInfo()?.backgroundSprite?.className || '未知对象'}
          </div>
        {:else}
          <DropTarget
            onDrop={handleBackgroundDrop}
            placeholder="拖拽 UIImage 对象到此处绑定为背景轨道"
            targetObject={switchObj}
            fieldName="boundBackgroundSprite"
            enableHistory={true}
            operationName="绑定开关背景轨道"
          />
        {/if}
      </div>

      <!-- 滑块按钮绑定 -->
      <div class="binding-item">
        <div class="binding-header">
          <span class="binding-label">滑块按钮 (UIImage) - 可移动按钮:</span>
          <span class="binding-status-badge {bindingInfo()?.hasKnob ? 'bound' : 'unbound'}">
            {bindingInfo()?.hasKnob ? '已绑定' : '未绑定'}
          </span>
          {#if bindingInfo()?.hasKnob}
            <button class="unbind-btn" onclick={unbindKnob} title="解除绑定">✕</button>
          {/if}
        </div>

        {#if bindingInfo()?.hasKnob}
          <div class="bound-object-info">
            已绑定: {bindingInfo()?.knobSprite?.className || '未知对象'}
          </div>
        {:else}
          <DropTarget
            onDrop={handleKnobDrop}
            placeholder="拖拽 UIImage 对象到此处绑定为滑块按钮"
            targetObject={switchObj}
            fieldName="boundKnobSprite"
            enableHistory={true}
            operationName="绑定开关滑块按钮"
          />
        {/if}
      </div>
    </div>

    <!-- 事件配置 -->
    <div class="property-section">
      <h4>⚡ 事件配置</h4>

      <!-- 事件类型选择 -->
      <div class="event-config-row">
        <label for="event-type-select">事件类型:</label>
        <select
          id="event-type-select"
          bind:value={selectedEventType}
          class="event-type-select"
        >
          {#each eventTypes as eventType}
            <option value={eventType.value}>{eventType.label}</option>
          {/each}
        </select>
      </div>

      <!-- 当前事件代码显示和选择按钮 -->
      <div class="event-config-row">
        <div class="event-code-label">当前事件代码:</div>
        <div class="event-code-display">
          <div class="event-code-text">
            {getCurrentEventCode() || '未设置事件'}
          </div>
          <button
            class="event-select-btn"
            onclick={openEventModal}
            title="选择事件"
          >
            📝 选择事件
          </button>
        </div>
      </div>
    </div>



    <!-- 绑定状态总览 -->
    <div class="property-section">
      <h4>📊 绑定状态总览</h4>

      <div class="binding-summary">
        <div class="summary-item">
          <span class="summary-label">背景轨道:</span>
          <span class="summary-status {bindingInfo()?.hasBackground ? 'ready' : 'missing'}">
            {bindingInfo()?.hasBackground ? '✅ 已绑定' : '❌ 未绑定'}
          </span>
        </div>
        <div class="summary-item">
          <span class="summary-label">滑块按钮:</span>
          <span class="summary-status {bindingInfo()?.hasKnob ? 'ready' : 'missing'}">
            {bindingInfo()?.hasKnob ? '✅ 已绑定' : '❌ 未绑定'}
          </span>
        </div>
        <div class="summary-item">
          <span class="summary-label">完整性:</span>
          <span class="summary-status {bindingInfo()?.hasBackground && bindingInfo()?.hasKnob ? 'ready' : 'incomplete'}">
            {bindingInfo()?.hasBackground && bindingInfo()?.hasKnob ? '✅ 完整' : '⚠️ 不完整'}
          </span>
        </div>
      </div>

      {#if !bindingInfo()?.hasBackground || !bindingInfo()?.hasKnob}
        <div class="binding-hint">
          💡 提示: 开关需要绑定背景轨道和滑块按钮才能正常工作
        </div>
      {/if}
    </div>
  </AccordionPanel>
{/if}

<!-- 事件选择弹窗 -->
{#if showEventModal}
  <EventSelectionModal
    onEventSelected={handleEventSelected}
    onClose={closeEventModal}
  />
{/if}

<style>
  /* 基础样式 */
  .property-section {
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 4px;
    padding: 8px;
    border: 1px solid var(--theme-border-light, #e9ecef);
    margin-bottom: 8px;
  }

  .property-section h4 {
    margin: 0 0 8px 0;
    font-size: 11px;
    font-weight: 600;
    color: var(--theme-text, #1a202c);
  }

  .property-row {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 6px;
  }

  .property-row:last-child {
    margin-bottom: 0;
  }

  .property-item-horizontal {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
  }

  .info-label {
    font-size: 10px;
    font-weight: 500;
    color: var(--theme-text-secondary, #718096);
    white-space: nowrap;
    min-width: 60px;
    text-align: right;
  }

  /* 输入控件样式 */

  .checkbox-label {
    font-size: 10px;
    color: var(--theme-text-secondary, #718096);
  }

  .unit-label {
    font-size: 9px;
    color: var(--theme-text-secondary, #718096);
  }

  /* 绑定相关样式 */
  .binding-item {
    margin-bottom: 12px;
    padding: 8px;
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 4px;
  }

  .binding-item:last-child {
    margin-bottom: 0;
  }

  .binding-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
  }

  .binding-label {
    font-size: 10px;
    font-weight: 500;
    color: var(--theme-text, #1a202c);
    flex: 1;
  }

  .binding-status-badge {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .binding-status-badge.bound {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .binding-status-badge.unbound {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
  }

  .unbind-btn {
    width: 16px;
    height: 16px;
    border: none;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    font-size: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .unbind-btn:hover {
    background: #c82333;
    transform: scale(1.1);
  }

  .bound-object-info {
    font-size: 9px;
    color: var(--theme-text-secondary, #718096);
    padding: 4px 8px;
    background: var(--theme-surface-variant, #f5f5f5);
    border-radius: 3px;
    border: 1px solid var(--theme-border-light, #e9ecef);
  }

  /* 事件配置样式 */
  .event-config-row {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 8px;
  }

  .event-config-row label {
    font-size: 10px;
    font-weight: 500;
    color: var(--theme-text-secondary, #718096);
    white-space: nowrap;
    min-width: 60px;
  }

  .event-type-select {
    padding: 4px 6px;
    font-size: 11px;
    border: 1px solid var(--theme-border, #e2e8f0);
    border-radius: 3px;
    background: var(--theme-surface, #ffffff);
    color: var(--theme-text, #1a202c);
    cursor: pointer;
  }

  .event-type-select:focus {
    outline: none;
    border-color: var(--theme-primary, #3b82f6);
    box-shadow: 0 0 0 2px var(--theme-primary-light, #dbeafe);
  }

  .event-code-label {
    font-size: 10px;
    font-weight: 500;
    color: var(--theme-text-secondary, #718096);
    white-space: nowrap;
    min-width: 60px;
  }

  .event-code-display {
    display: flex;
    gap: 8px;
    align-items: flex-start;
    flex: 1;
  }

  .event-code-text {
    flex: 1;
    padding: 6px 8px;
    font-family: monospace;
    font-size: 10px;
    background: var(--theme-surface-tertiary, #323e4d);
    border: 1px solid var(--theme-border, #42556d);
    border-radius: 3px;
    color: var(--theme-text, #374151);
    min-height: 20px;
    word-break: break-all;
  }

  .event-select-btn {
    padding: 6px 12px;
    font-size: 11px;
    background: var(--theme-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    flex-shrink: 0;
    white-space: nowrap;
  }

  .event-select-btn:hover {
    background: var(--theme-primary-dark, #2563eb);
  }



  /* 绑定状态总览样式 */
  .binding-summary {
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 4px;
    padding: 8px;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2px 0;
    font-size: 10px;
  }

  .summary-label {
    color: var(--theme-text-secondary, #718096);
    font-weight: 500;
  }

  .summary-status {
    font-weight: 600;
  }

  .summary-status.ready {
    color: #28a745;
  }

  .summary-status.missing {
    color: #dc3545;
  }

  .summary-status.incomplete {
    color: #ffc107;
  }

  .binding-hint {
    margin-top: 8px;
    padding: 6px 8px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    font-size: 9px;
    color: #856404;
    line-height: 1.3;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .property-row {
      flex-direction: column;
      align-items: stretch;
      gap: 4px;
    }

    .info-label {
      text-align: left;
      min-width: auto;
    }


  }
</style>
