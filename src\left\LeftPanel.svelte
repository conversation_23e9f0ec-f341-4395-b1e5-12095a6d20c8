<script lang="ts">
  import SimpleObjectTreePanel from './objectTree/SimpleObjectTreePanel.svelte';

  // 左侧面板组件 - 使用响应式对象树面板
  console.log('LeftPanel 组件已加载 - 响应式对象树版本');
</script>

<div class="left-panel">
  <SimpleObjectTreePanel />
</div>

<style>
  .left-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-background);
    overflow: hidden;
  }

  /* 确保对象树面板占满整个左侧面板 */
  .left-panel :global(.object-tree-panel) {
    height: 100%;
    flex: 1;
  }
</style>
