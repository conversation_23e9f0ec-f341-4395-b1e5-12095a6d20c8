/**
 * 基础类型定义
 * 所有显示对象的基础属性
 */

/**
 * 基础显示对象属性 (PIXI.DisplayObject)
 */
export interface BaseDisplayProperties {
  className: string;               // 对象类名
  name: string;
  // 位置和变换
  x: number;
  y: number;
  scaleX: number;    // scale.x
  scaleY: number;    // scale.y
  skewX: number;     // skew.x
  skewY: number;     // skew.y
  rotation: number;
  width: number;                  // 宽度
  height: number;                 // 高度
  // 显示属性
  alpha: number;
  visible: boolean;

  // 锚点和轴心
  anchorX: number;   // anchor.x
  anchorY: number;   // anchor.y
  pivotX: number;    // pivot.x
  pivotY: number;    // pivot.y
}

/**
 * 场景分析结果
 */
export interface SceneAnalysisResult {
  // 场景基本信息
  sceneClassName: string;
  //  路径-> 对象
  uiObjects: Map<string, BaseDisplayProperties>;     // WindowLayer 数据（独立结构）
}

/**
 * 操作记录数据接口
 */
export interface OperationRecordsData {
  version: string;
  timestamp: number;
  author: string;
  records: Record<string, SceneAnalysisResult>;
}

/**
 * 插件生成配置
 */
export interface PluginGenerationConfig {
  pluginName?: string;
  pluginDescription?: string;
  author?: string;
  version?: string;
}
