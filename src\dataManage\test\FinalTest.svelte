<script lang="ts">
  import DataBindingInput from '../components/DataBindingInput.svelte';

  let textValue = $state('');

  function handleTextChange(newValue: string) {
    textValue = newValue;
    console.log('文本变化:', newValue);
  }
</script>

<div class="test-container">
  <h2>最终测试 - DataBindingInput</h2>
  
  <div class="test-section">
    <h3>数据绑定输入组件</h3>
    <DataBindingInput 
      value={textValue}
      onChange={handleTextChange}
      placeholder="输入文本或选择数据绑定"
      showPreview={true}
    />
  </div>

  <div class="result-section">
    <h3>当前值</h3>
    <div class="result-display">
      <strong>文本内容:</strong>
      <pre>{textValue || '(空)'}</pre>
    </div>
  </div>

  <div class="instructions">
    <h3>测试步骤</h3>
    <ol>
      <li>点击"数据绑定"下拉框</li>
      <li>选择一个数据类型（如：角色数据）</li>
      <li>选择一个字段（如：名称）</li>
      <li>点击"应用绑定"按钮</li>
      <li>查看文本框中是否出现绑定表达式</li>
      <li>检查是否有预览效果</li>
    </ol>
    
    <h4>预期结果：</h4>
    <ul>
      <li>下拉框显示友好的文本（如"名称"而不是代码）</li>
      <li>支持选择嵌套字段（如"参数 → 0"）</li>
      <li>生成正确的绑定表达式（如"{{actors.name}}"）</li>
    </ul>
  </div>
</div>

<style>
  .test-container {
    max-width: 700px;
    margin: 20px auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .test-section, .result-section, .instructions {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
  }

  .test-section h3, .result-section h3, .instructions h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #333;
  }

  .result-display {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }

  .result-display strong {
    color: #495057;
  }

  .result-display pre {
    margin: 8px 0 0 0;
    padding: 8px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    font-family: monospace;
    font-size: 14px;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .instructions ol, .instructions ul {
    margin: 0;
    padding-left: 20px;
  }

  .instructions li {
    margin-bottom: 8px;
    line-height: 1.5;
  }

  .instructions h4 {
    margin-top: 16px;
    margin-bottom: 8px;
    color: #333;
  }
</style>
