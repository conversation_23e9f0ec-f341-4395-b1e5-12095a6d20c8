/**
 * Game_Follower 创建器
 * 专门用于创建RPG Maker MZ跟随者角色
 */

import { objManage } from '../../objManage';

declare global {
    interface Window {
        Game_Follower: any;
        Sprite_Character: any;
        Graphics: any;
        ImageManager: any;
        $gamePlayer: any;
        $dataActors: any[];
        $gameParty: any;
    }
}

/**
 * 创建跟随者角色精灵
 * @param options 创建选项
 * @returns 创建的跟随者精灵对象
 */
export async function createFollower(options: {
    memberIndex?: number; // 队伍成员索引 (0-2)
    actorId?: number;
    characterName?: string;
    characterIndex?: number;
    direction?: number;
    x?: number;
    y?: number;
} = {}): Promise<any> {
    console.log('=== 创建 Game_Follower 精灵 ===');
    console.log('创建选项:', options);

    try {
        // 1. 检查资源是否准备就绪
        const resourceStatus = objManage.getResourceStatus();
        if (!resourceStatus.all) {
            console.log('等待资源加载完成...', resourceStatus);
            const loaded = await objManage.waitForResources();
            if (!loaded) {
                throw new Error('资源加载超时');
            }
        }

        // 2. 检查必要的类是否存在
        if (!window.Game_Follower || !window.Sprite_Character) {
            throw new Error('RPG Maker MZ Follower 类未加载');
        }

        // 3. 创建 Game_Follower 实例
        const memberIndex = options.memberIndex !== undefined ? options.memberIndex : 0;
        const follower = new window.Game_Follower(memberIndex);
        console.log(`创建 Game_Follower 实例，队伍成员索引: ${memberIndex}`);

        // 4. 设置跟随者角色图像
        if (options.characterName && options.characterIndex !== undefined) {
            // 使用指定的角色图像
            follower.setImage(options.characterName, options.characterIndex);
            console.log(`设置跟随者图像: ${options.characterName}, 索引: ${options.characterIndex}`);
        } else if (options.actorId && window.$dataActors && window.$dataActors[options.actorId]) {
            // 使用指定角色ID的图像
            const actorData = window.$dataActors[options.actorId];
            follower.setImage(actorData.characterName || 'Actor2', actorData.characterIndex || 0);
            console.log(`使用角色ID ${options.actorId} 的图像: ${actorData.characterName}`);
        } else if (window.$gameParty && window.$gameParty.allMembers) {
            // 尝试使用队伍成员的图像
            const members = window.$gameParty.allMembers();
            const targetMemberIndex = memberIndex + 1; // 跟随者从第二个成员开始

            if (members && members[targetMemberIndex]) {
                const member = members[targetMemberIndex];
                const actorData = window.$dataActors[member.actorId()];
                if (actorData) {
                    follower.setImage(actorData.characterName || 'Actor2', actorData.characterIndex || 0);
                    console.log(`使用队伍成员 ${targetMemberIndex} 的图像: ${actorData.characterName}`);
                } else {
                    follower.setImage('Actor2', memberIndex);
                    console.log(`使用默认跟随者图像: Actor2, 索引: ${memberIndex}`);
                }
            } else {
                follower.setImage('Actor2', memberIndex);
                console.log(`使用默认跟随者图像: Actor2, 索引: ${memberIndex}`);
            }
        } else {
            // 使用默认图像
            follower.setImage('Actor2', memberIndex);
            console.log(`使用默认跟随者图像: Actor2, 索引: ${memberIndex}`);
        }

        // 5. 设置跟随者位置
        const x = options.x !== undefined ? options.x : Math.floor(window.Graphics.width / 2 / 48) + 1;
        const y = options.y !== undefined ? options.y : Math.floor(window.Graphics.height / 2 / 48);
        follower.setPosition(x, y);

        // 6. 设置跟随者方向
        const direction = options.direction || 2; // 默认向下
        follower.setDirection(direction);

        // 7. 预加载角色图像
        if (window.ImageManager && follower._characterName) {
            const bitmap = window.ImageManager.loadCharacter(follower._characterName);
            console.log('预加载跟随者图像:', follower._characterName, bitmap);

            // 等待图像加载完成
            if (bitmap && typeof bitmap.isReady === 'function' && !bitmap.isReady()) {
                console.log('等待跟随者图像加载完成...');
                await new Promise<void>((resolve) => {
                    if (typeof bitmap.addLoadListener === 'function') {
                        bitmap.addLoadListener(() => {
                            console.log('跟随者图像加载完成');
                            resolve();
                        });
                    } else {
                        const checkReady = () => {
                            if (bitmap.isReady && bitmap.isReady()) {
                                console.log('跟随者图像加载完成（轮询检测）');
                                resolve();
                            } else {
                                setTimeout(checkReady, 100);
                            }
                        };
                        checkReady();
                    }
                });
            }
        }

        console.log('Game_Follower 创建完成:', {
            memberIndex: follower._memberIndex,
            characterName: follower._characterName,
            characterIndex: follower._characterIndex,
            direction: follower._direction,
            x: follower._x,
            y: follower._y
        });

        // 8. 创建精灵对象
        const sprite = new window.Sprite_Character(follower);

        console.log('Sprite_Character (Follower) 创建完成:', sprite);

        // 验证锚点是否符合 RPG Maker MZ 源码标准
        if (sprite.anchor) {
            const expectedAnchorX = 0.5;
            const expectedAnchorY = 1;
            console.log('Follower 锚点验证:', {
                actualAnchorX: sprite.anchor.x,
                expectedAnchorX: expectedAnchorX,
                anchorXCorrect: sprite.anchor.x === expectedAnchorX,
                actualAnchorY: sprite.anchor.y,
                expectedAnchorY: expectedAnchorY,
                anchorYCorrect: sprite.anchor.y === expectedAnchorY,
                bothAnchorsCorrect: sprite.anchor.x === expectedAnchorX && sprite.anchor.y === expectedAnchorY
            });
        } else {
            console.warn('Follower Sprite 没有 anchor 属性！这不符合 RPG Maker MZ 标准');
        }

        // 强制更新精灵
        if (sprite.update) {
            sprite.update();
            console.log('跟随者精灵已强制更新');
        }

        // 9. 设置显示属性
        sprite.scale.x = 2; // 放大2倍
        sprite.scale.y = 2; // 放大2倍

        // 10. 通过objManage添加到舞台
        objManage.addToStage(sprite);

        // 11. 设置调试属性
        sprite.tint = 0xFFFFFF; // 确保没有色调变化
        console.log('跟随者精灵创建完成（已放大2倍）');

        return sprite;

    } catch (error) {
        console.error('创建 Game_Follower 精灵失败:', error);
        throw error;
    }
}

/**
 * 创建测试跟随者精灵
 * @param memberIndex 队伍成员索引
 * @returns 创建的跟随者精灵对象
 */
export async function createTestFollower(memberIndex: number = 0): Promise<any> {
    console.log(`=== 创建测试跟随者精灵 (索引: ${memberIndex}) ===`);

    try {
        return await createFollower({
            memberIndex: memberIndex,
            characterName: 'Actor2',
            characterIndex: memberIndex,
            direction: 2
        });
    } catch (error) {
        console.error('创建测试跟随者失败:', error);
        throw error;
    }
}

/**
 * 创建指定角色ID的跟随者精灵
 * @param actorId 角色ID
 * @param memberIndex 队伍成员索引
 * @returns 创建的跟随者精灵对象
 */
export async function createFollowerWithActor(actorId: number, memberIndex: number = 0): Promise<any> {
    console.log(`=== 创建角色ID ${actorId} 的跟随者精灵 (索引: ${memberIndex}) ===`);

    try {
        return await createFollower({
            actorId: actorId,
            memberIndex: memberIndex
        });
    } catch (error) {
        console.error(`创建角色ID ${actorId} 的跟随者失败:`, error);
        throw error;
    }
}

/**
 * 创建多个跟随者精灵
 * @param count 跟随者数量 (1-3)
 * @returns 创建的跟随者精灵数组
 */
export async function createMultipleFollowers(count: number = 3): Promise<any[]> {
    console.log(`=== 创建 ${count} 个跟随者精灵 ===`);

    try {
        const followers = [];
        for (let i = 0; i < Math.min(count, 3); i++) {
            const follower = await createTestFollower(i);
            followers.push(follower);
        }

        console.log(`成功创建 ${followers.length} 个跟随者精灵`);
        return followers;
    } catch (error) {
        console.error('创建多个跟随者失败:', error);
        throw error;
    }
}
