<script lang="ts">
  import type { ButtonModel } from '../../../type/ui/buttonModel.svelte';
  import Checkbox from '../../../components/Checkbox.svelte';
  import EventSelectionModal from '../../../modals/EventSelectionModal.svelte';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import DropTarget from '../../../components/drop/DropTarget.svelte';
  import type { BaseObjectModel } from '../../../type/baseObjectModel.svelte';

  let { model }: { model: ButtonModel } = $props();

  // 获取绑定状态信息
  let bindingInfo = $derived(() => {
    if (!model) return null;
    return model.getButtonInfo();
  });



  // 事件配置状态
  let selectedEventType = $state('onClickCode'); // 当前选择的事件类型
  let showEventModal = $state(false); // 是否显示事件选择弹窗

  // 事件类型选项
  const eventTypes = [
    { value: 'onClickCode', label: '点击事件' },
    { value: 'onHoverCode', label: '悬停事件' },
    { value: 'onHoverOutCode', label: '离开事件' },
    { value: 'onPressCode', label: '按下事件' },
    { value: 'onReleaseCode', label: '释放事件' },
    { value: 'onDoubleClickCode', label: '双击事件' }
  ];

  // 绑定处理函数
  function handleDefaultSpriteDrop(object: BaseObjectModel) {
    if (!model) return;
    console.log('🎯 绑定默认状态精灵:', object.className);
    const originalObject = object.getOriginalObject();

    // 🔑 UI更新模型：只更新 ButtonModel 的响应式属性
    model.boundDefaultSprite = originalObject;

    // 模型会通过 setupSpecificSync() 自动同步到 JavaScript 对象
    console.log('✅ 默认状态精灵绑定完成，模型已更新');
  }

  function handleHoverSpriteDrop(object: BaseObjectModel) {
    if (!model) return;
    console.log('🎯 绑定悬停状态精灵:', object.className);
    const originalObject = object.getOriginalObject();

    model.boundHoverSprite = originalObject;
    console.log('✅ 悬停状态精灵绑定完成，模型已更新');
  }

  function handlePressedSpriteDrop(object: BaseObjectModel) {
    if (!model) return;
    console.log('🎯 绑定按下状态精灵:', object.className);
    const originalObject = object.getOriginalObject();

    model.boundPressedSprite = originalObject;
    console.log('✅ 按下状态精灵绑定完成，模型已更新');
  }

  // 解绑函数
  function unbindDefaultSprite() {
    if (!model) return;
    model.boundDefaultSprite = null;
    console.log('🔓 默认状态精灵绑定已解除，模型已更新');
  }

  function unbindHoverSprite() {
    if (!model) return;
    model.boundHoverSprite = null;
    console.log('🔓 悬停状态精灵绑定已解除，模型已更新');
  }

  function unbindPressedSprite() {
    if (!model) return;
    model.boundPressedSprite = null;
    console.log('🔓 按下状态精灵绑定已解除，模型已更新');
  }

  // 打开事件选择弹窗
  function openEventModal() {
    showEventModal = true;
  }

  // 关闭事件选择弹窗
  function closeEventModal() {
    showEventModal = false;
  }

  // 处理事件选择
  function handleEventSelected(event: CustomEvent) {
    const selectedEvent = event.detail;
    if (selectedEvent && selectedEvent.code) {
      // 根据当前选择的事件类型设置对应的事件代码
      switch (selectedEventType) {
        case 'onClickCode':
          model.onClickCode = selectedEvent.code;
          break;
        case 'onHoverCode':
          model.onHoverCode = selectedEvent.code;
          break;
        case 'onHoverOutCode':
          model.onHoverOutCode = selectedEvent.code;
          break;
        case 'onPressCode':
          model.onPressCode = selectedEvent.code;
          break;
        case 'onReleaseCode':
          model.onReleaseCode = selectedEvent.code;
          break;
        case 'onDoubleClickCode':
          model.onDoubleClickCode = selectedEvent.code;
          break;
      }
    }
    closeEventModal();
  }

  // 获取当前选择事件类型的代码
  function getCurrentEventCode(): string {
    switch (selectedEventType) {
      case 'onClickCode': return model.onClickCode;
      case 'onHoverCode': return model.onHoverCode;
      case 'onHoverOutCode': return model.onHoverOutCode;
      case 'onPressCode': return model.onPressCode;
      case 'onReleaseCode': return model.onReleaseCode;
      case 'onDoubleClickCode': return model.onDoubleClickCode;
      default: return '';
    }
  }
</script>

<AccordionPanel title="🔘 UIButton 属性" expanded={true}>

  <!-- 🔑 子组件绑定状态 -->
  <div class="property-section">
    <h4>🔗 子组件绑定</h4>

    <!-- 默认状态精灵绑定 -->
    <div class="binding-item">
      <div class="binding-header">
        <span class="binding-label">默认状态精灵 (UIImage):</span>
        <span class="binding-status-badge {bindingInfo()?.bindings?.boundDefaultSprite ? 'bound' : 'unbound'}">
          {bindingInfo()?.bindings?.boundDefaultSprite ? '已绑定' : '未绑定'}
        </span>
        {#if bindingInfo()?.bindings?.boundDefaultSprite}
          <button class="unbind-btn" onclick={unbindDefaultSprite} title="解除绑定">✕</button>
        {/if}
      </div>

      {#if bindingInfo()?.bindings?.boundDefaultSprite}
        <div class="bound-object-info">
          已绑定: {bindingInfo()?.bindings?.boundDefaultSprite?.className || '未知对象'}
        </div>
      {:else}
        <DropTarget
          onDrop={handleDefaultSpriteDrop}
          placeholder="拖拽 UIImage 对象到此处绑定为默认状态"
          targetObject={model}
          fieldName="boundDefaultSprite"
          enableHistory={true}
          operationName="绑定按钮默认状态精灵"
        />
      {/if}
    </div>

    <!-- 悬停状态精灵绑定 -->
    <div class="binding-item">
      <div class="binding-header">
        <span class="binding-label">悬停状态精灵 (UIImage):</span>
        <span class="binding-status-badge {bindingInfo()?.bindings?.boundHoverSprite ? 'bound' : 'unbound'}">
          {bindingInfo()?.bindings?.boundHoverSprite ? '已绑定' : '未绑定'}
        </span>
        {#if bindingInfo()?.bindings?.boundHoverSprite}
          <button class="unbind-btn" onclick={unbindHoverSprite} title="解除绑定">✕</button>
        {/if}
      </div>

      {#if bindingInfo()?.bindings?.boundHoverSprite}
        <div class="bound-object-info">
          已绑定: {bindingInfo()?.bindings?.boundHoverSprite?.className || '未知对象'}
        </div>
      {:else}
        <DropTarget
          onDrop={handleHoverSpriteDrop}
          placeholder="拖拽 UIImage 对象到此处绑定为悬停状态"
          targetObject={model}
          fieldName="boundHoverSprite"
          enableHistory={true}
          operationName="绑定按钮悬停状态精灵"
        />
      {/if}
    </div>

    <!-- 按下状态精灵绑定 -->
    <div class="binding-item">
      <div class="binding-header">
        <span class="binding-label">按下状态精灵 (UIImage):</span>
        <span class="binding-status-badge {bindingInfo()?.bindings?.boundPressedSprite ? 'bound' : 'unbound'}">
          {bindingInfo()?.bindings?.boundPressedSprite ? '已绑定' : '未绑定'}
        </span>
        {#if bindingInfo()?.bindings?.boundPressedSprite}
          <button class="unbind-btn" onclick={unbindPressedSprite} title="解除绑定">✕</button>
        {/if}
      </div>

      {#if bindingInfo()?.bindings?.boundPressedSprite}
        <div class="bound-object-info">
          已绑定: {bindingInfo()?.bindings?.boundPressedSprite?.className || '未知对象'}
        </div>
      {:else}
        <DropTarget
          onDrop={handlePressedSpriteDrop}
          placeholder="拖拽 UIImage 对象到此处绑定为按下状态"
          targetObject={model}
          fieldName="boundPressedSprite"
          enableHistory={true}
          operationName="绑定按钮按下状态精灵"
        />
      {/if}
    </div>



    <div class="binding-info">
      <p class="binding-hint">
        💡 从左侧对象树拖拽 UIImage 对象到上方区域进行绑定
      </p>
    </div>
  </div>

  <!-- 状态配置 -->
  <div class="property-section">
    <h4>⚙️ 状态配置：</h4>
    <div class="property-row">
      <Checkbox bind:checked={model.enabled} label="启用按钮" />
    </div>
  </div>

  <!-- 事件配置 -->
  <div class="property-section">
    <h4>⚡ 事件配置：</h4>

    <!-- 事件类型选择 -->
    <div class="event-config-row">
      <label for="event-type-select">事件类型:</label>
      <select
        id="event-type-select"
        bind:value={selectedEventType}
        class="event-type-select"
      >
        {#each eventTypes as eventType}
          <option value={eventType.value}>{eventType.label}</option>
        {/each}
      </select>
    </div>

    <!-- 当前事件代码显示和选择按钮 -->
    <div class="event-config-row">
      <div class="event-code-label">当前事件代码:</div>
      <div class="event-code-display">
        <div class="event-code-text">
          {getCurrentEventCode() || '未设置事件'}
        </div>
        <button
          class="event-select-btn"
          onclick={openEventModal}
          title="选择事件"
        >
          📝 选择事件
        </button>
      </div>
    </div>
  </div>
</AccordionPanel>

<!-- 事件选择弹窗 -->
{#if showEventModal}
  <EventSelectionModal
    onEventSelected={handleEventSelected}
    onClose={closeEventModal}
  />
{/if}

<style>
  .property-section {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--theme-border-light, #f1f5f9);
  }

  .property-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .property-section h4 {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: var(--theme-text, #1a202c);
    font-weight: 600;
  }

  .property-row {
    margin-bottom: 8px;
  }

  /* 事件配置样式 */
  .event-config-row {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 12px;
  }

  .event-config-row label,
  .event-code-label {
    font-size: 11px;
    color: var(--theme-text-secondary, #666);
    font-weight: 500;
  }

  .event-type-select {
    padding: 4px 6px;
    font-size: 11px;
    border: 1px solid var(--theme-border, #e2e8f0);
    border-radius: 3px;
    background: var(--theme-surface, #ffffff);
    color: var(--theme-text, #1a202c);
    cursor: pointer;
  }

  .event-type-select:focus {
    outline: none;
    border-color: var(--theme-primary, #3b82f6);
    box-shadow: 0 0 0 2px var(--theme-primary-light, #dbeafe);
  }

  .event-code-display {
    display: flex;
    gap: 8px;
    align-items: flex-start;
  }

  .event-code-text {
    flex: 1;
    padding: 6px 8px;
    font-family: monospace;
    font-size: 10px;
    background: var(--theme-surface-tertiary, #323e4d);
    border: 1px solid var(--theme-border, #42556d);
    border-radius: 3px;
    color: var(--theme-text, #374151);
    min-height: 20px;
    word-break: break-all;
  }

  .event-select-btn {
    padding: 6px 12px;
    font-size: 11px;
    background: var(--theme-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    flex-shrink: 0;
    white-space: nowrap;
  }

  .event-select-btn:hover {
    background: var(--theme-primary-dark, #2563eb);
  }

  /* 🔑 绑定状态样式 */
  .binding-item {
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border-light, #e9ecef);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
  }

  .binding-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    font-size: 10px;
  }

  .bound-object-info {
    font-size: 9px;
    color: var(--theme-text-secondary, #666);
    padding: 4px 6px;
    background: var(--theme-surface-light, #f8f9fa);
    border-radius: 3px;
    border: 1px solid var(--theme-border-light, #e9ecef);
  }

  .unbind-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 2px;
    padding: 2px 4px;
    font-size: 8px;
    cursor: pointer;
    transition: background-color 0.15s ease;
  }

  .unbind-btn:hover {
    background: #c82333;
  }

  .binding-label {
    font-weight: 500;
    color: var(--theme-text, #1a202c);
  }

  .binding-status-badge {
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 9px;
    font-weight: 500;
    text-transform: uppercase;
  }

  .binding-status-badge.bound {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .binding-status-badge.unbound {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  .binding-info {
    margin-top: 8px;
  }

  .binding-hint {
    font-size: 9px;
    color: var(--theme-text-secondary, #718096);
    margin: 0;
    line-height: 1.4;
    font-style: italic;
  }
</style>
