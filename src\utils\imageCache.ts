/**
 * 图片缓存工具
 * 用于避免重复加载相同的图片URL，提升Canvas绘制性能
 */

interface CachedImage {
    element: HTMLImageElement;
    url: string;
    isReady: boolean;
}

class ImageCache {
    private cache = new Map<string, CachedImage>();

    /**
     * 确保图片已加载
     * @param url 图片URL（通常是blob URL）
     * @returns Promise<HTMLImageElement> 已加载的图片元素
     */
    async ensureImageLoaded(url: string): Promise<HTMLImageElement> {
        return new Promise((resolve, reject) => {
            // 检查缓存
            const cached = this.cache.get(url);
            if (cached && cached.isReady) {
                console.log('🎯 ImageCache: 使用缓存图片', url);
                resolve(cached.element);
                return;
            }

            console.log('🔄 ImageCache: 加载新图片', url);

            // 创建或重用图片元素
            let imageElement: HTMLImageElement;
            if (cached) {
                imageElement = cached.element;
            } else {
                imageElement = document.createElement('img');
                this.cache.set(url, {
                    element: imageElement,
                    url: url,
                    isReady: false
                });
            }

            // 设置加载完成回调
            imageElement.onload = () => {
                const cachedItem = this.cache.get(url);
                if (cachedItem) {
                    cachedItem.isReady = true;
                }
                console.log('✅ ImageCache: 图片加载完成', url);
                resolve(imageElement);
            };

            imageElement.onerror = () => {
                console.error('❌ ImageCache: 图片加载失败', url);
                // 从缓存中移除失败的图片
                this.cache.delete(url);
                reject(new Error(`图片加载失败: ${url}`));
            };

            // 只有URL变化时才重新加载
            if (imageElement.src !== url) {
                const cachedItem = this.cache.get(url);
                if (cachedItem) {
                    cachedItem.isReady = false;
                }
                imageElement.src = url;
            }
        });
    }

    /**
     * 清除指定URL的缓存
     * @param url 要清除的图片URL
     */
    clearCache(url: string): void {
        const cached = this.cache.get(url);
        if (cached) {
            // 清理事件监听器
            cached.element.onload = null;
            cached.element.onerror = null;
            this.cache.delete(url);
            console.log('🗑️ ImageCache: 清除缓存', url);
        }
    }

    /**
     * 清除所有缓存
     */
    clearAllCache(): void {
        for (const [url, cached] of this.cache) {
            cached.element.onload = null;
            cached.element.onerror = null;
        }
        this.cache.clear();
        console.log('🗑️ ImageCache: 清除所有缓存');
    }

    /**
     * 获取缓存状态信息
     */
    getCacheInfo(): { total: number; ready: number; loading: number } {
        let ready = 0;
        let loading = 0;
        
        for (const cached of this.cache.values()) {
            if (cached.isReady) {
                ready++;
            } else {
                loading++;
            }
        }

        return {
            total: this.cache.size,
            ready,
            loading
        };
    }

    /**
     * 预加载图片
     * @param url 图片URL
     */
    async preloadImage(url: string): Promise<void> {
        try {
            await this.ensureImageLoaded(url);
            console.log('📦 ImageCache: 预加载完成', url);
        } catch (error) {
            console.error('📦 ImageCache: 预加载失败', url, error);
        }
    }
}

// 创建全局单例
export const imageCache = new ImageCache();

// 导出类型
export type { CachedImage };
