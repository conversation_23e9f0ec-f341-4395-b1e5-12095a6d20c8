<script lang="ts">
  /**
   * 响应式图片属性面板
   * 使用新的响应式状态管理
   */

  import {
    sceneModelState,
    primarySelectedObjectInfo
  } from '../../../stores/sceneModelStore';
  import Image from '../../../components/Image.svelte';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import type { spriteProperties } from '../../../generators/type';
  import { selectImageResource } from '../../../logics/resourceLoader/resourceSelector';

  // 监听选中的对象 - 使用第一个选中的对象
  let currentState = $derived($sceneModelState);
  let primaryObject = $derived(currentState.selectedObjects[0] || null);

  // 强制更新标记
  let forceUpdateKey = $state(0);

  // 判断是否为sprite对象且有图片URL
  let isSprite = $derived(primaryObject?.className === 'Sprite');
  let spriteData = $derived(isSprite ? primaryObject as unknown as spriteProperties : null);

  // 直接从sprite.bitmap获取URL，确保响应式更新
  let imageUrl = $derived((() => {
    // 使用forceUpdateKey来强制重新计算
    forceUpdateKey;

    if (!isSprite || !primaryObject) return '';

    // 获取bitmap对象
    const bitmap = (primaryObject as any).bitmap;
    if (!bitmap) return '';

    // 优先使用bitmap._url，这是CustomResourcePath插件处理后的完整路径（blob URL或绝对路径）
    if (bitmap._url) {
      console.log('从bitmap._url获取URL:', bitmap._url);
      console.log('bitmap加载状态:', bitmap._loadingState);
      console.log('bitmap是否就绪:', bitmap.isReady ? bitmap.isReady() : 'unknown');

      // 如果是blob URL，直接使用
      if (bitmap._url.startsWith('blob:')) {
        return bitmap._url;
      }

      // 如果是文件路径但图片已经加载完成，可能需要等待blob URL
      if (bitmap._loadingState === 'loaded' && bitmap._url.startsWith('blob:')) {
        return bitmap._url;
      }

      // 如果还是文件路径，检查是否可以转换为可用的URL
      if (bitmap._url.includes('\\') || bitmap._url.includes('/')) {
        // 这可能是一个文件路径，我们需要等待它变成blob URL
        console.log('检测到文件路径，等待转换为blob URL...');

        // 临时返回空字符串，等待图片加载完成
        if (bitmap._loadingState === 'loading') {
          return '';
        }

        // 如果加载失败或其他状态，尝试使用原始路径
        return bitmap._url;
      }

      return bitmap._url;
    }

    // 备选：使用bitmap.url（标准属性）
    if (bitmap.url) {
      console.log('从bitmap.url获取URL:', bitmap.url);
      return bitmap.url;
    }

    // 最后备选：从序列化数据获取
    if (spriteData?.bitmap?.url) {
      console.log('从序列化数据获取URL:', spriteData.bitmap.url);
      return spriteData.bitmap.url;
    }

    console.log('没有找到图片URL');
    return '';
  })());

  let shouldShowImage = $derived(isSprite && imageUrl && imageUrl.trim() !== '');

  // 手风琴展开状态
  let isExpanded = $state(true);

  // 处理图片点击事件
  async function onImageClick() {
    if (!primaryObject) {
      console.warn('没有选中的对象');
      return;
    }

    console.log('=== 图片点击事件 ===');
    console.log('选中对象:', primaryObject);

    try {
      // 调用资源选择接口
      const result = await selectImageResource('选择图片资源');

      if (result.success && result.resourceObject) {
        console.log('✓ 图片选择成功:', result.filePath);
        console.log('✓ 创建的Bitmap对象:', result.resourceObject);

        // 设置当前对象的bitmap
        if (isSprite && primaryObject) {
          (primaryObject as any).bitmap = result.resourceObject;

          // 强制更新UI
          forceUpdateKey++;

          console.log('✓ 已设置新的bitmap到选中对象');
        }
      } else {
        console.log('图片选择取消或失败:', result.error);
      }
    } catch (error) {
      console.error('图片选择过程中发生错误:', error);
    }
  }

  // 监听bitmap加载状态变化
  $effect(() => {
    if (isSprite && primaryObject && (primaryObject as any).bitmap) {
      const bitmap = (primaryObject as any).bitmap;

      // 如果bitmap正在加载，添加加载完成监听器
      if (bitmap._loadingState === 'loading' && typeof bitmap.addLoadListener === 'function') {
        console.log('添加bitmap加载监听器...');

        const loadListener = () => {
          console.log('Bitmap加载完成，强制更新UI');
          forceUpdateKey++;
        };

        bitmap.addLoadListener(loadListener);

        // 清理函数
        return () => {
          if (typeof bitmap.removeLoadListener === 'function') {
            bitmap.removeLoadListener(loadListener);
          }
        };
      }
    }
  });

  // 监听对象变化，输出调试信息
  $effect(() => {
    if (primaryObject) {
      console.log('=== 图片属性面板更新 ===');
      console.log('选中对象:', primaryObject);
      console.log('对象类型:', primaryObject.className);
      console.log('是否为Sprite:', isSprite);
      console.log('图片URL:', imageUrl);
      console.log('是否显示图片:', shouldShowImage);

      if (spriteData?.bitmap) {
        console.log('Bitmap属性:', spriteData.bitmap);
      }
    }
  });
</script>

{#if primaryObject}
  <AccordionPanel
    title="图片属性"
    icon="🖼️"
    badge={shouldShowImage ? '有图片' : '无图片'}
    badgeVariant={shouldShowImage ? 'active' : 'inactive'}
    bind:expanded={isExpanded}
  >
    {#if shouldShowImage}
      <!-- 图片预览 -->
      <div class="image-layout">
        <div class="image-preview">
          <Image
            src={imageUrl}
            alt="Sprite图片"
            maxWidth="100%"
            maxHeight="200px"
            fit="contain"
            clickable={true}
            onClick={onImageClick}
          />
        </div>
      </div>
    {:else if isSprite}
      <!-- Sprite对象但没有图片 -->
      <div class="no-image-section">
        <div class="no-image-content">
          <div class="no-image-icon">🖼️</div>
          <div class="no-image-text">
            <span class="no-image-title">无图片</span>
            <span class="no-image-subtitle">bitmap.url为空</span>
          </div>
        </div>
      </div>
    {:else}
      <!-- 非Sprite对象 -->
      <div class="not-sprite-section">
        <div class="not-sprite-content">
          <div class="not-sprite-icon">ℹ️</div>
          <div class="not-sprite-text">
            <span class="not-sprite-title">非Sprite对象</span>
            <span class="not-sprite-subtitle">类型: {primaryObject?.className || '未知'}</span>
          </div>
        </div>
      </div>
    {/if}
  </AccordionPanel>
{:else}
  <div class="no-selection">
    <div class="no-selection-content">
      <div class="no-selection-icon">🎯</div>
      <div class="no-selection-text">
        <span class="no-selection-title">未选中对象</span>
        <span class="no-selection-subtitle">请在左侧对象树中选择一个对象</span>
      </div>
    </div>
  </div>
{/if}

<style>


  /* 图片布局容器 */
  .image-layout {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* 图片预览区域 */
  .image-preview {
    width: 100%;
    max-width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--theme-surface-dark, #1a202c);
    border-radius: var(--border-radius, 4px);
    border: 1px solid var(--theme-border, rgba(255, 255, 255, 0.2));
    overflow: hidden;
    min-height: 150px;
  }

  .no-image-section,
  .not-sprite-section,
  .no-selection {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-6, 1.5rem);
  }

  .no-image-content,
  .not-sprite-content,
  .no-selection-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2, 0.5rem);
    text-align: center;
  }

  .no-image-icon,
  .not-sprite-icon,
  .no-selection-icon {
    font-size: var(--font-size-xl, 1.25rem);
    opacity: 0.5;
  }

  .no-image-text,
  .not-sprite-text,
  .no-selection-text {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1, 0.25rem);
  }

  .no-image-title,
  .not-sprite-title,
  .no-selection-title {
    font-size: var(--font-size-sm, 0.875rem);
    font-weight: 500;
    color: var(--theme-text, #ffffff);
  }

  .no-image-subtitle,
  .not-sprite-subtitle,
  .no-selection-subtitle {
    font-size: var(--font-size-xs, 0.75rem);
    color: var(--theme-text-secondary, rgba(255, 255, 255, 0.8));
  }


</style>