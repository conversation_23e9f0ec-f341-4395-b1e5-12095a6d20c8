<script lang="ts">
  /**
   * 安全输入框组件
   * 自动处理快捷键冲突，防止在输入时触发全局快捷键
   */

  let {
    value = $bindable(''),
    type = 'text',
    placeholder = '',
    disabled = false,
    readonly = false,
    maxlength,
    minlength,
    pattern,
    required = false,
    autocomplete = 'off',
    class: className = '',
    style = '',
    id,
    name,
    ...restProps
  }: {
    value: string;
    type?: string;
    placeholder?: string;
    disabled?: boolean;
    readonly?: boolean;
    maxlength?: number;
    minlength?: number;
    pattern?: string;
    required?: boolean;
    autocomplete?: string;
    class?: string;
    style?: string;
    id?: string;
    name?: string;
    [key: string]: any;
  } = $props();

  let inputElement: HTMLInputElement;

  /**
   * 处理键盘按下事件
   * 阻止事件冒泡到全局快捷键处理器
   */
  function handleKeyDown(event: KeyboardEvent) {
    // 阻止事件冒泡，防止触发全局快捷键
    event.stopPropagation();

    // 调试日志（开发时可启用）
    // console.log('SafeInput: 键盘事件被安全处理', event.key);
  }

  /**
   * 处理输入事件
   */
  function handleInput(event: Event) {
    const target = event.target as HTMLInputElement;
    value = target.value;
  }

  /**
   * 处理焦点获得事件
   */
  function handleFocus(event: FocusEvent) {
    // 可以在这里添加焦点获得时的逻辑
    // console.log('SafeInput: 获得焦点');
  }

  /**
   * 处理焦点失去事件
   */
  function handleBlur(event: FocusEvent) {
    // 可以在这里添加焦点失去时的逻辑
    // console.log('SafeInput: 失去焦点');
  }

  /**
   * 获取输入框引用（供外部使用）
   */
  export function getInputElement(): HTMLInputElement {
    return inputElement;
  }

  /**
   * 聚焦到输入框
   */
  export function focus(): void {
    inputElement?.focus();
  }

  /**
   * 选中所有文本
   */
  export function selectAll(): void {
    inputElement?.select();
  }
</script>

<input
  bind:this={inputElement}
  {type}
  {placeholder}
  {disabled}
  {readonly}
  {maxlength}
  {minlength}
  {pattern}
  {required}
  {autocomplete}
  {id}
  {name}
  {value}
  class="component-input-base {className}"
  {style}
  onkeydown={handleKeyDown}
  oninput={handleInput}
  onfocus={handleFocus}
  onblur={handleBlur}
  {...restProps}
/>

<style>
  @import './shared-styles.css';

  .component-input-base {
    background-color: #252436 !important;
    background: #252436 !important;
  }

  input.component-input-base {
    background-color: #252436  !important;
    background: #252436 !important;
  }
</style>
