import { writable, derived } from 'svelte/store';
import { BitmapModel } from "../../type/bitmapModel.svelte";
import type { Element } from "../../type/bitmapModel.svelte";

// 🔧 新架构：使用 writable store 来确保引用更新
export const bitmapModel = writable(new BitmapModel());

// 🔧 独立的选中状态管理
export const selectedElement = writable<Element | null>(null);

// 🔧 导出当前选中元素索引的 derived store
export const selectedElementIndex = derived(
  [bitmapModel, selectedElement],
  ([$bitmapModel, $selectedElement]) => {
    if (!$selectedElement || !$bitmapModel?.elements) return -1;
    return $bitmapModel.elements.findIndex((el: any) => el === $selectedElement);
  }
);

// 🔧 简化的初始化方法：只初始化模型数据
export function initializeBitmapObjectState(bitmapData: any): void {
  console.log('🔧 初始化 BitmapModel:', bitmapData);

  // 创建新模型并更新 store
  const newModel = new BitmapModel(bitmapData);
  bitmapModel.set(newModel);

  console.log('🔧 BitmapModel 初始化完成');
  console.log('🔧 新模型的 elements 长度:', newModel.elements.length);
  console.log('🔧 新模型的 elements 内容:', newModel.elements);
}

// 🔧 重置方法：重置模型到初始状态
export function resetBitmapObjectState(): void {
  console.log('🔧 重置 BitmapModel');

  // 创建新的空模型并更新 store
  const newModel = new BitmapModel();
  bitmapModel.set(newModel);
}

// 🔧 辅助函数：更新元素属性（保持引用不断开）
export function updateElementProperty(elementIndex: number, updates: any): void {
  bitmapModel.update(model => {
    if (model && elementIndex >= 0 && elementIndex < model.elements.length) {
      model.updateElement(elementIndex, updates);
    }
    return model;
  });
}
