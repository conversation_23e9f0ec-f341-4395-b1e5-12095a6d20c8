<script lang="ts">
  /**
   * 中央面板组件
   * 包含工具栏和游戏预览
   */
  import ToolbarPanel from './ToolbarPanel.svelte';
  import Preview from './preview.svelte';
</script>

<div class="center-panel">
  <!-- 工具栏 -->
  <ToolbarPanel />

  <!-- 游戏预览区域 -->
  <div class="preview-container">
    <Preview />
  </div>
</div>

<style>
  .center-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-background);
    overflow: hidden;
  }

  .preview-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  /* 确保预览组件占满剩余空间 */
  .preview-container :global(.preview-component) {
    height: 100%;
    flex: 1;
  }
</style>