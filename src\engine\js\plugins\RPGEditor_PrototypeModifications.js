(function () {
    "use strict";
    // ==================== RPG Editor 组件类型修改 ====================
    // 使用原型链格式修改组件类型

    // ==================== 辅助方法 ====================


    // // ==================== 对象查找方法 ====================
    // // 获取插件参数
    // const parameters = PluginManager.parameters("RPGEditor_PrototypeModifications");
    // const DEBUG = parameters["debug"] === "true";

    // // 调试日志函数
    // function log(message, ...args) {
    //     if (DEBUG) {
    //         console.log("[RPGEditor]", message, ...args);
    //     }
    // }
    // // 工具函数：根据场景路径查找对象
    // function findObjectByScenePath(scenePath) {
    //     if (!scenePath || scenePath.length === 0) return null;

    //     let current = SceneManager._scene;
    //     if (!current) return null;

    //     // 检查第一个元素是否为创建操作标记
    //     const firstElement = scenePath[0];
    //     let isCreationOperation = firstElement === "+";

    //     // 确定实际的场景路径起始位置
    //     let startIndex = isCreationOperation ? 1 : 0;

    //     // 验证场景名称
    //     const expectedSceneName = scenePath[startIndex];
    //     const actualSceneName = current.constructor.name;
    //     if (actualSceneName !== expectedSceneName) {
    //         if (DEBUG) console.log(`[Scene Mismatch] Expected: ${expectedSceneName}, Actual: ${actualSceneName}`);
    //         return null;
    //     }

    //     // 确定遍历的结束位置
    //     let endIndex = scenePath.length;
    //     if (isCreationOperation) {
    //         // 创建操作：最后一个索引不查找，那是要创建的位置
    //         endIndex = scenePath.length - 1;
    //         if (DEBUG) console.log(`[Creation Operation] Finding parent object, skipping last index: ${scenePath[scenePath.length - 1]}`);
    //     }

    //     // 遍历路径
    //     for (let i = startIndex + 1; i < endIndex; i++) {
    //         const index = parseInt(scenePath[i]);
    //         if (isNaN(index) || !current.children || !current.children[index]) {
    //             if (DEBUG) console.log(`[Path Break] Index ${index} does not exist in ${current.constructor.name}`);
    //             return null;
    //         }
    //         current = current.children[index];
    //     }

    //     return current;
    // }

    // // 工具函数：从当前对象开始根据路径查找对象
    // function findObjectFromCurrent(currentObject, objectPath) {
    //     if (!currentObject || !objectPath || objectPath.length === 0) {
    //         return null;
    //     }

    //     let current = currentObject;

    //     // 跳过第一个元素（组件类型），从索引开始查找
    //     for (let i = 1; i < objectPath.length; i++) {
    //         const index = parseInt(objectPath[i]);

    //         if (isNaN(index) || !current.children || !current.children[index]) {
    //             if (DEBUG) log(`[Path Break] 索引 ${index} 在 ${current.constructor.name} 中不存在`);
    //             return null;
    //         }

    //         current = current.children[index];
    //         if (DEBUG) log(`[Path Follow] 找到索引 ${index}: ${current.constructor.name}`);
    //     }

    //     return current;
    // }



    // // 工具函数：创建游戏对象
    // function createGameObject(type, params = {}, parentContainer = null, targetIndex = null) {
    //     if (DEBUG) log(`[GameObject] Creating new object: ${type}`, params);

    //     let newObject = null;

    //     switch (type) {
    //         case "Sprite":
    //             newObject = new Sprite();
    //             newObject.name = params.name || "NewSprite";
    //             newObject.x = params.x || 0;
    //             newObject.y = params.y || 0;
    //             newObject.visible = params.visible !== undefined ? params.visible : true;
    //             break;

    //         case "Label":
    //             newObject = new Sprite();
    //             newObject.name = params.name || "NewLabel";
    //             newObject.x = params.x || 0;
    //             newObject.y = params.y || 0;
    //             newObject.visible = params.visible !== undefined ? params.visible : true;

    //             const bitmap = new Bitmap(200, 40);
    //             bitmap.fontSize = 20;
    //             bitmap.textColor = "#ffffff";
    //             bitmap.outlineColor = "rgba(0, 0, 0, 0.5)";
    //             bitmap.outlineWidth = 4;
    //             bitmap.drawText(params.text || "New Text", 0, 0, 200, 40, "left");
    //             bitmap.text = params.text || "New Text";

    //             newObject.bitmap = bitmap;
    //             break;

    //         case "Container":
    //             newObject = new PIXI.Container();
    //             newObject.name = params.name || "NewContainer";
    //             newObject.x = params.x || 0;
    //             newObject.y = params.y || 0;
    //             newObject.visible = params.visible !== undefined ? params.visible : true;
    //             break;

    //         case "Window":
    //             const rect = new Rectangle(0, 0, 200, 100);
    //             newObject = new Window_Base(rect);
    //             newObject.name = params.name || "NewWindow";
    //             newObject.x = params.x || 0;
    //             newObject.y = params.y || 0;
    //             newObject.visible = params.visible !== undefined ? params.visible : true;
    //             break;

    //         case "Button":
    //             newObject = new Sprite_Clickable();
    //             newObject.name = params.name || "NewButton";
    //             newObject.x = params.x || 0;
    //             newObject.y = params.y || 0;
    //             newObject.visible = params.visible !== undefined ? params.visible : true;

    //             const buttonBitmap = new Bitmap(120, 40);
    //             buttonBitmap.fillRect(0, 0, 120, 40, "#3498db");
    //             buttonBitmap.fontSize = 18;
    //             buttonBitmap.textColor = "#ffffff";
    //             buttonBitmap.drawText(params.text || "Button", 0, 0, 120, 40, "center");

    //             newObject.bitmap = buttonBitmap;
    //             newObject._isButton = true;
    //             break;

    //         case "LayoutContainer":
    //             if (typeof LayoutContainer !== "undefined") {
    //                 newObject = new LayoutContainer();
    //                 newObject.name = params.name || "NewLayoutContainer";
    //                 newObject.x = params.x || 0;
    //                 newObject.y = params.y || 0;
    //                 newObject.visible = params.visible !== undefined ? params.visible : true;
    //             } else {
    //                 // 如果没有LayoutContainer类，创建一个普通的Container
    //                 newObject = new PIXI.Container();
    //                 newObject.name = params.name || "NewLayoutContainer";
    //                 newObject.x = params.x || 0;
    //                 newObject.y = params.y || 0;
    //                 newObject.visible = params.visible !== undefined ? params.visible : true;
    //             }
    //             break;

    //         default:
    //             if (DEBUG) log(`[GameObject] Unknown object type: ${type}`);
    //             return null;
    //     }

    //     // 如果提供了父容器，则添加到父容器中
    //     if (newObject && parentContainer && parentContainer.addChild) {
    //         if (targetIndex !== null && targetIndex !== undefined) {
    //             // 在指定索引位置插入对象
    //             if (parentContainer.children && targetIndex <= parentContainer.children.length) {
    //                 parentContainer.addChildAt(newObject, targetIndex);
    //                 if (DEBUG) log('[Create Object] 成功在索引 ' + targetIndex + ' 位置创建并添加 ' + type + ' 对象');
    //             } else {
    //                 // 如果索引超出范围，直接添加到末尾
    //                 parentContainer.addChild(newObject);
    //                 if (DEBUG) log('[Create Object] 索引超出范围，添加 ' + type + ' 对象到末尾');
    //             }
    //         } else {
    //             // 没有指定索引，直接添加到末尾
    //             parentContainer.addChild(newObject);
    //             if (DEBUG) log('[Create Object] 成功创建并添加 ' + type + ' 对象到容器');
    //         }
    //     } else if (parentContainer && !parentContainer.addChild) {
    //         if (DEBUG) log('[Create Object] 父容器不支持 addChild 方法');
    //     }

    //     return newObject;
    // }

    // // ==================== Sprite_Character 类型修改 ====================

    // // 保存原始的 Sprite_Character 方法
    // const _Sprite_Character_initialize = Sprite_Character.prototype.initialize;

    // // 重写 Sprite_Character 的 initialize 方法
    // Sprite_Character.prototype.initialize = function (...args) {
    //     // 调用原始方法
    //     _Sprite_Character_initialize.call(this, ...args);

    //     // 应用 RPG Editor 的修改
    //     // 创建 Label 对象
    //     const targetPath_Label_Sprite_Character_1 = ["Sprite_Character", "1"];
    //     const parentPath_Label_Sprite_Character_1 = targetPath_Label_Sprite_Character_1.slice(0, -1);
    //     const targetIndex_Label_Sprite_Character_1 = targetPath_Label_Sprite_Character_1[targetPath_Label_Sprite_Character_1.length - 1];

    //     // 查找父容器对象
    //     const parentContainer_Label_Sprite_Character_1 = findObjectFromCurrent(this, parentPath_Label_Sprite_Character_1);

    //     if (parentContainer_Label_Sprite_Character_1) {
    //         createGameObject('Label', {
    //             name: 'Label_' + Date.now(),
    //             x: 0,
    //             y: 0,
    //             visible: true
    //         }, parentContainer_Label_Sprite_Character_1, targetIndex_Label_Sprite_Character_1);

    //         // 修改创建的对象属性
    //         const createdObject_Label_Sprite_Character_1 = findObjectFromCurrent(this, targetPath_Label_Sprite_Character_1);
    //         if (createdObject_Label_Sprite_Character_1) {
    //             createdObject_Label_Sprite_Character_1.x = -16;
    //             createdObject_Label_Sprite_Character_1.y = -84;
    //             if (DEBUG) log('[Create+Modify] 成功创建并修改对象属性，路径:', targetPath_Label_Sprite_Character_1);
    //         } else {
    //             if (DEBUG) log('[Create+Modify] 创建成功但无法找到对象进行属性修改，路径:', targetPath_Label_Sprite_Character_1);
    //         }
    //     } else {
    //         if (DEBUG) log('[Create] 无法找到父容器，路径:', parentPath_Label_Sprite_Character_1);
    //     }

    // };

    // // ==================== sprite 类型修改 ====================

    // //     // 保存原始的 sprite 方法
    // //     const _sprite_initialize = sprite.prototype.initialize;

    // //     // 重写 sprite 的 initialize 方法
    // //     sprite.prototype.initialize = function(...args) {
    // //         // 调用原始方法
    // //         _sprite_initialize.call(this, ...args);

    // //         // 应用 RPG Editor 的修改
    // // // 修改 Sprite 对象的属性
    // //         const targetPath_Sprite_sprite = ["sprite"];
    // //         const targetObject_Sprite_sprite = findObjectFromCurrent(this, targetPath_Sprite_sprite);

    // //         if (targetObject_Sprite_sprite) {
    // //             targetObject_Sprite_sprite._bitmap.elements = [object Object];
    // //             if (DEBUG) log('[Modify] 成功修改对象属性，路径:', targetPath_Sprite_sprite);
    // //         } else {
    // //             if (DEBUG) log('[Modify] 无法找到要修改的对象，路径:', targetPath_Sprite_sprite);
    // //         }

    // //     };

    // // ==================== 代码生成完成 ====================
})();