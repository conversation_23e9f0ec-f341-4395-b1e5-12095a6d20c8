<script lang="ts">
  /**
   * Input 输入框组件
   * 参考 React NumberInput 实现，支持拖拽调整值和双击编辑模式
   */

  import { historyManager } from '../historyManager';

  // Props using Svelte 5 syntax
  let {
    value = $bindable(''),
    type = 'text',
    placeholder = '',
    disabled = false,
    readonly = false,
    required = false,
    size = 'md',
    variant = 'default',
    fullWidth = false,
    leftIcon = '',
    rightIcon = '',
    maxlength = undefined,
    minlength = undefined,
    pattern = undefined,
    autocomplete = undefined,
    id = '',
    name = '',
    ariaLabel = '',
    ariaDescribedBy = '',
    min = undefined,
    max = undefined,
    step = 1,
    precision = 2,
    dragSensitivity = 1,
    onInput = () => {},
    onChange = () => {},
    onMove = undefined,
    onFocus = () => {},
    onBlur = () => {},
    onKeydown = () => {},
    onLeftIconClick = () => {},
    onRightIconClick = () => {},
    // 历史记录相关
    targetObject = undefined,
    fieldName = undefined,
    enableHistory = true
  }: {
    value?: string | number;
    type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url' | 'search';
    placeholder?: string;
    disabled?: boolean;
    readonly?: boolean;
    required?: boolean;
    size?: 'sm' | 'md' | 'lg';
    variant?: 'default' | 'success' | 'warning' | 'error';
    fullWidth?: boolean;
    leftIcon?: string;
    rightIcon?: string;
    maxlength?: number;
    minlength?: number;
    pattern?: string;
    autocomplete?: string;
    id?: string;
    name?: string;
    ariaLabel?: string;
    ariaDescribedBy?: string;
    min?: number;
    max?: number;
    step?: number;
    precision?: number;
    dragSensitivity?: number;
    onInput?: (value: string | number, event: Event) => void;
    onChange?: (value: string | number, event: Event) => void;
    onMove?: (value: string | number) => void; // 拖拽过程中的实时回调
    onFocus?: (event: FocusEvent) => void;
    onBlur?: (event: FocusEvent) => void;
    onKeydown?: (event: KeyboardEvent) => void;
    onLeftIconClick?: () => void;
    onRightIconClick?: () => void;
    // 历史记录相关
    targetObject?: any; // 对应的模型对象
    fieldName?: string; // 对应的字段名
    enableHistory?: boolean; // 是否启用历史记录
  } = $props();

  // 全局拖拽状态管理（类似 React 版本）
  const DragState = {
    isDragging: false,
    startValue: 0,
    initialValue: 0, // 鼠标按下时的初始值，用于记录历史
    currentValue: 0, // 当前滑动值，用于在鼠标抬起时使用
    startX: 0,
    currentOnChange: null as ((newValue: string | number) => void) | null,
    currentOnMove: null as ((newValue: string | number) => void) | null, // 滑动过程中的回调
    step: 1,
    min: undefined as number | undefined,
    max: undefined as number | undefined,
    precision: 2
  };

  // 内部状态
  let inputElement: HTMLInputElement;
  let labelElement: HTMLSpanElement;
  let isFocused = $state(false);
  let isEditing = $state(false); // 默认为 false，即默认显示标签模式
  let inputWidth = $state(0);

  // 输入模式的历史记录状态
  let editingInitialValue: string | number | null = null;

  // 辅助函数：将值转换为字符串
  function valueToString(val: string | number): string {
    if (typeof val === 'number') {
      return val.toString();
    }
    return val || '';
  }

  // 辅助函数：将字符串转换为数字（如果类型是number）
  function parseValue(val: string): string | number {
    if (type === 'number') {
      const num = parseFloat(val);
      return isNaN(num) ? 0 : num;
    }
    return val;
  }

  // 辅助函数：获取数字值（用于拖拽计算）
  function getNumericValue(val: string | number): number {
    if (typeof val === 'number') {
      return val;
    }
    return parseFloat(val) || 0;
  }

  // 计算输入框宽度 - 完全匹配内容宽度
  function calculateInputWidth(text: string, fontSize: string = '11px'): number {
    // 创建临时元素来测量文本宽度
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (context) {
      context.font = `${fontSize} var(--font-family-base, system-ui)`;
      const textWidth = context.measureText(text || placeholder || '0').width;
      return textWidth + 4; // 只添加4px用于最小padding，不设最小宽度
    }
    // 备用计算方法
    const textLength = (text || placeholder || '0').length;
    return textLength * (parseInt(fontSize) * 0.6) + 4;
  }

  // 更新输入框宽度
  function updateInputWidth() {
    const fontSize = size === 'sm' ? '10px' : size === 'lg' ? '12px' : '11px';
    inputWidth = calculateInputWidth(valueToString(value), fontSize);
  }

  // 处理输入事件
  function handleInput(event: Event) {
    const target = event.target as HTMLInputElement;
    const newValue = parseValue(target.value);
    value = newValue;
    updateInputWidth();
    onInput(newValue, event);
  }

  function handleChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const newValue = parseValue(target.value);
    value = newValue;
    updateInputWidth();
    onChange(newValue, event);
  }

  function handleFocus(event: FocusEvent) {
    isFocused = true;
    onFocus(event);
  }

  function handleBlur(event: FocusEvent) {
    isFocused = false;

    // 🎯 处理输入模式的历史记录
    if (isEditing) {
      handleInputModeHistoryRecord();
    }

    // 延迟退出编辑模式，避免点击时立即退出
    setTimeout(() => {
      if (!isFocused) {
        isEditing = false;
      }
    }, 100);
    onBlur(event);
  }

  // 处理标签点击（开始拖拽）
  function handleLabelPointerDown(event: PointerEvent) {
    // 如果正在编辑或者已经有其他组件在拖拽中，则不处理
    if (disabled || readonly || type !== 'number' || isEditing || DragState.isDragging) return;

    console.log("Input - 开始拖拽:", name || id, value);

    // 🎯 开始历史记录操作组
    console.log("🔍 LabelInput: 拖拽开始历史记录检查:", {
      enableHistory,
      isRecording: historyManager.isRecording(),
      targetObject: targetObject?.className || 'none',
      fieldName: fieldName || 'none',
      name: name || 'none'
    });

    if (enableHistory && historyManager.isRecording()) {
      const operationName = `拖拽调整${fieldName || name || '数值'}`;
      historyManager.startGroup(operationName);

      console.log("📝 HistoryManager: 开始操作组:", operationName);
    }

    // 存储拖拽状态
    DragState.isDragging = true;
    DragState.startValue = getNumericValue(value);
    DragState.initialValue = getNumericValue(value); // 记录初始值，用于历史记录
    DragState.currentValue = getNumericValue(value); // 初始化当前值
    DragState.startX = event.clientX;
    DragState.currentOnChange = (newValue: string | number) => {
      const changeEvent = new Event('change');
      onChange(newValue, changeEvent);
    };
    DragState.currentOnMove = onMove || null; // 存储 onMove 回调
    DragState.step = step;
    DragState.min = min;
    DragState.max = max;
    DragState.precision = precision;

    console.log("Input - 记录初始值:", DragState.initialValue);

    // 设置指针捕获，确保即使鼠标移出元素也能接收事件
    if (event.currentTarget && 'setPointerCapture' in event.currentTarget) {
      try {
        (event.currentTarget as Element).setPointerCapture(event.pointerId);
      } catch (err) {
        console.error("Error setting pointer capture:", err);
      }
    }

    // 添加事件监听
    window.addEventListener("pointermove", handlePointerMove);
    window.addEventListener("pointerup", handlePointerUp);
    window.addEventListener("mousemove", handlePointerMove);
    window.addEventListener("mouseup", handlePointerUp);

    // 设置鼠标样式
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';

    // 防止文本选择
    event.preventDefault();
  }

  // 处理鼠标移动（拖拽调整值）
  function handlePointerMove(event: MouseEvent | PointerEvent) {
    event.preventDefault();
    if (!DragState.isDragging) return;

    // 计算拖动距离
    const diff = event.clientX - DragState.startX;
    const sensitivity = dragSensitivity;

    // 计算新值 - 修复步长计算
    // 每移动 10px 触发一个步长单位
    const stepCount = Math.round(diff * sensitivity / 10);
    let newValue = DragState.startValue + stepCount * DragState.step;

    // 应用最小/最大限制
    if (DragState.min !== undefined) newValue = Math.max(DragState.min, newValue);
    if (DragState.max !== undefined) newValue = Math.min(DragState.max, newValue);

    // 应用精度
    newValue = Number(newValue.toFixed(DragState.precision));

    // 更新当前值
    DragState.currentValue = newValue;

    // console.log(`Input - 拖拽更新 ${name || id}: ${newValue}, 初始值: ${DragState.initialValue}`);

    // 使用 onMove 回调进行实时更新
    if (DragState.currentOnMove) {
      DragState.currentOnMove(type === 'number' ? newValue : newValue.toString());
    } else {
      // 如果没有 onMove，直接更新 value
      value = type === 'number' ? newValue : newValue.toString();
      updateInputWidth();
    }
  }

  // 处理鼠标释放（结束拖拽）
  function handlePointerUp(event: MouseEvent | PointerEvent) {
    console.log("Input - 结束拖拽:", name || id);

    // 确保历史记录操作正确结束（即使出现异常）
    let historyOperationStarted = false;
    if (enableHistory && DragState.isDragging && historyManager.isRecording()) {
      historyOperationStarted = true;
    }

    try {

    // 释放指针捕获
    if (event.target && 'releasePointerCapture' in event.target) {
      try {
        const pointerId = (event as PointerEvent).pointerId;
        if (pointerId) {
          (event.target as Element).releasePointerCapture(pointerId);
        }
      } catch (err) {
        console.error("Error releasing pointer capture:", err);
      }
    }

    // 🎯 处理历史记录
    console.log("🔍 LabelInput: 历史记录检查条件:", {
      enableHistory,
      isDragging: DragState.isDragging,
      isRecording: historyManager.isRecording(),
      targetObject: targetObject?.className || 'none',
      fieldName: fieldName || 'none'
    });

    if (enableHistory && DragState.isDragging && historyManager.isRecording()) {
      const finalValue = DragState.currentValue;
      const initialValue = DragState.initialValue;

      console.log("📝 HistoryManager: 准备记录变更:", {
        targetObject: targetObject?.className || 'unknown',
        fieldName: fieldName || name || 'value',
        initialValue,
        finalValue,
        hasTargetObject: !!targetObject
      });

      // 只有当值真正发生变化时才记录
      if (initialValue !== finalValue) {
        if (targetObject && fieldName) {
          // 记录到模型对象
          historyManager.recordChange(targetObject, fieldName, initialValue, finalValue);
          console.log("📝 HistoryManager: 已记录模型对象变更");
        } else {
          // 如果没有指定目标对象，创建一个虚拟对象来记录
          const virtualObject = {
            className: `LabelInput_${name || id || 'unnamed'}`,
            [fieldName || 'value']: finalValue
          };
          historyManager.recordChange(virtualObject, fieldName || 'value', initialValue, finalValue);
          console.log("📝 HistoryManager: 已记录虚拟对象变更");
        }
      } else {
        console.log("📝 HistoryManager: 值未变化，跳过记录");
      }

      // 结束操作组
      historyManager.endGroup();
      console.log("📝 HistoryManager: 操作组已结束");
    }

    // 在鼠标抬起时调用 onChange，传递最终值和初始值
    console.log("Input - 检查 onChange 条件:", {
      isDragging: DragState.isDragging,
      hasOnChange: !!DragState.currentOnChange,
      currentValue: DragState.currentValue,
      initialValue: DragState.initialValue
    });

    if (DragState.isDragging && DragState.currentOnChange) {
      console.log(`Input - 拖拽完成，调用 onChange: ${DragState.currentValue} (初始值: ${DragState.initialValue})`);

      // 更新 value
      value = type === 'number' ? DragState.currentValue : DragState.currentValue.toString();
      updateInputWidth();

      // 调用 onChange 回调
      DragState.currentOnChange(type === 'number' ? DragState.currentValue : DragState.currentValue.toString());
    } else {
      console.warn("Input - 未能调用 onChange，条件不满足");
    }

    // 重置拖拽状态
    DragState.isDragging = false;
    DragState.currentOnChange = null;
    DragState.currentOnMove = null;

    // 移除事件监听器
    window.removeEventListener("pointermove", handlePointerMove);
    window.removeEventListener("pointerup", handlePointerUp);
    window.removeEventListener("mousemove", handlePointerMove);
    window.removeEventListener("mouseup", handlePointerUp);

    // 恢复鼠标样式
    document.body.style.cursor = '';
    document.body.style.userSelect = '';

    } catch (error) {
      console.error("Input - 拖拽结束时发生错误:", error);
    } finally {
      // 确保历史记录操作正确结束
      if (historyOperationStarted) {
        try {
          historyManager.endGroup();
          console.log("📝 HistoryManager: 在finally中结束操作组");
        } catch (historyError) {
          console.error("📝 HistoryManager: 结束操作组时发生错误:", historyError);
        }
      }

      // 🎯 拖拽完成后主动失焦，确保快捷键能正常工作
      setTimeout(() => {
        if (labelElement) {
          labelElement.blur();
          console.log("📝 LabelInput: 拖拽完成后主动失焦");
        }
      }, 100);
    }
  }

  // 处理标签双击（进入编辑模式）
  function handleLabelDoubleClick() {
    if (disabled || readonly) return;

    console.log('进入编辑模式，当前值:', value);

    // 🎯 记录输入模式的初始值（用于历史记录）
    editingInitialValue = value;
    console.log("📝 LabelInput: 记录输入模式初始值:", editingInitialValue);

    // 先计算宽度，再进入编辑模式
    updateInputWidth();
    isEditing = true;

    console.log('计算的输入框宽度:', inputWidth);

    // 下一帧聚焦输入框并选中文本
    setTimeout(() => {
      if (inputElement) {
        inputElement.focus();
        inputElement.select();
      }
    }, 0);
  }

  // 处理键盘事件
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === 'Escape') {
      // 🎯 处理键盘退出编辑模式的历史记录
      if (event.key === 'Enter') {
        // Enter键确认输入，需要记录历史
        handleInputModeHistoryRecord();
      } else if (event.key === 'Escape') {
        // Escape键取消输入，恢复原值，不记录历史
        if (editingInitialValue !== null) {
          value = editingInitialValue;
          console.log("📝 LabelInput: Escape取消输入，恢复原值:", editingInitialValue);
        }
        editingInitialValue = null;
      }

      isEditing = false;
      inputElement?.blur();
    }
    onKeydown(event);
  }

  // 处理输入模式的历史记录（提取为独立函数，避免重复代码）
  function handleInputModeHistoryRecord() {
    if (editingInitialValue !== null) {
      const finalValue = value;

      console.log("🔍 LabelInput: 输入模式结束，检查历史记录:", {
        enableHistory,
        editingInitialValue,
        finalValue,
        hasChanged: editingInitialValue !== finalValue,
        targetObject: targetObject?.className || 'none',
        fieldName: fieldName || 'none'
      });

      // 只有当值真正发生变化时才记录
      if (enableHistory && editingInitialValue !== finalValue && historyManager.isRecording()) {
        if (targetObject && fieldName) {
          // 记录到模型对象
          historyManager.recordChange(targetObject, fieldName, editingInitialValue, finalValue);
          console.log("📝 HistoryManager: 已记录输入模式变更");
        } else {
          // 如果没有指定目标对象，创建一个虚拟对象来记录
          const virtualObject = {
            className: `LabelInput_${name || id || 'unnamed'}`,
            [fieldName || 'value']: finalValue
          };
          historyManager.recordChange(virtualObject, fieldName || 'value', editingInitialValue, finalValue);
          console.log("📝 HistoryManager: 已记录输入模式虚拟对象变更");
        }
      }

      // 清理输入模式历史记录状态
      editingInitialValue = null;
    }
  }

  // 初始化时计算宽度
  $effect(() => {
    updateInputWidth();
  });

  // 添加额外的安全措施，确保在iframe或其他元素获得焦点时也能正确处理鼠标事件
  $effect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && DragState.isDragging) {
        // 如果页面不可见且正在拖动，强制结束拖动
        console.log("页面不可见，强制结束拖动");
        DragState.isDragging = false;
        DragState.currentOnChange = null;
        DragState.currentOnMove = null;
      }
    };

    const handleBlurEvent = () => {
      // 当窗口失去焦点时，如果正在拖动，强制结束拖动
      if (DragState.isDragging) {
        console.log("窗口失焦，强制结束拖动");
        DragState.isDragging = false;
        DragState.currentOnChange = null;
        DragState.currentOnMove = null;
      }
    };

    // 监听页面可见性变化和窗口失焦事件
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('blur', handleBlurEvent);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('blur', handleBlurEvent);
    };
  });

  // 获取容器样式类
  function getContainerClass() {
    const baseClass = 'input-container';
    const sizeClass = size === 'sm' ? 'component-sm' : size === 'lg' ? 'component-lg' : '';
    const variantClass = variant === 'success' ? 'component-success' :
                        variant === 'warning' ? 'component-warning' :
                        variant === 'error' ? 'component-error' : '';
    const focusClass = isFocused ? 'input-focused' : '';
    const disabledClass = disabled ? 'input-disabled' : '';
    const fullWidthClass = fullWidth ? 'component-full-width' : '';

    return [baseClass, sizeClass, variantClass, focusClass, disabledClass, fullWidthClass]
      .filter(Boolean)
      .join(' ');
  }

  // 公开方法
  export function focus() {
    inputElement?.focus();
  }

  export function blur() {
    inputElement?.blur();
  }

  export function select() {
    inputElement?.select();
  }
</script>

<div class={getContainerClass()}>
  {#if leftIcon}
    <button
      type="button"
      class="input-icon input-icon-left"
      onclick={onLeftIconClick}
      tabindex="-1"
    >
      {leftIcon}
    </button>
  {/if}

  {#if isEditing}
    <!-- 编辑模式：显示输入框 -->
    <input
      bind:this={inputElement}
      value={valueToString(value)}
      {type}
      {placeholder}
      {disabled}
      {readonly}
      {required}
      {maxlength}
      {minlength}
      {pattern}
      {id}
      {name}
      {min}
      {max}
      {step}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      class="input-field"
      style="width: {inputWidth}px;"
      oninput={handleInput}
      onchange={handleChange}
      onfocus={handleFocus}
      onblur={handleBlur}
      onkeydown={handleKeydown}
    />
  {:else}
    <!-- 标签模式：显示值，支持拖拽 -->
    <span
      bind:this={labelElement}
      class="input-label"
      class:draggable={type === 'number' && !disabled && !readonly}
      style="width: {inputWidth}px;"
      onpointerdown={handleLabelPointerDown}
      ondblclick={handleLabelDoubleClick}
      role="textbox"
      tabindex={disabled ? -1 : 0}
      onkeydown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleLabelDoubleClick();
        }
      }}
    >
      {valueToString(value) || placeholder || ''}
    </span>
  {/if}

  {#if rightIcon}
    <button
      type="button"
      class="input-icon input-icon-right"
      onclick={onRightIconClick}
      tabindex="-1"
    >
      {rightIcon}
    </button>
  {/if}
</div>

<style>
  @import './shared-styles.css';

  .input-container {
    position: relative;
    display: inline-flex;
    align-items: center;
    background-color: transparent;
    border: none;
    border-radius: var(--border-radius);
    transition: var(--transition-base);
    width: fit-content;
  }

  .input-container:hover:not(.input-disabled) {
    border-color: var(--theme-border-dark);
  }

  .input-container.input-focused {
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px rgba(74, 85, 104, 0.2);
  }

  .input-container.input-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: var(--theme-surface-dark);
  }

  .input-container.input-full-width {
    width: 100%;
  }

  .input-field {
    border: var(--component-border);
    background: var(--component-bg);
    color: var(--component-text);
    font-family: var(--component-font-family);
    font-size: var(--component-font-size);
    line-height: var(--component-line-height);
    outline: none;
    border-radius: var(--component-border-radius);
    text-align: center;
    width: fit-content;
    padding: var(--component-padding);
    transition: var(--component-transition);
  }

  /* 隐藏数字输入框的调节按钮 */
  .input-field::-webkit-outer-spin-button,
  .input-field::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .input-field[type=number] {
    -moz-appearance: textfield;
    appearance: textfield;
  }

  .input-field::placeholder {
    color: var(--theme-text-secondary);
    opacity: 0.7;
  }

  .input-field:disabled {
    cursor: not-allowed;
  }

  .input-label {
    padding: 1px 2px;
    color: var(--component-text);
    font-family: var(--component-font-family);
    font-size: var(--component-font-size);
    line-height: var(--component-line-height);
    background: transparent;
    border: none;
    outline: none;
    cursor: default;
    user-select: none;
    white-space: nowrap;
    display: inline-block;
    text-align: center;
    border-bottom: 1px solid rgba(64, 105, 240, 0.3);
    position: relative;
    width: fit-content;
  }

  .input-label.draggable {
    cursor: ew-resize;
  }

  .input-label.draggable:hover {
    background: rgba(64, 105, 240, 0.1);
    border-radius: 2px;
    border-bottom-color: rgba(64, 105, 240, 0.6);
  }

  .input-label:focus {
    outline: 2px solid var(--theme-primary);
    outline-offset: -2px;
    border-radius: 2px;
  }

  .input-label:empty::before {
    content: attr(placeholder);
    color: var(--theme-text-secondary);
    opacity: 0.7;
  }

  .input-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--theme-text-secondary);
    cursor: pointer;
    transition: var(--transition-base);
  }

  .input-icon:hover {
    color: var(--theme-text);
  }

  .input-icon-left {
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-2);
  }

  .input-icon-right {
    padding-left: var(--spacing-2);
    padding-right: var(--spacing-3);
  }

  /* 尺寸变体 */
  .input-sm .input-field,
  .input-sm .input-label {
    padding: 1px 2px;
    font-size: 10px;
  }

  .input-md .input-field,
  .input-md .input-label {
    padding: 1px 2px;
    font-size: 11px;
  }

  .input-lg .input-field,
  .input-lg .input-label {
    padding: 1px 2px;
    font-size: 12px;
  }

  /* 状态变体 */
  .input-success {
    border-color: var(--theme-success);
  }

  .input-success.input-focused {
    border-color: var(--theme-success);
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
  }

  .input-warning {
    border-color: var(--theme-warning);
  }

  .input-warning.input-focused {
    border-color: var(--theme-warning);
    box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.2);
  }

  .input-error {
    border-color: var(--theme-error);
  }

  .input-error.input-focused {
    border-color: var(--theme-error);
    box-shadow: 0 0 0 2px rgba(229, 62, 62, 0.2);
  }
</style>
