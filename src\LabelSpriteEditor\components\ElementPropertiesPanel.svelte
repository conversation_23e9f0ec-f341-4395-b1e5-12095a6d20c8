<script lang="ts">
  import TextElementPanel from './TextElementPanel.svelte';
  import ImageCropPanel from './ImageCropPanel.svelte';
  import { selectedElement, selectedElementIndex } from '../stores/bitmapStore';
</script>

<div class="element-properties-panel">
  <div class="panel-header">
    <h3>元素属性</h3>
    {#if $selectedElement}
      <span class="element-indicator">
        <span class="element-type" class:text-type={$selectedElement.type === 'text'} class:image-type={$selectedElement.type === 'image'}>
          {$selectedElement.type === 'text' ? 'T' : '🖼'}
        </span>
        #{$selectedElementIndex + 1}
      </span>
    {/if}
  </div>

  <div class="panel-content">
    {#if $selectedElement}
      <div class="properties-form">
        <!-- 基本信息 -->
        <div class="property-section">
          <h4>基本信息 - {$selectedElement.type === 'text' ? '文本元素' : '图片元素'}</h4>
        </div>

        <!-- 文本元素属性 -->
        {#if $selectedElement.type === 'text'}
          <TextElementPanel element={$selectedElement} />
        {/if}

        <!-- 图片元素属性 -->
        {#if $selectedElement.type === 'image'}
          <ImageCropPanel element={$selectedElement} />
        {/if}
      </div>
    {:else}
      <div class="no-selection">
        <div class="no-selection-content">
          <div class="icon">📝</div>
          <h4>请选择一个元素</h4>
          <p>从左侧列表中选择一个元素来编辑其属性</p>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .element-properties-panel {
    height: 100%;
    background: var(--theme-surface, #ffffff);
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    padding: var(--spacing-1, 0.25rem) var(--spacing-2, 0.5rem);
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    background: var(--theme-surface-light, #f9fafb);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .panel-header h3 {
    margin: 0;
    font-size: var(--font-size-xs, 0.75rem);
    font-weight: 600;
    color: var(--theme-text, #111827);
  }

  .element-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-1, 0.25rem);
    font-size: var(--font-size-xs, 0.75rem);
    color: var(--theme-text-secondary, #6b7280);
  }

  .element-type {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 2px;
    font-size: 10px;
    font-weight: bold;
    color: white;
  }

  .element-type.text-type {
    background: var(--theme-primary, #3b82f6);
  }

  .element-type.image-type {
    background: var(--theme-success, #10b981);
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-1, 0.25rem) var(--spacing-2, 0.5rem);
  }

  .properties-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2, 0.5rem);
  }

  .property-section {
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: var(--border-radius, 4px);
    overflow: hidden;
  }

  .property-section h4 {
    margin: 0;
    padding: var(--spacing-2, 0.5rem);
    background: var(--theme-surface-light, #f9fafb);
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    font-size: var(--font-size-xs, 0.75rem);
    font-weight: 600;
    color: var(--theme-text, #111827);
  }



  .no-selection {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 300px;
  }

  .no-selection-content {
    text-align: center;
    color: var(--theme-text-secondary, #6b7280);
  }

  .no-selection-content .icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-3, 0.75rem);
  }

  .no-selection-content h4 {
    margin: 0 0 var(--spacing-2, 0.5rem) 0;
    font-size: var(--font-size-lg, 1.125rem);
    color: var(--theme-text, #111827);
  }

  .no-selection-content p {
    margin: 0;
    font-size: var(--font-size-sm, 0.875rem);
  }
</style>
