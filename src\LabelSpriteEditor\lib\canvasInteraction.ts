import type { Element, TextElement, ImageElement } from '../types';

/**
 * Canvas 交互处理器
 * 负责处理选中对象的包围盒绘制和鼠标交互
 */
export class CanvasInteractionHandler {
  private canvasElement: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private currentBitmap: any = null;
  private elements: Element[] = [];
  private selectedIndex: number = -1;

  // 鼠标交互状态
  private isDragging = false;
  private dragStartX = 0;
  private dragStartY = 0;
  private dragElementStartX = 0;
  private dragElementStartY = 0;

  // 包围盒样式配置
  private readonly SELECTION_BOX_STYLE = {
    strokeColor: '#3b82f6',
    strokeWidth: 2,
    fillColor: 'rgba(59, 130, 246, 0.1)',
    handleSize: 8,
    handleColor: '#3b82f6'
  };

  // 回调函数
  private onElementSelected?: (index: number) => void;
  private onElementMoved?: (index: number, x: number, y: number) => void;

  constructor(
    canvasElement: HTMLCanvasElement,
    ctx: CanvasRenderingContext2D,
    options?: {
      onElementSelected?: (index: number) => void;
      onElementMoved?: (index: number, x: number, y: number) => void;
    }
  ) {
    this.canvasElement = canvasElement;
    this.ctx = ctx;
    this.onElementSelected = options?.onElementSelected;
    this.onElementMoved = options?.onElementMoved;
    this.setupEventListeners();
  }

  /**
   * 更新数据
   */
  updateData(bitmap: any, elements: Element[], selectedIndex: number) {
    this.currentBitmap = bitmap;
    this.elements = elements;
    this.selectedIndex = selectedIndex;
  }

  /**
   * 计算文本元素的包围盒
   */
  private getTextElementBounds(element: TextElement): { x: number, y: number, width: number, height: number } {
    if (!this.currentBitmap || !this.ctx) {
      return { x: element.x, y: element.y, width: 100, height: 30 };
    }

    // 🔧 使用与 RPG Maker 相同的字体设置方式
    this.ctx.save();

    // 构建完整的字体字符串，与 RPG Maker 的 _makeFontNameText() 方法保持一致
    const fontSize = this.currentBitmap.fontSize || 16;
    const fontFace = this.currentBitmap.fontFace || 'Arial';
    const fontBold = this.currentBitmap.fontBold ? 'bold ' : '';
    const fontItalic = this.currentBitmap.fontItalic ? 'italic ' : '';

    this.ctx.font = `${fontItalic}${fontBold}${fontSize}px ${fontFace}`;
    this.ctx.textBaseline = 'alphabetic'; // 与 RPG Maker 保持一致

    // 测量文本宽度
    const textMetrics = this.ctx.measureText(element.text);
    const maxWidth = element.maxWidth || 0xffffffff;
    const textWidth = Math.min(textMetrics.width, maxWidth);
    const lineHeight = element.lineHeight || fontSize;

    this.ctx.restore();

    // 🔧 计算实际的文本包围盒位置
    // 参考 RPG Maker 的 drawText 方法中的坐标计算
    const align = element.align || 'left';
    const boundingY = element.y; // 使用传入的 y 坐标作为包围盒顶部
    const boundingHeight = lineHeight;

    // 🔧 包围盒宽度始终使用实际文本宽度（已经考虑了 maxWidth 限制）
    const boundingWidth = textWidth;

    // 🔧 根据对齐方式和 maxWidth 计算包围盒的 x 坐标
    let boundingX = element.x;

    if (element.maxWidth && element.maxWidth < 0xffffffff) {
      // 有 maxWidth 限制时，需要根据对齐方式调整起始位置
      if (align === 'center') {
        // center 对齐：文本在 maxWidth 区域的中心
        boundingX = element.x + (element.maxWidth / 2) - (textWidth / 2);
      } else if (align === 'right') {
        // right 对齐：文本在 maxWidth 区域的右侧
        boundingX = element.x + element.maxWidth - textWidth;
      } else {
        // left 对齐：文本从 x 坐标开始
        boundingX = element.x;
      }
    } else {
      // 没有 maxWidth 限制时，根据对齐方式调整
      if (align === 'center') {
        // center 对齐：以 x 坐标为中心
        boundingX = element.x - (textWidth / 2);
      } else if (align === 'right') {
        // right 对齐：文本结束于 x 坐标
        boundingX = element.x - textWidth;
      } else {
        // left 对齐：文本从 x 坐标开始
        boundingX = element.x;
      }
    }

    console.log('🔍 文本包围盒计算:', {
      element: { x: element.x, y: element.y, text: element.text, align, maxWidth: element.maxWidth },
      font: `${fontItalic}${fontBold}${fontSize}px ${fontFace}`,
      measured: { textWidth, lineHeight },
      bounds: { x: boundingX, y: boundingY, width: boundingWidth, height: boundingHeight }
    });

    return {
      x: boundingX,
      y: boundingY,
      width: boundingWidth,
      height: boundingHeight
    };
  }

  /**
   * 计算图像元素的包围盒
   */
  private getImageElementBounds(element: ImageElement): { x: number, y: number, width: number, height: number } {
    return {
      x: element.dx,
      y: element.dy,
      width: element.dw,
      height: element.dh
    };
  }

  /**
   * 获取元素的包围盒
   */
  private getElementBounds(element: Element): { x: number, y: number, width: number, height: number } {
    if (element.type === 'text') {
      return this.getTextElementBounds(element as TextElement);
    } else if (element.type === 'image') {
      return this.getImageElementBounds(element as ImageElement);
    }
    return { x: 0, y: 0, width: 0, height: 0 };
  }

  /**
   * 检查点是否在元素内
   */
  private isPointInElement(x: number, y: number, element: Element): boolean {
    const bounds = this.getElementBounds(element);
    return x >= bounds.x &&
           x <= bounds.x + bounds.width &&
           y >= bounds.y &&
           y <= bounds.y + bounds.height;
  }

  /**
   * 根据鼠标位置查找被点击的元素（返回最上层的元素）
   */
  private findElementAtPoint(x: number, y: number): number {
    // 从后往前遍历（后面的元素在上层）
    for (let i = this.elements.length - 1; i >= 0; i--) {
      const element = this.elements[i];
      if (this.isPointInElement(x, y, element)) {
        return i;
      }
    }
    return -1;
  }

  /**
   * 绘制选中元素的包围盒
   */
  drawSelectionBox() {
    if (!this.ctx || this.selectedIndex < 0 || !this.elements[this.selectedIndex]) {
      return;
    }

    const element = this.elements[this.selectedIndex];
    const bounds = this.getElementBounds(element);

    this.ctx.save();

    // 绘制选择框背景
    this.ctx.fillStyle = this.SELECTION_BOX_STYLE.fillColor;
    this.ctx.fillRect(bounds.x, bounds.y, bounds.width, bounds.height);

    // 绘制选择框边框
    this.ctx.strokeStyle = this.SELECTION_BOX_STYLE.strokeColor;
    this.ctx.lineWidth = this.SELECTION_BOX_STYLE.strokeWidth;
    this.ctx.setLineDash([5, 5]);
    this.ctx.strokeRect(bounds.x, bounds.y, bounds.width, bounds.height);

    // 绘制控制点（四个角）
    this.ctx.fillStyle = this.SELECTION_BOX_STYLE.handleColor;
    this.ctx.setLineDash([]);
    const handleSize = this.SELECTION_BOX_STYLE.handleSize;
    const halfHandle = handleSize / 2;

    // 左上角
    this.ctx.fillRect(bounds.x - halfHandle, bounds.y - halfHandle, handleSize, handleSize);
    // 右上角
    this.ctx.fillRect(bounds.x + bounds.width - halfHandle, bounds.y - halfHandle, handleSize, handleSize);
    // 左下角
    this.ctx.fillRect(bounds.x - halfHandle, bounds.y + bounds.height - halfHandle, handleSize, handleSize);
    // 右下角
    this.ctx.fillRect(bounds.x + bounds.width - halfHandle, bounds.y + bounds.height - halfHandle, handleSize, handleSize);

    this.ctx.restore();
  }

  /**
   * 获取鼠标在canvas上的相对坐标
   */
  private getMousePosition(event: MouseEvent): { x: number, y: number } {
    if (!this.canvasElement) return { x: 0, y: 0 };

    const rect = this.canvasElement.getBoundingClientRect();
    return {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };
  }

  /**
   * 鼠标按下事件处理
   */
  private handleMouseDown = (event: MouseEvent) => {
    if (!this.canvasElement || !this.elements.length) return;

    const mousePos = this.getMousePosition(event);
    const clickedElementIndex = this.findElementAtPoint(mousePos.x, mousePos.y);

    console.log('鼠标点击位置:', mousePos, '点击的元素索引:', clickedElementIndex);

    if (clickedElementIndex >= 0) {
      // 选中被点击的元素
      this.selectedIndex = clickedElementIndex;
      this.onElementSelected?.(clickedElementIndex);

      // 开始拖拽
      this.isDragging = true;
      this.dragStartX = mousePos.x;
      this.dragStartY = mousePos.y;

      const element = this.elements[clickedElementIndex];
      if (element.type === 'text') {
        this.dragElementStartX = element.x;
        this.dragElementStartY = element.y;
      } else if (element.type === 'image') {
        this.dragElementStartX = element.dx;
        this.dragElementStartY = element.dy;
      }

      console.log('开始拖拽元素:', clickedElementIndex, '起始位置:', { x: this.dragElementStartX, y: this.dragElementStartY });
    } else {
      // 点击空白区域，取消选择
      this.selectedIndex = -1;
      this.onElementSelected?.(-1);
    }
  };

  /**
   * 鼠标移动事件处理
   */
  private handleMouseMove = (event: MouseEvent) => {
    if (!this.isDragging || this.selectedIndex < 0 || !this.canvasElement) return;

    const mousePos = this.getMousePosition(event);
    const deltaX = mousePos.x - this.dragStartX;
    const deltaY = mousePos.y - this.dragStartY;

    const newX = this.dragElementStartX + deltaX;
    const newY = this.dragElementStartY + deltaY;

    console.log('拖拽中:', { deltaX, deltaY, newX, newY });

    // 通知外部更新元素位置
    this.onElementMoved?.(this.selectedIndex, newX, newY);
  };

  /**
   * 鼠标释放事件处理
   */
  private handleMouseUp = (event: MouseEvent) => {
    if (this.isDragging) {
      console.log('结束拖拽');
      this.isDragging = false;
    }
  };

  /**
   * 设置鼠标事件监听器
   */
  private setupEventListeners() {
    if (!this.canvasElement) return;

    this.canvasElement.addEventListener('mousedown', this.handleMouseDown);
    document.addEventListener('mousemove', this.handleMouseMove);
    document.addEventListener('mouseup', this.handleMouseUp);

    // 设置canvas样式
    this.canvasElement.style.cursor = 'default';
  }

  /**
   * 清理事件监听器
   */
  destroy() {
    if (!this.canvasElement) return;

    this.canvasElement.removeEventListener('mousedown', this.handleMouseDown);
    document.removeEventListener('mousemove', this.handleMouseMove);
    document.removeEventListener('mouseup', this.handleMouseUp);
  }
}
