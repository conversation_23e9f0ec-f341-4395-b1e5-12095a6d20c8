[null, {"id": 1, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 1, "maxTurns": 1, "message1": "%1倒下了！", "message2": "打倒了%1！", "message3": "", "message4": "%1复活了！", "minTurns": 1, "motion": 3, "name": "死亡", "note": "当生命值为0时，将会添加状态 #1 。", "overlay": 0, "priority": 100, "releaseByDamage": false, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 23, "dataId": 9, "value": 0}], "messageType": 1}, {"id": 2, "autoRemovalTiming": 2, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "防御", "note": "", "overlay": 0, "priority": 0, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": true, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 62, "dataId": 1, "value": 0}], "messageType": 1}, {"id": 3, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "永生", "note": "", "overlay": 0, "priority": 0, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 14, "dataId": 1, "value": 0}], "messageType": 1}, {"id": 4, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 2, "maxTurns": 1, "message1": "%1中毒了！", "message2": "使%1中毒了！", "message3": "", "message4": "%1解除了中毒！", "minTurns": 1, "motion": 1, "overlay": 1, "name": "中毒", "note": "", "priority": 50, "releaseByDamage": false, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 7, "value": -0.1}], "messageType": 1}, {"id": 5, "autoRemovalTiming": 1, "chanceByDamage": 100, "iconIndex": 3, "maxTurns": 5, "message1": "%1被致盲了！", "message2": "致盲了%1！", "message3": "", "message4": "%1解除了致盲！", "minTurns": 3, "motion": 1, "name": "黑暗", "note": "", "overlay": 2, "priority": 60, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 0, "value": -0.5}], "messageType": 1}, {"id": 6, "autoRemovalTiming": 1, "chanceByDamage": 100, "iconIndex": 4, "maxTurns": 5, "message1": "%1被沉默了！", "message2": "使%1沉默了！", "message3": "", "message4": "%1解除了沉默！", "minTurns": 3, "motion": 1, "name": "沉默", "note": "", "overlay": 3, "priority": 65, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 42, "dataId": 1, "value": 0}], "messageType": 1}, {"id": 7, "autoRemovalTiming": 1, "chanceByDamage": 50, "iconIndex": 5, "maxTurns": 4, "message1": "%1施放了激昂！", "message2": "使%1进入了激昂！", "message3": "", "message4": "%1解除了激昂！", "minTurns": 2, "motion": 1, "name": "激昂", "note": "", "overlay": 4, "priority": 70, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 1, "stepsToRemove": 100, "traits": [], "messageType": 1}, {"id": 8, "autoRemovalTiming": 1, "chanceByDamage": 50, "iconIndex": 6, "maxTurns": 4, "message1": "%1混乱了！", "message2": "使%1混乱了！", "message3": "", "message4": "%1解除了混乱！", "minTurns": 2, "motion": 1, "name": "混乱", "note": "", "overlay": 5, "priority": 75, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 2, "stepsToRemove": 100, "traits": [], "messageType": 1}, {"id": 9, "autoRemovalTiming": 1, "chanceByDamage": 50, "iconIndex": 7, "maxTurns": 4, "message1": "%1被魅惑了！", "message2": "使%1被魅惑了！", "message3": "", "message4": "%1解除了魅惑！", "minTurns": 2, "motion": 1, "name": "魅惑", "note": "", "overlay": 6, "priority": 80, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 3, "stepsToRemove": 100, "traits": [], "messageType": 1}, {"id": 10, "autoRemovalTiming": 1, "chanceByDamage": 100, "iconIndex": 8, "maxTurns": 5, "message1": "%1进入了睡眠！", "message2": "使%1进入了睡眠！", "message3": "%1正在睡眠。", "message4": "%1醒来了！", "minTurns": 3, "motion": 2, "name": "睡眠", "note": "", "overlay": 7, "priority": 90, "releaseByDamage": true, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 1, "value": -1}], "messageType": 1}, {"id": 11, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 12, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 1, "value": -1}], "iconIndex": 9, "maxTurns": 3, "message1": "%1被麻痹且无法动弹！", "message2": "麻痹了%1！", "message3": "%1被麻痹且无法动弹！", "message4": "%1解除了麻痹！", "minTurns": 3, "motion": 2, "name": "麻痹", "note": "", "overlay": 8, "priority": 95, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "messageType": 1}, {"id": 13, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 1, "value": -1}], "iconIndex": 9, "maxTurns": 2, "message1": "%1失去平衡！", "message2": "使%1失去了平衡！", "message3": "%1仍处于失衡......", "message4": "%1重获平衡！", "minTurns": 1, "motion": 1, "name": "眩晕", "note": "", "overlay": 10, "priority": 90, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "messageType": 1}, {"id": 14, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 15, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 7, "value": 0.1}], "iconIndex": 72, "maxTurns": 4, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 4, "motion": 0, "name": "生命值自动恢复", "note": "", "overlay": 0, "priority": 40, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 16, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 8, "value": 0.1}], "iconIndex": 72, "maxTurns": 4, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 4, "motion": 0, "name": "魔法值自动恢复", "note": "", "overlay": 0, "priority": 40, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 17, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 9, "value": 0.1}], "iconIndex": 72, "maxTurns": 4, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 4, "motion": 0, "name": "特技值自动恢复", "note": "", "overlay": 0, "priority": 40, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 18, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 5, "value": 1}], "iconIndex": 70, "maxTurns": 5, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 5, "motion": 0, "name": "魔法反射", "note": "", "overlay": 0, "priority": 40, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 19, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 62, "dataId": 2, "value": 1}], "iconIndex": 81, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "保护", "note": "", "overlay": 0, "priority": 40, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": true, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 20, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 23, "dataId": 0, "value": 9}], "iconIndex": 76, "maxTurns": 5, "message1": "%1变得更容易被击中！", "message2": "", "message3": "", "message4": "", "minTurns": 5, "motion": 0, "name": "嘲讽", "note": "", "overlay": 0, "priority": 40, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 21, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 23, "dataId": 0, "value": 0}], "iconIndex": 81, "maxTurns": 5, "message1": "%1变得更难被击中！", "message2": "", "message3": "", "message4": "", "minTurns": 5, "motion": 0, "name": "隐匿", "note": "", "overlay": 0, "priority": 40, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 22, "autoRemovalTiming": 1, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 6, "value": 1}], "iconIndex": 77, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "反击", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": true, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 23, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 14, "dataId": 4, "value": 1}, {"code": 14, "dataId": 5, "value": 1}, {"code": 14, "dataId": 6, "value": 1}, {"code": 14, "dataId": 7, "value": 1}, {"code": 14, "dataId": 8, "value": 1}, {"code": 14, "dataId": 9, "value": 1}, {"code": 14, "dataId": 10, "value": 1}, {"code": 14, "dataId": 12, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "iconIndex": 70, "maxTurns": 5, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 5, "motion": 0, "name": "状态异常封锁", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 24, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 23, "dataId": 4, "value": 0.25}], "iconIndex": 70, "maxTurns": 5, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 5, "motion": 0, "name": "降低MP消耗率", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 25, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 31, "dataId": 2, "value": 1}, {"code": 11, "dataId": 2, "value": 0.5}, {"code": 21, "dataId": 2, "value": 1.1}], "iconIndex": 64, "maxTurns": 5, "message1": "%1被火焰之力笼罩。", "message2": "%1被火焰之力笼罩。", "message3": "", "message4": "", "minTurns": 5, "motion": 0, "name": "火焰之力", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 26, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 31, "dataId": 3, "value": 1}, {"code": 11, "dataId": 3, "value": 0.5}, {"code": 21, "dataId": 2, "value": 1.1}], "iconIndex": 65, "maxTurns": 5, "message1": "%1被寒冰之力笼罩。", "message2": "%1被寒冰之力笼罩。", "message3": "", "message4": "", "minTurns": 5, "motion": 0, "name": "寒冰之力", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 27, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 31, "dataId": 4, "value": 1}, {"code": 11, "dataId": 4, "value": 0.5}, {"code": 21, "dataId": 2, "value": 1.1}], "iconIndex": 66, "maxTurns": 5, "message1": "%1被雷电之力笼罩。", "message2": "%1被雷电之力笼罩。", "message3": "", "message4": "", "minTurns": 5, "motion": 0, "name": "雷电之力", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 28, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 29, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 64, "dataId": 0, "value": 1}], "iconIndex": 72, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "遇敌减半", "note": "", "overlay": 0, "priority": 20, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": true, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 30, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 64, "dataId": 4, "value": 1}, {"code": 64, "dataId": 5, "value": 1}], "iconIndex": 70, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "双倍金钱/物品", "note": "", "overlay": 0, "priority": 20, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": true, "restriction": 0, "stepsToRemove": 100, "messageType": 1}]