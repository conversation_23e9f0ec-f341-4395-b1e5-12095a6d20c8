<script lang="ts">
  /**
   * Switch 开关组件
   * 基于 Skeleton UI 和全局主题色彩
   */
  
  // Props
  export let checked: boolean = false;
  export let disabled: boolean = false;
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let variant: 'default' | 'success' | 'warning' | 'error' = 'default';
  export let label: string = '';
  export let description: string = '';
  export let labelPosition: 'left' | 'right' = 'right';
  export let value: string | number = '';
  export let name: string = '';
  export let id: string = '';
  export let ariaLabel: string = '';
  export let ariaDescribedBy: string = '';
  
  // Events
  export let onChange: (checked: boolean, event: Event) => void = () => {};
  export let onFocus: (event: FocusEvent) => void = () => {};
  export let onBlur: (event: FocusEvent) => void = () => {};
  
  // 内部状态
  let switchElement: HTMLInputElement;
  
  // 处理变化事件
  function handleChange(event: Event) {
    const target = event.target as HTMLInputElement;
    checked = target.checked;
    onChange(checked, event);
  }
  
  // 获取容器样式类
  function getContainerClass() {
    const baseClass = 'switch-container';
    const sizeClass = `switch-${size}`;
    const variantClass = `switch-${variant}`;
    const checkedClass = checked ? 'switch-checked' : '';
    const disabledClass = disabled ? 'switch-disabled' : '';
    const positionClass = `switch-label-${labelPosition}`;
    
    return [baseClass, sizeClass, variantClass, checkedClass, disabledClass, positionClass]
      .filter(Boolean)
      .join(' ');
  }
  
  // 公开方法
  export function focus() {
    switchElement?.focus();
  }
  
  export function blur() {
    switchElement?.blur();
  }
  
  export function toggle() {
    if (!disabled) {
      checked = !checked;
      onChange(checked, new Event('change'));
    }
  }
</script>

<label class={getContainerClass()}>
  {#if (label || description || $$slots.default) && labelPosition === 'left'}
    <div class="switch-content">
      {#if label}
        <div class="switch-label">{label}</div>
      {/if}
      
      <slot />
      
      {#if description}
        <div class="switch-description">{description}</div>
      {/if}
    </div>
  {/if}
  
  <div class="switch-wrapper">
    <input
      bind:this={switchElement}
      type="checkbox"
      bind:checked
      {disabled}
      {value}
      {name}
      {id}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      class="switch-input"
      onchange={handleChange}
      onfocus={onFocus}
      onblur={onBlur}
    />
    
    <div class="switch-track">
      <div class="switch-thumb"></div>
    </div>
  </div>
  
  {#if (label || description || $$slots.default) && labelPosition === 'right'}
    <div class="switch-content">
      {#if label}
        <div class="switch-label">{label}</div>
      {/if}
      
      <slot />
      
      {#if description}
        <div class="switch-description">{description}</div>
      {/if}
    </div>
  {/if}
</label>

<style>
  .switch-container {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    cursor: pointer;
    transition: var(--transition-base);
    padding: var(--spacing-1);
    border-radius: var(--border-radius);
  }
  
  .switch-container:hover:not(.switch-disabled) {
    background-color: var(--theme-surface-light);
  }
  
  .switch-container.switch-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .switch-container.switch-label-left {
    flex-direction: row;
  }
  
  .switch-container.switch-label-right {
    flex-direction: row;
  }
  
  .switch-wrapper {
    position: relative;
    flex-shrink: 0;
  }
  
  .switch-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .switch-track {
    position: relative;
    background-color: var(--theme-surface-light);
    border: 2px solid var(--theme-border);
    border-radius: var(--border-radius-large);
    transition: var(--transition-base);
    cursor: pointer;
  }
  
  .switch-container:hover:not(.switch-disabled) .switch-track {
    border-color: var(--theme-primary);
  }
  
  .switch-checked .switch-track {
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);
  }
  
  .switch-thumb {
    position: absolute;
    top: 2px;
    background-color: var(--theme-text-inverse);
    border-radius: 50%;
    transition: var(--transition-base);
    box-shadow: 0 2px 4px var(--theme-shadow-light);
  }
  
  .switch-checked .switch-thumb {
    background-color: var(--theme-text-inverse);
  }
  
  .switch-content {
    flex: 1;
    min-width: 0;
  }
  
  .switch-label {
    color: var(--theme-text);
    font-family: var(--font-family-base);
    font-weight: 500;
    line-height: 1.4;
  }
  
  .switch-description {
    color: var(--theme-text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
    margin-top: var(--spacing-1);
  }
  
  /* 尺寸变体 */
  .switch-sm .switch-track {
    width: 32px;
    height: 18px;
  }
  
  .switch-sm .switch-thumb {
    width: 14px;
    height: 14px;
    left: 2px;
  }
  
  .switch-sm.switch-checked .switch-thumb {
    transform: translateX(14px);
  }
  
  .switch-sm .switch-label {
    font-size: var(--font-size-sm);
  }
  
  .switch-md .switch-track {
    width: 44px;
    height: 24px;
  }
  
  .switch-md .switch-thumb {
    width: 20px;
    height: 20px;
    left: 2px;
  }
  
  .switch-md.switch-checked .switch-thumb {
    transform: translateX(20px);
  }
  
  .switch-md .switch-label {
    font-size: var(--font-size-base);
  }
  
  .switch-lg .switch-track {
    width: 56px;
    height: 30px;
  }
  
  .switch-lg .switch-thumb {
    width: 26px;
    height: 26px;
    left: 2px;
  }
  
  .switch-lg.switch-checked .switch-thumb {
    transform: translateX(26px);
  }
  
  .switch-lg .switch-label {
    font-size: var(--font-size-lg);
  }
  
  /* 状态变体 */
  .switch-success.switch-checked .switch-track {
    background-color: var(--theme-success);
    border-color: var(--theme-success);
  }
  
  .switch-warning.switch-checked .switch-track {
    background-color: var(--theme-warning);
    border-color: var(--theme-warning);
  }
  
  .switch-error.switch-checked .switch-track {
    background-color: var(--theme-error);
    border-color: var(--theme-error);
  }
  
  /* 焦点样式 */
  .switch-input:focus-visible + .switch-track {
    outline: 2px solid var(--theme-primary);
    outline-offset: 2px;
  }
  
  /* 动画效果 */
  .switch-container:active:not(.switch-disabled) .switch-thumb {
    transform: scale(0.95);
  }
  
  .switch-container.switch-checked:active:not(.switch-disabled) .switch-thumb {
    transform: scale(0.95);
  }
  
  .switch-sm.switch-checked:active:not(.switch-disabled) .switch-thumb {
    transform: translateX(14px) scale(0.95);
  }
  
  .switch-md.switch-checked:active:not(.switch-disabled) .switch-thumb {
    transform: translateX(20px) scale(0.95);
  }
  
  .switch-lg.switch-checked:active:not(.switch-disabled) .switch-thumb {
    transform: translateX(26px) scale(0.95);
  }
</style>
