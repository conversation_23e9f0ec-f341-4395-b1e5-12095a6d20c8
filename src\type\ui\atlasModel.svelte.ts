import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * UIAtlas模型类 - 智能图集容器
 * 作为容器管理UIImage和UILabel子对象，支持编辑模式和合并模式
 */
export class AtlasModel extends BaseObjectModel {
    // 图集基础配置（使用继承的 width 和 height）

    // 模式状态
    isAtlasMode = $state(false);  // false: 编辑模式, true: 合并模式

    // 统计信息
    childrenCount = $state(0);
    canMerge = $state(false);

    // 存储子对象模型的备份（用于合并模式切换）
    private _childrenBackup: BaseObjectModel[] = [];

    constructor(obj: any) {
        super(obj);
        this.className = 'UIAtlas';

        // 初始化图集属性
        if (obj) {
            this.isAtlasMode = obj._isAtlasMode || false;
        }
        // 设置默认尺寸
        this.width = 512;
        this.height = 512;

        // 如果初始就是合并模式，需要隐藏子对象
        if (this.isAtlasMode) {
            this.hideChildrenInUI();
        }

        console.log('🎨 AtlasModel: 创建智能图集容器模型', {
            width: this.width,
            height: this.height,
            mode: this.isAtlasMode ? 'atlas' : 'edit'
        });
    }

    /**
     * 更新统计信息（从原始对象同步）
     */
    updateStats(): void {
        if (this._originalObject && typeof this._originalObject.getChildrenCount === 'function') {
            this.childrenCount = this._originalObject.getChildrenCount();
            this.canMerge = this._originalObject.canMerge();
            this.isAtlasMode = this._originalObject.isAtlasMode();
        }
    }

    /**
     * 从原始对象同步子对象到 children 数组（编辑模式）
     */
    private syncChildrenFromOriginal(): void {
        if (!this._originalObject || !this._originalObject._children) {
            return;
        }

        // 备份当前子对象到 _childrenBackup
        this._childrenBackup = [...this.children];

        // 清空当前 children 数组
        this.children.length = 0;

        // 从原始对象的 _children 重新创建子对象模型
        const originalChildren = this._originalObject._children || [];
        originalChildren.forEach((originalChild: any) => {
            const childModel = this.createChildModel(originalChild);
            this.children.push(childModel);
        });

        console.log('🎨 AtlasModel: 从原始对象同步子对象', {
            originalChildrenCount: originalChildren.length,
            modelChildrenCount: this.children.length
        });
    }

    /**
     * 隐藏子对象在UI中的显示（合并模式）
     */
    private hideChildrenInUI(): void {
        // 备份当前子对象
        this._childrenBackup = [...this.children];

        // 清空 children 数组，这样 UI 就不会显示子对象
        this.children.length = 0;

        console.log('🎨 AtlasModel: 隐藏子对象UI显示', {
            backupCount: this._childrenBackup.length,
            currentCount: this.children.length
        });
    }

    /**
     * 切换到编辑模式
     */
    switchToEditMode(): void {
        if (this._originalObject && typeof this._originalObject.switchToEditMode === 'function') {
            this._originalObject.switchToEditMode();
            this.updateStats();

            // 恢复子对象显示：重新同步子对象到 children 数组
            this.syncChildrenFromOriginal();

            console.log('🎨 AtlasModel: 切换到编辑模式', {
                isAtlasMode: this.isAtlasMode,
                childrenCount: this.children.length
            });
        }
    }

    /**
     * 切换到合并模式
     */
    async switchToAtlasMode(): Promise<void> {
        if (this._originalObject && typeof this._originalObject.switchToAtlasMode === 'function') {
            await this._originalObject.switchToAtlasMode();
            this.updateStats();

            // 隐藏子对象显示：清空 children 数组（但不销毁子对象模型）
            this.hideChildrenInUI();

            console.log('🎨 AtlasModel: 切换到合并模式', {
                isAtlasMode: this.isAtlasMode,
                childrenCount: this.children.length
            });
        }
    }

    /**
     * 解开合并（直接调用编辑模式）
     */
    unmerge(): void {
        // 直接调用切换到编辑模式，因为 unmerge 就是切换到编辑模式
        this.switchToEditMode();

        console.log('🎨 AtlasModel: 解开合并（切换到编辑模式）', {
            isAtlasMode: this.isAtlasMode,
            childrenCount: this.children.length
        });
    }

    /**
     * 切换模式
     */
    async toggleMode(): Promise<void> {
        if (this._originalObject && typeof this._originalObject.toggleMode === 'function') {
            await this._originalObject.toggleMode();
            this.updateStats();

            // 根据新模式同步子对象显示
            if (this.isAtlasMode) {
                this.hideChildrenInUI();
            } else {
                this.syncChildrenFromOriginal();
            }

            console.log('🎨 AtlasModel: 切换模式', {
                isAtlasMode: this.isAtlasMode,
                childrenCount: this.children.length
            });
        }
    }

    /**
     * 重写添加子对象方法，处理 UIAtlas 特殊逻辑
     */
    public addChild(child: any): void {
        // 先尝试调用原始对象的 addChild 方法
        if (this._originalObject && typeof this._originalObject.addChild === 'function') {
            try {
                this._originalObject.addChild(child);
            } catch (error) {
                console.error('🎨 AtlasModel: 添加子对象到原始对象失败', error);
                // 如果原始对象添加失败，抛出错误，不修改模型
                throw error;
            }
        }

        // 只有原始对象添加成功后，才修改模型
        // 只有在编辑模式下才在 UI 中显示子对象
        if (!this.isAtlasMode) {
            const childModel = this.createChildModel(child);
            childModel.parent = this;
            this.children.push(childModel);
        }

        // 更新统计信息
        this.updateStats();

        console.log('🎨 AtlasModel: 添加子对象成功', {
            childType: child.constructor.name,
            isAtlasMode: this.isAtlasMode,
            uiChildrenCount: this.children.length
        });
    }

    /**
     * 重写移除子对象方法，处理 UIAtlas 特殊逻辑
     */
    public removeChild(index: number): void {
        // 从 UI children 数组中移除（如果存在）
        if (index >= 0 && index < this.children.length) {
            const childModel = this.children[index];

            // 销毁子对象的响应式效果
            if (childModel.destroy) {
                childModel.destroy();
            }

            this.children.splice(index, 1);
        }

        // 从原始对象中移除（需要根据实际的原始子对象索引）
        if (this._originalObject && this._originalObject._children && index < this._originalObject._children.length) {
            const originalChild = this._originalObject._children[index];
            if (this._originalObject.removeChild && originalChild) {
                this._originalObject.removeChild(originalChild);
            }
        }

        // 更新统计信息
        this.updateStats();

        console.log('🎨 AtlasModel: 移除子对象', {
            index,
            isAtlasMode: this.isAtlasMode,
            uiChildrenCount: this.children.length
        });
    }

    /**
     * 获取状态信息
     */
    getStatus(): any {
        if (this._originalObject && typeof this._originalObject.getStatus === 'function') {
            return this._originalObject.getStatus();
        }
        return {
            mode: this.isAtlasMode ? 'atlas' : 'edit',
            childrenCount: this.childrenCount,
            canMerge: this.canMerge,
            hasTexture: false,
            size: {
                width: this.width,
                height: this.height
            }
        };
    }

    /**
     * 设置特有属性同步（重写基类方法）
     */
    protected setupSpecificSync(): void {
        // Atlas特有属性同步
        if (this._originalObject) {
            // UIAtlas 现在使用 getter/setter 来访问 _atlasWidth 和 _atlasHeight
            this._originalObject.width = this.width;
            this._originalObject.height = this.height;

            // 更新统计信息
            this.updateStats();
        }
        console.log('🎨 AtlasModel: Atlas特有属性已同步');
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        return `${indent}const ${varName} = new UIAtlas();`;
    }

    /**
     * 重写基础属性生成，避免尺寸冲突
     */
    protected generateBasicProperties(varName: string, indent: string): string {
        const codes: string[] = [];

        // 位置属性
        if (this.x !== 0 || this.y !== 0) {
            codes.push(`${indent}${varName}.x = ${this.x};`);
            codes.push(`${indent}${varName}.y = ${this.y};`);
        }

        // 对于 UIAtlas，跳过 width/height 设置，因为已经在构造函数中设置了
        // 避免与 atlasWidth/atlasHeight 冲突

        // 缩放属性
        if (this.scaleX !== 1 || this.scaleY !== 1) {
            codes.push(`${indent}${varName}.scale.x = ${this.scaleX};`);
            codes.push(`${indent}${varName}.scale.y = ${this.scaleY};`);
        }

        // 显示属性
        if (this.alpha !== 1) {
            codes.push(`${indent}${varName}.alpha = ${this.alpha};`);
        }

        if (!this.visible) {
            codes.push(`${indent}${varName}.visible = ${this.visible};`);
        }

        // 旋转属性
        if (this.rotation !== 0) {
            codes.push(`${indent}${varName}.rotation = ${this.rotation};`);
        }

        // 锚点属性
        if (this.anchorX !== 0 || this.anchorY !== 0) {
            codes.push(`${indent}${varName}.anchor.x = ${this.anchorX};`);
            codes.push(`${indent}${varName}.anchor.y = ${this.anchorY};`);
        }

        // 轴心属性
        if (this.pivotX !== 0 || this.pivotY !== 0) {
            codes.push(`${indent}${varName}.pivot.x = ${this.pivotX};`);
            codes.push(`${indent}${varName}.pivot.y = ${this.pivotY};`);
        }

        // 倾斜属性
        if (this.skewX !== 0 || this.skewY !== 0) {
            codes.push(`${indent}${varName}.skew.x = ${this.skewX};`);
            codes.push(`${indent}${varName}.skew.y = ${this.skewY};`);
        }

        // zIndex 属性
        if (this.zIndex !== 0) {
            codes.push(`${indent}${varName}.zIndex = ${this.zIndex};`);
        }

        return codes.join('\n');
    }

    /**
     * 重写完整的代码生成，确保正确的执行顺序
     */
    public generateCreationCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        // 1. 对象创建代码
        codes.push(this.generateObjectCreation(varName, indent));

        // 2. 基础属性设置（重写以避免重复）
        codes.push(this.generateAtlasBasicProperties(varName, indent));

        // 3. 子对象代码生成（在合并之前）
        if (this.generateChildrenCode) {
            codes.push(this.generateChildrenCreation(varName, indent));
        }

        // 4. 合并模式（在所有子对象添加完成后）
        codes.push(this.generateAtlasMergeCode(varName, indent));

        return codes.filter(code => code.trim()).join('\n');
    }

    /**
     * Atlas专用的基础属性生成（避免与构造函数冲突）
     */
    private generateAtlasBasicProperties(varName: string, indent: string): string {
        const codes: string[] = [];

        // 位置属性
        if (this.x !== 0 || this.y !== 0) {
            codes.push(`${indent}${varName}.x = ${this.x};`);
            codes.push(`${indent}${varName}.y = ${this.y};`);
        }

        // 对于 UIAtlas，跳过 width/height 设置，因为已经在构造函数中设置了

        // 缩放属性
        if (this.scaleX !== 1 || this.scaleY !== 1) {
            codes.push(`${indent}${varName}.scale.x = ${this.scaleX};`);
            codes.push(`${indent}${varName}.scale.y = ${this.scaleY};`);
        }

        // 显示属性
        if (this.alpha !== 1) {
            codes.push(`${indent}${varName}.alpha = ${this.alpha};`);
        }

        if (!this.visible) {
            codes.push(`${indent}${varName}.visible = ${this.visible};`);
        }

        // 旋转属性
        if (this.rotation !== 0) {
            codes.push(`${indent}${varName}.rotation = ${this.rotation};`);
        }

        // 锚点属性
        if (this.anchorX !== 0 || this.anchorY !== 0) {
            codes.push(`${indent}${varName}.anchor.x = ${this.anchorX};`);
            codes.push(`${indent}${varName}.anchor.y = ${this.anchorY};`);
        }

        // 轴心属性
        if (this.pivotX !== 0 || this.pivotY !== 0) {
            codes.push(`${indent}${varName}.pivot.x = ${this.pivotX};`);
            codes.push(`${indent}${varName}.pivot.y = ${this.pivotY};`);
        }

        // 倾斜属性
        if (this.skewX !== 0 || this.skewY !== 0) {
            codes.push(`${indent}${varName}.skew.x = ${this.skewX};`);
            codes.push(`${indent}${varName}.skew.y = ${this.skewY};`);
        }

        // zIndex 属性
        if (this.zIndex !== 0) {
            codes.push(`${indent}${varName}.zIndex = ${this.zIndex};`);
        }

        return codes.join('\n');
    }

    /**
     * 增强的子对象模型创建，带有更强的类型检测
     */
    private createChildModelWithTypeDetection(child: any): BaseObjectModel {
        const className = child.constructor.name;

        console.log(`🔧 AtlasModel: 增强类型检测`, {
            className: className,
            isUIComponent: child.isUIComponent,
            uiComponentType: child.uiComponentType,
            hasImagePath: !!child.imagePath,
            hasText: !!child.text,
            hasRegions: !!(child.regions && child.regions.length > 0)
        });

        // 1. 优先检查 UI 组件标识
        if (child.isUIComponent && child.uiComponentType) {
            const ModelClass = BaseObjectModel.getRegisteredModel(child.uiComponentType);
            if (ModelClass) {
                console.log(`🔧 AtlasModel: 使用 UI 组件类型创建模型:`, child.uiComponentType);
                return new ModelClass(child) as BaseObjectModel;
            }
        }

        // 2. 基于属性特征进行智能检测
        if (child.imagePath || child.regions || child.gridRows || child.gridCols) {
            // 具有图片相关属性，应该是 UIImage
            const ImageModelClass = BaseObjectModel.getRegisteredModel('UIImage');
            if (ImageModelClass) {
                console.log(`🔧 AtlasModel: 基于属性特征检测为 UIImage`);
                return new ImageModelClass(child) as BaseObjectModel;
            }
        }

        if (child.text || child.fontSize || child.textColor) {
            // 具有文本相关属性，应该是 UILabel
            const LabelModelClass = BaseObjectModel.getRegisteredModel('UILabel');
            if (LabelModelClass) {
                console.log(`🔧 AtlasModel: 基于属性特征检测为 UILabel`);
                return new LabelModelClass(child) as BaseObjectModel;
            }
        }

        // 3. 回退到基类的创建方法
        console.log(`🔧 AtlasModel: 回退到基类创建方法`);
        return this.createChildModel(child);
    }

    /**
     * 生成合并代码（在子对象添加完成后）
     */
    private generateAtlasMergeCode(varName: string, indent: string): string {
        const codes: string[] = [];

        // 在合并模式下，使用备份的子对象；在编辑模式下，使用当前子对象
        const sourceChildren = this.isAtlasMode ? this._childrenBackup : this.children;

        // 过滤掉 Graphics 对象后的有效子对象数量
        const validChildrenCount = sourceChildren.filter(child => {
            const className = child.className || child.constructor?.name || '';
            return !(className === 'Graphics' || className === 'PIXI.Graphics');
        }).length;

        if (validChildrenCount > 0) {
            codes.push(`${indent}// 所有子对象添加完成后，切换到合并模式以获得最佳性能`);
            codes.push(`${indent}// UIAtlas 内部会自动等待图片加载完成`);
            codes.push(`${indent}${varName}.switchToAtlasMode().then(() => {`);
            codes.push(`${indent}    console.log('UIAtlas 合并完成');`);
            codes.push(`${indent}}).catch(error => {`);
            codes.push(`${indent}    console.error('UIAtlas 合并失败:', error);`);
            codes.push(`${indent}});`);
        } else {
            codes.push(`${indent}// 注意：当前UIAtlas没有有效子对象，无需合并`);
        }

        return codes.join('\n');
    }

    /**
     * 重写子对象代码生成，过滤掉 Graphics 等内部对象
     */
    protected generateChildrenCreation(varName: string, indent: string): string {
        // 优先从原始对象重新创建子对象模型，确保类型正确
        let sourceChildren: BaseObjectModel[] = [];

        if (this._originalObject && this._originalObject._children) {
            // 从原始对象重新创建子对象模型，使用增强的类型检测
            sourceChildren = this._originalObject._children.map((originalChild: any) => {
                return this.createChildModelWithTypeDetection(originalChild);
            });
            console.log(`🎨 AtlasModel: 代码生成从原始对象重新创建子对象模型`, {
                originalChildrenCount: this._originalObject._children.length,
                modelChildrenCount: sourceChildren.length,
                childrenTypes: sourceChildren.map(child => child.className)
            });
        } else {
            // 回退到现有逻辑
            sourceChildren = this.isAtlasMode ? this._childrenBackup : this.children;
            console.log(`🎨 AtlasModel: 代码生成使用现有子对象`, {
                mode: this.isAtlasMode ? 'atlas' : 'edit',
                sourceType: this.isAtlasMode ? 'backup' : 'current',
                childrenCount: sourceChildren.length
            });
        }

        // 过滤掉 Graphics 对象（边界框等内部对象）
        const validChildren = sourceChildren.filter(child => {
            const className = child.className || child.constructor?.name || '';
            const isGraphics = className === 'Graphics' || className === 'PIXI.Graphics';

            if (isGraphics) {
                console.log(`🎨 AtlasModel: 代码生成时过滤掉 Graphics 对象`);
                return false;
            }

            return true;
        });

        if (validChildren.length === 0) {
            return '';
        }

        console.log(`🎨 AtlasModel: 代码生成使用子对象`, {
            mode: this.isAtlasMode ? 'atlas' : 'edit',
            sourceType: this.isAtlasMode ? 'backup' : 'current',
            validChildrenCount: validChildren.length
        });

        const codes: string[] = [];
        codes.push(`${indent}// 添加子对象`);

        validChildren.forEach((child, index) => {
            const childVarName = `${varName}_child${index}`;
            codes.push(child.generateCreationCode(childVarName, indent));
            codes.push(`${indent}${varName}.addChild(${childVarName});`);
        });

        return codes.join('\n');
    }

    /**
     * 生成示例代码
     */
    generateExampleCode(): string {
        const code = `    // 创建UIAtlas智能容器
    const exampleAtlas = new UIAtlas();
    exampleAtlas.x = 100;
    exampleAtlas.y = 50;
    exampleAtlas.width = 512;
    exampleAtlas.height = 512;

    // 添加UIImage子对象
    const atlasImage = new UIImage({
        imagePath: 'img/faces/Actor1.png',
        width: 64,
        height: 64
    });
    atlasImage.x = 10;
    atlasImage.y = 10;
    exampleAtlas.addChild(atlasImage);

    // 添加UILabel子对象
    const atlasLabel = new UILabel({
        text: 'Atlas Text',
        fontSize: 18,
        textColor: '#ffffff'
    });
    atlasLabel.x = 80;
    atlasLabel.y = 30;
    exampleAtlas.addChild(atlasLabel);

    // 切换到合并模式以获得最佳性能
    exampleAtlas.switchToAtlasMode().then(() => {
        console.log('UIAtlas 合并完成');
    }).catch(error => {
        console.error('UIAtlas 合并失败:', error);
    });

    // 添加到场景
    this.addChild(exampleAtlas);`;

        return code;
    }

    /**
     * 克隆当前Atlas对象 - 调用插件的 clone 方法
     */
    clone(): AtlasModel {
        console.log('🔄 AtlasModel: 开始克隆Atlas对象（调用插件方法）');

        // 1. 调用原始 UIAtlas 对象的 clone 方法
        const originalUIAtlas = this.getOriginalObject();
        if (!originalUIAtlas || typeof originalUIAtlas.clone !== 'function') {
            console.error('❌ AtlasModel: 原始对象没有 clone 方法');
            throw new Error('UIAtlas 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUIAtlas = originalUIAtlas.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 AtlasModel 包装克隆的对象
        const clonedModel = new AtlasModel(clonedUIAtlas);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ AtlasModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UIAtlas 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UIAtlas 特有属性
            backgroundColor: this.backgroundColor,
            backgroundOpacity: this.backgroundOpacity
        };
    }
}

// 注册AtlasModel到基类容器
BaseObjectModel.registerModel('UIAtlas', AtlasModel);
BaseObjectModel.registerModel('Atlas', AtlasModel);
