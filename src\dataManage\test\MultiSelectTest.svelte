<script lang="ts">
  import SimpleDataSelector from '../components/SimpleDataSelector.svelte';
  import type { DataSelection } from '../types/dataTypes';

  let selection1 = $state<DataSelection | null>(null);
  let selection2 = $state<DataSelection | null>(null);
  let selectionHistory = $state<DataSelection[]>([]);

  function handleSelect1(newSelection: DataSelection | null) {
    selection1 = newSelection;
    if (newSelection) {
      selectionHistory.push(newSelection);
    }
    console.log('选择器1:', newSelection);
  }

  function handleSelect2(newSelection: DataSelection | null) {
    selection2 = newSelection;
    if (newSelection) {
      selectionHistory.push(newSelection);
    }
    console.log('选择器2:', newSelection);
  }

  function clearHistory() {
    selectionHistory = [];
  }
</script>

<div class="test-container">
  <h2>多次选择测试</h2>
  <p>测试是否可以多次选择不同的字段，包括嵌套字段</p>
  
  <div class="selectors-section">
    <div class="selector-group">
      <h3>选择器 1</h3>
      <SimpleDataSelector 
        value={selection1}
        onSelect={handleSelect1}
        placeholder="选择第一个字段"
      />
      {#if selection1}
        <div class="selection-display">
          <strong>当前选择:</strong> {selection1.displayText}
          <br>
          <strong>字段路径:</strong> {selection1.fieldPath}
        </div>
      {/if}
    </div>

    <div class="selector-group">
      <h3>选择器 2</h3>
      <SimpleDataSelector 
        value={selection2}
        onSelect={handleSelect2}
        placeholder="选择第二个字段"
      />
      {#if selection2}
        <div class="selection-display">
          <strong>当前选择:</strong> {selection2.displayText}
          <br>
          <strong>字段路径:</strong> {selection2.fieldPath}
        </div>
      {/if}
    </div>
  </div>

  <div class="history-section">
    <div class="history-header">
      <h3>选择历史</h3>
      <button class="clear-button" onclick={clearHistory}>清除历史</button>
    </div>
    
    {#if selectionHistory.length > 0}
      <div class="history-list">
        {#each selectionHistory as selection, index}
          <div class="history-item">
            <span class="history-index">{index + 1}.</span>
            <span class="history-text">{selection.displayText}</span>
            <span class="history-path">({selection.fieldPath})</span>
          </div>
        {/each}
      </div>
    {:else}
      <div class="no-history">还没有选择记录</div>
    {/if}
  </div>

  <div class="instructions">
    <h3>测试步骤</h3>
    <ol>
      <li>在选择器1中选择一个字段（如：角色数据 → 名称）</li>
      <li>再次打开选择器1，选择另一个字段（如：角色数据 → 参数 → 0）</li>
      <li>在选择器2中选择不同的字段（如：技能数据 → MP消耗）</li>
      <li>验证是否可以多次选择，包括嵌套字段</li>
      <li>检查选择历史是否正确记录</li>
    </ol>
    
    <h4>预期结果：</h4>
    <ul>
      <li>✅ 可以多次选择不同字段</li>
      <li>✅ 支持选择嵌套字段</li>
      <li>✅ 每次选择后下拉框正确关闭</li>
      <li>✅ 再次打开时保持数据类型选择</li>
      <li>✅ 显示友好的字段名称</li>
    </ul>
  </div>
</div>

<style>
  .test-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .selectors-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 24px;
  }

  .selector-group {
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
  }

  .selector-group h3 {
    margin-top: 0;
    margin-bottom: 12px;
    color: #333;
  }

  .selection-display {
    margin-top: 12px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
  }

  .history-section {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
  }

  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .history-header h3 {
    margin: 0;
    color: #333;
  }

  .clear-button {
    padding: 4px 8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 3px;
    font-size: 12px;
    cursor: pointer;
  }

  .clear-button:hover {
    background: #c82333;
  }

  .history-list {
    max-height: 200px;
    overflow-y: auto;
  }

  .history-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .history-item:last-child {
    border-bottom: none;
  }

  .history-index {
    font-weight: 500;
    color: #666;
    min-width: 20px;
  }

  .history-text {
    font-weight: 500;
    color: #333;
    flex: 1;
  }

  .history-path {
    font-family: monospace;
    font-size: 12px;
    color: #666;
  }

  .no-history {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 20px;
  }

  .instructions {
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #f8f9fa;
  }

  .instructions h3, .instructions h4 {
    margin-top: 0;
    color: #333;
  }

  .instructions ol, .instructions ul {
    margin: 0;
    padding-left: 20px;
  }

  .instructions li {
    margin-bottom: 6px;
    line-height: 1.4;
  }

  @media (max-width: 768px) {
    .selectors-section {
      grid-template-columns: 1fr;
    }
  }
</style>
