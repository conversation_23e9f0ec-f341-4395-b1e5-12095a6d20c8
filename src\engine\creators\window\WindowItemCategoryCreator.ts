/**
 * Window_ItemCategory 创建器
 * 专门用于创建物品分类窗口
 */

import { BaseWindowCreator, type WindowCreationOptions } from './WindowCreator';
import { type HorzCommandWindowOptions } from './WindowHorzCommandCreator';

/**
 * 物品分类窗口创建选项
 */
export interface ItemCategoryWindowOptions extends HorzCommandWindowOptions {
    /** 是否显示物品分类 */
    showItemCategory?: boolean;
    /** 是否显示武器分类 */
    showWeaponCategory?: boolean;
    /** 是否显示防具分类 */
    showArmorCategory?: boolean;
    /** 是否显示重要物品分类 */
    showKeyItemCategory?: boolean;
    /** 初始选中的分类 */
    initialCategory?: 'item' | 'weapon' | 'armor' | 'keyItem';
}

/**
 * 创建物品分类窗口
 * @param options 创建选项
 * @returns 创建的物品分类窗口实例
 */
export async function createWindowItemCategory(options: ItemCategoryWindowOptions = {}): Promise<any> {
    console.log('=== 创建物品分类窗口 Window_ItemCategory ===');
    
    try {
        // 预加载资源
        BaseWindowCreator.preloadWindowResources('Window_ItemCategory');
        
        // 设置默认选项
        const defaultOptions: WindowCreationOptions = {
            autoOpen: true,
            addToStage: true,
            rect: options.rect || { x: 0, y: 0, width: 480, height: 72 },
            ...options
        };
        
        // 创建窗口实例
        const window = await BaseWindowCreator.createWindowInstance('Window_ItemCategory', defaultOptions);
        
        // Window_ItemCategory 特定的设置
        setupItemCategoryWindow(window, options);
        
        console.log('Window_ItemCategory 创建完成，窗口属性:', {
            x: window.x,
            y: window.y,
            width: window.width,
            height: window.height,
            maxItems: window.maxItems(),
            maxCols: window.maxCols(),
            index: window.index(),
            active: window.active,
            visible: window.visible
        });
        
        return window;
        
    } catch (error) {
        console.error('创建 Window_ItemCategory 失败:', error);
        throw error;
    }
}

/**
 * 设置物品分类窗口属性
 * @param window 窗口实例
 * @param options 物品分类窗口选项
 */
function setupItemCategoryWindow(window: any, options: ItemCategoryWindowOptions): void {
    console.log('设置物品分类窗口属性...');
    
    try {
        // 如果没有提供自定义命令，使用默认的物品分类
        if (!options.commands) {
            // 清除现有命令列表
            if (window.clearCommandList && typeof window.clearCommandList === 'function') {
                window.clearCommandList();
            }
            
            // 添加标准物品分类
            addStandardItemCategories(window, options);
            
            // 刷新窗口
            if (window.refresh && typeof window.refresh === 'function') {
                window.refresh();
                console.log('物品分类窗口已刷新');
            }
        } else {
            // 使用自定义命令
            if (options.commands && options.commands.length > 0) {
                // 清除现有命令列表
                if (window.clearCommandList && typeof window.clearCommandList === 'function') {
                    window.clearCommandList();
                }
                
                // 添加自定义命令
                options.commands.forEach((command, index) => {
                    if (window.addCommand && typeof window.addCommand === 'function') {
                        window.addCommand(
                            command.name,
                            command.symbol,
                            command.enabled !== false,
                            command.ext || null
                        );
                        console.log(`添加物品分类 ${index + 1}:`, command);
                    }
                });
                
                // 刷新窗口
                if (window.refresh && typeof window.refresh === 'function') {
                    window.refresh();
                    console.log('物品分类窗口已刷新');
                }
            }
        }
        
        // 设置初始选中的分类
        if (options.initialCategory && window.select && typeof window.select === 'function') {
            const categoryIndex = getCategoryIndex(options.initialCategory);
            if (categoryIndex >= 0) {
                window.select(categoryIndex);
                console.log('设置初始分类:', options.initialCategory, '索引:', categoryIndex);
            }
        } else if (options.initialIndex !== undefined && window.select && typeof window.select === 'function') {
            window.select(options.initialIndex);
            console.log('设置初始选中索引:', options.initialIndex);
        }
        
        // 设置激活状态
        if (options.activate !== undefined) {
            if (options.activate && window.activate && typeof window.activate === 'function') {
                window.activate();
                console.log('物品分类窗口已激活');
            } else if (!options.activate && window.deactivate && typeof window.deactivate === 'function') {
                window.deactivate();
                console.log('物品分类窗口已取消激活');
            }
        }
        
        console.log('物品分类窗口属性设置完成');
        
    } catch (error) {
        console.error('设置物品分类窗口属性失败:', error);
    }
}

/**
 * 添加标准物品分类
 * @param window 窗口实例
 * @param options 物品分类窗口选项
 */
function addStandardItemCategories(window: any, options: ItemCategoryWindowOptions): void {
    console.log('添加标准物品分类...');
    
    try {
        // 检查是否有 TextManager
        const hasTextManager = window.TextManager !== undefined;
        
        // 添加物品分类
        if (options.showItemCategory !== false && window.addCommand) {
            const itemText = hasTextManager ? window.TextManager.item : '物品';
            window.addCommand(itemText, 'item', true);
            console.log('添加物品分类');
        }
        
        // 添加武器分类
        if (options.showWeaponCategory !== false && window.addCommand) {
            const weaponText = hasTextManager ? window.TextManager.weapon : '武器';
            window.addCommand(weaponText, 'weapon', true);
            console.log('添加武器分类');
        }
        
        // 添加防具分类
        if (options.showArmorCategory !== false && window.addCommand) {
            const armorText = hasTextManager ? window.TextManager.armor : '防具';
            window.addCommand(armorText, 'armor', true);
            console.log('添加防具分类');
        }
        
        // 添加重要物品分类
        if (options.showKeyItemCategory !== false && window.addCommand) {
            const keyItemText = hasTextManager ? window.TextManager.keyItem : '重要物品';
            window.addCommand(keyItemText, 'keyItem', true);
            console.log('添加重要物品分类');
        }
        
        console.log('标准物品分类添加完成');
        
    } catch (error) {
        console.error('添加标准物品分类失败:', error);
    }
}

/**
 * 获取分类对应的索引
 * @param category 分类名称
 * @returns 索引
 */
function getCategoryIndex(category: string): number {
    switch (category) {
        case 'item': return 0;
        case 'weapon': return 1;
        case 'armor': return 2;
        case 'keyItem': return 3;
        default: return 0;
    }
}

/**
 * 创建并激活物品分类窗口
 * @param options 创建选项
 * @returns 创建的物品分类窗口实例
 */
export async function createAndActivateWindowItemCategory(options: ItemCategoryWindowOptions = {}): Promise<any> {
    console.log('=== 创建并激活 Window_ItemCategory ===');
    
    const window = await createWindowItemCategory({
        ...options,
        autoOpen: true,
        activate: true
    });
    
    console.log('Window_ItemCategory 已创建并激活');
    return window;
}

/**
 * 创建简单的物品分类窗口（用于测试）
 * @returns 创建的物品分类窗口实例
 */
export async function createSimpleWindowItemCategory(): Promise<any> {
    console.log('=== 创建简单物品分类窗口 ===');
    
    try {
        const window = await createWindowItemCategory({
            rect: { x: 100, y: 200, width: 480, height: 72 },
            autoOpen: true,
            addToStage: true,
            visible: true,
            initialCategory: 'item',
            activate: true,
            showItemCategory: true,
            showWeaponCategory: true,
            showArmorCategory: true,
            showKeyItemCategory: true
        });
        
        console.log('简单物品分类窗口创建成功');
        return window;
        
    } catch (error) {
        console.error('创建简单物品分类窗口失败:', error);
        throw error;
    }
}
