/**
 * 测试新的布局架构
 * UIList创建layout但不设置属性，通过LayoutModel和LayoutPropertyPanel外部管理
 */

(() => {
    'use strict';

    console.log('🧪 开始加载布局架构测试脚本');

    // 等待所有插件加载完成
    setTimeout(() => {
        console.log('🧪 开始布局架构测试');

        // 添加测试函数到全局
        window.testLayoutArchitecture = function() {
            console.log('🧪 执行布局架构测试');

            // 检查必要的类是否存在
            if (typeof UIList === 'undefined') {
                console.error('❌ UIList类未找到');
                return;
            }

            if (typeof UILayout === 'undefined') {
                console.error('❌ UILayout类未找到');
                return;
            }

            // 获取当前场景
            const scene = SceneManager._scene;
            if (!scene) {
                console.error('❌ 无法获取当前场景');
                return;
            }

            console.log('✅ 开始创建布局架构测试');

            try {
                // 1. 创建UIList（会自动创建空的UILayout）
                const testList = new UIList({
                    x: 100,
                    y: 100,
                    width: 250,
                    height: 300
                });
                testList.name = 'TestArchitectureList';

                // 2. 检查UIList是否创建了layout
                console.log('🔍 检查UIList结构:');
                console.log('  - UIList子对象:', testList.children.map(child => child.constructor.name));
                console.log('  - Layout对象存在:', !!testList.layout);
                console.log('  - Layout类型:', testList.layout?.constructor.name);
                console.log('  - Layout初始属性:', {
                    layoutType: testList.layout?.layoutType,
                    spacing: testList.layout?.spacing,
                    padding: testList.layout?.padding
                });

                // 3. 创建LayoutModel来管理layout属性
                console.log('📐 创建LayoutModel');
                
                // 模拟LayoutModel（因为实际的LayoutModel需要Svelte环境）
                const layoutModel = {
                    displayObject: testList.layout,
                    
                    // 模拟Svelte stores
                    layoutType: { 
                        value: 'vertical',
                        set: function(val) { 
                            this.value = val; 
                            this.sync(); 
                        },
                        sync: function() {
                            if (this.displayObject) {
                                this.displayObject.layoutType = this.value;
                                if (typeof this.displayObject.updateLayout === 'function') {
                                    this.displayObject.updateLayout();
                                }
                            }
                        }
                    },
                    
                    spacing: { 
                        value: 10,
                        set: function(val) { 
                            this.value = val; 
                            this.sync(); 
                        },
                        sync: function() {
                            if (this.displayObject) {
                                this.displayObject.spacing = this.value;
                                this.displayObject.horizontalSpacing = this.value;
                                this.displayObject.verticalSpacing = this.value;
                                if (typeof this.displayObject.updateLayout === 'function') {
                                    this.displayObject.updateLayout();
                                }
                            }
                        }
                    },
                    
                    padding: { 
                        value: 5,
                        set: function(val) { 
                            this.value = val; 
                            this.sync(); 
                        },
                        sync: function() {
                            if (this.displayObject) {
                                this.displayObject.padding = this.value;
                                if (typeof this.displayObject.updateLayout === 'function') {
                                    this.displayObject.updateLayout();
                                }
                            }
                        }
                    },

                    columns: { 
                        value: 2,
                        set: function(val) { 
                            this.value = val; 
                            this.sync(); 
                        },
                        sync: function() {
                            if (this.displayObject) {
                                this.displayObject.columns = this.value;
                                if (typeof this.displayObject.updateLayout === 'function') {
                                    this.displayObject.updateLayout();
                                }
                            }
                        }
                    }
                };

                // 设置displayObject引用
                layoutModel.layoutType.displayObject = testList.layout;
                layoutModel.spacing.displayObject = testList.layout;
                layoutModel.padding.displayObject = testList.layout;
                layoutModel.columns.displayObject = testList.layout;

                // 4. 设置测试数据
                const testData = [
                    { id: 1, title: '架构测试1' },
                    { id: 2, title: '架构测试2' },
                    { id: 3, title: '架构测试3' },
                    { id: 4, title: '架构测试4' }
                ];

                console.log('📊 设置测试数据');
                testList.setDataSource(testData);

                // 5. 添加到场景
                scene.addChild(testList);

                console.log('✅ 布局架构测试创建成功');

                // 返回测试对象
                return {
                    testList,
                    layoutModel,
                    testData
                };

            } catch (error) {
                console.error('❌ 布局架构测试失败:', error);
                console.error('错误堆栈:', error.stack);
                return null;
            }
        };

        // 添加外部属性控制测试
        window.testExternalLayoutControl = function() {
            console.log('🧪 测试外部布局属性控制');
            
            const testObjects = window.testLayoutArchitecture();
            if (!testObjects) return;

            const { testList, layoutModel } = testObjects;
            
            console.log('🔄 开始外部属性控制测试');
            
            // 测试序列
            const tests = [
                {
                    name: '设置水平布局',
                    action: () => layoutModel.layoutType.set('horizontal'),
                    delay: 2000
                },
                {
                    name: '增加间距',
                    action: () => layoutModel.spacing.set(20),
                    delay: 2000
                },
                {
                    name: '设置网格布局',
                    action: () => layoutModel.layoutType.set('grid'),
                    delay: 2000
                },
                {
                    name: '设置网格列数',
                    action: () => layoutModel.columns.set(3),
                    delay: 2000
                },
                {
                    name: '增加内边距',
                    action: () => layoutModel.padding.set(15),
                    delay: 2000
                },
                {
                    name: '回到垂直布局',
                    action: () => {
                        layoutModel.layoutType.set('vertical');
                        layoutModel.spacing.set(10);
                        layoutModel.padding.set(5);
                    },
                    delay: 1000
                }
            ];

            let currentTest = 0;
            
            function runNextTest() {
                if (currentTest >= tests.length) {
                    console.log('✅ 外部布局属性控制测试完成');
                    return;
                }

                const test = tests[currentTest];
                console.log(`🔄 执行测试: ${test.name}`);
                
                try {
                    test.action();
                    console.log(`✅ 测试完成: ${test.name}`);
                } catch (error) {
                    console.error(`❌ 测试失败: ${test.name}`, error);
                }

                currentTest++;
                setTimeout(runNextTest, test.delay);
            }

            runNextTest();
        };

        // 添加架构验证测试
        window.verifyLayoutArchitecture = function() {
            console.log('🔍 验证布局架构');
            
            const testObjects = window.testLayoutArchitecture();
            if (!testObjects) return;

            const { testList, layoutModel } = testObjects;
            
            console.log('🔍 架构验证结果:');
            
            // 验证1: UIList是否创建了layout
            const hasLayout = !!testList.layout;
            console.log(`  ✅ UIList创建layout: ${hasLayout ? '通过' : '失败'}`);
            
            // 验证2: layout是否是UILayout实例
            const isUILayout = testList.layout?.constructor.name === 'UILayout';
            console.log(`  ✅ Layout是UILayout实例: ${isUILayout ? '通过' : '失败'}`);
            
            // 验证3: LayoutModel是否能控制layout属性
            const originalSpacing = testList.layout?.spacing;
            layoutModel.spacing.set(99);
            const newSpacing = testList.layout?.spacing;
            const canControl = newSpacing === 99;
            console.log(`  ✅ LayoutModel控制属性: ${canControl ? '通过' : '失败'} (${originalSpacing} -> ${newSpacing})`);
            
            // 验证4: layout是否能自动更新
            const hasUpdateMethod = typeof testList.layout?.updateLayout === 'function';
            console.log(`  ✅ Layout自动更新: ${hasUpdateMethod ? '通过' : '失败'}`);
            
            // 验证5: UIList是否只管理数据
            const hasDataSource = !!testList.dataSource;
            const hasItemInstances = Array.isArray(testList.itemInstances);
            const dataManagement = hasDataSource && hasItemInstances;
            console.log(`  ✅ UIList数据管理: ${dataManagement ? '通过' : '失败'}`);
            
            // 恢复原始间距
            layoutModel.spacing.set(originalSpacing || 10);
            
            const allPassed = hasLayout && isUILayout && canControl && hasUpdateMethod && dataManagement;
            console.log(`🎯 架构验证总结: ${allPassed ? '✅ 全部通过' : '❌ 存在问题'}`);
            
            return {
                hasLayout,
                isUILayout,
                canControl,
                hasUpdateMethod,
                dataManagement,
                allPassed
            };
        };

        console.log('✅ 布局架构测试脚本加载完成');
        console.log('💡 使用方法:');
        console.log('  - testLayoutArchitecture() - 基础架构测试');
        console.log('  - testExternalLayoutControl() - 外部属性控制测试');
        console.log('  - verifyLayoutArchitecture() - 架构验证');

    }, 4000); // 延迟4秒确保所有插件加载完成

})();
