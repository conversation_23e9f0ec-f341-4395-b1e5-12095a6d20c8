/**
 * 测试新的布局架构
 * UIList创建layout但不设置属性，通过LayoutModel和LayoutPropertyPanel外部管理
 */

(() => {
    'use strict';

    console.log('🧪 开始加载布局架构测试脚本');

    // 等待所有插件加载完成
    setTimeout(() => {
        console.log('🧪 开始布局架构测试');

        // 添加测试函数到全局
        window.testLayoutArchitecture = function() {
            console.log('🧪 执行布局架构测试');

            // 检查必要的类是否存在
            if (typeof UIList === 'undefined') {
                console.error('❌ UIList类未找到');
                return;
            }

            if (typeof UILayout === 'undefined') {
                console.error('❌ UILayout类未找到');
                return;
            }

            // 获取当前场景
            const scene = SceneManager._scene;
            if (!scene) {
                console.error('❌ 无法获取当前场景');
                return;
            }

            console.log('✅ 开始创建布局架构测试');

            try {
                // 1. 创建UIList（会自动创建空的UILayout）
                const testList = new UIList({
                    x: 100,
                    y: 100,
                    width: 250,
                    height: 300
                });
                testList.name = 'TestArchitectureList';

                // 2. 检查UIList是否创建了layout
                console.log('🔍 检查UIList结构:');
                console.log('  - UIList子对象:', testList.children.map(child => child.constructor.name));
                console.log('  - Layout对象存在:', !!testList.layout);
                console.log('  - Layout类型:', testList.layout?.constructor.name);
                console.log('  - Layout初始属性:', {
                    layoutType: testList.layout?.layoutType,
                    spacing: testList.layout?.spacing,
                    padding: testList.layout?.padding
                });

                // 3. 验证UIList创建了layout
                console.log('📐 验证UIList布局架构');

                if (!testList.layout) {
                    console.error('❌ UIList没有创建layout对象');
                    return null;
                }

                if (testList.layout.constructor.name !== 'UILayout') {
                    console.error('❌ layout不是UILayout实例:', testList.layout.constructor.name);
                    return null;
                }

                console.log('✅ UIList成功创建了UILayout对象');
                console.log('  - Layout类型:', testList.layout.layoutType);
                console.log('  - Layout间距:', testList.layout.spacing);
                console.log('  - Layout自动更新:', testList.layout.autoUpdate);

                // 4. 设置测试数据
                const testData = [
                    { id: 1, title: '架构测试1' },
                    { id: 2, title: '架构测试2' },
                    { id: 3, title: '架构测试3' },
                    { id: 4, title: '架构测试4' }
                ];

                console.log('📊 设置测试数据');
                testList.setDataSource(testData);

                // 5. 添加到场景
                scene.addChild(testList);

                console.log('✅ 布局架构测试创建成功');

                // 返回测试对象
                return {
                    testList,
                    layout: testList.layout,
                    testData
                };

            } catch (error) {
                console.error('❌ 布局架构测试失败:', error);
                console.error('错误堆栈:', error.stack);
                return null;
            }
        };

        // 添加外部属性控制测试
        window.testExternalLayoutControl = function() {
            console.log('🧪 测试外部布局属性控制');
            
            const testObjects = window.testLayoutArchitecture();
            if (!testObjects) return;

            const { testList, layout } = testObjects;

            console.log('🔄 开始外部属性控制测试');

            // 测试序列（直接操作UILayout对象）
            const tests = [
                {
                    name: '设置水平布局',
                    action: () => {
                        layout.layoutType = 'horizontal';
                        layout.updateLayout();
                    },
                    delay: 2000
                },
                {
                    name: '增加间距',
                    action: () => {
                        layout.spacing = 20;
                        layout.horizontalSpacing = 20;
                        layout.verticalSpacing = 20;
                        layout.updateLayout();
                    },
                    delay: 2000
                },
                {
                    name: '设置网格布局',
                    action: () => {
                        layout.layoutType = 'grid';
                        layout.updateLayout();
                    },
                    delay: 2000
                },
                {
                    name: '设置网格列数',
                    action: () => {
                        layout.columns = 3;
                        layout.updateLayout();
                    },
                    delay: 2000
                },
                {
                    name: '增加内边距',
                    action: () => {
                        layout.padding = 15;
                        layout.updateLayout();
                    },
                    delay: 2000
                },
                {
                    name: '回到垂直布局',
                    action: () => {
                        layout.layoutType = 'vertical';
                        layout.spacing = 10;
                        layout.horizontalSpacing = 10;
                        layout.verticalSpacing = 10;
                        layout.padding = 5;
                        layout.updateLayout();
                    },
                    delay: 1000
                }
            ];

            let currentTest = 0;
            
            function runNextTest() {
                if (currentTest >= tests.length) {
                    console.log('✅ 外部布局属性控制测试完成');
                    return;
                }

                const test = tests[currentTest];
                console.log(`🔄 执行测试: ${test.name}`);
                
                try {
                    test.action();
                    console.log(`✅ 测试完成: ${test.name}`);
                } catch (error) {
                    console.error(`❌ 测试失败: ${test.name}`, error);
                }

                currentTest++;
                setTimeout(runNextTest, test.delay);
            }

            runNextTest();
        };

        // 添加架构验证测试
        window.verifyLayoutArchitecture = function() {
            console.log('🔍 验证布局架构');
            
            const testObjects = window.testLayoutArchitecture();
            if (!testObjects) return;

            const { testList, layout } = testObjects;

            console.log('🔍 架构验证结果:');

            // 验证1: UIList是否创建了layout
            const hasLayout = !!testList.layout;
            console.log(`  ✅ UIList创建layout: ${hasLayout ? '通过' : '失败'}`);

            // 验证2: layout是否是UILayout实例
            const isUILayout = testList.layout?.constructor.name === 'UILayout';
            console.log(`  ✅ Layout是UILayout实例: ${isUILayout ? '通过' : '失败'}`);

            // 验证3: 外部是否能控制layout属性
            const originalSpacing = testList.layout?.spacing;
            layout.spacing = 99;
            layout.updateLayout();
            const newSpacing = testList.layout?.spacing;
            const canControl = newSpacing === 99;
            console.log(`  ✅ 外部控制属性: ${canControl ? '通过' : '失败'} (${originalSpacing} -> ${newSpacing})`);

            // 验证4: layout是否能自动更新
            const hasUpdateMethod = typeof testList.layout?.updateLayout === 'function';
            console.log(`  ✅ Layout自动更新: ${hasUpdateMethod ? '通过' : '失败'}`);

            // 验证5: UIList是否只管理数据
            const hasDataSource = !!testList.dataSource;
            const hasItemInstances = Array.isArray(testList.itemInstances);
            const dataManagement = hasDataSource && hasItemInstances;
            console.log(`  ✅ UIList数据管理: ${dataManagement ? '通过' : '失败'}`);

            // 验证6: UIList是否不管理布局属性
            const listHasLayoutProps = testList.hasOwnProperty('layoutType') || testList.hasOwnProperty('spacing');
            const separationOfConcerns = !listHasLayoutProps;
            console.log(`  ✅ 职责分离: ${separationOfConcerns ? '通过' : '失败'} (UIList不应有布局属性)`);

            // 恢复原始间距
            layout.spacing = originalSpacing || 10;
            layout.updateLayout();

            const allPassed = hasLayout && isUILayout && canControl && hasUpdateMethod && dataManagement && separationOfConcerns;
            console.log(`🎯 架构验证总结: ${allPassed ? '✅ 全部通过' : '❌ 存在问题'}`);

            return {
                hasLayout,
                isUILayout,
                canControl,
                hasUpdateMethod,
                dataManagement,
                separationOfConcerns,
                allPassed
            };
        };

        console.log('✅ 布局架构测试脚本加载完成');
        console.log('💡 使用方法:');
        console.log('  - testLayoutArchitecture() - 基础架构测试');
        console.log('  - testExternalLayoutControl() - 外部属性控制测试');
        console.log('  - verifyLayoutArchitecture() - 架构验证');

    }, 4000); // 延迟4秒确保所有插件加载完成

})();
