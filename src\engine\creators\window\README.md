# RPG Maker MZ Window Creators

这个文件夹包含了所有 RPG Maker MZ 窗口的创建器，基于 `src/center/classTree/treeData.ts` 中定义的窗口层次结构实现。

## 窗口层次结构

```
Window_Base (窗口基类)
├── Window_Scrollable (可滚动窗口)
│   ├── Window_Selectable (可选择窗口)
│   │   ├── Window_Command (命令窗口)
│   │   │   ├── Window_HorzCommand (水平命令窗口)
│   │   │   │   ├── Window_MenuCommand (菜单命令窗口)
│   │   │   │   └── Window_ItemCategory (物品分类窗口)
│   │   │   ├── Window_TitleCommand (标题命令窗口)
│   │   │   └── Window_PartyCommand (队伍命令窗口)
│   │   ├── Window_ItemList (物品列表窗口)
│   │   └── Window_SavefileList (存档列表窗口)
│   └── Window_Help (帮助窗口)
├── Window_Gold (金钱窗口)
├── Window_StatusBase (状态基类窗口)
└── Window_Message (消息窗口)
```

## 已实现的创建器

### 基础创建器
- **WindowCreator.ts** - 基础窗口创建器，提供通用功能

### 具体窗口创建器
- **WindowBaseCreator.ts** - Window_Base 窗口基类
- **WindowScrollableCreator.ts** - Window_Scrollable 可滚动窗口
- **WindowSelectableCreator.ts** - Window_Selectable 可选择窗口
- **WindowCommandCreator.ts** - Window_Command 命令窗口
- **WindowHorzCommandCreator.ts** - Window_HorzCommand 水平命令窗口
- **WindowMenuCommandCreator.ts** - Window_MenuCommand 菜单命令窗口
- **WindowItemCategoryCreator.ts** - Window_ItemCategory 物品分类窗口
- **WindowMenuStatusCreator.ts** - Window_MenuStatus 菜单状态窗口

## 使用方法

### 1. 基本使用

```typescript
import { createWindowMenuCommand, createWindowItemCategory, createWindowMenuStatus, createWindowCommand } from '../creators';

// 创建菜单命令窗口
const menuWindow = await createWindowMenuCommand({
    rect: { x: 0, y: 0, width: 240, height: 300 },
    autoOpen: true,
    addToStage: true
});

// 创建物品分类窗口
const categoryWindow = await createWindowItemCategory({
    rect: { x: 0, y: 0, width: 480, height: 72 },
    initialCategory: 'item',
    autoOpen: true
});

// 创建菜单状态窗口（显示角色数据）
const statusWindow = await createWindowMenuStatus({
    rect: { x: 300, y: 100, width: 480, height: 400 },
    showFace: true,
    showName: true,
    showLevel: true,
    showParams: true,
    autoOpen: true
});

// 创建自定义命令窗口
const commandWindow = await createWindowCommand({
    rect: { x: 100, y: 100, width: 200, height: 150 },
    commands: [
        { name: '攻击', symbol: 'attack', enabled: true },
        { name: '防御', symbol: 'guard', enabled: true },
        { name: '逃跑', symbol: 'escape', enabled: false }
    ],
    autoOpen: true
});
```

### 2. 通过类名创建

```typescript
import { createObjectByClassName } from '../creators';

// 通过类名创建窗口
const window = await createObjectByClassName('Window_MenuCommand', {
    rect: { x: 50, y: 50, width: 240, height: 300 },
    autoOpen: true,
    addToStage: true
});
```

### 3. 窗口选项

每个窗口创建器都支持以下基本选项：

```typescript
interface WindowCreationOptions {
    /** 窗口矩形区域 */
    rect?: { x: number; y: number; width: number; height: number };
    /** 是否自动打开窗口 */
    autoOpen?: boolean;
    /** 是否添加到舞台 */
    addToStage?: boolean;
    /** 自定义初始化参数 */
    initParams?: any[];
    /** 窗口特定选项 */
    windowOptions?: any;
}
```

### 4. 特定窗口选项

#### 命令窗口 (Window_Command)
```typescript
interface CommandWindowOptions extends WindowCreationOptions {
    commands?: CommandItem[];     // 命令列表
    autoRefresh?: boolean;        // 是否自动刷新
    initialIndex?: number;        // 初始选中索引
    activate?: boolean;           // 是否激活窗口
}

interface CommandItem {
    name: string;                 // 命令名称
    symbol: string;               // 命令符号
    enabled?: boolean;            // 是否启用
    ext?: any;                    // 扩展数据
}
```

#### 菜单命令窗口 (Window_MenuCommand)
```typescript
interface MenuCommandWindowOptions extends WindowCreationOptions {
    showItemCommand?: boolean;        // 是否显示物品命令
    showSkillCommand?: boolean;       // 是否显示技能命令
    showEquipCommand?: boolean;       // 是否显示装备命令
    showStatusCommand?: boolean;      // 是否显示状态命令
    showFormationCommand?: boolean;   // 是否显示编队命令
    showOptionsCommand?: boolean;     // 是否显示选项命令
    showSaveCommand?: boolean;        // 是否显示保存命令
    showGameEndCommand?: boolean;     // 是否显示游戏结束命令
}
```

#### 物品分类窗口 (Window_ItemCategory)
```typescript
interface ItemCategoryWindowOptions extends WindowCreationOptions {
    showItemCategory?: boolean;       // 是否显示物品分类
    showWeaponCategory?: boolean;     // 是否显示武器分类
    showArmorCategory?: boolean;      // 是否显示防具分类
    showKeyItemCategory?: boolean;    // 是否显示重要物品分类
    initialCategory?: 'item' | 'weapon' | 'armor' | 'keyItem';  // 初始分类
}
```

## 特性

### 1. 资源管理
- 自动预加载窗口所需的资源
- 等待资源加载完成后再创建窗口
- 支持异步资源加载

### 2. 坐标管理
- **窗口对象默认使用 (0, 0) 坐标**，不会被自动居中
- 支持自定义窗口矩形区域
- 符合 RPG Maker MZ 窗口的标准显示行为

### 3. 生命周期管理
- 遵循 RPG Maker MZ 窗口生命周期：创建 → 打开 → 激活 → 更新 → 关闭
- 支持自动打开和手动控制
- 自动处理窗口状态

### 4. 命令管理
- 支持动态添加和删除命令
- 自动刷新窗口内容
- 支持命令启用/禁用状态

### 5. 错误处理
- 完善的错误检查和处理
- 详细的日志输出
- 优雅的降级处理

### 6. 数据绑定
- **自动关联游戏数据**：状态窗口自动关联 `$gameParty` 和 `Game_Actor` 数据
- **实时内容刷新**：窗口创建后自动调用 `refresh()` 方法显示内容
- **智能数据检查**：自动检查并创建测试数据（如果游戏数据不存在）
- **命令列表生成**：命令窗口自动调用 `makeCommandList()` 生成标准命令

### 7. 扩展性
- 基于继承的设计，易于扩展
- 支持自定义窗口选项
- 模块化的架构

## 注意事项

1. **资源依赖**: 确保 RPG Maker MZ 的核心资源已加载
2. **窗口层次**: 遵循 RPG Maker MZ 的窗口继承层次结构
3. **内存管理**: 及时关闭和清理不需要的窗口实例
4. **坐标系统**: 窗口对象默认使用 (0, 0) 坐标，不会被自动居中
5. **兼容性**: 基于 RPG Maker MZ v1.8.0 源码实现

## 示例

查看各个创建器文件中的 `createSimple*` 函数获取更多使用示例。
