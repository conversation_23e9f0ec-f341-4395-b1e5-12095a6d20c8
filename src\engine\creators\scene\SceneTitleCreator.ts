/**
 * Scene_Title 创建器
 * 专门用于创建标题场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';

/**
 * 标题场景创建选项
 */
export interface TitleSceneOptions extends SceneCreationOptions {
    /** 自定义标题背景1 */
    title1Name?: string;
    /** 自定义标题背景2 */
    title2Name?: string;
    /** 是否显示游戏标题 */
    showGameTitle?: boolean;
}

/**
 * 创建标题场景
 * @param options 创建选项
 * @returns 创建的标题场景实例
 */
export async function createSceneTitle(options: TitleSceneOptions = {}): Promise<any> {
    console.log('=== 创建标题场景 Scene_Title ===');
    
    try {
        // 预加载标题场景资源
        await preloadTitleResources(options);
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Title', defaultOptions);
        
        // Scene_Title 特定的设置
        console.log('Scene_Title 创建完成，场景属性:', {
            started: scene._started,
            active: scene._active,
            hasBackground: !!scene._backSprite1,
            hasCommandWindow: !!scene._commandWindow,
            hasGameTitle: !!scene._gameTitleSprite
        });
        
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Title 失败:', error);
        throw error;
    }
}

/**
 * 预加载标题场景资源
 * @param options 标题场景选项
 */
async function preloadTitleResources(options: TitleSceneOptions): Promise<void> {
    console.log('预加载标题场景资源...');
    
    if (!window.ImageManager || !window.$dataSystem) {
        console.warn('ImageManager 或 $dataSystem 未加载，跳过标题资源预加载');
        return;
    }
    
    try {
        // 预加载标题背景图片
        const title1Name = options.title1Name || window.$dataSystem.title1Name || '';
        const title2Name = options.title2Name || window.$dataSystem.title2Name || '';
        
        if (title1Name) {
            const bitmap1 = window.ImageManager.loadTitle1(title1Name);
            console.log('预加载标题背景1:', title1Name);
            
            // 等待背景1加载完成
            if (bitmap1 && typeof bitmap1.addLoadListener === 'function') {
                await new Promise<void>((resolve) => {
                    if (bitmap1.isReady && bitmap1.isReady()) {
                        resolve();
                    } else {
                        bitmap1.addLoadListener(() => resolve());
                    }
                });
            }
        }
        
        if (title2Name) {
            const bitmap2 = window.ImageManager.loadTitle2(title2Name);
            console.log('预加载标题背景2:', title2Name);
            
            // 等待背景2加载完成
            if (bitmap2 && typeof bitmap2.addLoadListener === 'function') {
                await new Promise<void>((resolve) => {
                    if (bitmap2.isReady && bitmap2.isReady()) {
                        resolve();
                    } else {
                        bitmap2.addLoadListener(() => resolve());
                    }
                });
            }
        }
        
        // 预加载窗口皮肤
        window.ImageManager.loadSystem('Window');
        
        console.log('标题场景资源预加载完成');
        
    } catch (error) {
        console.error('预加载标题场景资源失败:', error);
        // 不抛出错误，允许场景创建继续
    }
}

/**
 * 创建并启动标题场景
 * @param options 创建选项
 * @returns 创建的标题场景实例
 */
export async function createAndStartSceneTitle(options: TitleSceneOptions = {}): Promise<any> {
    console.log('=== 创建并启动 Scene_Title ===');
    
    const scene = await createSceneTitle({
        ...options,
        autoStart: true
    });
    
    console.log('Scene_Title 已创建并启动');
    return scene;
}

/**
 * 创建简单的标题场景（用于测试）
 * @returns 创建的标题场景实例
 */
export async function createSimpleSceneTitle(): Promise<any> {
    console.log('=== 创建简单标题场景 ===');
    
    try {
        const scene = await createSceneTitle({
            autoStart: false,
            addToStage: true,
            showGameTitle: true
        });
        
        console.log('简单标题场景创建成功');
        return scene;
        
    } catch (error) {
        console.error('创建简单标题场景失败:', error);
        throw error;
    }
}
