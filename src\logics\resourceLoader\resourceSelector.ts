/**
 * 资源选择器 - 使用Tauri API选择文件并通过CustomResourcePath.js加载资源
 */

// 全局类型声明
declare global {
  interface Window {
    PROJECT_CONFIG?: {
      projectPath: string;
      projectName: string;
      resourcePaths: any;
      scripts: any;
      useExternalResources: boolean;
    };
  }
}

/**
 * 资源选择结果接口
 */
export interface ResourceSelectionResult {
  success: boolean;
  filePath?: string;
  resourceObject?: any;
  error?: string;
}
/**
 * 选择并加载图片资源，返回相对路径
 * @param title 对话框标题
 * @returns Promise<string> 相对于img目录的路径，或错误信息
 */
export async function selectImageResourcePath(title = '选择图片文件'): Promise<string> {
  try {
    // 1. 使用Tauri API打开文件选择对话框
    const { open } = await import('@tauri-apps/plugin-dialog');
    const filePath = await open({
      directory: false,
      multiple: false,
      title,
      filters: [
        { name: 'All Images', extensions: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'] }
      ]
    });

    if (!filePath || typeof filePath !== 'string') {
      return '用户取消选择';
    }

    // 2. 获取当前项目路径
    const projectPath = await getCurrentProjectPath();
    if (!projectPath) {
      console.warn('无法获取项目路径，返回绝对路径');
      return filePath;
    }

    // 3. 转换为相对路径
    const relativePath = convertToRelativePath(filePath, projectPath);
    console.log('=== 图片路径转换 ===');
    console.log('选择的图片路径:', filePath);
    console.log('项目路径:', projectPath);
    console.log('转换后的相对路径:', relativePath);
    console.log('===================');

    return relativePath;

  } catch (error) {
    return String(error);
  }
}

/**
 * 获取当前项目路径
 */
async function getCurrentProjectPath(): Promise<string | null> {
  try {
    // 优先从全局配置获取
    if (window.PROJECT_CONFIG && window.PROJECT_CONFIG.projectPath) {
      return window.PROJECT_CONFIG.projectPath;
    }

    // 备选：从Tauri API获取
    const { TauriAPI } = await import('../../lib/tauriAPI');
    const result = await TauriAPI.Project.getCurrentProjectInfo();

    if (result.success && result.data) {
      return result.data.project_path;
    }

    return null;
  } catch (error) {
    console.error('获取项目路径失败:', error);
    return null;
  }
}

/**
 * 将绝对路径转换为相对于img目录的路径
 * 支持Windows和Mac/Linux路径格式
 */
function convertToRelativePath(absolutePath: string, projectPath: string): string {
  try {
    // 标准化路径分隔符（统一使用正斜杠）
    const normalizedAbsolutePath = absolutePath.replace(/\\/g, '/');
    const normalizedProjectPath = projectPath.replace(/\\/g, '/');

    // 确保项目路径以斜杠结尾
    const projectBasePath = normalizedProjectPath.endsWith('/')
      ? normalizedProjectPath
      : normalizedProjectPath + '/';

    // 检查文件是否在项目目录内
    if (!normalizedAbsolutePath.startsWith(projectBasePath)) {
      console.warn('选择的文件不在项目目录内，返回绝对路径');
      return absolutePath;
    }

    // 获取相对于项目根目录的路径
    const relativeToProject = normalizedAbsolutePath.substring(projectBasePath.length);

    // 查找img目录的位置
    const imgIndex = relativeToProject.indexOf('img/');
    if (imgIndex === -1) {
      console.warn('文件不在img目录内，返回相对于项目的路径');
      return relativeToProject;
    }

    // 🔧 修复：保留img/前缀，提取从img/开始的完整路径
    const imgRelativePath = relativeToProject.substring(imgIndex); // 保留 'img/'

    // 检查是否在pictures目录中（特殊处理：只返回文件名）
    if (imgRelativePath.startsWith('img/pictures/')) {
      return imgRelativePath.substring(13); // +13 跳过 'img/pictures/'
    }

    // 对于其他所有目录，返回完整的相对路径（包含img/前缀）
    return imgRelativePath;

  } catch (error) {
    console.error('转换相对路径时出错:', error);
    return absolutePath;
  }
}
/**
 * 选择并加载图片资源
 * @param title 对话框标题
 * @returns Promise<ResourceSelectionResult> 选择结果，包含Bitmap对象
 */
export async function selectImageResource(title = '选择图片文件'): Promise<ResourceSelectionResult> {
  try {
    // 1. 使用Tauri API打开文件选择对话框
    const { open } = await import('@tauri-apps/plugin-dialog');
    const filePath = await open({
      directory: false,
      multiple: false,
      title,
      filters: [
        { name: 'All Images', extensions: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'] }
      ]
    });

    if (!filePath || typeof filePath !== 'string') {
      return { success: false, error: '用户取消选择' };
    }

    // 2. 使用CustomResourcePath.js插件创建Bitmap对象
    if (typeof window.ImageManager === 'undefined') {
      return { success: false, error: 'ImageManager不可用', filePath };
    }

    const bitmap = window.ImageManager.loadBitmapFromUrl(filePath);

    return {
      success: true,
      filePath,
      resourceObject: bitmap
    };

  } catch (error) {
    return { success: false, error: String(error) };
  }
}

/**
 * 选择并加载音频资源
 * @param title 对话框标题
 * @returns Promise<ResourceSelectionResult> 选择结果，包含WebAudio对象
 */
export async function selectAudioResource(title = '选择音频文件'): Promise<ResourceSelectionResult> {
  try {
    const { open } = await import('@tauri-apps/plugin-dialog');
    const filePath = await open({
      directory: false,
      multiple: false,
      title,
      filters: [
        { name: 'All Audio', extensions: ['ogg', 'mp3', 'wav', 'm4a'] }
      ]
    });

    if (!filePath || typeof filePath !== 'string') {
      return { success: false, error: '用户取消选择' };
    }

    if (typeof window.AudioManager === 'undefined') {
      return { success: false, error: 'AudioManager不可用', filePath };
    }

    // 提取文件名和文件夹路径
    const fileName = filePath.split(/[/\\]/).pop()?.replace(/\.[^/.]+$/, '') || '';
    const folderPath = filePath.substring(0, filePath.lastIndexOf('/'));

    const audioBuffer = window.AudioManager.createBuffer(folderPath, fileName);

    return {
      success: true,
      filePath,
      resourceObject: audioBuffer
    };

  } catch (error) {
    return { success: false, error: String(error) };
  }
}
