<script lang="ts">
  import Modal from '../../components/Modal.svelte';
  import LabelSpriteEditor from './LabelSpriteEditor.svelte';
  import TextElementPanel from './TextElementPanel.svelte';
  import ImageCropPanel from './ImageCropPanel.svelte';
  import type { bitmapProperties } from '../types';
  import { bitmapModel } from '../stores/bitmapStore';

  // 🔧 Svelte 5: Props
  interface Props {
    open?: boolean;
    bitmapData?: bitmapProperties | null;
    stageSize?: { width: number; height: number };
    spritePosition?: { x: number; y: number };
    onClose?: () => void;
    onSave?: ((data: bitmapProperties) => void) | undefined;
  }

  let {
    open = $bindable(false),
    bitmapData = null,
    stageSize = { width: 800, height: 600 },
    spritePosition = { x: 0, y: 0 },
    onClose = () => {},
    onSave = undefined
  }: Props = $props();

  // 🔧 处理导出功能 - 将导出的数据传递给 onSave 回调
  function handleExport(exportedData: bitmapProperties) {
    console.log('🔧 LabelSpriteEditorModal: 接收到导出数据', exportedData);

    if (onSave) {
      console.log('🔧 LabelSpriteEditorModal: 调用 onSave 回调传递数据');
      onSave(exportedData);
    } else {
      console.warn('⚠️ LabelSpriteEditorModal: 没有设置 onSave 回调函数');
    }
  }

  // 🔧 处理关闭功能 - 统一的关闭处理
  function handleClose() {
    console.log('🔧 LabelSpriteEditorModal: 关闭编辑器');
    onClose();
  }
</script>

{#snippet modalContent()}
  <div class="editor-container">
    <!-- 编辑器内容 -->
    <div class="editor-content">
      {#if bitmapData}
        <div class="editor-layout">
          <!-- 主编辑器区域 -->
          <div class="main-editor">
            <LabelSpriteEditor
              {bitmapData}
              {stageSize}
              {spritePosition}
              onExport={handleExport}
              onClose={handleClose}
            />
          </div>
        </div>
      {:else}
        <div class="no-data">
          <p>没有可编辑的数据</p>
        </div>
      {/if}
    </div>
  </div>
{/snippet}

<Modal
  {open}
  maxWidth="xl"
  onClose={onClose}
  showHeader={false}
  showFooter={false}
  children={modalContent}
/>

<style>
  .editor-container {
    width: 100%;
    height: 80vh;
    min-height: 600px;
    display: flex;
    flex-direction: column;
    background: var(--theme-surface, #ffffff);
  }

  .custom-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-2, 0.5rem) var(--spacing-3, 0.75rem);
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    background: var(--theme-surface-light, #f9fafb);
    flex-shrink: 0;
  }

  .editor-title {
    margin: 0;
    font-size: var(--font-size-base, 1rem);
    font-weight: 600;
    color: var(--theme-text, #111827);
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-1, 0.25rem);
  }

  .action-btn {
    background: none;
    border: none;
    font-size: 16px;
    color: var(--theme-text-secondary, #6b7280);
    cursor: pointer;
    padding: var(--spacing-1, 0.25rem);
    border-radius: var(--border-radius, 4px);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
  }

  .action-btn:hover {
    background: var(--theme-surface-dark, #f3f4f6);
    color: var(--theme-text, #111827);
  }



  .close-btn:hover {
    background: var(--theme-error-light, #fef2f2);
    color: var(--theme-error, #dc2626);
  }

  .editor-content {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
  }

  .editor-layout {
    display: flex;
    height: 100%;
    gap: 1px;
  }

  .main-editor {
    flex: 1;
    min-width: 0;
  }

  .properties-panel {
    width: 300px;
    background: var(--theme-surface, #ffffff);
    border-left: 1px solid var(--theme-border, #e5e7eb);
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    background: var(--theme-surface-light, #f9fafb);
  }

  .panel-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--theme-text, #111827);
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  }

  .no-selection {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--theme-text-secondary, #6b7280);
    text-align: center;
    padding: 20px;
  }

  .no-selection p {
    margin: 0;
    font-size: 14px;
  }

  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--theme-text-secondary, #6b7280);
  }

  .no-data p {
    margin: 0;
    font-size: var(--font-size-lg, 1.125rem);
  }

  /* 覆盖Modal组件的默认padding，让FontPropertiesPanel直接贴着边框 */
  :global(.modal-body) {
    padding: 0 !important;
  }
</style>
