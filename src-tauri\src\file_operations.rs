use regex::Regex;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;

/// 操作记录文件的扩展名
const OPERATION_RECORDS_EXTENSION: &str = "mlzh";

/// 操作记录数据结构
#[derive(Debug, Serialize, Deserialize)]
pub struct OperationRecordsData {
    pub version: String,
    pub timestamp: u64,
    pub records: serde_json::Value,
}

/// 保存操作记录到项目目录
#[tauri::command]
pub async fn save_operation_records(
    project_path: String,
    records_data: String,
) -> Result<String, String> {
    println!("保存操作记录到项目: {}", project_path);

    let project_dir = PathBuf::from(&project_path);

    // 验证项目目录是否存在
    if !project_dir.exists() {
        return Err("项目目录不存在".to_string());
    }

    // 查找现有的 .mlzh 文件或创建新文件
    let records_file_path = find_or_create_records_file(&project_dir)?;

    // 解析传入的记录数据
    let parsed_data: serde_json::Value =
        serde_json::from_str(&records_data).map_err(|e| format!("解析操作记录数据失败: {}", e))?;

    // 创建完整的记录数据结构
    let operation_records = OperationRecordsData {
        version: "2.0.0".to_string(),
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map_err(|e| format!("获取时间戳失败: {}", e))?
            .as_secs(),
        records: parsed_data,
    };

    // 序列化并写入文件
    let json_content = serde_json::to_string_pretty(&operation_records)
        .map_err(|e| format!("序列化操作记录失败: {}", e))?;

    fs::write(&records_file_path, json_content)
        .map_err(|e| format!("写入操作记录文件失败: {}", e))?;

    let file_path_str = records_file_path.to_string_lossy().to_string();
    println!("操作记录已保存到: {}", file_path_str);

    Ok(file_path_str)
}

/// 从项目目录加载操作记录（自动检查文件是否存在）
#[tauri::command]
pub async fn load_operation_records(project_path: String) -> Result<Option<String>, String> {
    println!("从项目加载操作记录: {}", project_path);

    let project_dir = PathBuf::from(&project_path);

    // 验证项目目录是否存在
    if !project_dir.exists() {
        return Err("项目目录不存在".to_string());
    }

    // 查找现有的 .mlzh 文件
    let records_file_path = match find_existing_records_file(&project_dir) {
        Some(path) => path,
        None => {
            println!("项目中没有找到操作记录文件，返回空结果");
            return Ok(None);
        }
    };

    println!("找到操作记录文件: {}", records_file_path.display());

    // 读取文件内容
    let file_content = fs::read_to_string(&records_file_path)
        .map_err(|e| format!("读取操作记录文件失败: {}", e))?;

    // 解析文件内容
    let operation_records: OperationRecordsData =
        serde_json::from_str(&file_content).map_err(|e| format!("解析操作记录文件失败: {}", e))?;

    // 返回记录数据的 JSON 字符串
    let records_json = serde_json::to_string(&operation_records.records)
        .map_err(|e| format!("序列化记录数据失败: {}", e))?;

    println!(
        "成功加载操作记录，版本: {}, 时间戳: {}, 数据长度: {}",
        operation_records.version,
        operation_records.timestamp,
        records_json.len()
    );

    Ok(Some(records_json))
}

/// 查找现有的操作记录文件
fn find_existing_records_file(project_dir: &PathBuf) -> Option<PathBuf> {
    // 读取项目目录中的所有文件
    let entries = match fs::read_dir(project_dir) {
        Ok(entries) => entries,
        Err(_) => return None,
    };

    // 查找第一个 .mlzh 文件
    for entry in entries {
        if let Ok(entry) = entry {
            let path = entry.path();
            if path.is_file() {
                if let Some(extension) = path.extension() {
                    if extension == OPERATION_RECORDS_EXTENSION {
                        return Some(path);
                    }
                }
            }
        }
    }

    None
}

/// 查找现有的操作记录文件，如果不存在则创建新文件
fn find_or_create_records_file(project_dir: &PathBuf) -> Result<PathBuf, String> {
    // 首先尝试查找现有文件
    if let Some(existing_file) = find_existing_records_file(project_dir) {
        return Ok(existing_file);
    }

    // 如果没有现有文件，创建新文件
    // 使用项目目录名称作为文件名
    let project_name = project_dir
        .file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("project");

    let records_file_name = format!("{}.{}", project_name, OPERATION_RECORDS_EXTENSION);
    let records_file_path = project_dir.join(records_file_name);

    Ok(records_file_path)
}

/// 获取操作记录文件的路径（如果存在）
#[tauri::command]
pub async fn get_operation_records_file_path(
    project_path: String,
) -> Result<Option<String>, String> {
    let project_dir = PathBuf::from(&project_path);

    // 验证项目目录是否存在
    if !project_dir.exists() {
        return Err("项目目录不存在".to_string());
    }

    if let Some(records_file) = find_existing_records_file(&project_dir) {
        Ok(Some(records_file.to_string_lossy().to_string()))
    } else {
        Ok(None)
    }
}

/// 插件保存结果
#[derive(Debug, Serialize, Deserialize)]
pub struct PluginSaveResult {
    pub file_name: String,
    pub file_path: String,
    pub plugin_name: String,
}

/// 保存插件文件到项目的 js/plugins 目录并自动更新 plugins.js 配置
#[tauri::command]
pub async fn save_plugin_file(
    project_path: String,
    plugin_code: String,
) -> Result<PluginSaveResult, String> {
    println!("保存插件文件到项目: {}", project_path);

    let project_dir = PathBuf::from(&project_path);

    // 验证项目目录是否存在
    if !project_dir.exists() {
        return Err("项目目录不存在".to_string());
    }

    // 确保 js/plugins 目录存在
    let plugins_dir = project_dir.join("js").join("plugins");
    if !plugins_dir.exists() {
        fs::create_dir_all(&plugins_dir).map_err(|e| format!("创建 plugins 目录失败: {}", e))?;
    }

    // 使用固定的插件文件名
    let plugin_name = "RPGEditor_Plugin".to_string();
    let file_name = format!("{}.js", plugin_name);
    let plugin_file_path = plugins_dir.join(&file_name);

    // 写入插件文件
    fs::write(&plugin_file_path, plugin_code).map_err(|e| format!("写入插件文件失败: {}", e))?;
    println!("插件文件已保存: {}", plugin_file_path.to_string_lossy());

    // 自动更新 plugins.js 配置
    // let plugins_js_path = project_dir.join("js").join("plugins.js");
    // if plugins_js_path.exists() {
    //     match update_plugins_js_config(&plugins_js_path, &plugin_name) {
    //         Ok(message) => println!("plugins.js 更新: {}", message),
    //         Err(e) => println!("plugins.js 更新失败: {}", e),
    //     }
    // } else {
    //     println!("警告: plugins.js 文件不存在，跳过配置更新");
    // }

    let result = PluginSaveResult {
        file_name: file_name.clone(),
        file_path: plugin_file_path.to_string_lossy().to_string(),
        plugin_name: plugin_name.clone(),
    };

    Ok(result)
}

/// 内部函数：更新 plugins.js 配置文件
fn update_plugins_js_config(
    plugins_js_path: &PathBuf,
    plugin_name: &str,
) -> Result<String, String> {
    // 读取现有的 plugins.js 内容
    let content =
        fs::read_to_string(plugins_js_path).map_err(|e| format!("读取 plugins.js 失败: {}", e))?;

    // 检查插件是否已经存在
    if content.contains(plugin_name) {
        return Ok("插件已存在，配置无需修改".to_string());
    }

    // 查找插件数组的位置并添加新插件
    let updated_content = add_plugin_to_config(&content, plugin_name)?;

    // 写回文件
    fs::write(plugins_js_path, updated_content)
        .map_err(|e| format!("写入 plugins.js 失败: {}", e))?;

    Ok("配置更新成功".to_string())
}

/// 将插件添加到 plugins.js 配置中
fn add_plugin_to_config(content: &str, plugin_name: &str) -> Result<String, String> {
    // 查找插件数组的模式
    let re = Regex::new(r"(\$plugins\s*=\s*\[)([\s\S]*?)(\];)")
        .map_err(|e| format!("正则表达式错误: {}", e))?;

    if let Some(captures) = re.captures(content) {
        let prefix = captures.get(1).unwrap().as_str();
        let plugins_content = captures.get(2).unwrap().as_str();
        let suffix = captures.get(3).unwrap().as_str();

        // 创建新的插件条目
        let new_plugin_entry = format!(
            r#"{{"name":"{}","status":true,"description":"RPG Editor Generated Plugin","parameters":{{}}}}"#,
            plugin_name
        );

        // 如果插件数组不为空，需要添加逗号
        let separator = if plugins_content.trim().is_empty() {
            ""
        } else {
            ","
        };

        // 构建新的内容
        let new_content = format!(
            "{}{}{}{}{}",
            prefix, plugins_content, separator, new_plugin_entry, suffix
        );

        Ok(content.replace(&captures[0], &new_content))
    } else {
        Err("未找到 $plugins 数组定义".to_string())
    }
}
