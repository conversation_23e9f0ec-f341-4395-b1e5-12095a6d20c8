<script lang="ts">
  /**
   * 类连接线组件 - 显示类之间的继承关系连接线
   */
  
  import type { ConnectionLine } from './tree';

  // Props
  export let connection: ConnectionLine;
  export let isHighlighted: boolean = false;
  export let strokeWidth: number = 2;
  export let strokeColor: string = '#666666';

  // 计算连接线路径
  $: pathData = calculatePath(connection);
  $: lineColor = isHighlighted ? '#2196F3' : strokeColor;
  $: lineWidth = isHighlighted ? strokeWidth + 1 : strokeWidth;

  /**
   * 计算连接线的 SVG 路径
   * 使用贝塞尔曲线创建平滑的连接线
   */
  function calculatePath(conn: ConnectionLine): string {
    const { from, to } = conn;
    
    // 计算控制点，创建平滑的曲线
    const midY = from.y + (to.y - from.y) / 2;
    
    // 使用三次贝塞尔曲线
    return `M ${from.x} ${from.y} 
            C ${from.x} ${midY}, 
              ${to.x} ${midY}, 
              ${to.x} ${to.y}`;
  }

  /**
   * 计算箭头路径
   */
  function calculateArrowPath(conn: ConnectionLine): string {
    const { to } = conn;
    const arrowSize = 6;
    
    // 箭头指向子类节点
    return `M ${to.x - arrowSize} ${to.y - arrowSize} 
            L ${to.x} ${to.y} 
            L ${to.x + arrowSize} ${to.y - arrowSize}`;
  }

  $: arrowPath = calculateArrowPath(connection);
</script>

<!-- 继承关系连接线 -->
<g class="connection-line" class:highlighted={isHighlighted}>
  <!-- 主连接线 -->
  <path
    d={pathData}
    stroke={lineColor}
    stroke-width={lineWidth}
    fill="none"
    stroke-linecap="round"
    stroke-linejoin="round"
    class="main-line"
  />
  
  <!-- 箭头 -->
  <path
    d={arrowPath}
    stroke={lineColor}
    stroke-width={lineWidth}
    fill="none"
    stroke-linecap="round"
    stroke-linejoin="round"
    class="arrow"
  />
  
  <!-- 连接点 -->
  <circle
    cx={connection.from.x}
    cy={connection.from.y}
    r="3"
    fill={lineColor}
    class="connection-point from-point"
  />
  
  <circle
    cx={connection.to.x}
    cy={connection.to.y}
    r="3"
    fill={lineColor}
    class="connection-point to-point"
  />
</g>

<style>
  .connection-line {
    transition: all 0.3s ease;
  }

  .main-line {
    transition: all 0.3s ease;
  }

  .arrow {
    transition: all 0.3s ease;
  }

  .connection-point {
    transition: all 0.3s ease;
  }

  .connection-line.highlighted .main-line {
    stroke-width: 3;
    filter: drop-shadow(0 0 4px rgba(33, 150, 243, 0.5));
  }

  .connection-line.highlighted .arrow {
    stroke-width: 3;
    filter: drop-shadow(0 0 4px rgba(33, 150, 243, 0.5));
  }

  .connection-line.highlighted .connection-point {
    r: 4;
    filter: drop-shadow(0 0 4px rgba(33, 150, 243, 0.5));
  }

  /* 悬停效果 */
  .connection-line:hover .main-line {
    stroke-width: 3;
    opacity: 0.8;
  }

  .connection-line:hover .arrow {
    stroke-width: 3;
    opacity: 0.8;
  }

  .connection-line:hover .connection-point {
    r: 4;
    opacity: 0.8;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .main-line {
      stroke-width: 1.5;
    }
    
    .arrow {
      stroke-width: 1.5;
    }
    
    .connection-point {
      r: 2;
    }
    
    .connection-line.highlighted .main-line,
    .connection-line.highlighted .arrow {
      stroke-width: 2.5;
    }
    
    .connection-line.highlighted .connection-point {
      r: 3;
    }
  }
</style>
