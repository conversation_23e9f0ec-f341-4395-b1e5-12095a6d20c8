<script lang="ts">
  import { SimpleDataSelector, DataSelector } from '../index.ts';
  import type { DataSelection } from '../types/dataTypes.ts';
  
  // 演示状态
  let simpleSelection = $state<DataSelection | null>(null);
  let advancedSelection = $state<DataSelection | null>(null);
  let fieldValue = $state<any>(null);

  // 处理简单选择器的选择
  function handleSimpleSelect(selection: DataSelection | null) {
    simpleSelection = selection;
    updateFieldValue();
  }

  // 处理高级选择器的选择
  function handleAdvancedSelect(selection: DataSelection | null) {
    advancedSelection = selection;
  }

  // 更新字段值显示
  function updateFieldValue() {
    if (simpleSelection) {
      // 这里可以获取实际的字段值
      // fieldValue = dataManager.getFieldValue(simpleSelection.dataType, simpleSelection.fieldPath);
      fieldValue = `字段路径: ${simpleSelection.fieldPath}`;
    } else {
      fieldValue = null;
    }
  }

  // 清除所有选择
  function clearAll() {
    simpleSelection = null;
    advancedSelection = null;
    fieldValue = null;
  }
</script>

<div class="demo-container">
  <h2>数据管理系统演示</h2>
  
  <div class="demo-section">
    <h3>简单数据选择器</h3>
    <p>适用于大多数场景的简化版选择器</p>
    
    <div class="demo-item">
      <label>选择数据字段：</label>
      <SimpleDataSelector 
        value={simpleSelection}
        onSelect={handleSimpleSelect}
        placeholder="请选择一个数据字段"
      />
    </div>

    {#if simpleSelection}
      <div class="selection-info">
        <h4>选择结果：</h4>
        <div class="info-grid">
          <div class="info-item">
            <strong>数据类型：</strong> {simpleSelection.dataType}
          </div>
          <div class="info-item">
            <strong>字段路径：</strong> {simpleSelection.fieldPath}
          </div>
          <div class="info-item">
            <strong>字段类型：</strong> {simpleSelection.fieldInfo.type}
          </div>
          <div class="info-item">
            <strong>显示文本：</strong> {simpleSelection.displayText}
          </div>
          {#if simpleSelection.fieldInfo.description}
            <div class="info-item">
              <strong>字段描述：</strong> {simpleSelection.fieldInfo.description}
            </div>
          {/if}
        </div>
      </div>
    {/if}

    {#if fieldValue}
      <div class="field-value">
        <h4>字段信息：</h4>
        <pre>{JSON.stringify(fieldValue, null, 2)}</pre>
      </div>
    {/if}
  </div>

  <div class="demo-section">
    <h3>高级数据选择器</h3>
    <p>功能完整的选择器，包含搜索和详细信息</p>
    
    <div class="demo-item">
      <label>选择数据字段：</label>
      <DataSelector 
        onSelect={handleAdvancedSelect}
        placeholder="请选择一个数据字段"
        showSearch={true}
      />
    </div>

    {#if advancedSelection}
      <div class="selection-info">
        <h4>高级选择结果：</h4>
        <div class="info-grid">
          <div class="info-item">
            <strong>数据类型：</strong> {advancedSelection.dataType}
          </div>
          <div class="info-item">
            <strong>字段路径：</strong> {advancedSelection.fieldPath}
          </div>
          <div class="info-item">
            <strong>字段类型：</strong> {advancedSelection.fieldInfo.type}
          </div>
          <div class="info-item">
            <strong>显示文本：</strong> {advancedSelection.displayText}
          </div>
        </div>
      </div>
    {/if}
  </div>

  <div class="demo-section">
    <h3>紧凑模式</h3>
    <p>适用于空间有限的场景</p>
    
    <div class="demo-item">
      <label>紧凑选择器：</label>
      <SimpleDataSelector 
        compact={true}
        placeholder="紧凑模式"
      />
    </div>
  </div>

  <div class="demo-actions">
    <button class="clear-button" on:click={clearAll}>
      清除所有选择
    </button>
  </div>

  <div class="demo-section">
    <h3>使用说明</h3>
    <div class="usage-info">
      <h4>基本用法：</h4>
      <pre><code>{`<SimpleDataSelector 
  value={selection}
  onSelect={handleSelect}
  placeholder="选择数据字段"
/>`}</code></pre>

      <h4>高级用法：</h4>
      <pre><code>{`<DataSelector 
  onSelect={handleSelect}
  showSearch={true}
  maxHeight="400px"
/>`}</code></pre>

      <h4>获取字段值：</h4>
      <pre><code>{`import { dataManager } from 'src/dataManage';

const value = dataManager.getFieldValue(
  selection.dataType, 
  selection.fieldPath, 
  itemIndex // 可选，用于数组数据
);`}</code></pre>
    </div>
  </div>
</div>

<style>
  .demo-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .demo-section {
    margin-bottom: 32px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h3 {
    margin-top: 0;
    color: #333;
  }

  .demo-section p {
    color: #666;
    margin-bottom: 16px;
  }

  .demo-item {
    margin-bottom: 16px;
  }

  .demo-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
  }

  .selection-info {
    margin-top: 16px;
    padding: 16px;
    background: white;
    border-radius: 6px;
    border: 1px solid #ddd;
  }

  .selection-info h4 {
    margin-top: 0;
    margin-bottom: 12px;
    color: #333;
  }

  .info-grid {
    display: grid;
    gap: 8px;
  }

  .info-item {
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 14px;
  }

  .info-item strong {
    color: #495057;
  }

  .field-value {
    margin-top: 16px;
    padding: 16px;
    background: white;
    border-radius: 6px;
    border: 1px solid #ddd;
  }

  .field-value h4 {
    margin-top: 0;
    margin-bottom: 12px;
  }

  .field-value pre {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
    margin: 0;
  }

  .demo-actions {
    text-align: center;
    margin: 32px 0;
  }

  .clear-button {
    padding: 8px 16px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  }

  .clear-button:hover {
    background: #c82333;
  }

  .usage-info h4 {
    margin-top: 20px;
    margin-bottom: 8px;
    color: #333;
  }

  .usage-info pre {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
    margin-bottom: 16px;
  }

  .usage-info code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
</style>
