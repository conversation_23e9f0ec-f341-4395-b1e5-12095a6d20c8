/**
 * 载具对象复制器
 */

import type { VehicleCopyOptions, CopyResult } from './types';
import { DEFAULT_COPY_OPTIONS } from './types';
import { CopyUtils } from './CopyUtils';

/**
 * 载具复制器类
 */
export class VehicleCopier {
  
  /**
   * 复制载具对象
   * @param sourceVehicle 源载具对象
   * @param options 复制选项
   * @returns 复制结果
   */
  static async copy(sourceVehicle: any, options: VehicleCopyOptions = {}): Promise<CopyResult> {
    console.log('=== VehicleCopier: 开始复制载具对象 ===');
    console.log('源对象:', sourceVehicle);
    console.log('复制选项:', options);

    try {
      // 合并默认选项
      const copyOptions = { ...DEFAULT_COPY_OPTIONS, ...options };

      // 等待资源加载
      const resourcesReady = await CopyUtils.waitForResources();
      if (!resourcesReady) {
        throw new Error('资源加载超时');
      }

      // 解析源对象结构
      const structure = CopyUtils.parseObjectStructure(sourceVehicle);
      console.log('源对象结构:', structure);

      // 处理直接的 Sprite_Character 对象
      if (!structure.gameObject && sourceVehicle._character) {
        structure.gameObject = sourceVehicle._character;
        structure.displayObject = sourceVehicle;
      }

      if (!structure.gameObject) {
        throw new Error('无法找到有效的游戏对象');
      }

      const sourceGameObject = structure.gameObject;
      const sourceSprite = structure.displayObject;

      // 验证是否为载具对象
      if (sourceGameObject.constructor?.name !== 'Game_Vehicle') {
        throw new Error(`期望 Game_Vehicle 对象，实际得到: ${sourceGameObject.constructor?.name}`);
      }

      console.log('源载具信息:', {
        vehicleType: sourceGameObject._type,
        characterName: sourceGameObject._characterName,
        characterIndex: sourceGameObject._characterIndex,
        direction: sourceGameObject._direction,
        x: sourceGameObject._x,
        y: sourceGameObject._y
      });

      // 确定载具类型
      let vehicleType = options.vehicleType;
      if (!vehicleType) {
        // 根据源对象推断载具类型
        if (sourceGameObject._type !== undefined) {
          const typeMap = { 0: 'boat', 1: 'ship', 2: 'airship' };
          vehicleType = typeMap[sourceGameObject._type] || 'boat';
        } else if (sourceGameObject._characterName) {
          // 根据角色名称推断
          const name = sourceGameObject._characterName.toLowerCase();
          if (name.includes('ship')) {
            vehicleType = 'ship';
          } else if (name.includes('airship')) {
            vehicleType = 'airship';
          } else {
            vehicleType = 'boat';
          }
        } else {
          vehicleType = 'boat';
        }
      }

      // 动态导入创建器
      const { createVehicle } = await import('../../creators/character/VehicleCreator');

      // 创建新的载具对象
      const newVehicle = await createVehicle({
        type: this.getVehicleTypeEnum(vehicleType),
        direction: sourceGameObject._direction,
        x: sourceGameObject._x,
        y: sourceGameObject._y
      });

      if (!newVehicle) {
        throw new Error('创建新载具对象失败');
      }

      // 应用位置偏移
      if (newVehicle._character && copyOptions.positionOffset) {
        CopyUtils.applyPositionOffset(newVehicle._character, copyOptions.positionOffset);
      }

      // 复制显示属性
      if (newVehicle && sourceSprite) {
        CopyUtils.copyDisplayProperties(sourceSprite, newVehicle);
      }

      // 更新名称
      if (copyOptions.nameSuffix) {
        CopyUtils.updateDisplayName(newVehicle, copyOptions.nameSuffix);
      }

      console.log('载具对象复制成功:', newVehicle);

      return {
        success: true,
        copiedObject: newVehicle,
        sourceType: CopyUtils.detectObjectType(sourceVehicle),
        targetType: CopyUtils.detectObjectType(newVehicle)
      };

    } catch (error) {
      console.error('VehicleCopier: 复制载具对象失败:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        sourceType: CopyUtils.detectObjectType(sourceVehicle)
      };
    }
  }

  /**
   * 获取载具类型枚举值
   * @param vehicleType 载具类型字符串
   * @returns 载具类型枚举值
   */
  private static getVehicleTypeEnum(vehicleType: string): number {
    const typeMap = {
      'boat': 0,
      'ship': 1,
      'airship': 2
    };
    return typeMap[vehicleType] || 0;
  }

  /**
   * 验证载具对象是否可以复制
   * @param sourceVehicle 源载具对象
   * @returns 是否可以复制
   */
  static canCopy(sourceVehicle: any): boolean {
    try {
      const structure = CopyUtils.parseObjectStructure(sourceVehicle);
      
      // 处理直接的 Sprite_Character 对象
      if (!structure.gameObject && sourceVehicle._character) {
        structure.gameObject = sourceVehicle._character;
      }

      if (!structure.gameObject) {
        return false;
      }

      // 检查是否为载具对象
      const gameObjectType = structure.gameObject.constructor?.name;
      if (gameObjectType !== 'Game_Vehicle') {
        return false;
      }

      // 检查必要属性
      const gameObject = structure.gameObject;
      return !!(
        gameObject._type !== undefined &&
        gameObject._characterName &&
        gameObject._characterIndex !== undefined
      );

    } catch (error) {
      console.warn('VehicleCopier: 验证对象时出错:', error);
      return false;
    }
  }

  /**
   * 获取载具对象信息
   * @param sourceVehicle 源载具对象
   * @returns 载具信息
   */
  static getVehicleInfo(sourceVehicle: any): any {
    try {
      const structure = CopyUtils.parseObjectStructure(sourceVehicle);
      
      // 处理直接的 Sprite_Character 对象
      if (!structure.gameObject && sourceVehicle._character) {
        structure.gameObject = sourceVehicle._character;
      }

      if (!structure.gameObject) {
        return null;
      }

      const gameObject = structure.gameObject;
      
      const typeMap = { 0: 'boat', 1: 'ship', 2: 'airship' };
      const vehicleType = typeMap[gameObject._type] || 'unknown';
      
      return {
        type: 'Game_Vehicle',
        vehicleType: vehicleType,
        characterName: gameObject._characterName,
        characterIndex: gameObject._characterIndex,
        direction: gameObject._direction,
        position: { x: gameObject._x, y: gameObject._y },
        isWrapper: structure.isWrapper,
        displayName: structure.displayName
      };

    } catch (error) {
      console.warn('VehicleCopier: 获取载具信息时出错:', error);
      return null;
    }
  }
}
