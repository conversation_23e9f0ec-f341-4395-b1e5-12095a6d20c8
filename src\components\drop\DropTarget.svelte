<script lang="ts">
  /**
   * 拖拽接收组件 - 使用自定义拖拽实现，支持历史记录
   */
  import { dragStore } from '../../stores/dragStore';
  import { historyManager } from '../../historyManager';
  import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';

  interface Props {
    onDrop?: (object: BaseObjectModel) => void;
    placeholder?: string;
    children?: any;
    // 历史记录相关
    targetObject?: any; // 对应的模型对象
    fieldName?: string; // 对应的字段名
    enableHistory?: boolean; // 是否启用历史记录
    operationName?: string; // 操作名称，用于历史记录描述
  }

  let {
    onDrop,
    placeholder = "拖拽到此处",
    children,
    // 历史记录相关
    targetObject = undefined,
    fieldName = undefined,
    enableHistory = true,
    operationName = undefined
  }: Props = $props();

  // 响应式状态
  let isDragOver = $state(false);
  let dropElement: HTMLDivElement;
  let dragState = $state<{ isDragging: boolean; draggedObject: BaseObjectModel | null }>({
    isDragging: false,
    draggedObject: null
  });

  // 订阅拖拽状态
  dragStore.subscribe(state => {
    dragState = state;

    // 当拖拽状态改变时，更新鼠标样式
    if (state.isDragging) {
      document.body.style.cursor = 'copy';
    } else {
      document.body.style.cursor = '';
      isDragOver = false;
    }
  });

  // 组件挂载后添加自定义事件监听器
  $effect(() => {
    if (dropElement) {
      dropElement.addEventListener('customdrop', handleCustomDrop as EventListener);

      return () => {
        dropElement?.removeEventListener('customdrop', handleCustomDrop as EventListener);
      };
    }
  });

  // 鼠标进入
  function handleMouseEnter() {
    if (dragState.isDragging) {
      console.log('🎯 鼠标进入拖拽目标区域');
      isDragOver = true;
      document.body.style.cursor = 'copy';

      // 添加拖拽预览的高亮效果
      const dragPreview = document.querySelector('.drag-preview') as HTMLElement;
      if (dragPreview) {
        dragPreview.style.transform = 'rotate(0deg) scale(1.05)';
        dragPreview.style.opacity = '0.9';
      }
    }
  }

  // 鼠标离开
  function handleMouseLeave() {
    if (dragState.isDragging) {
      console.log('🎯 鼠标离开拖拽目标区域');
      isDragOver = false;
      document.body.style.cursor = 'grabbing';

      // 恢复拖拽预览的原始效果
      const dragPreview = document.querySelector('.drag-preview') as HTMLElement;
      if (dragPreview) {
        dragPreview.style.transform = 'rotate(5deg) scale(1)';
        dragPreview.style.opacity = '0.7';
      }
    }
  }

  // 自定义拖拽放置事件 - 支持历史记录
  function handleCustomDrop(event: CustomEvent) {
    console.log('🎯 接收到自定义拖拽事件');
    isDragOver = false;

    const { object } = event.detail;
    if (!object) {
      console.warn('🎯 DropTarget: 没有接收到有效对象');
      return;
    }

    // 🎯 历史记录处理
    let historyOperationStarted = false;
    let oldValue = null;

    try {
      // 开始历史记录操作
      if (enableHistory && historyManager.isRecording()) {
        const defaultOperationName = `绑定${object.className}到${fieldName || '组件'}`;
        const finalOperationName = operationName || defaultOperationName;

        historyManager.startGroup(finalOperationName);
        historyOperationStarted = true;

        console.log("📝 DropTarget: 开始历史记录操作:", {
          operationName: finalOperationName,
          targetObject: targetObject?.className || 'none',
          fieldName: fieldName || 'none',
          droppedObject: object.className
        });

        // 记录旧值
        if (targetObject && fieldName) {
          oldValue = targetObject[fieldName];
          console.log("📝 DropTarget: 记录旧值:", oldValue?.className || oldValue);
        }
      }

      // 执行拖拽放置操作
      onDrop?.(object);
      console.log('🎯 成功接收对象:', object.className);

      // 记录历史变更
      if (historyOperationStarted && targetObject && fieldName) {
        const newValue = targetObject[fieldName];

        // 只有当值真正发生变化时才记录
        if (oldValue !== newValue) {
          historyManager.recordChange(targetObject, fieldName, oldValue, newValue);
          console.log("📝 DropTarget: 已记录绑定变更:", {
            field: fieldName,
            oldValue: oldValue?.className || oldValue,
            newValue: newValue?.className || newValue
          });
        } else {
          console.log("📝 DropTarget: 值未变化，跳过记录");
        }
      }

    } catch (error) {
      console.error('🎯 DropTarget: 拖拽处理出错:', error);
    } finally {
      // 确保历史记录操作正确结束
      if (historyOperationStarted) {
        historyManager.endGroup();
        console.log("📝 DropTarget: 历史记录操作组已结束");
      }
    }
  }
</script>

<div
  bind:this={dropElement}
  class="drop-target"
  class:drag-over={isDragOver}
  class:can-accept={dragState.isDragging}
  onmouseenter={handleMouseEnter}
  onmouseleave={handleMouseLeave}
  data-drop-target="true"
  role="region"
  aria-label="拖拽接收区域"
>
  {#if children}
    {@render children?.()}
  {:else if isDragOver && dragState.draggedObject}
    <div class="drop-preview">
      准备接收: {dragState.draggedObject.className}
    </div>
  {:else}
    <div class="drop-placeholder">
      {placeholder}
    </div>
  {/if}
</div>

<style>
  .drop-target {
    min-height: 60px;
    border: 2px dashed #ccc;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .drop-target.can-accept {
    border-color: #2196F3;
    background-color: rgba(33, 150, 243, 0.05);
  }

  .drop-target.drag-over {
    border-color: #4CAF50;
    background-color: rgba(76, 175, 80, 0.1);
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    animation: pulse 1s infinite;
  }

  @keyframes pulse {
    0%, 100% {
      box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }
    50% {
      box-shadow: 0 6px 20px rgba(76, 175, 80, 0.5);
    }
  }

  .drop-placeholder {
    color: #999;
    font-size: 12px;
  }

  .drop-preview {
    color: #2196F3;
    font-size: 12px;
    font-weight: 500;
  }
</style>
