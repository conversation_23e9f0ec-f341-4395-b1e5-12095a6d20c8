/**
 * 选中对象拖动工具的类型定义
 */

import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';
import type { PropertyChanges } from './ModelWatcher.svelte';

/**
 * 拖动箭头类型
 */
export enum ArrowType {
  UP = 'up',
  DOWN = 'down',
  LEFT = 'left',
  RIGHT = 'right',
  CENTER = 'center'
}

/**
 * 工具模式
 */
export enum ToolMode {
  TRANSFORM = 'transform',
  SCALE = 'scale'
}

/**
 * 拖动箭头配置
 */
export interface ArrowConfig {
  type: ArrowType;
  x: number;
  y: number;
  size: number;
  color: string;
  hoverColor: string;
  activeColor: string;
}

/**
 * 包围盒配置
 */
export interface BoundingBoxConfig {
  strokeColor: string;
  strokeWidth: number;
  fillColor?: string;
  dashPattern?: number[];
}

/**
 * 拖动状态
 */
export interface DragState {
  isDragging: boolean;
  dragType: ArrowType | null;
  startX: number;
  startY: number;
  startObjectX: number;
  startObjectY: number;
  currentX: number;
  currentY: number;
}

/**
 * 选中对象信息
 */
export interface SelectedObjectInfo {
  object: BaseObjectModel;
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  center: {
    x: number;
    y: number;
  };
}

/**
 * 工具配置
 */
export interface ToolConfig {
  // 箭头配置
  arrowSize: number;
  arrowOffset: number;
  arrowColors: {
    normal: string;
    hover: string;
    active: string;
  };

  // 包围盒配置
  boundingBox: BoundingBoxConfig;

  // 拖动配置
  dragSensitivity: number;
  snapToGrid: boolean;
  gridSize: number;
}

/**
 * 事件回调接口
 */
export interface ToolEventCallbacks {
  onDragStart?: (object: BaseObjectModel, dragType: ArrowType) => void;
  onDragMove?: (object: BaseObjectModel, deltaX: number, deltaY: number) => void;
  onDragEnd?: (object: BaseObjectModel, finalX: number, finalY: number) => void;
  onSelectionChange?: (object: BaseObjectModel | null) => void;
  onModelPropertyChange?: (changes: PropertyChanges, model: BaseObjectModel) => void;
}

/**
 * 渲染上下文接口
 */
export interface RenderContext {
  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  width: number;
  height: number;
  scale?: { x: number; y: number };
  offset?: { x: number; y: number };
  rotation?: number;
}

/**
 * 鼠标事件信息
 */
export interface MouseEventInfo {
  x: number;
  y: number;
  button: number;
  ctrlKey: boolean;
  shiftKey: boolean;
  altKey: boolean;
}

/**
 * 碰撞检测结果
 */
export interface HitTestResult {
  hit: boolean;
  type: 'arrow' | 'boundingBox' | 'none';
  arrowType?: ArrowType;
  distance?: number;
}
