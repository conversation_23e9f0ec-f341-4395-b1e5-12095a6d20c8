<script lang="ts">
  import { DataBindingInput } from '../index';

  // 测试状态
  let textValue = $state('');

  // 处理文本变化
  function handleTextChange(newValue: string) {
    textValue = newValue;
    console.log('文本变化:', newValue);
  }
</script>

<div class="test-container">
  <h2>DataBindingInput 测试</h2>
  
  <div class="test-section">
    <h3>数据绑定输入组件</h3>
    <DataBindingInput 
      value={textValue}
      onChange={handleTextChange}
      placeholder="输入文本或选择数据绑定"
      showPreview={true}
    />
  </div>

  <div class="result-section">
    <h3>当前值</h3>
    <div class="result-display">
      <strong>文本内容:</strong>
      <pre>{textValue || '(空)'}</pre>
    </div>
  </div>

  <div class="instructions">
    <h3>测试说明</h3>
    <ul>
      <li>在文本框中输入内容</li>
      <li>或者使用数据绑定选择器选择RPG Maker MZ数据字段</li>
      <li>点击"应用绑定"按钮将数据绑定插入到文本中</li>
      <li>如果有数据绑定表达式，会显示预览效果</li>
    </ul>
  </div>
</div>

<style>
  .test-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .test-section, .result-section, .instructions {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
  }

  .test-section h3, .result-section h3, .instructions h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #333;
  }

  .result-display {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }

  .result-display strong {
    color: #495057;
  }

  .result-display pre {
    margin: 8px 0 0 0;
    padding: 8px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    font-family: monospace;
    font-size: 14px;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .instructions ul {
    margin: 0;
    padding-left: 20px;
  }

  .instructions li {
    margin-bottom: 8px;
    line-height: 1.5;
  }
</style>
