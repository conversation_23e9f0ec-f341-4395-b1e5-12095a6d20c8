<script lang="ts">
  /**
   * 工具栏面板组件
   * 包含位移工具、缩放工具等
   */

  // 工具类型常量
  const ToolType = {
    TRANSFORM: 'transform',
    SCALE: 'scale'
  } as const;

  type ToolTypeValue = typeof ToolType[keyof typeof ToolType];

  // 当前激活的工具
  let activeTool = $state<ToolTypeValue>(ToolType.TRANSFORM);

  // 工具配置
  const tools = [
    {
      id: ToolType.TRANSFORM,
      name: '位移工具',
      icon: '🎯',
      description: '移动和定位对象',
      shortcut: 'W'
    },
    {
      id: ToolType.SCALE,
      name: '缩放工具',
      icon: '📏',
      description: '缩放对象大小',
      shortcut: 'E'
    }
  ];

  /**
   * 切换工具
   */
  function selectTool(toolType: ToolTypeValue) {
    if (activeTool !== toolType) {
      activeTool = toolType;
      console.log('🔧 切换工具:', toolType);

      // 触发工具切换事件
      handleToolChange(toolType);
    }
  }

  /**
   * 处理工具切换
   */
  function handleToolChange(toolType: ToolTypeValue) {
    // 导入工具管理器
    import('../logics/tool/utils.svelte').then(({ getSelectionToolInstance }) => {
      const toolInstance = getSelectionToolInstance();
      if (toolInstance) {
        const tool = toolInstance.getTool();
        switch (toolType) {
          case ToolType.TRANSFORM:
            console.log('✅ 激活位移工具');
            tool.setToolMode('transform' as any);
            break;
          case ToolType.SCALE:
            console.log('✅ 激活缩放工具');
            tool.setToolMode('scale' as any);
            break;
        }
      }
    });
  }

  /**
   * 处理键盘快捷键
   */
  function handleKeydown(event: KeyboardEvent) {
    switch (event.key.toLowerCase()) {
      case 'w':
        selectTool(ToolType.TRANSFORM);
        event.preventDefault();
        break;
      case 'e':
        selectTool(ToolType.SCALE);
        event.preventDefault();
        break;
    }
  }
</script>

<svelte:window onkeydown={handleKeydown} />

<div class="toolbar-panel">
  <div class="toolbar-container">
    <!-- 工具按钮组 -->
    <div class="tool-group">
      <div class="tool-group-label">变换工具</div>
      <div class="tool-buttons">
        {#each tools as tool}
          <button
            class="tool-button"
            class:active={activeTool === tool.id}
            onclick={() => selectTool(tool.id)}
            title={`${tool.description} (${tool.shortcut})`}
          >
            <span class="tool-icon">{tool.icon}</span>
            <span class="tool-name">{tool.name}</span>
            <span class="tool-shortcut">{tool.shortcut}</span>
          </button>
        {/each}
      </div>
    </div>

    <!-- 工具选项区域 -->
    <!-- <div class="tool-options">
      {#if activeTool === ToolType.TRANSFORM}
        <div class="option-group">
          <label class="option-label" for="grid-snap">网格对齐</label>
          <input type="checkbox" class="option-checkbox" id="grid-snap" />
        </div>
        <div class="option-group">
          <label class="option-label" for="grid-size">网格大小</label>
          <input type="number" class="option-input" id="grid-size" value="16" min="1" max="64" />
        </div>
      {:else if activeTool === ToolType.SCALE}
        <div class="option-group">
          <label class="option-label" for="uniform-scale">等比缩放</label>
          <input type="checkbox" class="option-checkbox" id="uniform-scale" checked />
        </div>
        <div class="option-group">
          <label class="option-label" for="scale-step">缩放步长</label>
          <input type="number" class="option-input" id="scale-step" value="0.1" min="0.01" max="1" step="0.01" />
        </div>
      {/if}
    </div> -->

    <!-- 状态信息 -->
    <div class="status-info">
      <span class="status-text">当前工具: {tools.find(t => t.id === activeTool)?.name}</span>
    </div>
  </div>
</div>

<style>
  .toolbar-panel {
    background: var(--theme-surface);
    border-bottom: 1px solid var(--theme-border);
    padding: var(--spacing-1) var(--spacing-3);
    min-height: 40px;
    max-height: 40px;
    display: flex;
    align-items: center;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .toolbar-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    width: 100%;
  }

  .tool-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .tool-group-label {
    font-size: var(--font-size-xs);
    color: var(--theme-text-secondary);
    font-weight: 500;
    white-space: nowrap;
  }

  .tool-buttons {
    display: flex;
    gap: var(--spacing-1);
  }

  .tool-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: 4px 8px;
    background: var(--theme-background);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius-small);
    cursor: pointer;
    transition: var(--transition-base);
    font-size: var(--font-size-xs);
    color: var(--theme-text);
    min-width: 75px;
    height: 28px;
  }

  .tool-button:hover {
    background: var(--theme-surface-hover);
    border-color: var(--theme-primary);
  }

  .tool-button.active {
    background: var(--theme-primary);
    color: var(--theme-text-inverse);
    border-color: var(--theme-primary);
  }

  .tool-icon {
    font-size: 14px;
  }

  .tool-name {
    flex: 1;
    text-align: left;
    font-weight: 500;
  }

  .tool-shortcut {
    font-size: var(--font-size-xs);
    opacity: 0.7;
    background: rgba(255, 255, 255, 0.1);
    padding: 1px 4px;
    border-radius: 2px;
    font-family: var(--font-family-mono);
  }

  .tool-button.active .tool-shortcut {
    background: rgba(255, 255, 255, 0.2);
  }

  .tool-options {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding-left: var(--spacing-3);
    border-left: 1px solid var(--theme-border);
  }

  .option-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
  }

  .option-label {
    font-size: var(--font-size-xs);
    color: var(--theme-text-secondary);
    white-space: nowrap;
  }

  .option-checkbox {
    width: 16px;
    height: 16px;
  }

  .option-input {
    width: 45px;
    height: 20px;
    padding: 0 4px;
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius-small);
    background: var(--theme-background);
    color: var(--theme-text);
    font-size: var(--font-size-xs);
  }

  .option-input:focus {
    outline: none;
    border-color: var(--theme-primary);
  }

  .status-info {
    margin-left: auto;
    padding-left: var(--spacing-3);
    border-left: 1px solid var(--theme-border);
  }

  .status-text {
    font-size: var(--font-size-xs);
    color: var(--theme-text-secondary);
    font-family: var(--font-family-mono);
  }
</style>
