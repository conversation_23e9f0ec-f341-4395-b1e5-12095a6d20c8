// 导出所有手风琴面板相关组件
export { default as AccordionPanel } from './AccordionPanel.svelte';
export { default as PropertyContainer } from './PropertyContainer.svelte';
export { default as PropertyPanel } from './PropertyPanel.svelte';

// 类型定义
export interface PropertyConfig {
  label: string;
  component: any;
  props?: Record<string, any>;
  required?: boolean;
  tooltip?: string;
  width?: 'auto' | 'full' | 'half';
  actions?: Array<{
    component: any;
    props?: Record<string, any>;
    content?: string;
  }>;
}

export interface PanelConfig {
  title: string;
  icon?: string;
  badge?: string;
  badgeVariant?: 'active' | 'inactive' | 'info';
  expanded?: boolean;
  disabled?: boolean;
  properties: PropertyConfig[];
  columns?: number;
  gap?: string;
  labelWidth?: string;
}
