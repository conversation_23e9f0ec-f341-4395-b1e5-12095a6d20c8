<script lang="ts">
  import { dataStore } from '../stores/dataStore';
  import type { StaticDataType, FieldInfo, DataSelection } from '../types/dataTypes';

  // Props
  interface Props {
    onSelect?: (selection: DataSelection) => void;
    placeholder?: string;
    disabled?: boolean;
    showSearch?: boolean;
    maxHeight?: string;
  }

  let {
    onSelect,
    placeholder = "选择数据字段",
    disabled = false,
    showSearch = true,
    maxHeight = "300px"
  }: Props = $props();

  // 本地状态
  let isOpen = $state(false);
  let searchInput: HTMLInputElement;

  // 获取可用的数据类型
  const availableDataTypes = dataStore.getAvailableDataTypes();

  // 处理数据类型选择
  function handleDataTypeSelect(dataType: StaticDataType) {
    dataStore.selectDataType(dataType);
  }

  // 处理字段选择
  function handleFieldSelect(field: FieldInfo, fieldPath: string) {
    dataStore.selectField(field, fieldPath);
    const selection = dataStore.createCurrentSelection();
    if (selection && onSelect) {
      onSelect(selection);
    }
    isOpen = false;
  }

  // 构建字段路径
  function buildFieldPath(parentPath: string, fieldName: string): string {
    return parentPath ? `${parentPath}.${fieldName}` : fieldName;
  }

  // 渲染字段列表（递归）
  function renderFields(fields: FieldInfo[], parentPath: string = '', depth: number = 0): any {
    return fields.map(field => {
      const fieldPath = buildFieldPath(parentPath, field.name);
      const hasChildren = field.children && field.children.length > 0;

      return {
        field,
        fieldPath,
        hasChildren,
        depth,
        children: hasChildren ? renderFields(field.children!, fieldPath, depth + 1) : []
      };
    });
  }

  // 获取当前显示的字段
  $derived: {
    const fields = dataStore.filteredFields;
    renderFields(fields);
  }

  // 处理搜索
  function handleSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    dataStore.setSearchTerm(target.value);
  }

  // 清除搜索
  function clearSearch() {
    dataStore.clearSearch();
    if (searchInput) {
      searchInput.value = '';
    }
  }

  // 切换下拉框
  function toggleDropdown() {
    if (disabled) return;
    isOpen = !isOpen;
    if (isOpen && showSearch && searchInput) {
      setTimeout(() => searchInput.focus(), 100);
    }
  }

  // 关闭下拉框
  function closeDropdown() {
    isOpen = false;
  }

  // 点击外部关闭
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as Element;
    if (!target.closest('.data-selector')) {
      closeDropdown();
    }
  }
</script>

<svelte:window on:click={handleClickOutside} />

<div class="data-selector" class:disabled>
  <!-- 选择器按钮 -->
  <button
    class="selector-button"
    class:open={isOpen}
    on:click={toggleDropdown}
    {disabled}
  >
    <span class="selector-text">
      {dataStore.selectedField ?
        `${dataStore.currentDataTypeInfo?.displayName} → ${dataStore.selectedField.displayName}` :
        placeholder
      }
    </span>
    <span class="selector-arrow" class:rotated={isOpen}>▼</span>
  </button>

  <!-- 下拉面板 -->
  {#if isOpen}
    <div class="dropdown-panel" style="max-height: {maxHeight}">
      <!-- 搜索框 -->
      {#if showSearch}
        <div class="search-section">
          <input
            bind:this={searchInput}
            type="text"
            placeholder="搜索字段..."
            class="search-input"
            on:input={handleSearch}
          />
          {#if dataStore.searchTerm}
            <button class="clear-search" on:click={clearSearch}>✕</button>
          {/if}
        </div>
      {/if}

      <!-- 数据类型选择 -->
      <div class="data-types-section">
        <div class="section-title">选择数据类型</div>
        <div class="data-types-grid">
          {#each availableDataTypes as { type, displayName }}
            <button
              class="data-type-button"
              class:selected={dataStore.selectedDataType === type}
              class:loading={dataStore.isLoading && dataStore.selectedDataType === type}
              on:click={() => handleDataTypeSelect(type)}
            >
              {displayName}
            </button>
          {/each}
        </div>
      </div>

      <!-- 字段选择 -->
      {#if dataStore.selectedDataType}
        <div class="fields-section">
          <div class="section-title">选择字段</div>

          {#if dataStore.isLoading}
            <div class="loading">加载中...</div>
          {:else if dataStore.error}
            <div class="error">{dataStore.error}</div>
          {:else if dataStore.filteredFields.length === 0}
            <div class="no-results">
              {dataStore.searchTerm ? '没有找到匹配的字段' : '没有可用字段'}
            </div>
          {:else}
            <div class="fields-list">
              {#each renderFields(dataStore.filteredFields) as { field, fieldPath, hasChildren, depth, children }}
                <div class="field-item" style="padding-left: {depth * 20}px">
                  <button
                    class="field-button"
                    class:has-children={hasChildren}
                    on:click={() => handleFieldSelect(field, fieldPath)}
                  >
                    <span class="field-name">{field.displayName}</span>
                    <span class="field-type">{field.type}</span>
                    {#if field.description}
                      <span class="field-description">{field.description}</span>
                    {/if}
                  </button>
                </div>

                <!-- 递归渲染子字段 -->
                {#if hasChildren && children.length > 0}
                  {#each children as child}
                    <svelte:self {...child} />
                  {/each}
                {/if}
              {/each}
            </div>
          {/if}
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .data-selector {
    position: relative;
    width: 100%;
  }

  .data-selector.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  .selector-button {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s;
  }

  .selector-button:hover {
    border-color: #007acc;
  }

  .selector-button.open {
    border-color: #007acc;
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
  }

  .selector-text {
    flex: 1;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .selector-arrow {
    transition: transform 0.2s;
    margin-left: 8px;
  }

  .selector-arrow.rotated {
    transform: rotate(180deg);
  }

  .dropdown-panel {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow-y: auto;
  }

  .search-section {
    padding: 8px;
    border-bottom: 1px solid #eee;
    position: relative;
  }

  .search-input {
    width: 100%;
    padding: 6px 30px 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
  }

  .clear-search {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #999;
  }

  .section-title {
    padding: 8px 12px;
    font-weight: bold;
    background: #f5f5f5;
    border-bottom: 1px solid #eee;
    font-size: 12px;
    color: #666;
  }

  .data-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 4px;
    padding: 8px;
  }

  .data-type-button {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background: white;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
  }

  .data-type-button:hover {
    background: #f0f8ff;
    border-color: #007acc;
  }

  .data-type-button.selected {
    background: #007acc;
    color: white;
    border-color: #007acc;
  }

  .data-type-button.loading {
    opacity: 0.6;
  }

  .fields-list {
    max-height: 200px;
    overflow-y: auto;
  }

  .field-item {
    border-bottom: 1px solid #f0f0f0;
  }

  .field-button {
    width: 100%;
    padding: 8px 12px;
    border: none;
    background: white;
    text-align: left;
    cursor: pointer;
    transition: background 0.2s;
  }

  .field-button:hover {
    background: #f8f8f8;
  }

  .field-name {
    font-weight: 500;
    color: #333;
  }

  .field-type {
    font-size: 11px;
    color: #666;
    margin-left: 8px;
    padding: 2px 4px;
    background: #f0f0f0;
    border-radius: 2px;
  }

  .field-description {
    display: block;
    font-size: 11px;
    color: #999;
    margin-top: 2px;
  }

  .loading, .error, .no-results {
    padding: 16px;
    text-align: center;
    color: #666;
    font-size: 14px;
  }

  .error {
    color: #d32f2f;
  }
</style>
