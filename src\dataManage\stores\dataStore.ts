import {
  StaticDataType,
  type DataTypeInfo,
  type FieldInfo,
  type DataSelection
} from '../types/dataTypes';
import { DataManager } from '../managers/DataManager';

// 创建全局数据管理器实例
const dataManager = new DataManager();

// 状态管理
class DataStore {
  // 当前选择的数据类型
  selectedDataType: StaticDataType | null = null;

  // 当前数据类型的信息
  currentDataTypeInfo: DataTypeInfo | null = null;

  // 当前可用的字段列表
  availableFields: FieldInfo[] = [];

  // 当前选择的字段
  selectedField: FieldInfo | null = null;

  // 当前选择的字段路径
  selectedFieldPath: string = '';

  // 搜索关键词
  searchTerm: string = '';

  // 过滤后的字段列表
  filteredFields: FieldInfo[] = [];

  // 加载状态
  isLoading: boolean = false;

  // 错误信息
  error: string | null = null;

  constructor() {
    // 不使用$effect，改为手动调用过滤
  }

  /**
   * 获取所有可用的数据类型
   */
  getAvailableDataTypes() {
    return dataManager.getAvailableDataTypes();
  }

  /**
   * 选择数据类型
   */
  async selectDataType(dataType: StaticDataType) {
    if (this.selectedDataType === dataType) return;

    this.isLoading = true;
    this.error = null;

    try {
      this.selectedDataType = dataType;
      this.currentDataTypeInfo = await dataManager.getDataTypeInfo(dataType);

      if (this.currentDataTypeInfo) {
        this.availableFields = this.currentDataTypeInfo.fields;
        this.filteredFields = this.availableFields;
      } else {
        this.availableFields = [];
        this.filteredFields = [];
        this.error = `无法加载 ${dataType} 的数据`;
      }

      // 清除之前的字段选择
      this.selectedField = null;
      this.selectedFieldPath = '';

    } catch (err) {
      this.error = `加载数据类型失败: ${err}`;
      console.error('Error selecting data type:', err);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 选择字段
   */
  selectField(field: FieldInfo, fieldPath: string) {
    this.selectedField = field;
    this.selectedFieldPath = fieldPath;
  }

  /**
   * 创建当前选择的数据选择对象
   */
  createCurrentSelection(): DataSelection | null {
    if (!this.selectedDataType || !this.selectedField || !this.selectedFieldPath) {
      return null;
    }

    return dataManager.createSelection(
      this.selectedDataType,
      this.selectedFieldPath,
      this.selectedField
    );
  }

  /**
   * 获取字段值
   */
  getFieldValue(fieldPath?: string, itemIndex?: number): any {
    if (!this.selectedDataType) return null;

    const path = fieldPath || this.selectedFieldPath;
    if (!path) return null;

    return dataManager.getFieldValue(this.selectedDataType, path, itemIndex);
  }

  /**
   * 设置搜索关键词
   */
  setSearchTerm(term: string) {
    this.searchTerm = term;
    // 手动触发过滤
    if (this.searchTerm.trim()) {
      this.filterFields();
    } else {
      this.filteredFields = this.availableFields;
    }
  }

  /**
   * 清除搜索
   */
  clearSearch() {
    this.searchTerm = '';
  }

  /**
   * 重置所有选择
   */
  reset() {
    this.selectedDataType = null;
    this.currentDataTypeInfo = null;
    this.availableFields = [];
    this.filteredFields = [];
    this.selectedField = null;
    this.selectedFieldPath = '';
    this.searchTerm = '';
    this.error = null;
  }

  /**
   * 刷新当前数据类型
   */
  async refresh() {
    if (this.selectedDataType) {
      dataManager.refreshDataType(this.selectedDataType);
      await this.selectDataType(this.selectedDataType);
    }
  }

  // 私有方法

  private async filterFields() {
    if (!this.selectedDataType || !this.searchTerm.trim()) {
      this.filteredFields = this.availableFields;
      return;
    }

    try {
      const filtered = await dataManager.searchFields(this.selectedDataType, this.searchTerm);
      this.filteredFields = filtered;
    } catch (err) {
      console.error('Error filtering fields:', err);
      this.filteredFields = this.availableFields;
    }
  }
}

// 创建并导出全局状态实例
export const dataStore = new DataStore();

// 导出数据管理器实例（供高级用法）
export { dataManager };
