<script lang="ts">
  // 组件属性 - 现在用于包裹单个字段
  export let labelWidth: string = 'auto';
</script>

<div
  class="property-container"
  style="--label-width: {labelWidth}"
>
  <slot />
</div>

<style>
  .property-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 6px;
    padding: 4px 6px;
    border: 1px solid var(--theme-border, #e2e8f0);
    border-radius: 3px;
    background: var(--theme-surface, #ffffff);
    margin-bottom: 2px;
    min-height: 26px;
    box-sizing: border-box;
  }

  /* 嵌套的PropertyContainer样式保持一致，也支持左右布局和换行 */
  :global(.property-container .property-container) {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
    padding: 3px 5px;
    border: 1px solid var(--theme-border, #e2e8f0);
    border-radius: 3px;
    background: var(--theme-surface, #ffffff);
    margin-bottom: 1px;
    margin-right: 4px;
    min-height: 22px;
    box-sizing: border-box;
    flex: 0 1 auto;
  }

  /* 为Label组件提供样式变量 */
  :global(.property-container .label-component) {
    width: var(--label-width, auto);
    min-width: fit-content;
    flex-shrink: 0;
    color: white !important;
  }

  /* 嵌套的Label组件样式调整，适应左右布局 */
  :global(.property-container .property-container .label-component) {
    width: auto;
    min-width: fit-content;
    flex-shrink: 0;
    color: white !important;
    font-size: 10px;
  }

  /* 为UI组件提供样式 */
  :global(.property-container > *:not(.label-component)) {
    flex: 1;
    min-width: 0;
  }

  /* 嵌套的UI组件样式 */
  :global(.property-container .property-container > *:not(.label-component)) {
    flex: 1;
    min-width: 0;
  }



  /* 当空间不够时，嵌套的PropertyContainer会换行 */
  @media (max-width: 600px) {
    :global(.property-container .property-container) {
      flex: 1 1 100%;
      margin-right: 0;
    }
  }
</style>
