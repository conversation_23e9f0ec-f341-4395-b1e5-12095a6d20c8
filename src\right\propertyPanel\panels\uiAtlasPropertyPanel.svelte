<script lang="ts">
    import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
    import Label from '../../../components/Label.svelte';
    import type { AtlasModel } from '../../../type/ui/atlasModel.svelte';
    import { onMount } from 'svelte';

    // Props - Svelte v5语法
    const { model }: { model: AtlasModel } = $props();

    // 切换到编辑模式 - 恢复子对象显示
    function switchToEditMode() {
        console.log('🎨 属性面板: 切换到编辑模式 - 恢复子对象显示');
        model.switchToEditMode();
        // 刷新状态以更新UI
        model.updateStats();
    }

    // 切换到合并模式 - 隐藏子对象，显示合并纹理
    async function switchToAtlasMode() {
        try {
            console.log('🎨 属性面板: 切换到合并模式 - 隐藏子对象');
            await model.switchToAtlasMode();
            // 刷新状态以更新UI
            model.updateStats();
            console.log('🎨 属性面板: 合并完成，子对象已隐藏');
        } catch (error) {
            console.error('切换到合并模式失败:', error);
        }
    }

    // 解开合并 - 完全清理，恢复子对象
    function unmerge() {
        console.log('🎨 属性面板: 解开合并 - 完全恢复子对象');
        model.unmerge();
        // 刷新状态以更新UI
        model.updateStats();
    }



    // 刷新状态
    function refreshStatus() {
        console.log('🎨 属性面板: 刷新状态');
        model.updateStats();
    }
</script>

<div class="atlas-property-panel">


    <!-- 模式控制 -->
    <AccordionPanel title="模式控制" expanded={true}>
        <div class="mode-section">
            <!-- 状态显示 -->
            <PropertyContainer>
                <Label text="当前模式:" />
                <Label text={model.isAtlasMode ? '🎨 合并模式' : '✏️ 编辑模式'}
                       variant="secondary" />
            </PropertyContainer>

            <PropertyContainer>
                <Label text="子对象数量:" />
                <Label text={model.childrenCount.toString()} variant="secondary" />
            </PropertyContainer>

            <PropertyContainer>
                <Label text="可以合并:" />
                <Label text={model.canMerge ? '✅ 是' : '❌ 否'}
                       variant={model.canMerge ? 'success' : 'error'} />
            </PropertyContainer>

            <PropertyContainer>
                <Label text="子对象状态:" />
                <Label text={model.isAtlasMode ? '🙈 已隐藏' : '👁️ 可见'}
                       variant={model.isAtlasMode ? 'warning' : 'success'} />
            </PropertyContainer>

            <!-- 模式切换按钮 -->
            <div class="mode-controls">
                {#if model.isAtlasMode}
                    <!-- 合并模式下的控制 -->
                    <button class="mode-btn edit-btn" onclick={switchToEditMode}>
                        <span class="btn-icon">✏️</span>
                        切换到编辑模式
                    </button>
                    <button class="mode-btn unmerge-btn" onclick={unmerge}>
                        <span class="btn-icon">�</span>
                        完全解开合并
                    </button>
                {:else}
                    <!-- 编辑模式下的控制 -->
                    <button
                        class="mode-btn atlas-btn"
                        onclick={switchToAtlasMode}
                        disabled={!model.canMerge}
                    >
                        <span class="btn-icon">🎨</span>
                        切换到合并模式
                    </button>
                    <button class="mode-btn refresh-btn" onclick={refreshStatus}>
                        <span class="btn-icon">🔄</span>
                        刷新状态
                    </button>
                {/if}
            </div>

            <!-- 使用说明 -->
            <div class="usage-hint">
                {#if model.isAtlasMode}
                    <div class="hint-text">
                        🎨 <strong>合并模式</strong>：子对象已隐藏并合并为单一纹理，性能最优。
                        <br>UIAtlas现在是标准Sprite，所有子对象暂时不可见。
                        <br>点击"切换到编辑模式"可以恢复子对象显示并重新编辑。
                    </div>
                {:else}
                    <div class="hint-text">
                        ✏️ <strong>编辑模式</strong>：子对象可见，可以直接编辑。
                        <br>添加UIImage和UILabel子对象，调整位置和属性。
                        <br>编辑完成后切换到合并模式，子对象将被隐藏并合并为单一纹理。
                    </div>
                {/if}
            </div>
        </div>
    </AccordionPanel>


</div>

<style>
    .atlas-property-panel {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 8px;
    }



    /* 新增样式 - 模式控制 */
    .mode-section {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .status-display {
        display: flex;
        flex-direction: column;
        gap: 6px;
        padding: 8px;
        background: var(--bg-secondary);
        border-radius: 6px;
        border: 1px solid var(--border-color);
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
    }

    .status-label {
        color: var(--text-secondary);
        font-weight: 500;
    }

    .status-value {
        font-weight: 600;
    }

    .status-value.mode-edit {
        color: #2196F3;
    }

    .status-value.mode-atlas {
        color: #4CAF50;
    }

    .status-value.can-merge {
        color: #4CAF50;
    }

    .status-value.cannot-merge {
        color: #ff9800;
    }

    .status-value.children-visible {
        color: #4CAF50;
    }

    .status-value.children-hidden {
        color: #9E9E9E;
    }

    .mode-controls {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    .mode-btn {
        flex: 1;
        min-width: 120px;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--bg-primary);
        color: var(--text-primary);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.2s;
    }

    .mode-btn:hover:not(:disabled) {
        background: var(--bg-hover);
        border-color: var(--border-hover);
        transform: translateY(-1px);
    }

    .mode-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .mode-btn.edit-btn {
        border-color: #2196F3;
        color: #2196F3;
    }

    .mode-btn.edit-btn:hover:not(:disabled) {
        background: #2196F3;
        color: white;
    }

    .mode-btn.atlas-btn {
        border-color: #4CAF50;
        color: #4CAF50;
    }

    .mode-btn.atlas-btn:hover:not(:disabled) {
        background: #4CAF50;
        color: white;
    }

    .mode-btn.unmerge-btn {
        border-color: #ff9800;
        color: #ff9800;
    }

    .mode-btn.unmerge-btn:hover:not(:disabled) {
        background: #ff9800;
        color: white;
    }

    .mode-btn.refresh-btn {
        border-color: #9C27B0;
        color: #9C27B0;
    }

    .mode-btn.refresh-btn:hover:not(:disabled) {
        background: #9C27B0;
        color: white;
    }

    .btn-icon {
        font-size: 14px;
    }

    .usage-hint {
        padding: 8px;
        background: var(--bg-tertiary);
        border-radius: 4px;
        border-left: 3px solid var(--border-color);
    }

    .hint-text {
        font-size: 12px;
        line-height: 1.4;
        color: var(--text-secondary);
    }


</style>
