/**
 * 对象创建逻辑 - 优化版本
 * 适配新的 BaseObjectModel 架构和 UIAtlas 智能容器设计
 *
 * UIAtlas 更新说明：
 * - UIAtlas 现在是智能容器，继承自 Container
 * - 创建时不再添加示例元素，而是创建空容器
 * - 用户需要通过右键菜单添加 UIImage 和 UILabel 子对象
 * - 支持编辑模式和合并模式的切换
 */

import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';
import { SliderModel } from '../../type/ui/sliderModel.svelte';
import { SwitchModel } from '../../type/ui/switchModel.svelte';
import { getCurrentState } from '../../stores/sceneModelStore';
import { CreatableObjectType } from './contextMenuLogic';

/**
 * 对象创建参数接口
 */
interface ObjectCreationParams {
  name?: string;
  x?: number;
  y?: number;
  visible?: boolean;
  text?: string;
}

/**
 * 创建游戏对象的核心函数
 * 简化版本 - 直接创建基本对象
 */
function createGameObject(type: string, params: ObjectCreationParams = {}, parentContainer: any = null, targetIndex: number | null = null): any {
  console.log(`[GameObject] Creating new object: ${type}`, params);

  let newObject: any = null;

  switch (type) {
    case "Sprite":
      newObject = new (window as any).Sprite();
      newObject.name = params.name || "NewSprite";
      newObject.x = params.x || 0;
      newObject.y = params.y || 0;
      newObject.visible = params.visible !== undefined ? params.visible : true;

      // 创建默认的正方形 bitmap
      const spriteBitmap = new (window as any).Bitmap(64, 64);
      spriteBitmap.fillRect(0, 0, 64, 64, "#4CAF50"); // 绿色正方形
      spriteBitmap.strokeRect(0, 0, 64, 64, "#2E7D32", 2); // 深绿色边框
      spriteBitmap._url="empty"
      newObject.bitmap = spriteBitmap;
      break;

    case "UIImage":
      // 使用新的UIImage组件
      if (typeof (window as any).UIImage !== 'undefined') {
        newObject = new (window as any).UIImage({
          width: 100,
          height: 100,
          imagePath: '',
          scaleMode: 'stretch'
        });

        newObject.name = params.name || "NewUIImage";
        newObject.x = params.x || 0;
        newObject.y = params.y || 0;
        newObject.visible = params.visible !== undefined ? params.visible : true;

        console.log('[UIImage Debug] 创建UIImage组件:', {
          name: newObject.name,
          size: { width: newObject.imageWidth, height: newObject.imageHeight },
          position: { x: newObject.x, y: newObject.y }
        });
      } else {
        console.error('[UIImage] UIImage类不可用，请确保 uiBase.js 插件已加载');
        throw new Error('UIImage 类不可用，无法创建图片对象');
      }
      break;

    case "UILabel":
      // 使用新的UILabel组件
      if (typeof (window as any).UILabel !== 'undefined') {
        newObject = new (window as any).UILabel({
          width: 200,
          height: 40,
          text: params.text || "New Label",
          fontSize: 16,
          textColor: '#ffffff',
          textAlign: 'left'
        });

        newObject.name = params.name || "NewUILabel";
        newObject.x = params.x || 0;
        newObject.y = params.y || 0;
        newObject.visible = params.visible !== undefined ? params.visible : true;

        console.log('[UILabel Debug] 创建UILabel组件:', {
          name: newObject.name,
          text: newObject.text,
          size: { width: newObject.labelWidth, height: newObject.labelHeight },
          position: { x: newObject.x, y: newObject.y }
        });
      } else {
        console.error('[UILabel] UILabel类不可用，请确保 uiBase.js 插件已加载');
        throw new Error('UILabel 类不可用，无法创建文本对象');
      }
      break;

    case "Label":
      newObject = new (window as any).Sprite();
      newObject.name = params.name || "NewLabel";
      newObject.x = params.x || 0;
      newObject.y = params.y || 0;
      newObject.visible = params.visible !== undefined ? params.visible : true;

      const bitmap = new (window as any).Bitmap(200, 40);
      bitmap.fontSize = 20;
      bitmap.textColor = "#ffffff";
      bitmap.outlineColor = "rgba(0, 0, 0, 0.5)";
      bitmap.outlineWidth = 4;
      bitmap.elements = [{
            type: 'text',  // 添加类型标识
            text: "New Text",                    // 原始文本（可能是绑定表达式）
            displayText: "New Text",      // 实际显示的文本
            bindingExpression: "", // 绑定表达式（如果有）
            x: 0,
            y: 0,
            maxWidth: 80,
            lineHeight: 36,
            align: "center"
        }];
      // bitmap.drawText(params.text || "New Text", 0, 0, 200, 40, "left");
      // bitmap.text = params.text || "New Text";
        bitmap.redrawing()
      newObject.bitmap = bitmap;
      break;

    case "Container":
      newObject = new (window as any).PIXI.Container();
      newObject.name = params.name || "NewContainer";
      newObject.x = params.x || 0;
      newObject.y = params.y || 0;
      newObject.visible = params.visible !== undefined ? params.visible : true;
      break;

    case "Window":
      const rect = new (window as any).Rectangle(0, 0, 200, 100);
      newObject = new (window as any).Window_Base(rect);
      newObject.name = params.name || "NewWindow";
      newObject.x = params.x || 0;
      newObject.y = params.y || 0;
      newObject.visible = params.visible !== undefined ? params.visible : true;
      break;

    case "Button":
      // 使用自定义的 SpriteButton，支持 elements 属性
      if (typeof (window as any).SpriteButton !== 'undefined') {
        newObject = new (window as any).SpriteButton();
        newObject.name = params.name || "NewButton";
        newObject.setText(params.name || "Button");

        // 设置按钮类型和属性
        newObject.setButtonType('normal');
        newObject.setEnabled(true);

        // 设置位置
        newObject.x = params.x || 50;
        newObject.y = params.y || 50;
        newObject.visible = params.visible !== undefined ? params.visible : true;

        // 设置默认点击事件
        newObject.setClickHandler(() => {
          console.log(`按钮 ${newObject.name} 被点击了！`);
        });

        console.log('[SpriteButton Debug] 创建自定义按钮:', {
          name: newObject.name,
          text: newObject.getText(),
          position: { x: newObject.x, y: newObject.y }
        });
      } else {
        // 如果 SpriteButton 不可用，直接报错
        console.error('[SpriteButton] 自定义 SpriteButton 不可用，请确保 spriteButton.js 插件已加载');
        throw new Error('SpriteButton 类不可用，无法创建按钮对象');
      }
      break;

    case "Slider":
      // 使用自定义的 UISlider 组件，如果不存在则回退到 Slider
      const SliderClass = (window as any).UISlider ;
      if (typeof SliderClass !== 'undefined') {
        newObject = new SliderClass({
          width: 200,
          height: 20,
          value: 50,
          minValue: 0,
          maxValue: 100,
          step: 1,
          trackColor: '#404040',
          fillColor: '#00ff00',
          thumbColor: '#ffffff',
          enabled: true,
          onChange: (value: number) => {
            console.log(`滑动条 ${newObject.name} 值改变:`, value);
          },
          onDragStart: (value: number) => {
            console.log(`滑动条 ${newObject.name} 开始拖拽:`, value);
          },
          onDragEnd: (value: number) => {
            console.log(`滑动条 ${newObject.name} 结束拖拽:`, value);
          }
        });

        newObject.name = params.name || "NewSlider";
        newObject.x = params.x || 50;
        newObject.y = params.y || 50;
        newObject.visible = params.visible !== undefined ? params.visible : true;

        console.log('[Slider Debug] 创建滑动条组件:', {
          name: newObject.name,
          value: newObject.getValue(),
          position: { x: newObject.x, y: newObject.y },
          size: { width: newObject.sliderWidth, height: newObject.sliderHeight }
        });
      } else {
        // 如果 UISlider/Slider 不可用，直接报错
        console.error('[Slider] 自定义 UISlider/Slider 不可用，请确保 uiSlider.js 插件已加载');
        throw new Error('UISlider/Slider 类不可用，无法创建滑动条对象');
      }
      break;

    case "Switch":
      // 使用自定义的 UISwitch 组件
      if (typeof (window as any).UISwitch !== 'undefined') {
        newObject = new (window as any).UISwitch({
          width: 60,
          height: 30,
          isOn: false,
          enabled: true,
          animationDuration: 200,
          onChange: (isOn: boolean) => {
            console.log(`开关 ${newObject.name} 状态改变:`, isOn ? '开启' : '关闭');
          },
          onToggle: (isOn: boolean) => {
            console.log(`开关 ${newObject.name} 切换:`, isOn ? '开启' : '关闭');
          }
        });

        newObject.name = params.name || "NewSwitch";
        newObject.x = params.x || 50;
        newObject.y = params.y || 50;
        newObject.visible = params.visible !== undefined ? params.visible : true;

        console.log('[Switch Debug] 创建开关组件:', {
          name: newObject.name,
          isOn: newObject.getValue(),
          position: { x: newObject.x, y: newObject.y },
          size: { width: newObject.switchWidth, height: newObject.switchHeight }
        });
      } else {
        // 如果 UISwitch 不可用，直接报错
        console.error('[Switch] UISwitch 不可用，请确保 switch.js 插件已加载');
        throw new Error('UISwitch 类不可用，无法创建开关对象');
      }
      break;

    case "UIButton":
      // 使用自定义的 UIButton 组件
      if (typeof (window as any).UIButton !== 'undefined') {
        newObject = new (window as any).UIButton({
          width: 120,
          height: 40,
          frames: {
            default: { image: '', x: 0, y: 0 }  // 不包含尺寸，由UIButton对象控制
          },
          enabled: true,
          onClick: 'console.log("Button clicked!");',
          onHover: '',
          onHoverOut: '',
          onPress: '',
          onRelease: '',
          onDoubleClick: ''
        });

        newObject.name = params.name || "NewUIButton";
        newObject.x = params.x || 50;
        newObject.y = params.y || 50;
        newObject.visible = params.visible !== undefined ? params.visible : true;

        console.log('[UIButton Debug] 创建按钮组件:', {
          name: newObject.name,
          position: { x: newObject.x, y: newObject.y },
          size: { width: newObject.width, height: newObject.height },
          enabled: newObject.enabled
        });
      } else {
        // 如果 UIButton 不可用，直接报错
        console.error('[UIButton] 自定义 UIButton 不可用，请确保 uiButton.js 插件已加载');
        throw new Error('UIButton 类不可用，无法创建按钮对象');
      }
      break;

    case "UIList":
      // 使用自定义的 UIList 组件
      if (typeof (window as any).UIList !== 'undefined') {
        newObject = new (window as any).UIList({
          width: 200,
          height: 300,
          selectionMode: 'single',
          itemSpacing: 5,
          scrollEnabled: true,
          enabled: true
        });

        newObject.name = params.name || "NewUIList";
        newObject.x = params.x || 50;
        newObject.y = params.y || 50;
        newObject.visible = params.visible !== undefined ? params.visible : true;

        console.log('[UIList Debug] 创建列表组件:', {
          name: newObject.name,
          position: { x: newObject.x, y: newObject.y },
          size: { width: newObject.width, height: newObject.height },
          selectionMode: newObject.selectionMode,
          enabled: newObject.enabled
        });
      } else {
        console.error('[UIList] UIList类不可用，请确保 uiList.js 插件已加载');
        throw new Error('UIList 类不可用，无法创建列表对象');
      }
      break;

    case "UIItem":
      // 使用自定义的 UIItem 组件
      if (typeof (window as any).UIItem !== 'undefined') {
        newObject = new (window as any).UIItem({
          width: 180,
          height: 40,
          enabled: true
        });

        newObject.name = params.name || "NewUIItem";
        newObject.x = params.x || 0;
        newObject.y = params.y || 0;
        newObject.visible = params.visible !== undefined ? params.visible : true;

        console.log('[UIItem Debug] 创建列表项组件:', {
          name: newObject.name,
          position: { x: newObject.x, y: newObject.y },
          size: { width: newObject.width, height: newObject.height },
          enabled: newObject.enabled
        });
      } else {
        console.error('[UIItem] UIItem类不可用，请确保 uiItem.js 插件已加载');
        throw new Error('UIItem 类不可用，无法创建列表项对象');
      }
      break;

    case "UILayout":
      // 使用自定义的 UILayout 组件
      if (typeof (window as any).UILayout !== 'undefined') {
        newObject = new (window as any).UILayout({
          layoutType: 'vertical',
          spacing: 5,
          padding: 0,
          columns: 2,
          rows: 0,
          mainAxisAlignment: 'start',
          crossAxisAlignment: 'start',
          containerWidth: 0,
          containerHeight: 0,
          autoUpdate: true
        });

        newObject.name = params.name || "NewUILayout";
        newObject.x = params.x || 0;
        newObject.y = params.y || 0;
        newObject.visible = params.visible !== undefined ? params.visible : true;

        console.log('[UILayout Debug] 创建布局组件:', {
          name: newObject.name,
          position: { x: newObject.x, y: newObject.y },
          layoutType: newObject.layoutType,
          spacing: newObject.spacing,
          autoUpdate: newObject.autoUpdate
        });
      } else {
        console.error('[UILayout] UILayout类不可用，请确保 uiLayout.js 插件已加载');
        throw new Error('UILayout 类不可用，无法创建布局对象');
      }
      break;

    case "UIAtlas":
      // 使用自定义的 UIAtlas 智能容器
      if (typeof (window as any).UIAtlas !== 'undefined') {
        newObject = new (window as any).UIAtlas({
          width: 512,
          height: 512,
          backgroundColor: 'transparent'
        });

        // newObject.name = params.name || "NewUIAtlas";
        // newObject.x = params.x || 50;
        // newObject.y = params.y || 50;
        // newObject.visible = params.visible !== undefined ? params.visible : true;

        console.log('[UIAtlas Debug] 创建智能图集容器:', {
          name: newObject.name,
          position: { x: newObject.x, y: newObject.y },
          size: { width: newObject.atlasWidth, height: newObject.atlasHeight },
          mode: 'edit',
          childrenCount: newObject.getChildrenCount()
        });

        // 注意：不再添加示例元素，UIAtlas现在是空容器
        // 用户需要通过右键菜单添加UIImage和UILabel子对象
        console.log('[UIAtlas Debug] 智能容器创建完成，请通过右键菜单添加UIImage或UILabel子对象');
      } else {
        // 如果 UIAtlas 不可用，直接报错
        console.error('[UIAtlas] 自定义 UIAtlas 不可用，请确保 UIAtlas.js 插件已加载');
        throw new Error('UIAtlas 类不可用，无法创建图集对象');
      }
      break;

    default:
      console.log(`[GameObject] Unknown object type: ${type}`);
      return null;
  }

  // 如果提供了父容器，则添加到父容器中
  if (newObject && parentContainer && parentContainer.addChild) {
    if (targetIndex !== null && targetIndex !== undefined) {
      // 在指定索引位置插入对象
      if (parentContainer.children && targetIndex <= parentContainer.children.length) {
        parentContainer.addChildAt(newObject, targetIndex);
        console.log('[Create Object] 成功在索引 ' + targetIndex + ' 位置创建并添加 ' + type + ' 对象');
      } else {
        // 如果索引超出范围，直接添加到末尾
        parentContainer.addChild(newObject);
        console.log('[Create Object] 索引超出范围，添加 ' + type + ' 对象到末尾');
      }
    } else {
      // 没有指定索引，直接添加到末尾
      parentContainer.addChild(newObject);
      console.log('[Create Object] 成功创建并添加 ' + type + ' 对象到容器');
    }
  } else if (parentContainer && !parentContainer.addChild) {
    console.log('[Create Object] 父容器不支持 addChild 方法');
  }

  return newObject;
}

/**
 * 处理对象创建请求
 * @param objectType 要创建的对象类型
 * @param targetNode 目标节点（新对象将作为其子对象）
 */
export function handleObjectCreation(objectType: string, targetNode: BaseObjectModel): void {
  console.log('开始创建对象:', {
    objectType,
    targetNode: targetNode.className
  });

  try {
    // 获取实际的父容器对象
    const parentContainer = (targetNode as any)._originalObject;

    if (!parentContainer || !parentContainer.addChild) {
      console.error('父容器不支持添加子对象');
      return;
    }

    console.log('使用的父容器:', {
      hasAddChild: !!(parentContainer && parentContainer.addChild),
      containerType: parentContainer?.constructor?.name
    });

    // 创建对象参数
    const params: ObjectCreationParams = {
      name: `${objectType}_${Date.now()}`,
      x: 0,
      y: 0,
      visible: true
    };

    // 如果是 Label 或 Button，添加默认文本
    if (objectType === CreatableObjectType.LABEL) {
      params.text = "New Text";
    } else if (objectType === CreatableObjectType.BUTTON) {
      params.text = "Button";
    }

    // 创建新对象（不立即添加到容器）
    const newObject = createGameObject(objectType, params, null);

    if (!newObject) {
      console.error('创建对象失败');
      return;
    }

    console.log('对象创建成功:', newObject);

    // 检查是否需要添加地图滚动监听
    // const isInWindowLayer = isInWindowLayerContainer(targetNode);

    // if (!isInWindowLayer) {
    //   // 不在 WindowLayer 容器中，添加地图滚动监听
    //   console.log('对象不在 WindowLayer 中，添加地图滚动监听');
    //   addMapScrollListener(newObject, params.x || 0, params.y || 0);
    // } else {
    //   console.log('对象在 WindowLayer 中，保持固定在屏幕位置');
    // }

    // 找到对应的父模型并添加新对象
    const state = getCurrentState();
    if (state.currentScene) {
      // 查找目标节点对应的模型对象
      const findModelForObject = (model: BaseObjectModel, originalObj: any): BaseObjectModel | null => {
        if ((model as any)._originalObject === originalObj) {
          return model;
        }
        for (const child of model.children) {
          const found = findModelForObject(child, originalObj);
          if (found) return found;
        }
        return null;
      };

      const parentModel = findModelForObject(state.currentScene, parentContainer);
      if (parentModel) {
        // 使用 addChild 方法添加新对象，这会同时更新原始容器和模型
        parentModel.addChild(newObject);
        console.log('新对象已通过模型添加，原始容器和UI都将更新');
      } else {
        console.warn('未找到对应的父模型，回退到直接添加到原始容器');
        // 回退方案：直接添加到原始容器
        if (parentContainer && parentContainer.addChild) {
          parentContainer.addChild(newObject);
        }
      }
    } else {
      console.warn('没有当前场景模型，回退到直接添加到原始容器');
      // 回退方案：直接添加到原始容器
      if (parentContainer && parentContainer.addChild) {
        parentContainer.addChild(newObject);
      }
    }

    console.log('对象创建完成');

  } catch (error) {
    console.error('创建对象时发生错误:', error);
  }
}