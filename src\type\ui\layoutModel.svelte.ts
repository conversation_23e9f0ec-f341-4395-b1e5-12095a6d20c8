import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * LayoutModel - UILayout的模型对象
 * 继承自BaseObjectModel，管理UILayout的所有属性和状态
 */
export class LayoutModel extends BaseObjectModel {
    // 组件类型
    public readonly componentType = 'UILayout';

    // 🔑 布局类型
    layoutType = $state('vertical'); // 'vertical', 'horizontal', 'grid'

    // 🔑 间距设置
    spacing = $state(5);              // 统一间距
    horizontalSpacing = $state(5);    // 水平间距
    verticalSpacing = $state(5);      // 垂直间距
    padding = $state(0);              // 内边距

    // 🔑 网格布局参数
    columns = $state(2);              // 网格列数
    rows = $state(0);                 // 网格行数（0表示自动）

    // 🔑 对齐方式
    mainAxisAlignment = $state('start');     // 主轴对齐：start, center, end, space-between, space-around
    crossAxisAlignment = $state('start');    // 交叉轴对齐：start, center, end, stretch

    // 🔑 容器尺寸
    containerWidth = $state(0);       // 容器宽度（0表示自动）
    containerHeight = $state(0);      // 容器高度（0表示自动）

    // 🔑 自动更新设置
    autoUpdate = $state(true);        // 是否自动更新布局

    // 🔑 高级设置
    wrapContent = $state(false);      // 是否自动换行（未来功能）
    reverseOrder = $state(false);     // 是否反向排列

    constructor(layout: any = {}) {
        super(layout);

        // 初始化布局属性
        this.layoutType = layout.layoutType || 'vertical';
        this.spacing = layout.spacing || 5;
        this.horizontalSpacing = layout.horizontalSpacing || layout.spacing || 5;
        this.verticalSpacing = layout.verticalSpacing || layout.spacing || 5;
        this.padding = layout.padding || 0;
        this.columns = layout.columns || 2;
        this.rows = layout.rows || 0;
        this.mainAxisAlignment = layout.mainAxisAlignment || 'start';
        this.crossAxisAlignment = layout.crossAxisAlignment || 'start';
        this.containerWidth = layout.containerWidth || 0;
        this.containerHeight = layout.containerHeight || 0;
        this.autoUpdate = layout.autoUpdate !== false;
        this.wrapContent = layout.wrapContent || false;
        this.reverseOrder = layout.reverseOrder || false;

        console.log('📐 LayoutModel: 创建布局模型', this);
    }

    /**
     * 设置Layout特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Layout特有的属性
     */
    protected setupSpecificSync(): void {
        try {
            console.log('📐 LayoutModel: setupSpecificSync 被调用', {
                layoutType: this.layoutType,
                spacing: this.spacing,
                padding: this.padding,
                hasOriginalObject: !!this._originalObject
            });

            // 同步所有Layout特有属性
            this.syncLayoutProperties();

        } catch (error) {
            console.error('❌ LayoutModel: 特有属性同步失败', error);
        }
    }

    /**
     * 同步Layout属性到原始对象
     */
    private syncLayoutProperties(): void {
        if (!this._originalObject) return;

        // 同步布局类型
        if (this.layoutType !== undefined) {
            this._originalObject.layoutType = this.layoutType;
            console.log('📐 LayoutModel: 同步布局类型', this.layoutType);
        }

        // 同步间距 - 关键修复
        if (this.spacing !== undefined) {
            this._originalObject.spacing = this.spacing;
            this._originalObject.horizontalSpacing = this.spacing;
            this._originalObject.verticalSpacing = this.spacing;
            console.log('📐 LayoutModel: 同步统一间距', {
                modelSpacing: this.spacing,
                objectSpacing: this._originalObject.spacing,
                objectHorizontalSpacing: this._originalObject.horizontalSpacing,
                objectVerticalSpacing: this._originalObject.verticalSpacing
            });
        }

        // 同步水平间距
        if (this.horizontalSpacing !== undefined) {
            this._originalObject.horizontalSpacing = this.horizontalSpacing;
            console.log('📐 LayoutModel: 同步水平间距', this.horizontalSpacing);
        }

        // 同步垂直间距
        if (this.verticalSpacing !== undefined) {
            this._originalObject.verticalSpacing = this.verticalSpacing;
            console.log('📐 LayoutModel: 同步垂直间距', this.verticalSpacing);
        }

        // 同步内边距
        if (this.padding !== undefined) {
            this._originalObject.padding = this.padding;
            console.log('📐 LayoutModel: 同步内边距', this.padding);
        }

        // 同步网格列数
        if (this.columns !== undefined) {
            this._originalObject.columns = this.columns;
            console.log('📐 LayoutModel: 同步网格列数', this.columns);
        }

        // 同步网格行数
        if (this.rows !== undefined) {
            this._originalObject.rows = this.rows;
            console.log('📐 LayoutModel: 同步网格行数', this.rows);
        }

        // 同步主轴对齐
        if (this.mainAxisAlignment !== undefined) {
            this._originalObject.mainAxisAlignment = this.mainAxisAlignment;
            console.log('📐 LayoutModel: 同步主轴对齐', this.mainAxisAlignment);
        }

        // 同步交叉轴对齐
        if (this.crossAxisAlignment !== undefined) {
            this._originalObject.crossAxisAlignment = this.crossAxisAlignment;
            console.log('📐 LayoutModel: 同步交叉轴对齐', this.crossAxisAlignment);
        }

        // 同步容器宽度
        if (this.containerWidth !== undefined) {
            this._originalObject.containerWidth = this.containerWidth;
            console.log('📐 LayoutModel: 同步容器宽度', this.containerWidth);
        }

        // 同步容器高度
        if (this.containerHeight !== undefined) {
            this._originalObject.containerHeight = this.containerHeight;
            console.log('📐 LayoutModel: 同步容器高度', this.containerHeight);
        }

        // 同步自动更新设置
        if (this.autoUpdate !== undefined) {
            this._originalObject.autoUpdate = this.autoUpdate;
            console.log('📐 LayoutModel: 同步自动更新设置', this.autoUpdate);
        }

        // 触发布局更新
        this.triggerLayoutUpdate();
    }

    /**
     * 触发布局更新
     */
    private triggerLayoutUpdate(): void {
        if (this._originalObject && typeof this._originalObject.updateLayout === 'function') {
            console.log('📐 LayoutModel: 触发布局更新', {
                layoutType: this._originalObject.layoutType,
                spacing: this._originalObject.spacing,
                padding: this._originalObject.padding
            });
            this._originalObject.updateLayout();
        } else {
            console.warn('📐 LayoutModel: 无法触发布局更新', {
                hasOriginalObject: !!this._originalObject,
                hasUpdateMethod: !!(this._originalObject && typeof this._originalObject.updateLayout === 'function')
            });
        }
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const lines: string[] = [];

        // 生成构造函数调用
        lines.push(`${indent}const ${varName} = new UILayout({`);
        lines.push(`${indent}    layoutType: '${this.layoutType}',`);
        lines.push(`${indent}    spacing: ${this.spacing},`);
        lines.push(`${indent}    horizontalSpacing: ${this.horizontalSpacing},`);
        lines.push(`${indent}    verticalSpacing: ${this.verticalSpacing},`);
        lines.push(`${indent}    padding: ${this.padding},`);
        lines.push(`${indent}    columns: ${this.columns},`);
        lines.push(`${indent}    rows: ${this.rows},`);
        lines.push(`${indent}    mainAxisAlignment: '${this.mainAxisAlignment}',`);
        lines.push(`${indent}    crossAxisAlignment: '${this.crossAxisAlignment}',`);
        lines.push(`${indent}    containerWidth: ${this.containerWidth},`);
        lines.push(`${indent}    containerHeight: ${this.containerHeight},`);
        lines.push(`${indent}    autoUpdate: ${this.autoUpdate}`);
        lines.push(`${indent}});`);

        return lines.join('\n');
    }

    /**
     * 克隆当前Layout对象 - 调用插件的 clone 方法
     */
    clone(): LayoutModel {
        console.log('🔄 LayoutModel: 开始克隆Layout对象（调用插件方法）');

        // 1. 调用原始 UILayout 对象的 clone 方法
        const originalUILayout = this.getOriginalObject();
        if (!originalUILayout || typeof originalUILayout.clone !== 'function') {
            console.error('❌ LayoutModel: 原始对象没有 clone 方法');
            throw new Error('UILayout 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUILayout = originalUILayout.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 LayoutModel 包装克隆的对象
        const clonedModel = new LayoutModel(clonedUILayout);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ LayoutModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UILayout 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UILayout 特有属性
            layoutType: this.layoutType,
            spacing: this.spacing,
            horizontalSpacing: this.horizontalSpacing,
            verticalSpacing: this.verticalSpacing,
            padding: this.padding,
            columns: this.columns,
            rows: this.rows,
            mainAxisAlignment: this.mainAxisAlignment,
            crossAxisAlignment: this.crossAxisAlignment,
            containerWidth: this.containerWidth,
            containerHeight: this.containerHeight,
            autoUpdate: this.autoUpdate,
            wrapContent: this.wrapContent,
            reverseOrder: this.reverseOrder
        };
    }

    /**
     * 获取布局信息摘要
     */
    getLayoutInfo(): any {
        return {
            layoutType: this.layoutType,
            spacing: this.spacing,
            horizontalSpacing: this.horizontalSpacing,
            verticalSpacing: this.verticalSpacing,
            padding: this.padding,
            columns: this.columns,
            rows: this.rows,
            mainAxisAlignment: this.mainAxisAlignment,
            crossAxisAlignment: this.crossAxisAlignment,
            containerWidth: this.containerWidth,
            containerHeight: this.containerHeight,
            autoUpdate: this.autoUpdate,
            wrapContent: this.wrapContent,
            reverseOrder: this.reverseOrder
        };
    }
}

// 注册LayoutModel到基类容器
BaseObjectModel.registerModel('UILayout', LayoutModel);
BaseObjectModel.registerModel('Layout', LayoutModel);
