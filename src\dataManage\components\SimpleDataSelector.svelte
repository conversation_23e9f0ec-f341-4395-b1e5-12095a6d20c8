<script lang="ts">
  import { dataStore } from '../stores/dataStore';
  import type { StaticDataType, DataSelection } from '../types/dataTypes';

  // Props
  interface Props {
    value?: DataSelection | null;
    onSelect?: (selection: DataSelection | null) => void;
    placeholder?: string;
    disabled?: boolean;
    compact?: boolean;
  }

  let {
    value = null,
    onSelect,
    placeholder = "选择数据字段",
    disabled = false,
    compact = false
  }: Props = $props();

  // 本地状态
  let isOpen = $state(false);
  let selectedDataType = $state<StaticDataType | null>(null);

  // 获取可用的数据类型
  const availableDataTypes = dataStore.getAvailableDataTypes();

  // 当前显示的文本
  const displayText = $derived.by(() => {
    if (value) {
      // 现在displayText已经不包含数据类型前缀了，直接使用
      return value.displayText || value.fieldInfo.displayName || value.fieldPath;
    }
    return placeholder;
  });

  // 处理数据类型选择
  async function handleDataTypeSelect(dataType: StaticDataType) {
    selectedDataType = dataType;
    await dataStore.selectDataType(dataType);
  }

  // 处理字段选择
  function handleFieldSelect(field: any, fieldPath: string) {
    dataStore.selectField(field, fieldPath);
    const selection = dataStore.createCurrentSelection();
    if (onSelect) {
      onSelect(selection);
    }
    isOpen = false;
    selectedDataType = null;
  }

  // 清除选择
  function clearSelection() {
    if (onSelect) {
      onSelect(null);
    }
    isOpen = false;
    selectedDataType = null;
  }

  // 切换下拉框
  function toggleDropdown() {
    if (disabled) return;
    isOpen = !isOpen;
  }

  // 关闭下拉框
  function closeDropdown() {
    isOpen = false;
    // 不要重置selectedDataType，保持用户的选择
  }

  // 点击外部关闭
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as Element;
    if (!target.closest('.simple-data-selector')) {
      closeDropdown();
    }
  }

  // 构建字段路径
  function buildFieldPath(parentPath: string, fieldName: string): string {
    return parentPath ? `${parentPath}.${fieldName}` : fieldName;
  }
</script>

{#snippet renderField(field: any, fieldPath: string, depth: number)}
  <button
    class="field-item"
    class:child-field={depth > 0}
    style="padding-left: {depth * 20 + 12}px"
    onclick={() => handleFieldSelect(field, fieldPath)}
  >
    <div class="field-main">
      <span class="field-name">{field.displayName}</span>
      <span class="field-type">{field.type}</span>
    </div>
    {#if field.description}
      <div class="field-description">{field.description}</div>
    {/if}
  </button>

  <!-- 递归渲染子字段 -->
  {#if field.children && field.children.length > 0}
    {#each field.children as childField}
      {@const childFieldPath = buildFieldPath(fieldPath, childField.name)}
      {@render renderField(childField, childFieldPath, depth + 1)}
    {/each}
  {/if}
{/snippet}

<svelte:window on:click={handleClickOutside} />

<div class="simple-data-selector" class:compact class:disabled>
  <!-- 选择器按钮 -->
  <div class="selector-display">
    <button
      class="selector-button"
      class:open={isOpen}
      class:has-value={!!value}
      onclick={toggleDropdown}
      {disabled}
    >
      <span class="selector-text">{displayText}</span>
      <div class="selector-actions">
        {#if value}
          <span
            class="clear-button"
            onclick={(e) => { e.stopPropagation(); clearSelection(); }}
            title="清除选择"
            role="button"
            tabindex="0"
          >
            ✕
          </span>
        {/if}
        <span class="selector-arrow" class:rotated={isOpen}>▼</span>
      </div>
    </button>
  </div>

  <!-- 下拉面板 -->
  {#if isOpen}
    <div class="dropdown-panel">
      {#if !selectedDataType}
        <!-- 数据类型选择 -->
        <div class="panel-section">
          <div class="section-title">选择数据类型</div>
          <div class="data-types-list">
            {#each availableDataTypes as { type, displayName }}
              <button
                class="list-item"
                onclick={() => handleDataTypeSelect(type)}
              >
                {displayName}
              </button>
            {/each}
          </div>
        </div>
      {:else}
        <!-- 字段选择 -->
        <div class="panel-section">
          <div class="section-header">
            <button class="back-button" onclick={() => selectedDataType = null}>
              ← 返回
            </button>
            <span class="section-title">
              {availableDataTypes.find(t => t.type === selectedDataType)?.displayName} - 选择字段
            </span>
          </div>

          {#if dataStore.isLoading}
            <div class="loading">加载中...</div>
          {:else if dataStore.error}
            <div class="error">{dataStore.error}</div>
          {:else if dataStore.availableFields.length === 0}
            <div class="no-results">没有可用字段</div>
          {:else}
            <div class="fields-list">
              {#each dataStore.availableFields as field}
                {@render renderField(field, field.name, 0)}
              {/each}
            </div>
          {/if}
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .simple-data-selector {
    position: relative;
    width: 100%;
  }

  .simple-data-selector.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  .simple-data-selector.compact .selector-button {
    padding: 4px 8px;
    font-size: 12px;
  }

  .selector-button {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s;
  }

  .selector-button:hover {
    border-color: #007acc;
  }

  .selector-button.open {
    border-color: #007acc;
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
  }

  .selector-button.has-value {
    border-color: #28a745;
  }

  .selector-text {
    flex: 1;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .selector-actions {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .clear-button {
    background: none;
    border: none;
    cursor: pointer;
    color: #999;
    padding: 2px;
    border-radius: 2px;
    transition: all 0.2s;
  }

  .clear-button:hover {
    background: #f0f0f0;
    color: #666;
  }

  .selector-arrow {
    transition: transform 0.2s;
    color: #666;
  }

  .selector-arrow.rotated {
    transform: rotate(180deg);
  }

  .dropdown-panel {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
  }

  .panel-section {
    padding: 0;
  }

  .section-title {
    padding: 8px 12px;
    font-weight: bold;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    font-size: 12px;
    color: #666;
    margin: 0;
  }

  .section-header {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    padding: 4px;
  }

  .back-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    color: #007acc;
    font-size: 12px;
    border-radius: 3px;
  }

  .back-button:hover {
    background: #e3f2fd;
  }

  .data-types-list, .fields-list {
    max-height: 250px;
    overflow-y: auto;
  }

  .list-item, .field-item {
    width: 100%;
    padding: 8px 12px;
    border: none;
    background: white;
    text-align: left;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.2s;
  }

  .list-item:hover, .field-item:hover {
    background: #f8f9fa;
  }

  .field-item.child-field {
    padding-left: 24px;
    background: #fafafa;
  }

  .field-main {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .field-name {
    font-weight: 500;
    color: #333;
    flex: 1;
  }

  .field-type {
    font-size: 10px;
    color: #666;
    padding: 2px 4px;
    background: #e9ecef;
    border-radius: 2px;
  }

  .field-description {
    font-size: 11px;
    color: #999;
    margin-top: 2px;
    line-height: 1.3;
  }

  .loading, .error, .no-results {
    padding: 16px;
    text-align: center;
    color: #666;
    font-size: 14px;
  }

  .error {
    color: #dc3545;
  }
</style>
