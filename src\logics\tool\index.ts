/**
 * 对象选中拖动工具 - 入口文件
 *
 * 使用示例:
 * ```typescript
 * import { initializeSelectionTool } from './logics/tool';
 *
 * // 简单初始化
 * initializeSelectionTool(canvasElement);
 *
 * // 或者使用自定义配置
 * initializeSelectionTool(canvasElement, {
 *   arrowSize: 10,
 *   snapToGrid: true,
 *   gridSize: 16
 * });
 * ```
 */

// 导出所有类型
export type {
  ArrowType,
  ArrowConfig,
  BoundingBoxConfig,
  DragState,
  SelectedObjectInfo,
  ToolConfig,
  ToolEventCallbacks,
  RenderContext,
  MouseEventInfo,
  HitTestResult
} from './types';

// 导出核心类
export { ObjectSelectionTool } from './ObjectSelectionTool';
export { ToolIntegration } from './ToolIntegration';
export { SelectionManager } from './SelectionManager';
export { DragController } from './DragController';
export { DrawingUtils } from './DrawingUtils';

// 导出便捷函数
import { ToolIntegration } from './ToolIntegration';
import type { ToolConfig } from './types';

/**
 * 创建选中工具实例的便捷函数
 */
export function createSelectionTool(config?: Partial<ToolConfig>): ToolIntegration {
  return new ToolIntegration(config);
}

// 导出工具管理器
export {
  initializeSelectionTool,
  destroySelectionTool,
  setSelectionToolEnabled,
  updateSelectionToolConfig,
  getSelectionToolState,
  selectObjectInTool,
  clearSelectionInTool,
  checkSelectionToolStatus,
  getSelectionToolInstance,
  TOOL_PRESETS
} from './utils.svelte';

/**
 * 默认配置
 */
export const DEFAULT_TOOL_CONFIG: ToolConfig = {
  arrowSize: 8,
  arrowOffset: 25,
  arrowColors: {
    normal: '#2196F3',
    hover: '#1976D2',
    active: '#0D47A1'
  },
  boundingBox: {
    strokeColor: '#2196F3',
    strokeWidth: 2,
    fillColor: 'rgba(33, 150, 243, 0.1)',
    dashPattern: [5, 5]
  },
  dragSensitivity: 1,
  snapToGrid: false,
  gridSize: 10
};

/**
 * Unity风格配置
 */
export const UNITY_STYLE_CONFIG: ToolConfig = {
  arrowSize: 12,
  arrowOffset: 30,
  arrowColors: {
    normal: '#FF6B35',
    hover: '#FF8C42',
    active: '#FF4500'
  },
  boundingBox: {
    strokeColor: '#FF6B35',
    strokeWidth: 2,
    fillColor: 'rgba(255, 107, 53, 0.1)',
    dashPattern: []
  },
  dragSensitivity: 1,
  snapToGrid: true,
  gridSize: 16
};

/**
 * 简洁风格配置
 */
export const MINIMAL_STYLE_CONFIG: ToolConfig = {
  arrowSize: 6,
  arrowOffset: 20,
  arrowColors: {
    normal: '#666666',
    hover: '#333333',
    active: '#000000'
  },
  boundingBox: {
    strokeColor: '#666666',
    strokeWidth: 1,
    dashPattern: [3, 3]
  },
  dragSensitivity: 1,
  snapToGrid: false,
  gridSize: 10
};

/**
 * 全局工具实例（单例模式）
 */
let globalToolInstance: ToolIntegration | null = null;

/**
 * 获取全局工具实例
 */
export function getGlobalSelectionTool(): ToolIntegration | null {
  return globalToolInstance;
}

/**
 * 设置全局工具实例
 */
export function setGlobalSelectionTool(tool: ToolIntegration): void {
  if (globalToolInstance) {
    globalToolInstance.destroy();
  }
  globalToolInstance = tool;
}

/**
 * 初始化全局工具
 */
export function initializeGlobalTool(
  canvas: HTMLCanvasElement,
  config?: Partial<ToolConfig>
): ToolIntegration {
  const tool = createSelectionTool(config);
  tool.initialize(canvas);
  setGlobalSelectionTool(tool);
  return tool;
}

/**
 * 销毁全局工具
 */
export function destroyGlobalTool(): void {
  if (globalToolInstance) {
    globalToolInstance.destroy();
    globalToolInstance = null;
  }
}

// 工具状态枚举
export enum ToolState {
  IDLE = 'idle',
  SELECTING = 'selecting',
  DRAGGING = 'dragging',
  HOVERING = 'hovering'
}

/**
 * 工具使用指南
 */
export const USAGE_GUIDE = {
  setup: `
    1. 导入工具: import { createSelectionTool } from './logics/tool';
    2. 创建实例: const tool = createSelectionTool(config);
    3. 初始化: tool.initialize(canvasElement);
  `,

  features: `
    - Unity风格的选中对象UI
    - 方向箭头拖动（上下左右）
    - 包围盒显示
    - 网格对齐（可选）
    - 键盘快捷键支持
    - 事件回调系统
  `,

  controls: `
    - 点击箭头: 开始拖动
    - 拖动: 移动对象
    - ESC键: 取消拖动
    - Delete键: 删除对象（需要实现回调）
  `,

  customization: `
    - 箭头大小和颜色
    - 包围盒样式
    - 网格对齐设置
    - 拖动灵敏度
    - 事件回调
  `
};
