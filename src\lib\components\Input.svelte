<script lang="ts">
  /**
   * Input 输入框组件
   * 基于 Skeleton UI 和全局主题色彩
   */
  
  // Props
  export let value: string = '';
  export let type: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url' | 'search' = 'text';
  export let placeholder: string = '';
  export let disabled: boolean = false;
  export let readonly: boolean = false;
  export let required: boolean = false;
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let variant: 'default' | 'success' | 'warning' | 'error' = 'default';
  export let fullWidth: boolean = false;
  export let leftIcon: string = '';
  export let rightIcon: string = '';
  export let maxlength: number | undefined = undefined;
  export let minlength: number | undefined = undefined;
  export let pattern: string | undefined = undefined;
  export let autocomplete: string | undefined = undefined;
  export let id: string = '';
  export let name: string = '';
  export let ariaLabel: string = '';
  export let ariaDescribedBy: string = '';
  
  // Events
  export let onInput: (value: string, event: Event) => void = () => {};
  export let onChange: (value: string, event: Event) => void = () => {};
  export let onFocus: (event: FocusEvent) => void = () => {};
  export let onBlur: (event: FocusEvent) => void = () => {};
  export let onKeydown: (event: KeyboardEvent) => void = () => {};
  export let onLeftIconClick: () => void = () => {};
  export let onRightIconClick: () => void = () => {};
  
  // 内部状态
  let inputElement: HTMLInputElement;
  let isFocused = $state(false);
  
  // 处理输入事件
  function handleInput(event: Event) {
    const target = event.target as HTMLInputElement;
    value = target.value;
    onInput(value, event);
  }
  
  function handleChange(event: Event) {
    const target = event.target as HTMLInputElement;
    value = target.value;
    onChange(value, event);
  }
  
  function handleFocus(event: FocusEvent) {
    isFocused = true;
    onFocus(event);
  }
  
  function handleBlur(event: FocusEvent) {
    isFocused = false;
    onBlur(event);
  }
  
  // 获取容器样式类
  function getContainerClass() {
    const baseClass = 'input-container';
    const sizeClass = `input-${size}`;
    const variantClass = `input-${variant}`;
    const focusClass = isFocused ? 'input-focused' : '';
    const disabledClass = disabled ? 'input-disabled' : '';
    const fullWidthClass = fullWidth ? 'input-full-width' : '';
    
    return [baseClass, sizeClass, variantClass, focusClass, disabledClass, fullWidthClass]
      .filter(Boolean)
      .join(' ');
  }
  
  // 公开方法
  export function focus() {
    inputElement?.focus();
  }
  
  export function blur() {
    inputElement?.blur();
  }
  
  export function select() {
    inputElement?.select();
  }
</script>

<div class={getContainerClass()}>
  {#if leftIcon}
    <button
      type="button"
      class="input-icon input-icon-left"
      onclick={onLeftIconClick}
      tabindex="-1"
    >
      {leftIcon}
    </button>
  {/if}
  
  <input
    bind:this={inputElement}
    bind:value
    {type}
    {placeholder}
    {disabled}
    {readonly}
    {required}
    {maxlength}
    {minlength}
    {pattern}
    {autocomplete}
    {id}
    {name}
    aria-label={ariaLabel}
    aria-describedby={ariaDescribedBy}
    class="input-field"
    oninput={handleInput}
    onchange={handleChange}
    onfocus={handleFocus}
    onblur={handleBlur}
    onkeydown={onKeydown}
  />
  
  {#if rightIcon}
    <button
      type="button"
      class="input-icon input-icon-right"
      onclick={onRightIconClick}
      tabindex="-1"
    >
      {rightIcon}
    </button>
  {/if}
</div>

<style>
  .input-container {
    position: relative;
    display: inline-flex;
    align-items: center;
    background-color: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    transition: var(--transition-base);
  }
  
  .input-container:hover:not(.input-disabled) {
    border-color: var(--theme-border-dark);
  }
  
  .input-container.input-focused {
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px rgba(74, 85, 104, 0.2);
  }
  
  .input-container.input-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: var(--theme-surface-dark);
  }
  
  .input-container.input-full-width {
    width: 100%;
  }
  
  .input-field {
    flex: 1;
    border: none;
    background: transparent;
    color: var(--theme-text);
    font-family: var(--font-family-base);
    outline: none;
    width: 100%;
  }
  
  .input-field::placeholder {
    color: var(--theme-text-secondary);
    opacity: 0.7;
  }
  
  .input-field:disabled {
    cursor: not-allowed;
  }
  
  .input-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--theme-text-secondary);
    cursor: pointer;
    transition: var(--transition-base);
  }
  
  .input-icon:hover {
    color: var(--theme-text);
  }
  
  .input-icon-left {
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-2);
  }
  
  .input-icon-right {
    padding-left: var(--spacing-2);
    padding-right: var(--spacing-3);
  }
  
  /* 尺寸变体 */
  .input-sm .input-field {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
  }
  
  .input-md .input-field {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-base);
  }
  
  .input-lg .input-field {
    padding: var(--spacing-4) var(--spacing-5);
    font-size: var(--font-size-lg);
  }
  
  /* 状态变体 */
  .input-success {
    border-color: var(--theme-success);
  }
  
  .input-success.input-focused {
    border-color: var(--theme-success);
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
  }
  
  .input-warning {
    border-color: var(--theme-warning);
  }
  
  .input-warning.input-focused {
    border-color: var(--theme-warning);
    box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.2);
  }
  
  .input-error {
    border-color: var(--theme-error);
  }
  
  .input-error.input-focused {
    border-color: var(--theme-error);
    box-shadow: 0 0 0 2px rgba(229, 62, 62, 0.2);
  }
</style>
