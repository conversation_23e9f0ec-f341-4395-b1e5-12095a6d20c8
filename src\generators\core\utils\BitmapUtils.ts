/**
 * Bitmap 处理工具类
 * 统一处理所有 Bitmap 相关的逻辑
 */

import { type BitmapProperties } from '../../types/sprite';
import { PathUtils } from './PathUtils';

/**
 * Bitmap 处理工具
 */
export class BitmapUtils {

  // 默认 Bitmap 属性
  private static readonly DEFAULT_PROPERTIES: Readonly<BitmapProperties> = Object.freeze({
    fontBold: false,
    fontFace: 'GameFont',
    fontItalic: false,
    fontSize: 28,
    outlineColor: 'rgba(0, 0, 0, 0.5)',
    outlineWidth: 4,
    textColor: '#ffffff',
    _paintOpacity: 255,
    _smooth: true
  });

  /**
   * 序列化 Bitmap 对象
   * @param bitmap Bitmap 对象
   * @returns 序列化后的属性
   */
  static serialize(bitmap: any): BitmapProperties {
    if (!bitmap) {
      return { ...this.DEFAULT_PROPERTIES };
    }

    const result: BitmapProperties = {
      fontBold: bitmap.fontBold !== undefined ? bitmap.fontBold : this.DEFAULT_PROPERTIES.fontBold,
      fontFace: bitmap.fontFace || this.DEFAULT_PROPERTIES.fontFace,
      fontItalic: bitmap.fontItalic !== undefined ? bitmap.fontItalic : this.DEFAULT_PROPERTIES.fontItalic,
      fontSize: bitmap.fontSize !== undefined ? bitmap.fontSize : this.DEFAULT_PROPERTIES.fontSize,
      outlineColor: bitmap.outlineColor || this.DEFAULT_PROPERTIES.outlineColor,
      outlineWidth: bitmap.outlineWidth !== undefined ? bitmap.outlineWidth : this.DEFAULT_PROPERTIES.outlineWidth,
      textColor: bitmap.textColor || this.DEFAULT_PROPERTIES.textColor,
      _paintOpacity: bitmap._paintOpacity !== undefined ? bitmap._paintOpacity : this.DEFAULT_PROPERTIES._paintOpacity,
      _smooth: bitmap._smooth !== undefined ? bitmap._smooth : this.DEFAULT_PROPERTIES._smooth,
    };

    // 处理 URL
    const url = this.extractUrl(bitmap);
    if (url) {
      result.url = PathUtils.convertToRelativePath(url);
    }

    // 处理 elements
    if (bitmap.elements && Array.isArray(bitmap.elements)) {
      result.elements = this.serializeElements(bitmap.elements);
    }

    // 处理 regions
    if (bitmap.regions && Array.isArray(bitmap.regions)) {
      result.regions = bitmap.regions.map((region: any) => ({
        id: region.id || '',
        label: region.label || '',
        sx: region.sx || 0,
        sy: region.sy || 0,
        sw: region.sw || 0,
        sh: region.sh || 0
      }));
    }

    return result;
  }

  /**
   * 反序列化为 Bitmap 对象
   * @param data Bitmap 属性
   * @returns Bitmap 对象
   */
  static deserialize(data: BitmapProperties): any {
    // 在 RPG Maker MZ 环境中创建真正的 Bitmap
    if (typeof window !== 'undefined' && (window as any).Bitmap) {
      if (data.url) {
        return (window as any).ImageManager.loadBitmapFromUrl(data.url);
      } else {
        const bitmap = new (window as any).Bitmap(816, 624); // 默认尺寸
        Object.assign(bitmap, data);
        return bitmap;
      }
    }

    // 测试环境下返回模拟对象
    return this.createMockBitmap(data);
  }

  /**
   * 提取 Bitmap 的 URL
   * @param bitmap Bitmap 对象
   * @returns URL 字符串
   */
  private static extractUrl(bitmap: any): string | undefined {
    // 优先使用原始路径（CustomResourcePath 插件保存的）
    if (bitmap._originalPath) {
      return bitmap._originalPath;
    }

    // 如果是 Bitmap 对象，尝试获取 URL
    if (bitmap._url) {
      // 如果是 blob URL，尝试从其他属性获取原始路径
      if (bitmap._url.startsWith('blob:')) {
        return bitmap._filePath || undefined;
      } else if (bitmap._url.trim() !== '') {
        return bitmap._url;
      }
    }

    // 如果是字符串路径
    if (typeof bitmap === 'string') {
      return bitmap;
    }

    return undefined;
  }

  /**
   * 序列化 elements 数组
   * @param elements elements 数组
   * @returns 序列化后的 elements
   */
  private static serializeElements(elements: any[]): any[] {
    return elements.map(element => {
      if (!element) return null;

      const serialized: any = {
        type: element.type,
        id: element.id
      };

      // 文本元素属性
      if (element.type === 'text') {
        Object.assign(serialized, {
          text: element.text,
          x: element.x,
          y: element.y,
          maxWidth: element.maxWidth,
          lineHeight: element.lineHeight,
          align: element.align
        });
      }

      // 图像元素属性
      if (element.type === 'image') {
        // 提取 sourceUrl
        const sourceUrl = this.extractSourceUrl(element);
        if (sourceUrl) {
          serialized.sourceUrl = PathUtils.convertToRelativePath(sourceUrl);
        }

        // 图像位置和尺寸
        Object.assign(serialized, {
          sx: element.sx,
          sy: element.sy,
          sw: element.sw,
          sh: element.sh,
          dx: element.dx,
          dy: element.dy,
          dw: element.dw,
          dh: element.dh
        });
      }

      return serialized;
    });
  }

  /**
   * 提取图像元素的 sourceUrl
   * @param element 图像元素
   * @returns sourceUrl 字符串
   */
  private static extractSourceUrl(element: any): string | undefined {
    // 如果直接有 sourceUrl
    if (element.sourceUrl) {
      return element.sourceUrl;
    }

    // 如果有 source 对象，从中提取 URL
    if (element.source) {
      if (element.source._originalPath) {
        return element.source._originalPath;
      } else if (element.source._url) {
        return element.source._url;
      } else if (element.source.url) {
        return element.source.url;
      }
    }

    return undefined;
  }

  /**
   * 创建模拟 Bitmap 对象（用于测试环境）
   * @param data Bitmap 属性
   * @returns 模拟 Bitmap 对象
   */
  private static createMockBitmap(data: BitmapProperties): any {
    return {
      ...data,
      width: 816,
      height: 624,
      _canvas: null,
      _context: null,
      _baseTexture: null
    };
  }
}
