/**
 * Operation Manager - Simplified Version
 * Generate plugin code directly based on SceneModel
 */

import {
  getCurrentState
} from '../stores/sceneModelStore';
import { generatePluginCode } from './pluginGenerator';
import type { SceneModel } from '../type/senceModel.svelte';

/**
 * Operation Manager Class
 */
export class OperationManager {
  // Store scene models for code generation
  private sceneModels: Map<string, SceneModel> = new Map();

  // Store generated plugin code for each scene
  private scenePluginCodes: Map<string, string> = new Map();

  /**
   * Clear all records
   */
  public clearAllRecords(): void {
    this.sceneModels.clear();
    this.scenePluginCodes.clear();
    console.log('All scene records cleared');
  }

  /**
   * Generate and save scene code directly
   * @param sceneName Scene name
   * @returns Generated code
   */
  public generateAndSaveSceneCode(sceneName: string): string | null {
    console.log(`=== Starting to generate and save scene code: ${sceneName} ===`);

    // 1. Get current scene model
    const currentState = getCurrentState();
    if (!currentState.currentScene) {
      console.error('No current scene model found');
      return null;
    }

    const sceneModel = currentState.currentScene;

    // 2. Save scene model to Map
    this.sceneModels.set(sceneName, sceneModel);

    // 3. Generate plugin code for all scenes
    const pluginCode = generatePluginCode(this.sceneModels);

    console.log(`Scene ${sceneName} code generation completed`);
    return pluginCode;
  }

  /**
   * Add scene model to collection
   * @param sceneName Scene name
   * @param sceneModel Scene model
   */
  public addSceneModel(sceneName: string, sceneModel: SceneModel): void {
    this.sceneModels.set(sceneName, sceneModel);
    console.log(`Added scene model: ${sceneName}`);
  }

  /**
   * Get all scene models
   * @returns Scene models Map
   */
  public getAllSceneModels(): Map<string, SceneModel> {
    return new Map(this.sceneModels);
  }

  /**
   * Generate plugin code for all scenes
   * @param includePlugins Optional list of plugin files to include
   * @returns Generated plugin code
   */
  public async generateAllScenesPluginCode(includePlugins?: string[]): Promise<string> {
    console.log('=== Generating plugin code for all scenes ===');
    return await generatePluginCode(this.sceneModels, { includePlugins });
  }

  /**
   * Save current scene plugin code
   * @param sceneName Scene name
   * @param includePlugins Optional list of plugin files to include
   * @returns Generated plugin code for current scene
   */
  public async saveCurrentScenePluginCode(sceneName?: string, includePlugins?: string[]): Promise<string | null> {
    console.log('=== Saving current scene plugin code ===');

    // 1. Get current scene model
    const currentState = getCurrentState();
    if (!currentState.currentScene) {
      console.error('No current scene model found');
      return null;
    }

    const sceneModel = currentState.currentScene;

    // 2. Determine scene name
    const finalSceneName = sceneName || this.getCurrentSceneName(sceneModel);

    // 3. Update scene model in collection
    this.sceneModels.set(finalSceneName, sceneModel);

    // 4. Generate plugin code for all scenes with included plugins
    const pluginCode = await generatePluginCode(this.sceneModels, { includePlugins });

    // 5. Store the plugin code
    this.scenePluginCodes.set('all_scenes', pluginCode);

    console.log(`Current scene ${finalSceneName} plugin code saved`);
    return pluginCode;
  }

  /**
   * Get saved plugin code
   * @returns Saved plugin code
   */
  public getSavedPluginCode(): string | null {
    return this.scenePluginCodes.get('all_scenes') || null;
  }

  /**
   * Load plugin code from saved data
   * @param pluginCode Plugin code to load
   */
  public loadPluginCode(pluginCode: string): void {
    console.log('=== Loading plugin code ===');

    // Store the plugin code
    this.scenePluginCodes.set('all_scenes', pluginCode);

    // Parse and extract scene information (simplified for now)
    // TODO: Implement proper parsing to extract scene models
    console.log('Plugin code loaded, length:', pluginCode.length);
  }

  /**
   * Get current scene name from scene model
   * @param sceneModel Scene model
   * @returns Scene name
   */
  private getCurrentSceneName(sceneModel: SceneModel): string {
    // Use scene model's className or fallback
    return sceneModel.className || 'Scene_Title';
  }
}
