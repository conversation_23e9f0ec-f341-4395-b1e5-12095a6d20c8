/**
 * 处理器工厂
 * 管理所有处理器的注册和获取
 */

import { BaseProcessor } from './BaseProcessor';
import { SpriteProcessor } from './SpriteProcessor';
import { SpriteButtonProcessor } from './SpriteButtonProcessor';
import { type BaseDisplayProperties } from '../types/base';

/**
 * 处理器工厂类
 */
export class ProcessorFactory {

  // 处理器注册表
  private static processors = new Map<string, BaseProcessor<any>>();

  // 单例实例缓存
  private static instances = new Map<string, BaseProcessor<any>>();

  /**
   * 注册处理器
   * @param className 类名
   * @param processor 处理器实例
   */
  static register<T extends BaseDisplayProperties>(
    className: string,
    processor: BaseProcessor<T>
  ): void {
    this.processors.set(className, processor);
    console.log(`✅ 注册处理器: ${className} -> ${processor.constructor.name}`);
  }

  /**
   * 获取处理器
   * @param className 类名
   * @returns 处理器实例
   */
  static getProcessor(className: string): BaseProcessor<any> | undefined {
    return this.processors.get(className);
  }

  /**
   * 获取单例处理器（性能优化）
   * @param className 类名
   * @returns 处理器实例
   */
  static getSingletonProcessor(className: string): BaseProcessor<any> | undefined {
    // 先检查缓存
    if (this.instances.has(className)) {
      return this.instances.get(className);
    }

    // 获取处理器
    const processor = this.processors.get(className);
    if (processor) {
      // 缓存实例
      this.instances.set(className, processor);
    }

    return processor;
  }

  /**
   * 检查是否支持某个类名
   * @param className 类名
   * @returns 是否支持
   */
  static isSupported(className: string): boolean {
    return this.processors.has(className);
  }

  /**
   * 获取所有支持的类名
   * @returns 类名数组
   */
  static getSupportedClassNames(): string[] {
    return Array.from(this.processors.keys());
  }

  /**
   * 清空所有注册的处理器
   */
  static clear(): void {
    this.processors.clear();
    this.instances.clear();
  }

  /**
   * 获取处理器数量
   * @returns 处理器数量
   */
  static getProcessorCount(): number {
    return this.processors.size;
  }

  /**
   * 批量注册处理器
   * @param processors 处理器映射
   */
  static registerBatch(processors: Record<string, BaseProcessor<any>>): void {
    for (const [className, processor] of Object.entries(processors)) {
      this.register(className, processor);
    }
  }
}

// 自动注册内置处理器
ProcessorFactory.registerBatch({
  'Sprite': new SpriteProcessor(),
  'SpriteButton': new SpriteButtonProcessor(),
});

console.log(`🏭 ProcessorFactory 初始化完成，已注册 ${ProcessorFactory.getProcessorCount()} 个处理器`);
console.log(`📋 支持的类型: ${ProcessorFactory.getSupportedClassNames().join(', ')}`);
