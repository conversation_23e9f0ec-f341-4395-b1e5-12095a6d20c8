// 测试列表数据源功能
// 这个文件用于测试 DataBindingModal 的列表模式

// 模拟 RPG Maker MZ 的全局变量
window.$dataItems = [
  null, // 索引 0 通常为空
  { id: 1, name: '药草', iconIndex: 1, description: '恢复少量HP', price: 50 },
  { id: 2, name: '魔法药', iconIndex: 2, description: '恢复少量MP', price: 100 },
  { id: 3, name: '解毒剂', iconIndex: 3, description: '解除毒状态', price: 30 }
];

window.$dataSkills = [
  null,
  { id: 1, name: '攻击', iconIndex: 1, description: '普通攻击', mpCost: 0 },
  { id: 2, name: '治疗', iconIndex: 2, description: '恢复HP', mpCost: 5 },
  { id: 3, name: '火球', iconIndex: 3, description: '火属性魔法', mpCost: 10 }
];

window.$gameParty = {
  numItems: (item) => Math.floor(Math.random() * 10) + 1, // 随机数量
  allMembers: () => [
    {
      actorId: () => 1,
      name: () => '主角',
      level: 10,
      hp: 100,
      mp: 50
    }
  ]
};

window.DataManager = {
  savefileInfo: (id) => {
    if (id <= 3) {
      return {
        title: `存档${id}`,
        playtime: `0${id}:30:45`,
        timestamp: Date.now() - id * 1000000
      };
    }
    return null;
  }
};

// 测试列表数据源表达式
console.log('=== 测试列表数据源 ===');

// 测试存档文件列表
try {
  const saveFilesExpression = `Array.from({length: 20}, (_, i) => i + 1).map(id => ({
    id,
    title: window.DataManager.savefileInfo(id)?.title || '空存档',
    playtime: window.DataManager.savefileInfo(id)?.playtime || '00:00:00',
    timestamp: window.DataManager.savefileInfo(id)?.timestamp || 0,
    exists: !!window.DataManager.savefileInfo(id)
  }))`;
  
  const saveFiles = eval(saveFilesExpression);
  console.log('✅ 存档文件列表:', saveFiles.slice(0, 5));
} catch (error) {
  console.error('❌ 存档文件列表测试失败:', error);
}

// 测试道具列表
try {
  const itemsExpression = `window.$dataItems.slice(1).filter(item => item).map(item => ({
    id: item.id,
    name: item.name,
    iconIndex: item.iconIndex,
    description: item.description,
    price: item.price,
    count: window.$gameParty.numItems(item)
  }))`;
  
  const items = eval(itemsExpression);
  console.log('✅ 道具列表:', items);
} catch (error) {
  console.error('❌ 道具列表测试失败:', error);
}

// 测试技能列表
try {
  const skillsExpression = `window.$dataSkills.slice(1).filter(skill => skill).map(skill => ({
    id: skill.id,
    name: skill.name,
    iconIndex: skill.iconIndex,
    description: skill.description,
    mpCost: skill.mpCost
  }))`;
  
  const skills = eval(skillsExpression);
  console.log('✅ 技能列表:', skills);
} catch (error) {
  console.error('❌ 技能列表测试失败:', error);
}

console.log('=== 测试完成 ===');
