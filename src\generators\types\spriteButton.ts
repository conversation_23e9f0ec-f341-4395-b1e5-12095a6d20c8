/**
 * SpriteButton 相关类型定义
 */

import { type SpriteProperties } from './sprite';

/**
 * 按钮状态枚举
 */
export type ButtonState = 'normal' | 'hover' | 'pressed' | 'disabled';

/**
 * 按钮类型枚举
 */
export type ButtonType = 'normal' | 'toggle' | 'radio';

/**
 * SpriteButton 属性
 */
export interface SpriteButtonProperties extends SpriteProperties {
  // 按钮特有属性
  buttonType: ButtonType;
  buttonText: string;      // 按钮文本
  currentState: ButtonState;
  isPressed: boolean;
  isEnabled: boolean;

  // 事件处理
  onClick?: string;        // 点击事件代码
  onHover?: string;        // 悬停事件代码
  onPress?: string;        // 按下事件代码
  onRelease?: string;      // 释放事件代码

  // 按钮组（用于 radio 类型）
  buttonGroup?: string;
}
