<script lang="ts">
  /**
   * Label 标签组件
   * 基于 Skeleton UI 和全局主题色彩
   */
  
  // Props
  export let text: string = '';
  export let htmlFor: string = '';
  export let required: boolean = false;
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let variant: 'default' | 'secondary' | 'success' | 'warning' | 'error' = 'default';
  export let weight: 'normal' | 'medium' | 'semibold' | 'bold' = 'medium';
  export let disabled: boolean = false;
  
  // 获取标签样式类
  function getLabelClass() {
    const baseClass = 'label';
    const sizeClass = `label-${size}`;
    const variantClass = `label-${variant}`;
    const weightClass = `label-${weight}`;
    const disabledClass = disabled ? 'label-disabled' : '';
    
    return [baseClass, sizeClass, variantClass, weightClass, disabledClass]
      .filter(Boolean)
      .join(' ');
  }
</script>

<label 
  class={getLabelClass()}
  for={htmlFor}
  aria-disabled={disabled}
>
  <span class="label-text">
    {text}
    <slot />
  </span>
  {#if required}
    <span class="label-required" aria-label="必填">*</span>
  {/if}
</label>

<style>
  .label {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-1);
    font-family: var(--font-family-base);
    color: var(--theme-text);
    cursor: pointer;
    transition: var(--transition-base);
  }
  
  .label:hover:not(.label-disabled) {
    color: var(--theme-primary);
  }
  
  .label-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .label-text {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
  }
  
  .label-required {
    color: var(--theme-error);
    font-weight: bold;
    margin-left: var(--spacing-1);
  }
  
  /* 尺寸变体 */
  .label-sm {
    font-size: var(--font-size-sm);
  }
  
  .label-md {
    font-size: var(--font-size-base);
  }
  
  .label-lg {
    font-size: var(--font-size-lg);
  }
  
  /* 颜色变体 */
  .label-default {
    color: var(--theme-text);
  }
  
  .label-secondary {
    color: var(--theme-text-secondary);
  }
  
  .label-success {
    color: var(--theme-success);
  }
  
  .label-warning {
    color: var(--theme-warning);
  }
  
  .label-error {
    color: var(--theme-error);
  }
  
  /* 字重变体 */
  .label-normal {
    font-weight: 400;
  }
  
  .label-medium {
    font-weight: 500;
  }
  
  .label-semibold {
    font-weight: 600;
  }
  
  .label-bold {
    font-weight: 700;
  }
  
  /* 焦点样式 */
  .label:focus-visible {
    outline: 2px solid var(--theme-primary);
    outline-offset: 2px;
    border-radius: var(--border-radius-small);
  }
</style>
