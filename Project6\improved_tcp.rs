use once_cell::sync::Lazy;
use std::io::{Read, Write};
use std::net::TcpStream;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::{Arc, Mutex, RwLock};
use std::thread;
use std::time::Duration;
use std::time::Instant;
use tauri::{command, Emitter, State, Window};

// 连接状态
#[derive(Debug, Clone)]
pub struct ConnectionState {
    pub is_connected: bool,
    pub host: String,
    pub port: u16,
    pub last_error: Option<String>,
}

// TCP连接管理器
pub struct TcpManager {
    stream: Option<Arc<Mutex<TcpStream>>>,
    window: Option<Window>,
    is_listening: AtomicBool,
    should_stop: AtomicBool,
    state: ConnectionState,
}

impl TcpManager {
    pub fn new() -> Self {
        Self {
            stream: None,
            window: None,
            is_listening: AtomicBool::new(false),
            should_stop: AtomicBool::new(false),
            state: ConnectionState {
                is_connected: false,
                host: String::new(),
                port: 0,
                last_error: None,
            },
        }
    }

    pub fn connect(&mut self, host: String, port: u16, window: Window) -> Result<(), String> {
        // 如果已经连接，先断开
        if self.state.is_connected {
            self.disconnect();
        }

        let address = format!("{}:{}", host, port);
        println!("尝试连接到: {}", address);

        match TcpStream::connect(&address) {
            Ok(stream) => {
                // 设置超时
                stream
                    .set_read_timeout(Some(Duration::from_secs(30)))
                    .map_err(|e| format!("设置读取超时失败: {}", e))?;
                stream
                    .set_write_timeout(Some(Duration::from_secs(5)))
                    .map_err(|e| format!("设置写入超时失败: {}", e))?;

                self.stream = Some(Arc::new(Mutex::new(stream)));
                self.window = Some(window);
                self.state.is_connected = true;
                self.state.host = host;
                self.state.port = port;
                self.state.last_error = None;
                self.should_stop.store(false, Ordering::Relaxed);

                println!("连接成功: {}", address);
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("连接失败: {}", e);
                self.state.last_error = Some(error_msg.clone());
                Err(error_msg)
            }
        }
    }

    pub fn disconnect(&mut self) {
        println!("断开连接");
        self.should_stop.store(true, Ordering::Relaxed);
        self.stream = None;
        self.window = None;
        self.state.is_connected = false;
        self.is_listening.store(false, Ordering::Relaxed);
    }

    pub fn start_listening(&mut self) -> Result<(), String> {
        if !self.state.is_connected {
            return Err("未建立连接".to_string());
        }

        if self.is_listening.load(Ordering::Relaxed) {
            return Err("已经在监听中".to_string());
        }

        let stream = self.stream.as_ref().ok_or("TCP连接不存在")?.clone();
        let window = self.window.as_ref().ok_or("窗口句柄不存在")?.clone();
        let should_stop = self.should_stop.clone();
        let is_listening = self.is_listening.clone();

        is_listening.store(true, Ordering::Relaxed);

        thread::spawn(move || {
            let mut buffer = [0; 1024];
            println!("开始监听数据");

            while !should_stop.load(Ordering::Relaxed) {
                let read_result = {
                    let mut stream = match stream.lock() {
                        Ok(s) => s,
                        Err(e) => {
                            println!("获取流锁失败: {}", e);
                            thread::sleep(Duration::from_millis(100));
                            continue;
                        }
                    };

                    stream.read(&mut buffer)
                };

                match read_result {
                    Ok(n) if n > 0 => {
                        let data = String::from_utf8_lossy(&buffer[0..n]).to_string();
                        println!("接收到数据: {} bytes", n);

                        if let Err(e) = window.emit("device-data", &data) {
                            println!("发送数据到前端失败: {}", e);
                        }
                    }
                    Ok(_) => {
                        println!("连接被远程关闭");
                        break;
                    }
                    Err(e) => {
                        println!("读取数据出错: {}", e);
                        break;
                    }
                }
            }

            is_listening.store(false, Ordering::Relaxed);
            println!("监听线程结束");
        });

        Ok(())
    }

    pub fn send_data(&self, data: &str) -> Result<(), String> {
        if !self.state.is_connected {
            return Err("TCP连接未建立".to_string());
        }

        let stream = self.stream.as_ref().ok_or("TCP连接不存在")?;

        println!("准备发送数据: {} bytes", data.len());
        let start_time = Instant::now();

        let result = {
            let mut stream = stream.lock().map_err(|e| format!("获取锁失败: {}", e))?;

            stream
                .write_all(data.as_bytes())
                .map_err(|e| format!("写入数据失败: {}", e))
        };

        match result {
            Ok(_) => {
                println!("发送数据成功，耗时: {:?}", start_time.elapsed());
                Ok(())
            }
            Err(e) => {
                println!("发送数据失败: {}", e);
                Err(e)
            }
        }
    }

    pub fn get_state(&self) -> ConnectionState {
        self.state.clone()
    }
}

// 安全的全局TCP管理器
static TCP_MANAGER: Lazy<Arc<RwLock<TcpManager>>> =
    Lazy::new(|| Arc::new(RwLock::new(TcpManager::new())));

// Tauri命令：初始化TCP连接
#[command]
pub fn init_tcp_connection(host: String, port: u16, window: Window) -> Result<(), String> {
    let mut manager = TCP_MANAGER
        .write()
        .map_err(|e| format!("获取写锁失败: {}", e))?;

    manager.connect(host, port, window)?;
    manager.start_listening()?;

    Ok(())
}

// Tauri命令：发送数据
#[command]
pub fn send_data_to_device(data: String) -> Result<(), String> {
    let manager = TCP_MANAGER
        .read()
        .map_err(|e| format!("获取读锁失败: {}", e))?;

    manager.send_data(&data)
}

// Tauri命令：断开连接
#[command]
pub fn disconnect_tcp() -> Result<(), String> {
    let mut manager = TCP_MANAGER
        .write()
        .map_err(|e| format!("获取写锁失败: {}", e))?;

    manager.disconnect();
    Ok(())
}

// Tauri命令：获取连接状态
#[command]
pub fn get_connection_state() -> Result<ConnectionState, String> {
    let manager = TCP_MANAGER
        .read()
        .map_err(|e| format!("获取读锁失败: {}", e))?;

    Ok(manager.get_state())
}

// Tauri命令：重连
#[command]
pub fn reconnect_tcp(window: Window) -> Result<(), String> {
    let manager = TCP_MANAGER
        .read()
        .map_err(|e| format!("获取读锁失败: {}", e))?;

    let state = manager.get_state();
    drop(manager); // 释放读锁

    if !state.host.is_empty() && state.port > 0 {
        init_tcp_connection(state.host, state.port, window)
    } else {
        Err("没有之前的连接信息".to_string())
    }
}

// 错误类型定义
#[derive(Debug, Clone)]
pub enum TcpError {
    ConnectionFailed(String),
    SendFailed(String),
    ReceiveFailed(String),
    NotConnected,
    AlreadyConnected,
    LockError(String),
}

impl std::fmt::Display for TcpError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TcpError::ConnectionFailed(msg) => write!(f, "连接失败: {}", msg),
            TcpError::SendFailed(msg) => write!(f, "发送失败: {}", msg),
            TcpError::ReceiveFailed(msg) => write!(f, "接收失败: {}", msg),
            TcpError::NotConnected => write!(f, "未建立连接"),
            TcpError::AlreadyConnected => write!(f, "已经连接"),
            TcpError::LockError(msg) => write!(f, "锁错误: {}", msg),
        }
    }
}

impl std::error::Error for TcpError {}

// 连接配置
#[derive(Debug, Clone)]
pub struct TcpConfig {
    pub read_timeout: Duration,
    pub write_timeout: Duration,
    pub connect_timeout: Duration,
    pub retry_attempts: u32,
    pub retry_delay: Duration,
}

impl Default for TcpConfig {
    fn default() -> Self {
        Self {
            read_timeout: Duration::from_secs(30),
            write_timeout: Duration::from_secs(5),
            connect_timeout: Duration::from_secs(10),
            retry_attempts: 3,
            retry_delay: Duration::from_secs(1),
        }
    }
}

// 带重试的连接函数
#[command]
pub fn init_tcp_connection_with_retry(
    host: String,
    port: u16,
    window: Window,
    config: Option<TcpConfig>,
) -> Result<(), String> {
    let config = config.unwrap_or_default();
    let mut last_error = String::new();

    for attempt in 1..=config.retry_attempts {
        println!("连接尝试 {}/{}", attempt, config.retry_attempts);

        match init_tcp_connection(host.clone(), port, window.clone()) {
            Ok(_) => {
                println!("连接成功");
                return Ok(());
            }
            Err(e) => {
                last_error = e;
                println!("连接失败: {}", last_error);

                if attempt < config.retry_attempts {
                    println!("等待 {:?} 后重试", config.retry_delay);
                    thread::sleep(config.retry_delay);
                }
            }
        }
    }

    Err(format!(
        "连接失败，已重试 {} 次: {}",
        config.retry_attempts, last_error
    ))
}

// 心跳检测
#[command]
pub fn start_heartbeat(interval_secs: u64) -> Result<(), String> {
    let manager = TCP_MANAGER
        .read()
        .map_err(|e| format!("获取读锁失败: {}", e))?;

    if !manager.state.is_connected {
        return Err("未建立连接".to_string());
    }

    let should_stop = manager.should_stop.clone();
    drop(manager);

    thread::spawn(move || {
        let interval = Duration::from_secs(interval_secs);

        while !should_stop.load(Ordering::Relaxed) {
            thread::sleep(interval);

            // 发送心跳包
            if let Err(e) = send_data_to_device("PING".to_string()) {
                println!("心跳发送失败: {}", e);
                break;
            }

            println!("心跳发送成功");
        }

        println!("心跳线程结束");
    });

    Ok(())
}
