import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * MaskModel - UIMask的模型对象
 * 继承自BaseObjectModel，管理UIMask的所有属性和状态
 */
export class MaskModel extends BaseObjectModel {
    // 组件类型
    public readonly componentType = 'UIMask';

    // 🎭 遮罩类型
    maskType = $state('rectangle'); // 'rectangle', 'circle', 'image'

    // 🎭 图片路径（当类型为image时）
    maskImage = $state('');

    // 🎭 位置偏移
    offsetX = $state(0);
    offsetY = $state(0);

    // 🎭 遮罩尺寸
    maskWidth = $state(200);
    maskHeight = $state(200);

    // 🎭 绑定的目标对象
    boundTarget = $state(null);
    boundTargetName = $state(''); // 用于显示的目标对象名称

    constructor(mask: any = {}) {
        super(mask);

        // 初始化遮罩属性
        this.maskType = mask.maskType || 'rectangle';
        this.maskImage = mask.maskImage || '';
        this.offsetX = mask.offsetX || 0;
        this.offsetY = mask.offsetY || 0;
        this.maskWidth = mask.maskWidth || 200;
        this.maskHeight = mask.maskHeight || 200;

        // 初始化绑定信息
        if (mask.boundTarget) {
            this.boundTarget = mask.boundTarget;
            this.boundTargetName = mask.boundTarget.name || mask.boundTarget.constructor.name;
        }

        console.log('🎭 MaskModel: 创建遮罩模型', this);
    }

    /**
     * 设置Mask特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Mask特有的属性
     */
    protected setupSpecificSync(): void {
        try {
            console.log('🎭 MaskModel: setupSpecificSync 被调用', {
                maskType: this.maskType,
                maskWidth: this.maskWidth,
                maskHeight: this.maskHeight,
                hasOriginalObject: !!this._originalObject
            });

            // 同步所有Mask特有属性
            this.syncMaskProperties();

        } catch (error) {
            console.error('❌ MaskModel: 特有属性同步失败', error);
        }
    }

    /**
     * 同步Mask属性到原始对象
     */
    private syncMaskProperties(): void {
        if (!this._originalObject) return;

        // 同步遮罩类型
        if (this.maskType !== undefined) {
            this._originalObject.maskType = this.maskType;
            console.log('🎭 MaskModel: 同步遮罩类型', this.maskType);
        }

        // 同步图片路径
        if (this.maskImage !== undefined) {
            this._originalObject.maskImage = this.maskImage;
            console.log('🎭 MaskModel: 同步图片路径', this.maskImage);
        }

        // 同步位置偏移
        if (this.offsetX !== undefined) {
            this._originalObject.offsetX = this.offsetX;
            console.log('🎭 MaskModel: 同步X偏移', this.offsetX);
        }

        if (this.offsetY !== undefined) {
            this._originalObject.offsetY = this.offsetY;
            console.log('🎭 MaskModel: 同步Y偏移', this.offsetY);
        }

        // 同步遮罩尺寸
        if (this.maskWidth !== undefined) {
            this._originalObject.maskWidth = this.maskWidth;
            console.log('🎭 MaskModel: 同步遮罩宽度', this.maskWidth);
        }

        if (this.maskHeight !== undefined) {
            this._originalObject.maskHeight = this.maskHeight;
            console.log('🎭 MaskModel: 同步遮罩高度', this.maskHeight);
        }

        // 触发遮罩更新
        this.triggerMaskUpdate();
    }

    /**
     * 触发遮罩更新
     */
    private triggerMaskUpdate(): void {
        if (!this._originalObject) return;

        // 根据变化的属性调用相应的更新方法
        if (typeof this._originalObject.updateMaskType === 'function') {
            this._originalObject.updateMaskType();
        }
        
        if (typeof this._originalObject.updateMaskSize === 'function') {
            this._originalObject.updateMaskSize();
        }

        if (typeof this._originalObject.updateMaskPosition === 'function') {
            this._originalObject.updateMaskPosition();
        }

        console.log('🎭 MaskModel: 触发遮罩更新完成');
    }

    /**
     * 绑定目标对象
     */
    bindTarget(targetObject: any): boolean {
        if (!this._originalObject || typeof this._originalObject.bindTarget !== 'function') {
            console.error('🎭 MaskModel: 原始对象不支持绑定操作');
            return false;
        }

        const success = this._originalObject.bindTarget(targetObject);
        if (success) {
            this.boundTarget = targetObject;
            this.boundTargetName = targetObject?.name || targetObject?.constructor.name || '';
            console.log('🎭 MaskModel: 目标对象绑定成功', this.boundTargetName);
        }

        return success;
    }

    /**
     * 解除绑定
     */
    unbindTarget(): void {
        if (this._originalObject && typeof this._originalObject.unbindTarget === 'function') {
            this._originalObject.unbindTarget();
        }

        this.boundTarget = null;
        this.boundTargetName = '';
        console.log('🎭 MaskModel: 目标对象绑定已解除');
    }

    /**
     * 应用遮罩
     */
    applyMask(): void {
        if (this._originalObject && typeof this._originalObject.applyMask === 'function') {
            this._originalObject.applyMask();
            console.log('🎭 MaskModel: 遮罩已应用');
        }
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const lines: string[] = [];

        // 生成构造函数调用
        lines.push(`${indent}const ${varName} = new UIMask({`);
        lines.push(`${indent}    maskType: '${this.maskType}',`);
        lines.push(`${indent}    maskImage: '${this.maskImage}',`);
        lines.push(`${indent}    offsetX: ${this.offsetX},`);
        lines.push(`${indent}    offsetY: ${this.offsetY},`);
        lines.push(`${indent}    maskWidth: ${this.maskWidth},`);
        lines.push(`${indent}    maskHeight: ${this.maskHeight}`);
        lines.push(`${indent}});`);

        return lines.join('\n');
    }

    /**
     * 克隆当前Mask对象 - 调用插件的 clone 方法
     */
    clone(): MaskModel {
        console.log('🔄 MaskModel: 开始克隆Mask对象（调用插件方法）');

        // 1. 调用原始 UIMask 对象的 clone 方法
        const originalUIMask = this.getOriginalObject();
        if (!originalUIMask || typeof originalUIMask.clone !== 'function') {
            console.error('❌ MaskModel: 原始对象没有 clone 方法');
            throw new Error('UIMask 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUIMask = originalUIMask.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 MaskModel 包装克隆的对象
        const clonedModel = new MaskModel(clonedUIMask);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ MaskModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UIMask 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UIMask 特有属性
            maskType: this.maskType,
            maskImage: this.maskImage,
            offsetX: this.offsetX,
            offsetY: this.offsetY,
            maskWidth: this.maskWidth,
            maskHeight: this.maskHeight,

            // 绑定信息（注意：不保存实际对象引用，只保存名称）
            boundTargetName: this.boundTargetName
        };
    }

    /**
     * 获取遮罩信息摘要
     */
    getMaskInfo(): any {
        return {
            maskType: this.maskType,
            maskImage: this.maskImage,
            offset: { x: this.offsetX, y: this.offsetY },
            size: { width: this.maskWidth, height: this.maskHeight },
            boundTarget: this.boundTargetName,
            hasBoundTarget: !!this.boundTarget
        };
    }

    /**
     * 获取可绑定的目标对象列表
     */
    getAvailableTargets(): any[] {
        try {
            // 从根模型开始查找场景
            let scene = this.parent;
            while (scene && scene.parent) {
                scene = scene.parent;
            }

            if (!scene) {
                console.warn('🎭 MaskModel: 无法找到场景对象');
                return [];
            }

            const targets: any[] = [];

            // 递归收集所有UI组件
            const collectTargets = (model: any) => {
                if (model && model !== this) { // 排除自己
                    // 检查是否是可遮罩的对象
                    if (model.componentType && model.componentType !== 'UIMask') {
                        targets.push({
                            model: model,
                            name: model.name || `${model.componentType}_${model.id || 'unknown'}`,
                            type: model.componentType
                        });
                    }

                    // 递归检查子对象
                    if (model.children && Array.isArray(model.children)) {
                        model.children.forEach(collectTargets);
                    }
                }
            };

            collectTargets(scene);
            console.log('🎭 MaskModel: 找到可绑定目标', targets.length, '个');
            return targets;

        } catch (error) {
            console.error('🎭 MaskModel: 获取可用目标失败', error);
            return [];
        }
    }
}

// 注册MaskModel到基类容器
BaseObjectModel.registerModel('UIMask', MaskModel);
BaseObjectModel.registerModel('Mask', MaskModel);
