(()=>{
    //=============================================================================
    // UIImage Class - 图片组件，参考UIButton实现
    //=============================================================================

    /**
     * 图片组件类 - 继承自 Sprite，参考UIButton的尺寸设置实现
     */
    class UIImage extends Sprite {
        constructor(properties = {}) {
            super();

            console.log('🖼️ UIImage: 创建图片组件', properties);

            // 基础属性
            this.isUIComponent = true;
            this.uiComponentType = 'UIImage';
            this._width = properties.width || 100;
            this._height = properties.height || 100;
            this.imagePath = properties.imagePath || '';

            // 回调
            this.onLoadCallback = null;

            // 源图片bitmap
            this.sourceBitmap = null;

            // 裁切相关属性
            this.regions = properties.regions || [];
            this.currentRegionIndex = properties.currentRegionIndex || 0;
            this.gridRows = properties.gridRows || 1;
            this.gridCols = properties.gridCols || 1;

            // 创建bitmap并加载图片
            this.createBitmap();
            if (this.imagePath) {
                this.loadImage();
            }
        }

        /**
         * 获取宽度
         */
        get width() {
            return this._width;
        }

        /**
         * 设置宽度 - 触发重绘
         */
        set width(value) {
            if (this._width !== value) {
                this._width = value;
                this.refresh();
                console.log('📏 UIImage: 设置宽度', value);
            }
        }

        /**
         * 获取高度
         */
        get height() {
            return this._height;
        }

        /**
         * 设置高度 - 触发重绘
         */
        set height(value) {
            if (this._height !== value) {
                this._height = value;
                this.refresh();
                console.log('📏 UIImage: 设置高度', value);
            }
        }

        /**
         * 创建bitmap - 参考UIButton实现
         */
        createBitmap() {
            this.bitmap = new Bitmap(this.width, this.height);
            console.log('🎨 UIImage: 创建bitmap', this.width, 'x', this.height);
        }

        /**
         * 加载图片 - 参考UIButton的图片加载
         */
        loadImage() {
            if (typeof ImageManager !== 'undefined' && this.imagePath) {
                console.log('📥 UIImage: 加载图片', this.imagePath);
                this.sourceBitmap = ImageManager.loadBitmapFromUrl(this.imagePath);
                this.sourceBitmap.addLoadListener(() => {
                    this.onImageLoaded();
                });
            }
        }

        /**
         * 图片加载完成回调
         */
        onImageLoaded() {
            console.log('✅ UIImage: 图片加载完成', this.sourceBitmap.width, 'x', this.sourceBitmap.height);

            const originalWidth = this.sourceBitmap.width;
            const originalHeight = this.sourceBitmap.height;

            // 🔧 区分不同情况的尺寸处理
            if (this.regions.length === 0) {
                // 情况1: 没有预设区域 - 自动设置为图片原始尺寸（新建或更换图片）
                console.log('📏 UIImage: 没有预设区域，设置为图片原始尺寸', {
                    old: `${this._width}x${this._height}`,
                    new: `${originalWidth}x${originalHeight}`
                });

                this._width = originalWidth;
                this._height = originalHeight;

                // 创建默认区域（整张图片）
                this.createDefaultRegion();

                // 通知ImageModel尺寸变化
                if (this.onLoadCallback) {
                    this.onLoadCallback(originalWidth, originalHeight, 1, 1);
                }
            } else {
                // 情况2: 有预设区域 - 保持当前尺寸（Clone或已有裁切）
                console.log('📏 UIImage: 有预设区域，保持当前尺寸', {
                    currentSize: `${this._width}x${this._height}`,
                    imageSize: `${originalWidth}x${originalHeight}`,
                    regions: this.regions.length
                });

                // 不改变尺寸，只通知ImageModel图片已加载
                if (this.onLoadCallback) {
                    this.onLoadCallback(this._width, this._height, 1, 1);
                }
            }

            // 刷新显示 - 参考UIButton
            this.refresh();
        }

        /**
         * 创建默认区域（整张图片）
         */
        createDefaultRegion() {
            if (this.sourceBitmap) {
                this.regions = [{
                    sx: 0,
                    sy: 0,
                    sw: this.sourceBitmap.width,
                    sh: this.sourceBitmap.height
                }];
                this.currentRegionIndex = 0;
                console.log('🔧 UIImage: 创建默认区域', this.regions[0]);
            }
        }

        /**
         * 刷新图片显示 - 参考UIButton的refresh实现
         */
        refresh() {
            if (!this.bitmap) {
                console.log('⚠️ UIImage: bitmap不存在，重新创建');
                this.createBitmap();
            }

            this.bitmap.clear();

            if (!this.sourceBitmap) {
                console.log('⚠️ UIImage: 源图片不存在');
                return;
            }

            if (!this.sourceBitmap.isReady || !this.sourceBitmap.isReady()) {
                console.log('⚠️ UIImage: 图片未准备好，等待加载');
                return;
            }

            console.log('🎨 UIImage: 刷新显示', this.width, 'x', this.height);
            this.setImageBitmap(this.sourceBitmap);
        }

        /**
         * 设置图片bitmap - 支持裁切功能
         */
        setImageBitmap(sourceBitmap) {
            if (!sourceBitmap || !sourceBitmap.canvas) {
                console.warn('UIImage: 无效的sourceBitmap');
                return;
            }

            // 确保bitmap存在且尺寸正确
            if (!this.bitmap || this.bitmap.width !== this.width || this.bitmap.height !== this.height) {
                this.bitmap = new Bitmap(this.width, this.height);
            }

            // 清除bitmap内容
            this.bitmap.clear();

            // 获取当前区域信息
            const region = this.getCurrentRegion();
            if (!region) {
                console.warn('UIImage: 没有可用的区域');
                return;
            }

            // 使用高质量的Canvas绘制 - 支持裁切
            const context = this.bitmap.canvas.getContext('2d');
            if (context) {
                // 设置高质量缩放
                context.imageSmoothingEnabled = true;
                context.imageSmoothingQuality = 'high';

                // 绘制指定区域到目标尺寸
                context.drawImage(
                    sourceBitmap.canvas,
                    region.sx, region.sy, region.sw, region.sh,  // 源区域
                    0, 0, this.width, this.height                // 目标尺寸
                );

                // 标记bitmap已更新 - 使用RPG Maker MZ方法
                if (this.bitmap._baseTexture && this.bitmap._baseTexture.update) {
                    this.bitmap._baseTexture.update();
                } else if (this.bitmap._onLoad) {
                    this.bitmap._onLoad();
                }
            }

            // 🔧 设置bitmap的url属性，让属性面板能够检测到
            if (this.sourceBitmap && this.sourceBitmap.url) {
                // 使用Object.defineProperty来设置只读属性
                Object.defineProperty(this.bitmap, 'url', {
                    value: this.sourceBitmap.url,
                    writable: false,
                    enumerable: true,
                    configurable: true
                });
                this.bitmap._loadingState = 'loaded';
            }

            console.log('🎨 UIImage: 设置图片', `区域[${region.sx},${region.sy},${region.sw},${region.sh}]`, '->', this.width, 'x', this.height);
        }

        /**
         * 更新显示尺寸 - 完全参考UIButton的updateDisplaySize实现
         */
        updateDisplaySize(newWidth, newHeight) {
            if (this._width !== newWidth || this._height !== newHeight) {
                this._width = newWidth;
                this._height = newHeight;

                // 重新绘制当前状态 - 参考UIButton
                this.refresh();

                console.log('📏 UIImage: 更新显示尺寸', newWidth, 'x', newHeight);
            }
        }

        /**
         * 设置尺寸 - 参考UIButton实现
         */
        setSize(width, height) {
            this.updateDisplaySize(width, height);
        }

        /**
         * 设置显示尺寸 - 兼容方法
         */
        setDisplaySize(width, height) {
            this.updateDisplaySize(width, height);
        }

        /**
         * 设置图片路径
         */
        setImagePath(path) {
            this.imagePath = path;
            this.loadImage();
        }

        /**
         * 设置加载完成回调
         */
        setOnLoadCallback(callback) {
            this.onLoadCallback = callback;
        }

        // ==================== 裁切功能 ====================

        /**
         * 获取当前区域信息
         */
        getCurrentRegion() {
            return this.regions[this.currentRegionIndex] || null;
        }

        /**
         * 设置当前显示区域
         */
        setCurrentRegion(index) {
            if (index >= 0 && index < this.regions.length) {
                this.currentRegionIndex = index;
                this.refresh(); // 重新绘制
                console.log('🔧 UIImage: 切换到区域', index, this.regions[index]);
                return true;
            }
            return false;
        }

        /**
         * 生成网格区域
         */
        generateGridRegions(rows, cols) {
            if (!this.sourceBitmap) {
                console.warn('🖼️ UIImage: 图片未加载，无法生成网格区域');
                return;
            }

            this.gridRows = rows;
            this.gridCols = cols;
            this.regions = [];

            const cellWidth = Math.floor(this.sourceBitmap.width / cols);
            const cellHeight = Math.floor(this.sourceBitmap.height / rows);

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    this.regions.push({
                        sx: col * cellWidth,
                        sy: row * cellHeight,
                        sw: cellWidth,
                        sh: cellHeight
                    });
                }
            }

            this.currentRegionIndex = 0;
            console.log('🔧 UIImage: 生成网格区域', `${rows}x${cols}`, '共', this.regions.length, '个区域');

            // 重新绘制
            this.refresh();
        }

        /**
         * 添加自定义区域
         */
        addRegion(sx, sy, sw, sh) {
            this.regions.push({ sx, sy, sw, sh });
            console.log('🔧 UIImage: 添加区域', { sx, sy, sw, sh });
        }

        /**
         * 重置为默认区域（完整图片）
         */
        resetToDefaultRegion() {
            if (!this.sourceBitmap) {
                console.warn('🖼️ UIImage: 图片未加载，无法重置区域');
                return;
            }

            // 重置网格设置
            this.gridRows = 1;
            this.gridCols = 1;

            // 🔧 重置尺寸为图片原始尺寸
            const originalWidth = this.sourceBitmap.width;
            const originalHeight = this.sourceBitmap.height;

            console.log('📏 UIImage: 重置尺寸为图片原始尺寸', {
                old: `${this._width}x${this._height}`,
                new: `${originalWidth}x${originalHeight}`
            });

            this._width = originalWidth;
            this._height = originalHeight;

            // 创建默认区域（完整图片）
            this.regions = [{
                sx: 0,
                sy: 0,
                sw: originalWidth,
                sh: originalHeight
            }];
            this.currentRegionIndex = 0;

            // 重新绘制
            this.refresh();

            // 🔧 通知ImageModel尺寸已重置
            if (this.onLoadCallback) {
                this.onLoadCallback(originalWidth, originalHeight, 1, 1);
            }

            console.log('🔄 UIImage: 重置为默认区域', {
                size: `${originalWidth}x${originalHeight}`,
                regions: this.regions.length,
                gridSize: `${this.gridRows}x${this.gridCols}`
            });
        }

        /**
         * 是否为多区域裁切
         */
        isMultiRegion() {
            return this.regions.length > 1;
        }

        /**
         * 获取属性
         */
        getProperties() {
            return {
                // 基础属性
                x: this.x,
                y: this.y,
                width: this.width,
                height: this.height,
                // 图片属性
                imagePath: this.imagePath,
                // 裁切属性
                regions: this.regions,
                currentRegionIndex: this.currentRegionIndex,
                gridRows: this.gridRows,
                gridCols: this.gridCols
            };
        }

        /**
         * 克隆当前 UIImage 对象
         * @param {Object} options 克隆选项
         * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
         * @param {number} options.offsetX 水平偏移量 (默认: 20)
         * @param {number} options.offsetY 垂直偏移量 (默认: 20)
         * @returns {UIImage} 克隆的 UIImage 对象
         */
        clone(options = {}) {
            console.log('🔄 UIImage: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = {
                // 基础属性
                width: this.width,
                height: this.height,
                visible: this.visible,

                // UIImage 特有属性
                imagePath: this.imagePath,
                scaleMode: this.scaleMode,
                preserveAspectRatio: this.preserveAspectRatio,
                regions: JSON.parse(JSON.stringify(this.regions)), // 深拷贝区域数据
                currentRegionIndex: this.currentRegionIndex,
                gridRows: this.gridRows,
                gridCols: this.gridCols
            };

            // 2. 创建克隆对象
            const clonedImage = new UIImage(cloneProperties);

            // 3. 设置位置和变换属性
            clonedImage.x = this.x + (offsetPosition ? offsetX : 0);
            clonedImage.y = this.y + (offsetPosition ? offsetY : 0);
            clonedImage.scale.x = this.scale.x;
            clonedImage.scale.y = this.scale.y;
            clonedImage.rotation = this.rotation;
            clonedImage.alpha = this.alpha;
            clonedImage.anchor.x = this.anchor.x;
            clonedImage.anchor.y = this.anchor.y;
            clonedImage.pivot.x = this.pivot.x;
            clonedImage.pivot.y = this.pivot.y;
            clonedImage.skew.x = this.skew.x;
            clonedImage.skew.y = this.skew.y;
            clonedImage.zIndex = this.zIndex;

            // 4. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                    clonedImage.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                }
            }

            console.log('✅ UIImage: 克隆完成，包含', clonedChildren.length, '个子对象');
            return clonedImage;
        }
    }

    // 导出到全局
    window.UIImage = UIImage;
})()