/**
 * 绘制工具类 - 负责绘制箭头和包围盒
 */

import type {
  ArrowConfig,
  ArrowType,
  BoundingBoxConfig,
  SelectedObjectInfo,
  RenderContext,
  ToolMode
} from './types';

export class DrawingUtils {
  /**
   * 绘制方向箭头或缩放控制器
   */
  static drawArrow(
    ctx: CanvasRenderingContext2D,
    config: ArrowConfig,
    isHovered: boolean = false,
    isActive: boolean = false,
    mode: ToolMode = 'transform' as ToolMode
  ): void {
    const { x, y, size, color, hoverColor, activeColor, type } = config;

    // 选择颜色
    let currentColor = color;
    if (isActive) {
      currentColor = activeColor;
    } else if (isHovered) {
      currentColor = hoverColor;
    }

    ctx.save();
    ctx.fillStyle = currentColor;
    ctx.strokeStyle = currentColor;
    ctx.lineWidth = 2;

    // 绘制箭头背景圆圈
    ctx.beginPath();
    ctx.arc(x, y, size + 4, 0, Math.PI * 2);
    ctx.fillStyle = isActive ? 'rgba(33, 150, 243, 0.3)' : 'rgba(255, 255, 255, 0.8)';
    ctx.fill();
    ctx.strokeStyle = currentColor;
    ctx.stroke();

    // 绘制箭头、方块或缩放控制器
    ctx.fillStyle = currentColor;
    ctx.beginPath();

    if (mode === 'scale') {
      // 缩放模式：绘制正方形
      const halfSize = size * 0.5;
      switch (type) {
        case 'up':
          // Y轴缩放控制器（上方正方形）
          ctx.rect(x - halfSize, y - size, size, size);
          break;
        case 'right':
          // X轴缩放控制器（右方正方形）
          ctx.rect(x, y - halfSize, size, size);
          break;
        case 'center':
          // 等比缩放控制器（中心正方形）
          ctx.rect(x - halfSize, y - halfSize, size, size);
          break;
        case 'down':
          ctx.rect(x - halfSize, y, size, size);
          break;
        case 'left':
          ctx.rect(x - size, y - halfSize, size, size);
          break;
      }
    } else {
      // 变换模式：绘制箭头
      switch (type) {
        case 'up':
          // Y轴箭头（向上）
          ctx.moveTo(x, y - size);
          ctx.lineTo(x - size * 0.4, y + size * 0.2);
          ctx.lineTo(x + size * 0.4, y + size * 0.2);
          break;
        case 'right':
          // X轴箭头（向右）
          ctx.moveTo(x + size, y);
          ctx.lineTo(x - size * 0.2, y - size * 0.4);
          ctx.lineTo(x - size * 0.2, y + size * 0.4);
          break;
        case 'center':
          // 中心方块
          const halfSize = size * 0.5;
          ctx.rect(x - halfSize, y - halfSize, size, size);
          break;
        case 'down':
          ctx.moveTo(x, y + size);
          ctx.lineTo(x - size * 0.6, y - size * 0.3);
          ctx.lineTo(x + size * 0.6, y - size * 0.3);
          break;
        case 'left':
          ctx.moveTo(x - size, y);
          ctx.lineTo(x + size * 0.3, y - size * 0.6);
          ctx.lineTo(x + size * 0.3, y + size * 0.6);
          break;
      }
    }

    ctx.closePath();
    ctx.fill();
    ctx.restore();
  }

  /**
   * 绘制包围盒
   */
  static drawBoundingBox(
    ctx: CanvasRenderingContext2D,
    bounds: { x: number; y: number; width: number; height: number },
    config: BoundingBoxConfig
  ): void {
    const { x, y, width, height } = bounds;
    const { strokeColor, strokeWidth, fillColor, dashPattern } = config;

    ctx.save();
    ctx.strokeStyle = strokeColor;
    ctx.lineWidth = strokeWidth;

    // 设置虚线样式
    if (dashPattern) {
      ctx.setLineDash(dashPattern);
    }

    // 绘制填充
    if (fillColor) {
      ctx.fillStyle = fillColor;
      ctx.fillRect(x, y, width, height);
    }

    // 绘制边框
    ctx.strokeRect(x, y, width, height);

    // 绘制角落的小方块（类似Unity的选择框）
    const cornerSize = 6;
    ctx.fillStyle = strokeColor;
    ctx.setLineDash([]); // 重置虚线

    // 四个角
    const corners = [
      { x: x - cornerSize / 2, y: y - cornerSize / 2 }, // 左上
      { x: x + width - cornerSize / 2, y: y - cornerSize / 2 }, // 右上
      { x: x - cornerSize / 2, y: y + height - cornerSize / 2 }, // 左下
      { x: x + width - cornerSize / 2, y: y + height - cornerSize / 2 } // 右下
    ];

    corners.forEach(corner => {
      ctx.fillRect(corner.x, corner.y, cornerSize, cornerSize);
    });

    ctx.restore();
  }

  /**
   * 绘制坐标轴线
   */
  static drawAxisLines(
    ctx: CanvasRenderingContext2D,
    center: { x: number; y: number },
    arrowOffset: number
  ): void {
    ctx.save();
    ctx.lineWidth = 2;

    // X轴线（红色）
    ctx.strokeStyle = '#F44336';
    ctx.beginPath();
    ctx.moveTo(center.x, center.y);
    ctx.lineTo(center.x + arrowOffset * 0.8, center.y);
    ctx.stroke();

    // Y轴线（绿色）
    ctx.strokeStyle = '#4CAF50';
    ctx.beginPath();
    ctx.moveTo(center.x, center.y);
    ctx.lineTo(center.x, center.y - arrowOffset * 0.8);
    ctx.stroke();

    ctx.restore();
  }

  /**
   * 绘制选中对象的完整UI（包围盒 + 坐标轴 + 箭头）
   */
  static drawSelectionUI(
    ctx: CanvasRenderingContext2D,
    objectInfo: SelectedObjectInfo,
    arrowConfigs: ArrowConfig[],
    boundingBoxConfig: BoundingBoxConfig,
    hoveredArrow: ArrowType | null = null,
    activeArrow: ArrowType | null = null,
    mode: ToolMode = 'transform' as ToolMode
  ): void {
    // 绘制包围盒
    this.drawBoundingBox(ctx, objectInfo.bounds, boundingBoxConfig);

    // 只在变换模式下绘制坐标轴线
    if (mode === 'transform') {
      this.drawAxisLines(ctx, objectInfo.center, 25);
    }

    // 绘制箭头/正方形和中心控制器
    arrowConfigs.forEach(config => {
      const isHovered = hoveredArrow === config.type;
      const isActive = activeArrow === config.type;
      this.drawArrow(ctx, config, isHovered, isActive, mode);
    });
  }

  /**
   * 创建Unity风格的坐标轴配置
   */
  static createArrowConfigs(
    objectInfo: SelectedObjectInfo,
    arrowSize: number,
    arrowOffset: number,
    colors: { normal: string; hover: string; active: string },
    mode: ToolMode = 'transform' as ToolMode
  ): ArrowConfig[] {
    const { center } = objectInfo;

    if (mode === 'scale') {
      // 缩放模式：使用不同的颜色方案
      return [
        // Y轴缩放（绿色，向上）
        {
          type: 'up' as ArrowType,
          x: center.x,
          y: center.y - arrowOffset,
          size: arrowSize,
          color: '#4CAF50', // 绿色
          hoverColor: '#66BB6A',
          activeColor: '#2E7D32'
        },
        // X轴缩放（红色，向右）
        {
          type: 'right' as ArrowType,
          x: center.x + arrowOffset,
          y: center.y,
          size: arrowSize,
          color: '#F44336', // 红色
          hoverColor: '#EF5350',
          activeColor: '#C62828'
        },
        // 等比缩放（黄色，中心）
        {
          type: 'center' as ArrowType,
          x: center.x,
          y: center.y,
          size: arrowSize * 0.8,
          color: '#FF9800', // 橙色
          hoverColor: '#FFB74D',
          activeColor: '#F57C00'
        }
      ];
    } else {
      // 变换模式：原有的颜色方案
      return [
        // Y轴箭头（绿色，向上）
        {
          type: 'up' as ArrowType,
          x: center.x,
          y: center.y - arrowOffset,
          size: arrowSize,
          color: '#4CAF50', // 绿色
          hoverColor: '#66BB6A',
          activeColor: '#2E7D32'
        },
        // X轴箭头（红色，向右）
        {
          type: 'right' as ArrowType,
          x: center.x + arrowOffset,
          y: center.y,
          size: arrowSize,
          color: '#F44336', // 红色
          hoverColor: '#EF5350',
          activeColor: '#C62828'
        },
        // 中心方块（蓝色，自由移动）
        {
          type: 'center' as ArrowType,
          x: center.x,
          y: center.y,
          size: arrowSize * 0.6, // 稍小一些
          color: '#2196F3', // 蓝色
          hoverColor: '#42A5F5',
          activeColor: '#1565C0'
        }
      ];
    }
  }

  /**
   * 检测点击是否在箭头上
   */
  static hitTestArrow(
    x: number,
    y: number,
    arrowConfig: ArrowConfig,
    tolerance: number = 5
  ): boolean {
    const distance = Math.sqrt(
      Math.pow(x - arrowConfig.x, 2) + Math.pow(y - arrowConfig.y, 2)
    );
    return distance <= arrowConfig.size + tolerance;
  }

  /**
   * 检测点击是否在包围盒上
   */
  static hitTestBoundingBox(
    x: number,
    y: number,
    bounds: { x: number; y: number; width: number; height: number }
  ): boolean {
    return (
      x >= bounds.x &&
      x <= bounds.x + bounds.width &&
      y >= bounds.y &&
      y <= bounds.y + bounds.height
    );
  }
}
