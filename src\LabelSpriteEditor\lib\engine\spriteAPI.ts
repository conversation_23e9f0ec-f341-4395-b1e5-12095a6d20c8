/**
 * Sprite操作API
 * 封装RPG Maker的Sprite和Bitmap操作
 */

import { getEngineObjects, isEngineReady } from './engineLoader';
import type { bitmapProperties, Element, TextElement, ImageElement } from '../../types';

/**
 * 创建Sprite对象
 */
export function createSprite(bitmapData: bitmapProperties, spritePosition?: { x: number; y: number }): any {
  if (!isEngineReady()) {
    throw new Error('RPG Maker引擎尚未初始化');
  }

  const { Bitmap, Sprite } = getEngineObjects();

  // 创建Bitmap
  const bitmap = new Bitmap(816, 624);

  // 设置bitmap属性
  bitmap.fontBold = bitmapData.fontBold;
  bitmap.fontFace = bitmapData.fontFace;
  bitmap.fontItalic = bitmapData.fontItalic;
  bitmap.fontSize = bitmapData.fontSize;
  bitmap.outlineColor = bitmapData.outlineColor;
  bitmap.outlineWidth = bitmapData.outlineWidth;
  bitmap.textColor = bitmapData.textColor;
  bitmap._paintOpacity = bitmapData._paintOpacity;
  bitmap._smooth = bitmapData._smooth;

  // 初始化elements数组
  bitmap.elements = bitmapData.elements ? [...bitmapData.elements] : [];

  // 如果有背景图片URL，加载它
  if (bitmapData.url) {
    bitmap.url = bitmapData.url;
    // 这里可以添加背景图片加载逻辑
  }

  // 创建Sprite
  const sprite = new Sprite(bitmap);

  // 设置sprite坐标（如果提供了的话）
  if (spritePosition) {
    sprite.x = spritePosition.x;
    sprite.y = spritePosition.y;
    console.log('设置编辑器内部sprite坐标:', spritePosition);
  } else {
    sprite.x = 0;
    sprite.y = 0;
  }

  return sprite;
}

/**
 * 从Sprite提取bitmap数据
 */
export function extractBitmapData(sprite: any): bitmapProperties {
  if (!sprite || !sprite.bitmap) {
    throw new Error('无效的Sprite对象');
  }

  const bitmap = sprite.bitmap;

  return {
    fontBold: bitmap.fontBold || false,
    fontFace: bitmap.fontFace || 'Arial',
    fontItalic: bitmap.fontItalic || false,
    fontSize: bitmap.fontSize || 16,
    outlineColor: bitmap.outlineColor || '#000000',
    outlineWidth: bitmap.outlineWidth || 0,
    textColor: bitmap.textColor || '#ffffff',
    _paintOpacity: bitmap._paintOpacity || 255,
    _smooth: bitmap._smooth || false,
    elements: bitmap.elements ? [...bitmap.elements] : [],
    url: bitmap.url || undefined,
    regions: bitmap.regions ? [...bitmap.regions] : []  // 🔧 添加 regions 字段，深拷贝
  };
}

/**
 * 更新Sprite的bitmap数据
 */
export function updateSpriteData(sprite: any, bitmapData: bitmapProperties): void {
  if (!sprite || !sprite.bitmap) {
    throw new Error('无效的Sprite对象');
  }

  const bitmap = sprite.bitmap;

  // 更新bitmap属性
  bitmap.fontBold = bitmapData.fontBold;
  bitmap.fontFace = bitmapData.fontFace;
  bitmap.fontItalic = bitmapData.fontItalic;
  bitmap.fontSize = bitmapData.fontSize;
  bitmap.outlineColor = bitmapData.outlineColor;
  bitmap.outlineWidth = bitmapData.outlineWidth;
  bitmap.textColor = bitmapData.textColor;
  bitmap._paintOpacity = bitmapData._paintOpacity;
  bitmap._smooth = bitmapData._smooth;

  // 更新elements
  bitmap.elements = bitmapData.elements ? [...bitmapData.elements] : [];

  // 更新URL
  if (bitmapData.url) {
    bitmap.url = bitmapData.url;
  }
}

/**
 * 在bitmap上绘制文本元素
 */
export function drawTextElement(bitmap: any, element: TextElement): void {
  if (!bitmap || element.type !== 'text') {
    return;
  }

  // 保存当前字体设置
  const originalFontFace = bitmap.fontFace;
  const originalFontSize = bitmap.fontSize;
  const originalTextColor = bitmap.textColor;

  try {
    // 应用元素特定的字体设置（如果有的话）
    // 这里可以扩展支持每个元素的独立字体设置

    // 绘制文本
    bitmap.drawText(
      element.text,
      element.x,
      element.y,
      element.maxWidth,
      element.lineHeight,
      element.align
    );
  } finally {
    // 恢复原始字体设置
    bitmap.fontFace = originalFontFace;
    bitmap.fontSize = originalFontSize;
    bitmap.textColor = originalTextColor;
  }
}

/**
 * 在bitmap上绘制图像元素
 */
export function drawImageElement(bitmap: any, element: ImageElement): void {
  if (!bitmap || element.type !== 'image' || !element.source) {
    return;
  }

  // 如果source是字符串（URL），需要先加载图像
  if (typeof element.source === 'string') {
    // 使用RPG Maker的ImageManager加载图片
    try {
      console.log('开始加载图片:', element.source);

      // 检查ImageManager是否可用
      if (typeof window !== 'undefined' && (window as any).ImageManager) {
        const ImageManager = (window as any).ImageManager;

        // 使用ImageManager加载bitmap（会被CustomResourcePath插件处理）
        const sourceBitmap = ImageManager.loadBitmapFromUrl(element.source);

        if (sourceBitmap) {
          // 如果bitmap已经加载完成，直接绘制
          if (sourceBitmap.isReady && sourceBitmap.isReady()) {
            drawLoadedImage(bitmap, element, sourceBitmap);
          } else {
            // 等待bitmap加载完成
            if (typeof sourceBitmap.addLoadListener === 'function') {
              sourceBitmap.addLoadListener(() => {
                console.log('图片加载完成，开始绘制:', element.source);
                drawLoadedImage(bitmap, element, sourceBitmap);

                // 触发重新绘制事件，通知UI更新
                if (typeof window !== 'undefined') {
                  window.dispatchEvent(new CustomEvent('labelSpriteImageLoaded', {
                    detail: { source: element.source, bitmap: bitmap }
                  }));
                }
              });
            } else {
              // 备用方案：延迟绘制
              setTimeout(() => {
                if (sourceBitmap.isReady && sourceBitmap.isReady()) {
                  drawLoadedImage(bitmap, element, sourceBitmap);

                  // 触发重新绘制事件
                  if (typeof window !== 'undefined') {
                    window.dispatchEvent(new CustomEvent('labelSpriteImageLoaded', {
                      detail: { source: element.source, bitmap: bitmap }
                    }));
                  }
                }
              }, 100);
            }
          }
        } else {
          console.error('无法创建源bitmap:', element.source);
        }
      } else {
        console.error('ImageManager不可用，无法加载图片');
      }
    } catch (error) {
      console.error('加载图片失败:', element.source, error);
    }
    return;
  }

  // 如果source是已加载的图像对象
  drawLoadedImage(bitmap, element, element.source);
}

// 绘制已加载的图像
function drawLoadedImage(bitmap: any, element: ImageElement, sourceBitmap: any): void {
  try {
    if (element.sx !== undefined && element.sy !== undefined &&
        element.sw !== undefined && element.sh !== undefined) {
      // 使用源矩形绘制
      bitmap.blt(
        sourceBitmap,
        element.sx, element.sy, element.sw, element.sh,
        element.dx, element.dy, element.dw, element.dh
      );
    } else {
      // 简单绘制
      const sourceWidth = sourceBitmap.width || sourceBitmap._canvas?.width || 0;
      const sourceHeight = sourceBitmap.height || sourceBitmap._canvas?.height || 0;

      bitmap.blt(
        sourceBitmap,
        0, 0, sourceWidth, sourceHeight,
        element.dx, element.dy, element.dw || sourceWidth, element.dh || sourceHeight
      );
    }
    console.log('图像绘制成功:', element.source);
  } catch (error) {
    console.error('绘制图像元素失败:', error);
  }
}

/**
 * 重新绘制所有元素
 */
export function redrawAllElements(sprite: any): void {
  if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
    return;
  }

  const bitmap = sprite.bitmap;

  // 清空bitmap
  bitmap.clear();

  // 重新绘制所有元素
  for (const element of bitmap.elements) {
    if (element.type === 'text') {
      drawTextElement(bitmap, element as TextElement);
    } else if (element.type === 'image') {
      drawImageElement(bitmap, element as ImageElement);
    }
  }
}

/**
 * 添加元素到sprite
 */
export function addElementToSprite(sprite: any, element: Element): void {
  if (!sprite || !sprite.bitmap) {
    throw new Error('无效的Sprite对象');
  }

  const bitmap = sprite.bitmap;

  // 确保elements数组存在
  if (!bitmap.elements) {
    bitmap.elements = [];
  }

  // 添加元素
  bitmap.elements.push({ ...element });

  // 重新绘制
  if (element.type === 'text') {
    drawTextElement(bitmap, element as TextElement);
  } else if (element.type === 'image') {
    drawImageElement(bitmap, element as ImageElement);
  }
}

/**
 * 从sprite删除元素
 */
export function removeElementFromSprite(sprite: any, elementIndex: number): void {
  if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
    return;
  }

  const bitmap = sprite.bitmap;

  if (elementIndex < 0 || elementIndex >= bitmap.elements.length) {
    return;
  }

  // 删除元素
  bitmap.elements.splice(elementIndex, 1);

  // 重新绘制所有元素
  redrawAllElements(sprite);
}

/**
 * 更新sprite中的元素
 */
export function updateElementInSprite(sprite: any, elementIndex: number, updates: Partial<Element>): void {
  if (!sprite || !sprite.bitmap || !sprite.bitmap.elements) {
    return;
  }

  const bitmap = sprite.bitmap;

  if (elementIndex < 0 || elementIndex >= bitmap.elements.length) {
    return;
  }

  // 更新元素
  Object.assign(bitmap.elements[elementIndex], updates);

  // 重新绘制所有元素
  redrawAllElements(sprite);
}

/**
 * 获取sprite的canvas元素（用于显示）
 */
export function getSpriteCanvas(sprite: any): HTMLCanvasElement | null {
  if (!sprite || !sprite.bitmap || !sprite.bitmap._canvas) {
    return null;
  }

  return sprite.bitmap._canvas;
}
