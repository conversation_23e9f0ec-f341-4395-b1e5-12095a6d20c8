import { BaseObjectModel } from './baseObjectModel.svelte';

/**
 * 响应式容器模型类
 * 管理容器对象及其子对象的响应式状态
 */
export class ContainerModel extends BaseObjectModel {

    constructor(container: any) {
        super(container);

        // setupSync() 已经在基类构造函数中调用了
    }


    /**
     * 设置Container特有属性同步（重写基类方法）
     * Container通常没有特有属性需要同步
     */
    protected setupSpecificSync(): void {
        // Container通常没有特有的属性需要同步
        // 如果将来有Container特有的属性，在这里添加
        console.log('🔧 ContainerModel: Container特有属性已同步');
    }

    /**
     * 递归查找子对象（包括子容器中的对象）
     */
    findChildRecursive(predicate: (child: BaseObjectModel) => boolean): BaseObjectModel | null {
        // 首先在直接子对象中查找
        const directChild = this.findChild(predicate);
        if (directChild) return directChild;

        // 然后在子容器中递归查找
        for (const child of this.children) {
            if (child instanceof ContainerModel) {
                const found = child.findChildRecursive(predicate);
                if (found) return found;
            }
        }

        return null;
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        return `${indent}const ${varName} = new Container();`;
    }

    /**
     * 克隆Container对象（实现抽象方法）
     * @returns 克隆的ContainerModel实例
     */
    clone(): ContainerModel {
        console.log('🔄 ContainerModel: 开始克隆Container对象（手动创建）');

        // 1. 获取原始Container对象
        const originalContainer = this.getOriginalObject();
        if (!originalContainer) {
            console.error('❌ ContainerModel: 原始对象不存在');
            throw new Error('Container 原始对象不存在');
        }

        // 2. 🔑 手动创建新的Container对象（因为PIXI.Container没有clone方法）
        const clonedContainer = new PIXI.Container();

        // 复制基础属性
        clonedContainer.x = originalContainer.x + 20; // 偏移位置
        clonedContainer.y = originalContainer.y + 20;
        clonedContainer.visible = originalContainer.visible;
        clonedContainer.alpha = originalContainer.alpha;
        clonedContainer.rotation = originalContainer.rotation;
        clonedContainer.scale.x = originalContainer.scale.x;
        clonedContainer.scale.y = originalContainer.scale.y;

        // 3. 创建新的 ContainerModel 包装克隆的对象
        const clonedModel = new ContainerModel(clonedContainer);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;

                // 🔑 将克隆的子对象的原始对象添加到克隆的Container中
                const clonedChildOriginal = clonedChildModel.getOriginalObject();
                if (clonedChildOriginal) {
                    clonedContainer.addChild(clonedChildOriginal);
                }
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ ContainerModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }
}

// 注册ContainerModel到基类容器
BaseObjectModel.registerModel('Container', ContainerModel);