/**
 * 插件代码生成器 - 重构版本
 * 直接基于SceneModel生成RPG Maker MZ插件代码
 * 支持多个场景在一个插件中
 * 支持合并指定的插件文件
 */

import type { SceneModel } from '../type/senceModel.svelte';
import { TauriAPI } from '../lib/tauriAPI';

/**
 * 默认要包含的插件列表
 */
export const DEFAULT_INCLUDE_PLUGINS = [
  'UIAtlas.js',
  'uiBase.js',
  'uiButton.js',
  'uiSlider.js',
  'uiImage.js',
  'switch.js',
  'RPGEditor_BitmapTracker.js',
  'RPGEditor_DisableDefaultInput.js',
  // 'RPGEditor_PrototypeModifications.js',
  'spriteEventExpand.js',
];

/**
 * 插件生成配置
 */
export interface PluginGenerationConfig {
  pluginName?: string;
  pluginDescription?: string;
  author?: string;
  version?: string;
  includePlugins?: string[]; // 要包含的插件文件名列表
}

/**
 * 使用默认插件配置生成插件代码
 * @param sceneModels 场景模型Map，key为场景名称，value为SceneModel
 * @param config 插件生成配置
 * @returns 生成的插件代码
 */
export async function generatePluginCode(
  sceneModels: Map<string, SceneModel>,
  config: PluginGenerationConfig = {}
): Promise<string> {
  console.log('=== 开始生成插件代码 ===');

  const {
    pluginName = 'RPGEditor_GeneratedPlugin',
    pluginDescription = 'Auto-generated plugin from RPG Editor',
    author = 'RPG Editor',
    version = '1.0.0',
    includePlugins = DEFAULT_INCLUDE_PLUGINS
  } = config;

  // 生成插件头部
  const header = generatePluginHeader(pluginName, pluginDescription, author, version);

  // 读取并合并指定的插件文件
  const includedPluginsCode = await readAndMergePlugins(includePlugins);

  // 生成所有场景的处理代码
  const sceneCode = generateAllScenesCode(sceneModels);

  // 组装完整插件代码 - 插件在前，生成的代码在后
  const fullCode = [
    header,
    '',
    '(() => {',
    '  "use strict";',
    '',
    '  // ===== 包含的插件代码 =====',
    includedPluginsCode,
    '',
    '  // ===== 生成的场景代码 =====',
    '  // 插件初始化',
    '  console.log("RPG Editor Generated Plugin loaded");',
    '',
    sceneCode,
    '',
    '})();'
  ].join('\n');

  console.log('=== 插件代码生成完成 ===');
  return fullCode;
}

/**
 * 读取并合并指定的插件文件
 * @param pluginNames 要包含的插件文件名列表
 * @returns 合并后的插件代码
 */
async function readAndMergePlugins(pluginNames: string[]): Promise<string> {
  if (!pluginNames || pluginNames.length === 0) {
    return '';
  }

  console.log('=== 开始读取插件文件 ===', pluginNames);

  const pluginCodes: string[] = [];
  let successfulBasePath: string | null = null; // 缓存成功的基础路径

  for (const pluginName of pluginNames) {
    try {
      // 确保文件名有.js扩展名
      const fileName = pluginName.endsWith('.js') ? pluginName : `${pluginName}.js`;

      console.log(`读取插件文件: ${fileName}`);

      let content = null;
      let successPath = null;

      // 如果已经有成功的基础路径，优先使用
      if (successfulBasePath) {
        const cachedPath = `${successfulBasePath}${fileName}`;
        try {
          console.log(`使用缓存路径: ${cachedPath}`);
          const result = await TauriAPI.Project.readProjectFile(cachedPath);

          if (result.success && result.data) {
            content = result.data;
            successPath = cachedPath;
            console.log(`✓ 缓存路径成功: ${cachedPath}`);
          }
        } catch (error) {
          console.log(`✗ 缓存路径失败: ${cachedPath} - ${error}`);
        }
      }

      // 如果缓存路径失败，尝试所有可能的路径
      if (!content) {
        const possibleBasePaths = [
          `../src/engine/js/plugins/`,  // 已知成功的路径
          `src/engine/js/plugins/`,
          `engine/js/plugins/`,
          `./src/engine/js/plugins/`,
          `js/plugins/`
        ];

        for (const basePath of possibleBasePaths) {
          const fullPath = `${basePath}${fileName}`;
          try {
            console.log(`尝试路径: ${fullPath}`);
            const result = await TauriAPI.Project.readProjectFile(fullPath);

            if (result.success && result.data) {
              content = result.data;
              successPath = fullPath;
              successfulBasePath = basePath; // 缓存成功的基础路径
              console.log(`✓ 成功读取文件: ${fullPath}`);
              break;
            } else {
              console.log(`✗ 路径失败: ${fullPath} - ${result.error}`);
            }
          } catch (error) {
            console.log(`✗ 路径异常: ${fullPath} - ${error}`);
            continue;
          }
        }
      }

      if (content && content.trim() && successPath) {
        // 清理插件代码 - 移除可能的插件头部注释和外层包装
        const cleanedCode = cleanPluginCode(content, fileName);

        if (cleanedCode.trim()) {
          pluginCodes.push(`  // ===== ${fileName} =====`);
          pluginCodes.push(cleanedCode);
          pluginCodes.push('');
          console.log(`✓ 成功读取插件: ${fileName} (路径: ${successPath})`);
        } else {
          console.warn(`⚠ 插件文件为空或无效: ${fileName}`);
        }
      } else {
        console.error(`✗ 读取插件失败: ${fileName} - 所有路径都无法访问`);
      }
    } catch (error) {
      console.error(`✗ 读取插件异常: ${pluginName}`, error);
    }
  }

  console.log(`=== 插件读取完成，共读取 ${pluginCodes.length / 3} 个插件 ===`);
  return pluginCodes.join('\n');
}

/**
 * 清理插件代码，移除头部注释和外层包装
 * @param code 原始插件代码
 * @param fileName 文件名（用于日志）
 * @returns 清理后的代码
 */
function cleanPluginCode(code: string, fileName: string): string {
  let cleanedCode = code.trim();

  // 移除插件头部注释 (/*: ... */)
  cleanedCode = cleanedCode.replace(/^\/\*:[\s\S]*?\*\/\s*/m, '');

  // 移除外层的立即执行函数包装 (() => { ... })();
  const iifeMatcher = /^\(\(\)\s*=>\s*\{([\s\S]*)\}\)\(\);?\s*$/;
  const iifeMatch = cleanedCode.match(iifeMatcher);
  if (iifeMatch) {
    cleanedCode = iifeMatch[1];
    console.log(`  移除了 ${fileName} 的外层包装`);
  }

  // 移除 "use strict"; 声明（避免重复）
  cleanedCode = cleanedCode.replace(/^\s*["']use strict["'];\s*/m, '');

  // 确保代码有适当的缩进
  const lines = cleanedCode.split('\n');
  const indentedLines = lines.map(line => {
    if (line.trim() === '') return line;
    return '  ' + line;
  });

  return indentedLines.join('\n');
}

/**
 * 生成插件头部注释
 */
function generatePluginHeader(
  name: string,
  description: string,
  author: string,
  version: string
): string {
  return `/*:
 * @target MZ
 * @plugindesc ${name} v${version}
 * <AUTHOR>
 * @version ${version}
 * @description ${description}
 *
 * @help ${name}.js
 *
 * This plugin was automatically generated by RPG Editor.
 * It recreates the scene objects and UI elements based on saved data.
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 * Free for commercial and non-commercial use.
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 * Version ${version}: Initial release
 */`;
}

/**
 * 生成所有场景的处理代码
 */
function generateAllScenesCode(sceneModels: Map<string, SceneModel>): string {
  const sceneCodes: string[] = [];

  for (const [sceneName, sceneModel] of sceneModels.entries()) {
    console.log(`生成场景代码: ${sceneName}`);
    const code = generateSingleSceneCode(sceneName, sceneModel);
    sceneCodes.push(code);
  }

  return sceneCodes.join('\n\n');
}

/**
 * 递归生成所有对象的代码（包括嵌套的子对象）
 */
function generateAllObjectsCode(children: any[], indent: string): string {
  const codes: string[] = [];
  let objectCounter = 0;

  function processChildren(childrenArray: any[], currentIndent: string, parentVar?: string) {
    childrenArray.forEach((child) => {
      const varName = `obj_${child.name || child.className}_${objectCounter++}`;

      // 生成当前对象的创建代码
      codes.push(child.generateCreationCode(varName, currentIndent));

      // 添加到父对象
      if (parentVar) {
        codes.push(`${currentIndent}${parentVar}.addChild(${varName});`);
      } else {
        codes.push(`${currentIndent}this.addChild(${varName});`);
      }
      codes.push('');

      // 检查是否需要递归处理子对象
      // 如果generateChildrenCode为true，说明子对象已经在generateCreationCode中处理了，不需要重复处理
      if (child.children && child.children.length > 0 && child.generateChildrenCode === false) {
        codes.push(`${currentIndent}// 添加 ${varName} 的子对象`);
        processChildren(child.children, currentIndent, varName);
      } else if (child.children && child.children.length > 0 && child.generateChildrenCode !== false) {
        codes.push(`${currentIndent}// ${varName} 的子对象已在组件创建代码中处理`);
      }
    });
  }

  processChildren(children, indent);
  return codes.join('\n');
}

/**
 * 生成单个场景的代码
 */
function generateSingleSceneCode(sceneName: string, sceneModel: SceneModel): string {
  const codes: string[] = [];

  codes.push(`  // ===== ${sceneName} 场景处理 =====`);
  codes.push(`  ${sceneName}.prototype.create = function() {`);
  codes.push(`    Scene_Base.prototype.create.call(this);`);
  codes.push(`    console.log('RPG Editor: 开始创建 ${sceneName} 的自定义对象');`);
  codes.push('');

  // 递归生成所有子对象的代码
  const allObjectCodes = generateAllObjectsCode(sceneModel.children, '    ');
  codes.push(allObjectCodes);

  codes.push(`    console.log('RPG Editor: ${sceneName} 自定义对象创建完成');`);
  codes.push(`  };`);
  codes.push('');

  // 生成场景特定的方法重写
  codes.push(generateSceneSpecificMethodOverrides(sceneName));

  return codes.join('\n');
}











/**
 * 生成场景特定的方法重写
 */
function generateSceneSpecificMethodOverrides(sceneClassName: string): string {
  if (sceneClassName === 'Scene_Title') {
    return generateSceneTitleMethodOverrides();
  }

  if (sceneClassName === 'Scene_Map') {
    return generateSceneMapMethodOverrides();
  }

  return '';
}

/**
 * 生成 Scene_Title 特定的方法重写
 */
function generateSceneTitleMethodOverrides(): string {
  const code = [
    `  // ===== Scene_Title 方法重写 =====`,
    `  // 重写 createBackground - 跳过原生背景创建`,
    `  Scene_Title.prototype.createBackground = function() {`,
    `    console.log('RPG Editor: 跳过原生背景创建，使用编辑器对象');`,
    `  };`,
    '',
    `  // 重写 createForeground - 跳过原生前景创建`,
    `  Scene_Title.prototype.createForeground = function() {`,
    `    console.log('RPG Editor: 跳过原生前景创建，使用编辑器对象');`,
    `  };`,
    '',
    `  // 重写 adjustBackground - 避免访问不存在的背景精灵`,
    `  Scene_Title.prototype.adjustBackground = function() {`,
    `    console.log('RPG Editor: 跳过背景调整，使用编辑器设置的属性');`,
    `  };`,
    '',
    `  // 重写 createCommandWindow - 跳过原生命令窗口创建`,
    `  Scene_Title.prototype.createCommandWindow = function() {`,
    `    console.log('RPG Editor: 跳过原生命令窗口创建，使用编辑器对象');`,
    `  };`,
    '',
    `  // 重写 isBusy - 安全检查命令窗口状态`,
    `  Scene_Title.prototype.isBusy = function() {`,
    `    if (this._commandWindow && this._commandWindow.isClosing) {`,
    `      return this._commandWindow.isClosing();`,
    `    }`,
    `    return Scene_Base.prototype.isBusy.call(this);`,
    `  };`,
    '',
    `  // 重写 update - 安全检查命令窗口`,
    `  Scene_Title.prototype.update = function() {`,
    `    if (!this.isBusy() && this._commandWindow && this._commandWindow.open) {`,
    `      this._commandWindow.open();`,
    `    }`,
    `    Scene_Base.prototype.update.call(this);`,
    `  };`,
    '',
    `  // 重写 terminate - 安全检查游戏标题精灵`,
    `  Scene_Title.prototype.terminate = function() {`,
    `    Scene_Base.prototype.terminate.call(this);`,
    `    SceneManager.snapForBackground();`,
    `    if (this._gameTitleSprite && this._gameTitleSprite.bitmap) {`,
    `      this._gameTitleSprite.bitmap.destroy();`,
    `    }`,
    `  };`,
    '',
    `  // 重写 scaleSprite - 安全检查精灵对象`,
    `  Scene_Title.prototype.scaleSprite = function(sprite) {`,
    `    if (sprite && sprite.bitmap) {`,
    `      Scene_Base.prototype.scaleSprite.call(this, sprite);`,
    `    }`,
    `  };`,
    '',
    `  // 重写 centerSprite - 安全检查精灵对象`,
    `  Scene_Title.prototype.centerSprite = function(sprite) {`,
    `    if (sprite && sprite.bitmap) {`,
    `      Scene_Base.prototype.centerSprite.call(this, sprite);`,
    `    }`,
    `  };`
  ];

  return code.join('\n');
}

/**
 * 生成 Scene_Map 特定的方法重写
 */
function generateSceneMapMethodOverrides(): string {
  const code = [
    `  // ===== Scene_Map 方法重写 =====`,
    `  // 重写 createSpriteset - 跳过原生精灵集创建`,
    `  Scene_Map.prototype.createSpriteset = function() {`,
    `    console.log('RPG Editor: 跳过原生精灵集创建，使用编辑器对象');`,
    `  };`,
    '',
    `  // 重写 createAllWindows - 跳过原生窗口创建`,
    `  Scene_Map.prototype.createAllWindows = function() {`,
    `    console.log('RPG Editor: 跳过原生窗口创建，使用编辑器对象');`,
    `  };`,
    '',
    `  // 重写 update - 安全检查精灵集`,
    `  Scene_Map.prototype.update = function() {`,
    `    Scene_Base.prototype.update.call(this);`,
    `    if (this._spriteset && this._spriteset.update) {`,
    `      this._spriteset.update();`,
    `    }`,
    `  };`,
    '',
    `  // 重写 updateTransferPlayer - 安全检查`,
    `  Scene_Map.prototype.updateTransferPlayer = function() {`,
    `    if ($gamePlayer.isTransferring()) {`,
    `      SceneManager.goto(Scene_Map);`,
    `    }`,
    `  };`
  ];

  return code.join('\n');
}
