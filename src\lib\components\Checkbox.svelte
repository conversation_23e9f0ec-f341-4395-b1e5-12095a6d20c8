<script lang="ts">
  /**
   * Checkbox 多选框组件
   * 基于 Skeleton UI 和全局主题色彩
   */
  
  // Props
  export let checked: boolean = false;
  export let indeterminate: boolean = false;
  export let disabled: boolean = false;
  export let required: boolean = false;
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let variant: 'default' | 'success' | 'warning' | 'error' = 'default';
  export let label: string = '';
  export let description: string = '';
  export let value: string | number = '';
  export let name: string = '';
  export let id: string = '';
  export let ariaLabel: string = '';
  export let ariaDescribedBy: string = '';
  
  // Events
  export let onChange: (checked: boolean, event: Event) => void = () => {};
  export let onFocus: (event: FocusEvent) => void = () => {};
  export let onBlur: (event: FocusEvent) => void = () => {};
  
  // 内部状态
  let checkboxElement: HTMLInputElement;
  
  // 处理变化事件
  function handleChange(event: Event) {
    const target = event.target as HTMLInputElement;
    checked = target.checked;
    onChange(checked, event);
  }
  
  // 获取容器样式类
  function getContainerClass() {
    const baseClass = 'checkbox-container';
    const sizeClass = `checkbox-${size}`;
    const variantClass = `checkbox-${variant}`;
    const checkedClass = checked ? 'checkbox-checked' : '';
    const indeterminateClass = indeterminate ? 'checkbox-indeterminate' : '';
    const disabledClass = disabled ? 'checkbox-disabled' : '';
    
    return [baseClass, sizeClass, variantClass, checkedClass, indeterminateClass, disabledClass]
      .filter(Boolean)
      .join(' ');
  }
  
  // 公开方法
  export function focus() {
    checkboxElement?.focus();
  }
  
  export function blur() {
    checkboxElement?.blur();
  }
  
  // 监听 indeterminate 状态变化
  $effect(() => {
    if (checkboxElement) {
      checkboxElement.indeterminate = indeterminate;
    }
  });
</script>

<label class={getContainerClass()}>
  <input
    bind:this={checkboxElement}
    type="checkbox"
    bind:checked
    {disabled}
    {required}
    {value}
    {name}
    {id}
    aria-label={ariaLabel}
    aria-describedby={ariaDescribedBy}
    class="checkbox-input"
    onchange={handleChange}
    onfocus={onFocus}
    onblur={onBlur}
  />
  
  <div class="checkbox-indicator">
    <div class="checkbox-icon">
      {#if indeterminate}
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
          <path d="M2 6H10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      {:else if checked}
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
          <path d="M2 6L5 9L10 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      {/if}
    </div>
  </div>
  
  {#if label || description || $$slots.default}
    <div class="checkbox-content">
      {#if label}
        <div class="checkbox-label">{label}</div>
      {/if}
      
      <slot />
      
      {#if description}
        <div class="checkbox-description">{description}</div>
      {/if}
    </div>
  {/if}
</label>

<style>
  .checkbox-container {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-2);
    cursor: pointer;
    transition: var(--transition-base);
    padding: var(--spacing-1);
    border-radius: var(--border-radius);
  }
  
  .checkbox-container:hover:not(.checkbox-disabled) {
    background-color: var(--theme-surface-light);
  }
  
  .checkbox-container.checkbox-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .checkbox-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .checkbox-indicator {
    position: relative;
    flex-shrink: 0;
    border: 2px solid var(--theme-border);
    border-radius: var(--border-radius-small);
    background-color: var(--theme-surface);
    transition: var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .checkbox-container:hover:not(.checkbox-disabled) .checkbox-indicator {
    border-color: var(--theme-primary);
  }
  
  .checkbox-checked .checkbox-indicator,
  .checkbox-indeterminate .checkbox-indicator {
    border-color: var(--theme-primary);
    background-color: var(--theme-primary);
  }
  
  .checkbox-icon {
    color: var(--theme-text-inverse);
    opacity: 0;
    transform: scale(0);
    transition: var(--transition-fast);
  }
  
  .checkbox-checked .checkbox-icon,
  .checkbox-indeterminate .checkbox-icon {
    opacity: 1;
    transform: scale(1);
  }
  
  .checkbox-content {
    flex: 1;
    min-width: 0;
  }
  
  .checkbox-label {
    color: var(--theme-text);
    font-family: var(--font-family-base);
    font-weight: 500;
    line-height: 1.4;
  }
  
  .checkbox-description {
    color: var(--theme-text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
    margin-top: var(--spacing-1);
  }
  
  /* 尺寸变体 */
  .checkbox-sm .checkbox-indicator {
    width: 16px;
    height: 16px;
  }
  
  .checkbox-sm .checkbox-icon svg {
    width: 10px;
    height: 10px;
  }
  
  .checkbox-sm .checkbox-label {
    font-size: var(--font-size-sm);
  }
  
  .checkbox-md .checkbox-indicator {
    width: 20px;
    height: 20px;
  }
  
  .checkbox-md .checkbox-icon svg {
    width: 12px;
    height: 12px;
  }
  
  .checkbox-md .checkbox-label {
    font-size: var(--font-size-base);
  }
  
  .checkbox-lg .checkbox-indicator {
    width: 24px;
    height: 24px;
  }
  
  .checkbox-lg .checkbox-icon svg {
    width: 14px;
    height: 14px;
  }
  
  .checkbox-lg .checkbox-label {
    font-size: var(--font-size-lg);
  }
  
  /* 状态变体 */
  .checkbox-success .checkbox-checked .checkbox-indicator,
  .checkbox-success .checkbox-indeterminate .checkbox-indicator {
    border-color: var(--theme-success);
    background-color: var(--theme-success);
  }
  
  .checkbox-warning .checkbox-checked .checkbox-indicator,
  .checkbox-warning .checkbox-indeterminate .checkbox-indicator {
    border-color: var(--theme-warning);
    background-color: var(--theme-warning);
  }
  
  .checkbox-error .checkbox-checked .checkbox-indicator,
  .checkbox-error .checkbox-indeterminate .checkbox-indicator {
    border-color: var(--theme-error);
    background-color: var(--theme-error);
  }
  
  /* 焦点样式 */
  .checkbox-input:focus-visible + .checkbox-indicator {
    outline: 2px solid var(--theme-primary);
    outline-offset: 2px;
  }
</style>
