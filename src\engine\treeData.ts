/**
 * RPG Maker MZ 类数据定义
 */


// 基类型枚举
export enum BaseClassType {
  GAME_OBJECTS = 'Game Objects',      // Game_* 系列
  SCENES = 'Scenes',                  // Scene_* 系列
  WINDOWS = 'Windows',                // Window_* 系列
  MANAGERS = 'Managers',              // *Manager 系列
  CORE_CLASSES = 'Core Classes',      // 核心基础类
  INPUT_SYSTEM = 'Input System',      // 输入处理类
  GRAPHICS = 'Graphics',              // 图形渲染类
  UTILITIES = 'Utilities'             // 工具类
}

// 类信息接口
export interface ClassInfo {
  name: string;
  type: 'class' | 'static' | 'interface';
  category: BaseClassType;
  description: string;
  ico: string; // 图标标识
  properties?: string[];
  methods?: string[];
  parent?: string;
  children: string[];
  isAbstract?: boolean;
  level?: number; // 在继承树中的层级
}

/**
 * RPG Maker MZ 所有类的数据映射
 */
export const RPG_MAKER_CLASSES = new Map<string, ClassInfo>([
  // ===== Game Objects =====
  ['Game_Temp', {
    name: 'Game_Temp',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '临时游戏数据管理',
    ico: '🗂️',
    children: []
  }],

  ['Game_System', {
    name: 'Game_System',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '系统数据管理',
    ico: '⚙️',
    children: []
  }],

  ['Game_BattlerBase', {
    name: 'Game_BattlerBase',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '战斗者基类',
    ico: '⚔️',
    children: ['Game_Battler']
  }],

  ['Game_Battler', {
    name: 'Game_Battler',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '战斗者类',
    ico: '🛡️',
    parent: 'Game_BattlerBase',
    children: ['Game_Actor', 'Game_Enemy']
  }],

  ['Game_Actor', {
    name: 'Game_Actor',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '角色类',
    ico: '👤',
    parent: 'Game_Battler',
    children: []
  }],

  ['Game_Enemy', {
    name: 'Game_Enemy',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '敌人类',
    ico: '👹',
    parent: 'Game_Battler',
    children: []
  }],

  ['Game_Unit', {
    name: 'Game_Unit',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '单位基类',
    ico: '👥',
    children: ['Game_Party', 'Game_Troop']
  }],

  ['Game_Party', {
    name: 'Game_Party',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '队伍类',
    ico: '🎭',
    parent: 'Game_Unit',
    children: []
  }],

  ['Game_Troop', {
    name: 'Game_Troop',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '敌群类',
    ico: '👺',
    parent: 'Game_Unit',
    children: []
  }],

  ['Game_CharacterBase', {
    name: 'Game_CharacterBase',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '角色基类',
    ico: '🚶',
    children: ['Game_Character']
  }],

  ['Game_Character', {
    name: 'Game_Character',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '角色类',
    ico: '🎮',
    parent: 'Game_CharacterBase',
    children: ['Game_Player', 'Game_Follower', 'Game_Vehicle', 'Game_Event']
  }],

  ['Game_Player', {
    name: 'Game_Player',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '玩家角色类',
    ico: '🎯',
    parent: 'Game_Character',
    children: []
  }],

  ['Game_Follower', {
    name: 'Game_Follower',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '跟随者类',
    ico: '👣',
    parent: 'Game_Character',
    children: []
  }],

  ['Game_Vehicle', {
    name: 'Game_Vehicle',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '载具类',
    ico: '🚗',
    parent: 'Game_Character',
    children: []
  }],

  ['Game_Event', {
    name: 'Game_Event',
    type: 'class',
    category: BaseClassType.GAME_OBJECTS,
    description: '事件类',
    ico: '📅',
    parent: 'Game_Character',
    children: []
  }],

  // ===== Scenes =====
  ['Scene_Base', {
    name: 'Scene_Base',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '场景基类',
    ico: '🎬',
    children: ['Scene_Boot', 'Scene_Title', 'Scene_Map', 'Scene_MenuBase', 'Scene_Battle']
  }],

  ['Scene_Boot', {
    name: 'Scene_Boot',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '启动场景',
    ico: '🚀',
    parent: 'Scene_Base',
    children: []
  }],

  ['Scene_Title', {
    name: 'Scene_Title',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '标题场景',
    ico: '🏠',
    parent: 'Scene_Base',
    children: []
  }],

  ['Scene_Map', {
    name: 'Scene_Map',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '地图场景',
    ico: '🗺️',
    parent: 'Scene_Base',
    children: []
  }],

  ['Scene_MenuBase', {
    name: 'Scene_MenuBase',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '菜单基类',
    ico: '📋',
    parent: 'Scene_Base',
    children: ['Scene_Menu', 'Scene_ItemBase', 'Scene_Status', 'Scene_Options', 'Scene_File']
  }],

  ['Scene_Menu', {
    name: 'Scene_Menu',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '主菜单场景',
    ico: '📱',
    parent: 'Scene_MenuBase',
    children: []
  }],

  ['Scene_ItemBase', {
    name: 'Scene_ItemBase',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '物品基类场景',
    ico: '📦',
    parent: 'Scene_MenuBase',
    children: ['Scene_Item', 'Scene_Skill', 'Scene_Equip']
  }],

  ['Scene_Item', {
    name: 'Scene_Item',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '物品场景',
    ico: '🎒',
    parent: 'Scene_ItemBase',
    children: []
  }],

  ['Scene_Skill', {
    name: 'Scene_Skill',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '技能场景',
    ico: '✨',
    parent: 'Scene_ItemBase',
    children: []
  }],

  ['Scene_Equip', {
    name: 'Scene_Equip',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '装备场景',
    ico: '⚔️',
    parent: 'Scene_ItemBase',
    children: []
  }],

  ['Scene_Status', {
    name: 'Scene_Status',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '状态场景',
    ico: '📊',
    parent: 'Scene_MenuBase',
    children: []
  }],

  ['Scene_Options', {
    name: 'Scene_Options',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '选项场景',
    ico: '⚙️',
    parent: 'Scene_MenuBase',
    children: []
  }],

  ['Scene_File', {
    name: 'Scene_File',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '文件场景基类',
    ico: '📁',
    parent: 'Scene_MenuBase',
    children: ['Scene_Save', 'Scene_Load']
  }],

  ['Scene_Save', {
    name: 'Scene_Save',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '保存场景',
    ico: '💾',
    parent: 'Scene_File',
    children: []
  }],

  ['Scene_Load', {
    name: 'Scene_Load',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '读取场景',
    ico: '📂',
    parent: 'Scene_File',
    children: []
  }],

  ['Scene_Battle', {
    name: 'Scene_Battle',
    type: 'class',
    category: BaseClassType.SCENES,
    description: '战斗场景',
    ico: '⚔️',
    parent: 'Scene_Base',
    children: []
  }],

  // ===== Windows =====
  ['Window_Base', {
    name: 'Window_Base',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '窗口基类',
    ico: '🪟',
    children: ['Window_Scrollable', 'Window_Gold', 'Window_StatusBase', 'Window_Message']
  }],

  ['Window_Scrollable', {
    name: 'Window_Scrollable',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '可滚动窗口',
    ico: '📜',
    parent: 'Window_Base',
    children: ['Window_Selectable', 'Window_Help']
  }],

  ['Window_Selectable', {
    name: 'Window_Selectable',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '可选择窗口',
    ico: '🎯',
    parent: 'Window_Scrollable',
    children: ['Window_Command', 'Window_ItemList', 'Window_SavefileList']
  }],

  ['Window_Command', {
    name: 'Window_Command',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '命令窗口',
    ico: '🎮',
    parent: 'Window_Selectable',
    children: ['Window_HorzCommand', 'Window_TitleCommand', 'Window_PartyCommand']
  }],

  ['Window_HorzCommand', {
    name: 'Window_HorzCommand',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '水平命令窗口',
    ico: '↔️',
    parent: 'Window_Command',
    children: ['Window_MenuCommand', 'Window_ItemCategory']
  }],

  ['Window_MenuCommand', {
    name: 'Window_MenuCommand',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '菜单命令窗口',
    ico: '📋',
    parent: 'Window_HorzCommand',
    children: []
  }],

  ['Window_ItemCategory', {
    name: 'Window_ItemCategory',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '物品分类窗口',
    ico: '🏷️',
    parent: 'Window_HorzCommand',
    children: []
  }],

  ['Window_TitleCommand', {
    name: 'Window_TitleCommand',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '标题命令窗口',
    ico: '🏠',
    parent: 'Window_Command',
    children: []
  }],

  ['Window_PartyCommand', {
    name: 'Window_PartyCommand',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '队伍命令窗口',
    ico: '👥',
    parent: 'Window_Command',
    children: []
  }],

  ['Window_ItemList', {
    name: 'Window_ItemList',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '物品列表窗口',
    ico: '📝',
    parent: 'Window_Selectable',
    children: []
  }],

  ['Window_SavefileList', {
    name: 'Window_SavefileList',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '存档列表窗口',
    ico: '💾',
    parent: 'Window_Selectable',
    children: []
  }],

  ['Window_Help', {
    name: 'Window_Help',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '帮助窗口',
    ico: '❓',
    parent: 'Window_Scrollable',
    children: []
  }],

  ['Window_Gold', {
    name: 'Window_Gold',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '金钱窗口',
    ico: '💰',
    parent: 'Window_Base',
    children: []
  }],

  ['Window_StatusBase', {
    name: 'Window_StatusBase',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '状态基类窗口',
    ico: '📊',
    parent: 'Window_Base',
    children: ['Window_MenuStatus']
  }],

  ['Window_MenuStatus', {
    name: 'Window_MenuStatus',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '菜单状态窗口',
    ico: '📈',
    parent: 'Window_StatusBase',
    children: []
  }],

  ['Window_Message', {
    name: 'Window_Message',
    type: 'class',
    category: BaseClassType.WINDOWS,
    description: '消息窗口',
    ico: '💬',
    parent: 'Window_Base',
    children: []
  }],

  // ===== Managers =====
  ['DataManager', {
    name: 'DataManager',
    type: 'static',
    category: BaseClassType.MANAGERS,
    description: '数据管理器',
    ico: '🗃️',
    children: []
  }],

  ['ConfigManager', {
    name: 'ConfigManager',
    type: 'static',
    category: BaseClassType.MANAGERS,
    description: '配置管理器',
    ico: '🔧',
    children: []
  }],

  ['StorageManager', {
    name: 'StorageManager',
    type: 'static',
    category: BaseClassType.MANAGERS,
    description: '存储管理器',
    ico: '💽',
    children: []
  }],

  ['ImageManager', {
    name: 'ImageManager',
    type: 'static',
    category: BaseClassType.MANAGERS,
    description: '图像管理器',
    ico: '🖼️',
    children: []
  }],

  ['AudioManager', {
    name: 'AudioManager',
    type: 'static',
    category: BaseClassType.MANAGERS,
    description: '音频管理器',
    ico: '🔊',
    children: []
  }]
]);
