<script lang="ts">
  import type { ImageElement } from '../types';
  import LabelInput from '../../components/LabelInput.svelte';
  import ImageCropPanel from './ImageCropPanel.svelte';
  import { selectImageFile } from '../../right/propertyLogic/spriteImageLogic';

  // Props
  let {
    element = $bindable(null as ImageElement | null),
    onChange = () => {}
  }: {
    element?: ImageElement | null;
    onChange?: (key: string, value: any) => void;
  } = $props();

  // 处理属性变化
  function handleChange(key: string, value: any) {
    if (!element) return;
    onChange(key, value);
  }

  // 处理图片选择
  async function handleImageSelect() {
    if (!element) return;

    try {
      console.log('开始选择图片...');
      const result = await selectImageFile(element.source);

      if (result.success && result.relativePath) {
        console.log('图片选择成功:', result.relativePath);
        handleChange('source', result.relativePath);
      } else {
        console.log('图片选择取消或失败:', result.error);
      }
    } catch (error) {
      console.error('图片选择失败:', error);
    }
  }

  // 处理ImageCropPanel的onChange事件
  function handleImageCropChange(updates: Partial<ImageElement>) {
    if (!element) return;

    // 将updates对象的每个属性单独调用handleChange
    for (const [key, value] of Object.entries(updates)) {
      handleChange(key, value);
    }
  }
</script>

<div class="image-element-panel">
  {#if element}
    <!-- 图片预览与裁切 -->
    <div class="property-section">
      <h4>图片预览与裁切</h4>
      <div class="property-group vertical">
        <ImageCropPanel
          bind:element={element}
          onChange={handleImageCropChange}
        />
        {#if element.source}
          <div class="image-path-info">
            <span class="path-label">路径:</span>
            <span class="path-value">{element.source}</span>
            <button
              class="change-image-btn"
              onclick={handleImageSelect}
            >
              更换图片
            </button>
          </div>
        {:else}
          <button
            class="select-image-btn"
            onclick={handleImageSelect}
          >
            选择图片
          </button>
        {/if}
      </div>
    </div>

    <!-- 位置属性 -->
    <div class="property-section">
      <h4>位置属性</h4>
      <div class="property-row">
        <div class="property-group">
          <label>X偏移:</label>
          <LabelInput
            value={element.dx || 0}
            type="number"
            size="sm"
            onChange={(newValue) => handleChange('dx', typeof newValue === 'number' ? newValue : parseInt(newValue as string))}
            targetObject={element}
            fieldName="dx"
            name="X偏移"
          />
        </div>
        <div class="property-group">
          <label>Y偏移:</label>
          <LabelInput
            value={element.dy || 0}
            type="number"
            size="sm"
            onChange={(newValue) => handleChange('dy', typeof newValue === 'number' ? newValue : parseInt(newValue as string))}
            targetObject={element}
            fieldName="dy"
            name="Y偏移"
          />
        </div>
      </div>
    </div>

    <!-- 尺寸属性 -->
    <div class="property-section">
      <h4>尺寸属性</h4>
      <div class="property-row">
        <div class="property-group">
          <label>宽度:</label>
          <LabelInput
            value={element.dw || element.sw || 1}
            type="number"
            size="sm"
            min={1}
            onChange={(newValue) => handleChange('dw', typeof newValue === 'number' ? newValue : parseInt(newValue as string))}
            targetObject={element}
            fieldName="dw"
            name="显示宽度"
          />
        </div>
        <div class="property-group">
          <label>高度:</label>
          <LabelInput
            value={element.dh || element.sh || 1}
            type="number"
            size="sm"
            min={1}
            onChange={(newValue) => handleChange('dh', typeof newValue === 'number' ? newValue : parseInt(newValue as string))}
            targetObject={element}
            fieldName="dh"
            name="显示高度"
          />
        </div>
      </div>
    </div>

    <!-- 源图片裁剪属性 -->
    <div class="property-section">
      <h4>源图片裁剪</h4>
      <div class="property-row">
        <div class="property-group">
          <label>源X:</label>
          <LabelInput
            value={element.sx || 0}
            type="number"
            size="sm"
            onChange={(newValue) => handleChange('sx', typeof newValue === 'number' ? newValue : parseInt(newValue as string))}
            targetObject={element}
            fieldName="sx"
            name="源X坐标"
          />
        </div>
        <div class="property-group">
          <label>源Y:</label>
          <LabelInput
            value={element.sy || 0}
            type="number"
            size="sm"
            onChange={(newValue) => handleChange('sy', typeof newValue === 'number' ? newValue : parseInt(newValue as string))}
            targetObject={element}
            fieldName="sy"
            name="源Y坐标"
          />
        </div>
      </div>
      <div class="property-row">
        <div class="property-group">
          <label>源宽度:</label>
          <LabelInput
            value={element.sw || 1}
            type="number"
            size="sm"
            min={1}
            onChange={(newValue) => handleChange('sw', typeof newValue === 'number' ? newValue : parseInt(newValue as string))}
            targetObject={element}
            fieldName="sw"
            name="源宽度"
          />
        </div>
        <div class="property-group">
          <label>源高度:</label>
          <LabelInput
            value={element.sh || 1}
            type="number"
            size="sm"
            min={1}
            onChange={(newValue) => handleChange('sh', typeof newValue === 'number' ? newValue : parseInt(newValue as string))}
            targetObject={element}
            fieldName="sh"
            name="源高度"
          />
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .image-element-panel {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2, 0.5rem);
  }

  .property-section {
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: var(--border-radius, 4px);
    overflow: hidden;
  }

  .property-section h4 {
    margin: 0;
    padding: var(--spacing-2, 0.5rem);
    background: var(--theme-surface-light, #f9fafb);
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    font-size: var(--font-size-xs, 0.75rem);
    font-weight: 600;
    color: var(--theme-text, #111827);
  }

  .property-section > .property-group,
  .property-section > .property-row {
    padding: var(--spacing-2, 0.5rem);
  }

  .property-section > .property-group:not(:last-child),
  .property-section > .property-row:not(:last-child) {
    border-bottom: 1px solid var(--theme-border-light, #f3f4f6);
  }

  .property-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-1, 0.25rem);
  }

  .property-group.vertical {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  .property-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2, 0.5rem);
  }

  .property-group label {
    font-size: var(--font-size-xs, 0.75rem);
    font-weight: 500;
    color: var(--theme-text, #111827);
    white-space: nowrap;
    flex-shrink: 0;
    min-width: fit-content;
  }

  /* 图片路径信息 */
  .image-path-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-1, 0.25rem);
    padding: var(--spacing-2, 0.5rem);
    background: var(--theme-surface-light, #f9fafb);
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: var(--border-radius, 4px);
    font-size: var(--font-size-xs, 0.75rem);
  }

  .path-label {
    font-weight: 600;
    color: var(--theme-text, #111827);
  }

  .path-value {
    color: var(--theme-text-secondary, #6b7280);
    word-break: break-all;
    font-family: monospace;
    flex: 1;
  }

  .change-image-btn,
  .select-image-btn {
    padding: var(--spacing-1, 0.25rem) var(--spacing-2, 0.5rem);
    background: var(--theme-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: var(--border-radius, 4px);
    cursor: pointer;
    font-size: var(--font-size-xs, 0.75rem);
    white-space: nowrap;
    margin-left: var(--spacing-2, 0.5rem);
  }

  .change-image-btn:hover,
  .select-image-btn:hover {
    background: var(--theme-primary-dark, #2563eb);
  }
</style>
