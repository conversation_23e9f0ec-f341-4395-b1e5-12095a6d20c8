/**
 * UILayout - 通用布局管理器
 * 支持垂直、水平、网格等多种布局方式
 * 继承自PIXI.Container，专注于子元素的位置管理
 */

(() => {
    'use strict';

    // 确保 PIXI 可用
    if (typeof PIXI === 'undefined') {
        console.error('UILayout: PIXI 未找到');
        return;
    }

    /**
     * UILayout - 通用布局容器
     */
    class UILayout extends PIXI.Container {
        constructor(properties = {}) {
            super();

            console.log('📐 UILayout: 创建布局管理器', properties);

            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'UILayout';

            // 布局类型：'vertical', 'horizontal', 'grid'
            this.layoutType = properties.layoutType || 'vertical';

            // 基础布局参数
            this.spacing = properties.spacing || 5;              // 项目间距
            this.padding = properties.padding || 0;              // 内边距
            this.horizontalSpacing = properties.horizontalSpacing || this.spacing; // 水平间距
            this.verticalSpacing = properties.verticalSpacing || this.spacing;     // 垂直间距

            // 网格布局参数
            this.columns = properties.columns || 2;              // 网格列数
            this.rows = properties.rows || 0;                    // 网格行数（0表示自动）

            // 对齐方式
            this.mainAxisAlignment = properties.mainAxisAlignment || 'start';     // 主轴对齐：start, center, end, space-between, space-around
            this.crossAxisAlignment = properties.crossAxisAlignment || 'start';   // 交叉轴对齐：start, center, end, stretch

            // 容器尺寸（0表示自动计算）
            this.containerWidth = properties.width || 0;
            this.containerHeight = properties.height || 0;

            // 自动更新布局
            this.autoUpdate = properties.autoUpdate !== false;

            // 布局状态
            this._needsLayout = true;
            this._isUpdatingLayout = false;

            // 设置事件监听器
            this.setupEventListeners();

            console.log('✅ UILayout: 布局管理器创建完成', {
                layoutType: this.layoutType,
                spacing: this.spacing,
                autoUpdate: this.autoUpdate
            });
        }

        /**
         * 设置事件监听器
         */
        setupEventListeners() {
            // 🔑 重写update方法，确保子元素的update被调用
            if (!this._originalUpdate) {
                this._originalUpdate = this.update;
            }

            this.update = function() {
                // 调用原始的 update 方法
                if (this._originalUpdate) {
                    this._originalUpdate.call(this);
                }

                // 🔑 确保所有子元素的update方法被调用
                if (this.children && this.children.length > 0) {
                    this.children.forEach(child => {
                        if (child && typeof child.update === 'function') {
                            try {
                                child.update();
                            } catch (error) {
                                console.warn('UILayout子元素update失败:', error);
                            }
                        }
                    });
                }

                // 检查是否需要更新布局
                if (this._needsLayout && !this._isUpdatingLayout) {
                    this.updateLayout();
                }
            };

            console.log('✅ UILayout: 事件监听器设置完成');
        }

        /**
         * 添加子元素并更新布局
         */
        addChild(child) {
            const result = super.addChild(child);
            
            if (this.autoUpdate) {
                this.requestLayoutUpdate();
            }
            
            console.log('📐 UILayout: 添加子元素', {
                childType: child.constructor.name,
                totalChildren: this.children.length
            });
            
            return result;
        }

        /**
         * 移除子元素并更新布局
         */
        removeChild(child) {
            const result = super.removeChild(child);
            
            if (this.autoUpdate) {
                this.requestLayoutUpdate();
            }
            
            console.log('📐 UILayout: 移除子元素', {
                totalChildren: this.children.length
            });
            
            return result;
        }

        /**
         * 请求布局更新（异步）
         */
        requestLayoutUpdate() {
            this._needsLayout = true;
        }

        /**
         * 立即更新布局
         */
        updateLayout() {
            if (this._isUpdatingLayout) return;
            
            this._isUpdatingLayout = true;
            this._needsLayout = false;

            console.log('📐 UILayout: 开始更新布局', {
                layoutType: this.layoutType,
                childrenCount: this.children.length
            });

            try {
                switch (this.layoutType) {
                    case 'vertical':
                        this.updateVerticalLayout();
                        break;
                    case 'horizontal':
                        this.updateHorizontalLayout();
                        break;
                    case 'grid':
                        this.updateGridLayout();
                        break;
                    default:
                        console.warn('UILayout: 未知的布局类型', this.layoutType);
                        this.updateVerticalLayout(); // 默认使用垂直布局
                }

                console.log('✅ UILayout: 布局更新完成');
            } catch (error) {
                console.error('UILayout: 布局更新失败', error);
            } finally {
                this._isUpdatingLayout = false;
            }
        }

        /**
         * 垂直布局
         */
        updateVerticalLayout() {
            let currentY = this.padding;
            const containerCenterX = this.containerWidth / 2;

            console.log('📐 UILayout: 垂直布局开始', {
                padding: this.padding,
                verticalSpacing: this.verticalSpacing,
                childrenCount: this.children.length
            });

            this.children.forEach((child, index) => {
                if (!child.visible) return;

                console.log(`📐 UILayout: 处理子对象${index}`, {
                    name: child.name,
                    width: child.width,
                    height: child.height,
                    currentY: currentY,
                    verticalSpacing: this.verticalSpacing
                });

                // 设置Y位置
                child.y = currentY;

                // 设置X位置（根据对齐方式）
                switch (this.crossAxisAlignment) {
                    case 'center':
                        child.x = containerCenterX - (child.width || 0) / 2;
                        break;
                    case 'end':
                        child.x = this.containerWidth - (child.width || 0) - this.padding;
                        break;
                    case 'start':
                    default:
                        child.x = this.padding;
                        break;
                }

                // 更新下一个元素的Y位置
                const childHeight = child.height || 0;
                currentY += childHeight + this.verticalSpacing;

                console.log(`📐 UILayout: 子对象${index}布局完成`, {
                    finalX: child.x,
                    finalY: child.y,
                    childHeight: childHeight,
                    nextY: currentY
                });
            });

            // 自动计算容器高度
            if (this.containerHeight === 0) {
                this.containerHeight = currentY - this.verticalSpacing + this.padding;
            }
        }

        /**
         * 水平布局
         */
        updateHorizontalLayout() {
            let currentX = this.padding;
            const containerCenterY = this.containerHeight / 2;

            this.children.forEach((child, index) => {
                if (!child.visible) return;

                // 设置X位置
                child.x = currentX;

                // 设置Y位置（根据对齐方式）
                switch (this.crossAxisAlignment) {
                    case 'center':
                        child.y = containerCenterY - (child.height || 0) / 2;
                        break;
                    case 'end':
                        child.y = this.containerHeight - (child.height || 0) - this.padding;
                        break;
                    case 'start':
                    default:
                        child.y = this.padding;
                        break;
                }

                // 更新下一个元素的X位置
                currentX += (child.width || 0) + this.horizontalSpacing;
            });

            // 自动计算容器宽度
            if (this.containerWidth === 0) {
                this.containerWidth = currentX - this.horizontalSpacing + this.padding;
            }
        }

        /**
         * 网格布局
         */
        updateGridLayout() {
            const visibleChildren = this.children.filter(child => child.visible);
            
            visibleChildren.forEach((child, index) => {
                const row = Math.floor(index / this.columns);
                const col = index % this.columns;

                // 计算位置
                child.x = this.padding + col * ((child.width || 0) + this.horizontalSpacing);
                child.y = this.padding + row * ((child.height || 0) + this.verticalSpacing);
            });

            // 自动计算容器尺寸
            if (visibleChildren.length > 0) {
                const totalRows = Math.ceil(visibleChildren.length / this.columns);
                const maxChildWidth = Math.max(...visibleChildren.map(child => child.width || 0));
                const maxChildHeight = Math.max(...visibleChildren.map(child => child.height || 0));

                if (this.containerWidth === 0) {
                    this.containerWidth = this.padding * 2 + 
                        this.columns * maxChildWidth + 
                        (this.columns - 1) * this.horizontalSpacing;
                }

                if (this.containerHeight === 0) {
                    this.containerHeight = this.padding * 2 + 
                        totalRows * maxChildHeight + 
                        (totalRows - 1) * this.verticalSpacing;
                }
            }
        }

        /**
         * 设置布局类型
         */
        setLayoutType(layoutType) {
            if (this.layoutType !== layoutType) {
                console.log('📐 UILayout: 切换布局类型', this.layoutType, '->', layoutType);
                this.layoutType = layoutType;
                this.requestLayoutUpdate();
            }
        }

        /**
         * 设置间距
         */
        setSpacing(spacing) {
            this.spacing = spacing;
            this.horizontalSpacing = spacing;
            this.verticalSpacing = spacing;
            this.requestLayoutUpdate();
        }

        /**
         * 设置容器尺寸
         */
        setContainerSize(width, height) {
            this.containerWidth = width || 0;
            this.containerHeight = height || 0;
            this.requestLayoutUpdate();
        }

        /**
         * 获取计算后的容器尺寸
         */
        getCalculatedSize() {
            return {
                width: this.containerWidth,
                height: this.containerHeight
            };
        }

        /**
         * 克隆布局管理器
         */
        clone(options = {}) {
            const clonedLayout = new UILayout({
                layoutType: this.layoutType,
                spacing: this.spacing,
                padding: this.padding,
                horizontalSpacing: this.horizontalSpacing,
                verticalSpacing: this.verticalSpacing,
                columns: this.columns,
                rows: this.rows,
                mainAxisAlignment: this.mainAxisAlignment,
                crossAxisAlignment: this.crossAxisAlignment,
                width: this.containerWidth,
                height: this.containerHeight,
                autoUpdate: this.autoUpdate
            });

            // 复制基础属性
            clonedLayout.x = this.x;
            clonedLayout.y = this.y;
            clonedLayout.visible = this.visible;
            clonedLayout.alpha = this.alpha;

            // 克隆子元素
            this.children.forEach(child => {
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone(options);
                    clonedLayout.addChild(clonedChild);
                }
            });

            console.log('🔄 UILayout: 布局管理器克隆完成');
            return clonedLayout;
        }
    }

    // 导出到全局
    window.UILayout = UILayout;
    console.log('✅ UILayout插件加载完成');

})();
