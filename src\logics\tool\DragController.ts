/**
 * 拖动控制器 - 处理对象拖动逻辑
 */

import type {
  ArrowType,
  DragState,
  SelectedObjectInfo,
  ToolEventCallbacks,
  MouseEventInfo,
  ToolConfig,
  RenderContext
} from './types';
import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';
import { CoordinateTransform } from './CoordinateTransform';
import { performanceMonitor } from './PerformanceMonitor';
import { historyManager } from '../../historyManager';

export class DragController {
  private dragState: DragState = {
    isDragging: false,
    dragType: null,
    startX: 0,
    startY: 0,
    startObjectX: 0,
    startObjectY: 0,
    currentX: 0,
    currentY: 0
  };

  private config: ToolConfig;
  private callbacks: ToolEventCallbacks;
  private renderContext: RenderContext | undefined = undefined;

  // 历史记录相关
  private historyOperationStarted: boolean = false;
  private enableHistory: boolean = true;

  constructor(config: ToolConfig, callbacks: ToolEventCallbacks = {}) {
    this.config = config;
    this.callbacks = callbacks;
  }

  /**
   * 设置渲染上下文
   */
  setRenderContext(context: RenderContext): void {
    this.renderContext = context;
  }

  /**
   * 开始拖动
   */
  startDrag(
    object: BaseObjectModel,
    dragType: ArrowType,
    mouseX: number,
    mouseY: number
  ): void {
    // 验证输入参数
    if (isNaN(mouseX) || isNaN(mouseY)) {
      console.error('🎯 DragController: 开始拖动时鼠标坐标包含 NaN', { mouseX, mouseY });
      return;
    }

    if (isNaN(object.x) || isNaN(object.y)) {
      console.error('🎯 DragController: 对象位置包含 NaN，重置为 0', {
        object: object.className,
        x: object.x,
        y: object.y
      });
      object.x = 0;
      object.y = 0;
    }

    this.dragState = {
      isDragging: true,
      dragType,
      startX: mouseX,
      startY: mouseY,
      startObjectX: object.x,
      startObjectY: object.y,
      currentX: mouseX,
      currentY: mouseY
    };

    console.log('🎯 开始拖动:', {
      object: object.className,
      dragType,
      startPosition: { x: object.x, y: object.y },
      mousePosition: { x: mouseX, y: mouseY }
    });

    // 🎯 开始历史记录操作组
    if (this.enableHistory && historyManager.isRecording()) {
      const operationName = `拖动${object.className}`;
      historyManager.startGroup(operationName);
      this.historyOperationStarted = true;

      console.log("📝 DragController: 开始拖动历史记录操作组:", operationName);
    }

    this.callbacks.onDragStart?.(object, dragType);
  }

  /**
   * 更新拖动 - 优化版本，使用坐标转换和模型驱动更新
   */
  updateDrag(
    object: BaseObjectModel,
    mouseX: number,
    mouseY: number
  ): boolean {
    if (!this.dragState.isDragging || !this.dragState.dragType) {
      return false;
    }

    // 开始性能监控
    performanceMonitor.startTimer('drag-update');

    this.dragState.currentX = mouseX;
    this.dragState.currentY = mouseY;

    // 1. 验证输入参数
    if (isNaN(mouseX) || isNaN(mouseY)) {
      console.error('🎯 DragController: 鼠标坐标包含 NaN', { mouseX, mouseY });
      return false;
    }

    // 2. 将鼠标坐标转换为模型坐标
    performanceMonitor.startTimer('coordinate-transform');
    const currentModelPos = CoordinateTransform.mouseToModel(
      mouseX,
      mouseY,
      this.renderContext
    );

    const startModelPos = CoordinateTransform.mouseToModel(
      this.dragState.startX,
      this.dragState.startY,
      this.renderContext
    );
    performanceMonitor.endTimer('coordinate-transform', 'coordinateTransformTime');

    // 3. 验证转换结果
    if (isNaN(currentModelPos.x) || isNaN(currentModelPos.y) ||
        isNaN(startModelPos.x) || isNaN(startModelPos.y)) {
      console.error('🎯 DragController: 坐标转换结果包含 NaN', {
        current: currentModelPos,
        start: startModelPos,
        renderContext: this.renderContext
      });
      return false;
    }

    // 4. 计算模型坐标系中的移动增量
    const deltaX = currentModelPos.x - startModelPos.x;
    const deltaY = currentModelPos.y - startModelPos.y;

    // 5. 根据拖动类型限制移动方向
    let finalDeltaX = 0;
    let finalDeltaY = 0;

    switch (this.dragState.dragType) {
      case 'up':
        // Y轴移动（向上为负）
        finalDeltaY = deltaY;
        break;
      case 'right':
        // X轴移动（向右为正）
        finalDeltaX = deltaX;
        break;
      case 'center':
        // 自由移动（XY都可以）
        finalDeltaX = deltaX;
        finalDeltaY = deltaY;
        break;
      case 'down':
        // 垂直移动（保留兼容性）
        finalDeltaY = deltaY;
        break;
      case 'left':
        // 水平移动（保留兼容性）
        finalDeltaX = deltaX;
        break;
    }

    // 6. 计算新的模型位置
    let newModelX = this.dragState.startObjectX + finalDeltaX;
    let newModelY = this.dragState.startObjectY + finalDeltaY;

    // 7. 验证计算结果
    if (isNaN(newModelX) || isNaN(newModelY)) {
      console.error('🎯 DragController: 新位置计算结果包含 NaN', {
        startPos: { x: this.dragState.startObjectX, y: this.dragState.startObjectY },
        delta: { x: finalDeltaX, y: finalDeltaY },
        newPos: { x: newModelX, y: newModelY }
      });
      return false;
    }

    // 8. 应用网格对齐（在模型坐标系中）
    if (this.config.snapToGrid) {
      const snappedPos = CoordinateTransform.snapToGrid(
        { x: newModelX, y: newModelY },
        this.config.gridSize
      );
      newModelX = snappedPos.x;
      newModelY = snappedPos.y;
    }

    // 9. 只修改模型属性，让响应式系统处理渲染更新
    performanceMonitor.startTimer('model-update');
    object.x = newModelX;
    object.y = newModelY;
    performanceMonitor.endTimer('model-update', 'modelUpdateTime');

    // 仅在调试模式下输出日志
    if (CoordinateTransform.isDebugEnabled()) {
      console.log('🎯 拖动更新 (模型驱动):', {
        object: object.className,
        mouseDelta: { x: deltaX, y: deltaY },
        finalDelta: { x: finalDeltaX, y: finalDeltaY },
        newModelPosition: { x: newModelX, y: newModelY },
        useCoordinateTransform: !!this.renderContext
      });
    }

    // 10. 通知回调（传递模型坐标系的增量）
    this.callbacks.onDragMove?.(object, finalDeltaX, finalDeltaY);

    // 结束性能监控
    performanceMonitor.endTimer('drag-update', 'dragLatency');
    return true;
  }

  /**
   * 结束拖动
   */
  endDrag(object: BaseObjectModel): void {
    if (!this.dragState.isDragging) {
      return;
    }

    console.log('🎯 结束拖动:', {
      object: object.className,
      finalPosition: { x: object.x, y: object.y },
      startPosition: { x: this.dragState.startObjectX, y: this.dragState.startObjectY }
    });

    // 🎯 处理历史记录
    if (this.historyOperationStarted && this.enableHistory && historyManager.isRecording()) {
      const startX = this.dragState.startObjectX;
      const startY = this.dragState.startObjectY;
      const finalX = object.x;
      const finalY = object.y;

      // 检查位置是否真的发生了变化
      const hasPositionChanged = (startX !== finalX) || (startY !== finalY);

      if (hasPositionChanged) {
        // 记录X坐标变化
        if (startX !== finalX) {
          historyManager.recordChange(object, 'x', startX, finalX);
          console.log("📝 DragController: 已记录X坐标变更", { from: startX, to: finalX });
        }

        // 记录Y坐标变化
        if (startY !== finalY) {
          historyManager.recordChange(object, 'y', startY, finalY);
          console.log("📝 DragController: 已记录Y坐标变更", { from: startY, to: finalY });
        }
      } else {
        console.log("📝 DragController: 位置未变化，跳过历史记录");
      }

      // 结束操作组
      historyManager.endGroup();
      console.log("📝 DragController: 拖动历史记录操作组已结束");
    }

    this.callbacks.onDragEnd?.(object, object.x, object.y);

    // 重置状态
    this.dragState = {
      isDragging: false,
      dragType: null,
      startX: 0,
      startY: 0,
      startObjectX: 0,
      startObjectY: 0,
      currentX: 0,
      currentY: 0
    };

    // 重置历史记录状态
    this.historyOperationStarted = false;
  }

  /**
   * 取消拖动（恢复到原始位置）
   */
  cancelDrag(object: BaseObjectModel): void {
    if (!this.dragState.isDragging) {
      return;
    }

    // 恢复到开始位置
    object.x = this.dragState.startObjectX;
    object.y = this.dragState.startObjectY;

    console.log('🎯 取消拖动:', {
      object: object.className,
      restoredPosition: { x: object.x, y: object.y }
    });

    // 🎯 取消拖动时不记录历史，直接结束操作组
    if (this.historyOperationStarted && this.enableHistory && historyManager.isRecording()) {
      historyManager.endGroup();
      console.log("📝 DragController: 取消拖动，结束历史记录操作组（不记录变更）");
    }

    // 重置历史记录状态
    this.historyOperationStarted = false;

    // 调用回调
    this.callbacks.onDragEnd?.(object, object.x, object.y);

    // 重置拖动状态
    this.dragState = {
      isDragging: false,
      dragType: null,
      startX: 0,
      startY: 0,
      startObjectX: 0,
      startObjectY: 0,
      currentX: 0,
      currentY: 0
    };
  }

  /**
   * 获取当前拖动状态
   */
  getDragState(): DragState {
    return { ...this.dragState };
  }

  /**
   * 是否正在拖动
   */
  isDragging(): boolean {
    return this.dragState.isDragging;
  }

  /**
   * 获取当前拖动类型
   */
  getCurrentDragType(): ArrowType | null {
    return this.dragState.dragType;
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ToolConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 更新回调
   */
  updateCallbacks(callbacks: Partial<ToolEventCallbacks>): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * 启用/禁用历史记录
   */
  setHistoryEnabled(enabled: boolean): void {
    this.enableHistory = enabled;
    console.log(`🎯 DragController: 历史记录${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 获取历史记录启用状态
   */
  isHistoryEnabled(): boolean {
    return this.enableHistory;
  }

  /**
   * 获取当前是否有进行中的历史记录操作
   */
  isHistoryOperationActive(): boolean {
    return this.historyOperationStarted;
  }

  /**
   * 计算拖动预览位置（不实际移动对象）
   */
  calculatePreviewPosition(
    _object: BaseObjectModel,
    mouseX: number,
    mouseY: number
  ): { x: number; y: number } | null {
    if (!this.dragState.isDragging || !this.dragState.dragType) {
      return null;
    }

    const deltaX = mouseX - this.dragState.startX;
    const deltaY = mouseY - this.dragState.startY;

    let finalDeltaX = 0;
    let finalDeltaY = 0;

    switch (this.dragState.dragType) {
      case 'up':
        finalDeltaY = deltaY;
        break;
      case 'right':
        finalDeltaX = deltaX;
        break;
      case 'center':
        finalDeltaX = deltaX;
        finalDeltaY = deltaY;
        break;
      case 'down':
        finalDeltaY = deltaY;
        break;
      case 'left':
        finalDeltaX = deltaX;
        break;
    }

    if (this.config.snapToGrid) {
      finalDeltaX = Math.round(finalDeltaX / this.config.gridSize) * this.config.gridSize;
      finalDeltaY = Math.round(finalDeltaY / this.config.gridSize) * this.config.gridSize;
    }

    return {
      x: this.dragState.startObjectX + finalDeltaX,
      y: this.dragState.startObjectY + finalDeltaY
    };
  }
}
