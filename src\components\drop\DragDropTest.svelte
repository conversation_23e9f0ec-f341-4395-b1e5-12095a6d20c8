<script lang="ts">
  /**
   * 简单的拖拽测试组件
   */
  import DragSource from './DragSource.svelte';
  import DropTarget from './DropTarget.svelte';
  import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';

  // 测试数据
  let testObject: BaseObjectModel = {
    className: 'TestSprite',
    name: 'Test',
    x: 100,
    y: 200,
    children: []
  };

  let droppedObject = $state<BaseObjectModel | null>(null);

  function handleDrop(object: BaseObjectModel) {
    droppedObject = object;
    console.log('🎯 测试接收到对象:', object);
  }

  function handleDragStart(object: BaseObjectModel) {
    console.log('🎯 测试开始拖拽:', object.className);
  }
</script>

<div class="test-container">
  <h3>拖拽测试</h3>
  
  <div class="test-layout">
    <!-- 拖拽源 -->
    <div class="drag-area">
      <h4>拖拽源</h4>
      <DragSource object={testObject} ondragstart={handleDragStart}>
        {#snippet children()}
          <div class="test-item">
            拖拽我: {testObject.className}
          </div>
        {/snippet}
      </DragSource>
    </div>

    <!-- 拖拽目标 -->
    <div class="drop-area">
      <h4>拖拽目标</h4>
      <DropTarget onDrop={handleDrop} placeholder="拖拽到此处" />
      
      {#if droppedObject}
        <div class="result">
          接收到: {droppedObject.className}
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .test-container {
    padding: 20px;
    border: 2px solid #ddd;
    border-radius: 8px;
    margin: 20px;
  }

  .test-layout {
    display: flex;
    gap: 20px;
    margin-top: 10px;
  }

  .drag-area, .drop-area {
    flex: 1;
    padding: 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  .test-item {
    padding: 10px;
    background: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 4px;
    text-align: center;
    cursor: grab;
  }

  .test-item:active {
    cursor: grabbing;
  }

  .result {
    margin-top: 10px;
    padding: 8px;
    background: #e8f5e8;
    border: 1px solid #4CAF50;
    border-radius: 4px;
    font-size: 12px;
  }

  h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
  }
</style>
