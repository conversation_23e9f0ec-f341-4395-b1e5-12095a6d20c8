/**
 * Window_MenuCommand 创建器
 * 专门用于创建菜单命令窗口
 */

import { BaseWindowCreator, type WindowCreationOptions } from './WindowCreator';
import { type HorzCommandWindowOptions } from './WindowHorzCommandCreator';

/**
 * 菜单命令窗口创建选项
 */
export interface MenuCommandWindowOptions extends HorzCommandWindowOptions {
    /** 是否显示物品命令 */
    showItemCommand?: boolean;
    /** 是否显示技能命令 */
    showSkillCommand?: boolean;
    /** 是否显示装备命令 */
    showEquipCommand?: boolean;
    /** 是否显示状态命令 */
    showStatusCommand?: boolean;
    /** 是否显示编队命令 */
    showFormationCommand?: boolean;
    /** 是否显示选项命令 */
    showOptionsCommand?: boolean;
    /** 是否显示保存命令 */
    showSaveCommand?: boolean;
    /** 是否显示游戏结束命令 */
    showGameEndCommand?: boolean;
}

/**
 * 创建菜单命令窗口
 * @param options 创建选项
 * @returns 创建的菜单命令窗口实例
 */
export async function createWindowMenuCommand(options: MenuCommandWindowOptions = {}): Promise<any> {
    console.log('=== 创建菜单命令窗口 Window_MenuCommand ===');

    try {
        // 预加载资源
        BaseWindowCreator.preloadWindowResources('Window_MenuCommand');

        // 设置默认选项
        const defaultOptions: WindowCreationOptions = {
            autoOpen: true,
            addToStage: true,
            rect: options.rect || { x: 0, y: 0, width: 240, height: 300 },
            ...options
        };

        // 创建窗口实例
        const window = await BaseWindowCreator.createWindowInstance('Window_MenuCommand', defaultOptions);

        // Window_MenuCommand 特定的设置
        setupMenuCommandWindow(window, options);

        // 确保窗口有内容显示
        await ensureMenuCommandContent(window, options);

        console.log('Window_MenuCommand 创建完成，窗口属性:', {
            x: window.x,
            y: window.y,
            width: window.width,
            height: window.height,
            maxItems: window.maxItems(),
            index: window.index(),
            active: window.active,
            visible: window.visible
        });

        return window;

    } catch (error) {
        console.error('创建 Window_MenuCommand 失败:', error);
        throw error;
    }
}

/**
 * 设置菜单命令窗口属性
 * @param window 窗口实例
 * @param options 菜单命令窗口选项
 */
function setupMenuCommandWindow(window: any, options: MenuCommandWindowOptions): void {
    console.log('设置菜单命令窗口属性...');

    try {
        // 如果没有提供自定义命令，使用默认的菜单命令
        if (!options.commands) {
            // 清除现有命令列表
            if (window.clearCommandList && typeof window.clearCommandList === 'function') {
                window.clearCommandList();
            }

            // 添加标准菜单命令
            addStandardMenuCommands(window, options);

            // 刷新窗口
            if (window.refresh && typeof window.refresh === 'function') {
                window.refresh();
                console.log('菜单命令窗口已刷新');
            }
        } else {
            // 使用自定义命令
            if (options.commands && options.commands.length > 0) {
                // 清除现有命令列表
                if (window.clearCommandList && typeof window.clearCommandList === 'function') {
                    window.clearCommandList();
                }

                // 添加自定义命令
                options.commands.forEach((command, index) => {
                    if (window.addCommand && typeof window.addCommand === 'function') {
                        window.addCommand(
                            command.name,
                            command.symbol,
                            command.enabled !== false,
                            command.ext || null
                        );
                        console.log(`添加菜单命令 ${index + 1}:`, command);
                    }
                });

                // 刷新窗口
                if (window.refresh && typeof window.refresh === 'function') {
                    window.refresh();
                    console.log('菜单命令窗口已刷新');
                }
            }
        }

        // 设置初始选中索引
        if (options.initialIndex !== undefined && window.select && typeof window.select === 'function') {
            window.select(options.initialIndex);
            console.log('设置初始选中索引:', options.initialIndex);
        } else if (window.selectLast && typeof window.selectLast === 'function') {
            // 使用 RPG Maker MZ 的默认行为：选择上次选中的命令
            window.selectLast();
            console.log('选择上次选中的菜单命令');
        }

        // 设置激活状态
        if (options.activate !== undefined) {
            if (options.activate && window.activate && typeof window.activate === 'function') {
                window.activate();
                console.log('菜单命令窗口已激活');
            } else if (!options.activate && window.deactivate && typeof window.deactivate === 'function') {
                window.deactivate();
                console.log('菜单命令窗口已取消激活');
            }
        }

        console.log('菜单命令窗口属性设置完成');

    } catch (error) {
        console.error('设置菜单命令窗口属性失败:', error);
    }
}

/**
 * 确保菜单命令窗口有内容显示
 * @param window 窗口实例
 * @param options 菜单命令窗口选项
 */
async function ensureMenuCommandContent(window: any, options: MenuCommandWindowOptions): Promise<void> {
    console.log('确保菜单命令窗口有内容显示...');

    try {
        // 检查是否有 makeCommandList 方法
        if (window.makeCommandList && typeof window.makeCommandList === 'function') {
            // 调用原生的 makeCommandList 方法来生成标准命令
            window.makeCommandList();
            console.log('调用原生 makeCommandList 方法');
        } else {
            // 如果没有原生方法，手动添加命令
            console.log('手动添加菜单命令');
            if (window.clearCommandList && typeof window.clearCommandList === 'function') {
                window.clearCommandList();
            }
            addStandardMenuCommands(window, options);
        }

        // 强制刷新窗口内容
        if (window.refresh && typeof window.refresh === 'function') {
            window.refresh();
            console.log('菜单命令窗口内容已刷新');
        }

        // 确保窗口可见且打开
        if (window.visible !== undefined) {
            window.visible = true;
        }

        if (window.openness !== undefined) {
            window.openness = 255; // 完全打开
        }

        // 检查命令数量
        const commandCount = window.maxItems ? window.maxItems() : 0;
        console.log('菜单命令数量:', commandCount);

        if (commandCount === 0) {
            console.warn('菜单命令窗口没有命令项，尝试添加默认命令');
            addFallbackCommands(window);
            if (window.refresh && typeof window.refresh === 'function') {
                window.refresh();
            }
        }

    } catch (error) {
        console.error('确保菜单命令窗口内容失败:', error);
    }
}

/**
 * 添加后备命令（当原生方法失败时）
 * @param window 窗口实例
 */
function addFallbackCommands(window: any): void {
    console.log('添加后备菜单命令...');

    const fallbackCommands = [
        { name: '物品', symbol: 'item', enabled: true },
        { name: '技能', symbol: 'skill', enabled: true },
        { name: '装备', symbol: 'equip', enabled: true },
        { name: '状态', symbol: 'status', enabled: true },
        { name: '选项', symbol: 'options', enabled: true },
        { name: '保存', symbol: 'save', enabled: true },
        { name: '游戏结束', symbol: 'gameEnd', enabled: true }
    ];

    fallbackCommands.forEach(command => {
        if (window.addCommand && typeof window.addCommand === 'function') {
            window.addCommand(command.name, command.symbol, command.enabled);
            console.log('添加后备命令:', command.name);
        }
    });
}

/**
 * 添加标准菜单命令
 * @param window 窗口实例
 * @param options 菜单命令窗口选项
 */
function addStandardMenuCommands(window: any, options: MenuCommandWindowOptions): void {
    console.log('添加标准菜单命令...');

    try {
        // 检查是否有 TextManager
        const hasTextManager = window.TextManager !== undefined;

        // 添加主要命令
        if (options.showItemCommand !== false && window.addCommand) {
            const itemText = hasTextManager ? window.TextManager.item : '物品';
            window.addCommand(itemText, 'item', true);
            console.log('添加物品命令');
        }

        if (options.showSkillCommand !== false && window.addCommand) {
            const skillText = hasTextManager ? window.TextManager.skill : '技能';
            window.addCommand(skillText, 'skill', true);
            console.log('添加技能命令');
        }

        if (options.showEquipCommand !== false && window.addCommand) {
            const equipText = hasTextManager ? window.TextManager.equip : '装备';
            window.addCommand(equipText, 'equip', true);
            console.log('添加装备命令');
        }

        if (options.showStatusCommand !== false && window.addCommand) {
            const statusText = hasTextManager ? window.TextManager.status : '状态';
            window.addCommand(statusText, 'status', true);
            console.log('添加状态命令');
        }

        // 添加编队命令
        if (options.showFormationCommand === true && window.addCommand) {
            const formationText = hasTextManager ? window.TextManager.formation : '编队';
            window.addCommand(formationText, 'formation', true);
            console.log('添加编队命令');
        }

        // 添加选项命令
        if (options.showOptionsCommand !== false && window.addCommand) {
            const optionsText = hasTextManager ? window.TextManager.options : '选项';
            window.addCommand(optionsText, 'options', true);
            console.log('添加选项命令');
        }

        // 添加保存命令
        if (options.showSaveCommand !== false && window.addCommand) {
            const saveText = hasTextManager ? window.TextManager.save : '保存';
            window.addCommand(saveText, 'save', true);
            console.log('添加保存命令');
        }

        // 添加游戏结束命令
        if (options.showGameEndCommand !== false && window.addCommand) {
            const gameEndText = hasTextManager ? window.TextManager.gameEnd : '游戏结束';
            window.addCommand(gameEndText, 'gameEnd', true);
            console.log('添加游戏结束命令');
        }

        console.log('标准菜单命令添加完成');

    } catch (error) {
        console.error('添加标准菜单命令失败:', error);
    }
}

/**
 * 创建并激活菜单命令窗口
 * @param options 创建选项
 * @returns 创建的菜单命令窗口实例
 */
export async function createAndActivateWindowMenuCommand(options: MenuCommandWindowOptions = {}): Promise<any> {
    console.log('=== 创建并激活 Window_MenuCommand ===');

    const window = await createWindowMenuCommand({
        ...options,
        autoOpen: true,
        activate: true
    });

    console.log('Window_MenuCommand 已创建并激活');
    return window;
}

/**
 * 创建简单的菜单命令窗口（用于测试）
 * @returns 创建的菜单命令窗口实例
 */
export async function createSimpleWindowMenuCommand(): Promise<any> {
    console.log('=== 创建简单菜单命令窗口 ===');

    try {
        const window = await createWindowMenuCommand({
            rect: { x: 50, y: 100, width: 240, height: 300 },
            autoOpen: true,
            addToStage: true,
            visible: true,
            initialIndex: 0,
            activate: true,
            showItemCommand: true,
            showSkillCommand: true,
            showEquipCommand: true,
            showStatusCommand: true,
            showFormationCommand: false,
            showOptionsCommand: true,
            showSaveCommand: true,
            showGameEndCommand: true
        });

        console.log('简单菜单命令窗口创建成功');
        return window;

    } catch (error) {
        console.error('创建简单菜单命令窗口失败:', error);
        throw error;
    }
}
