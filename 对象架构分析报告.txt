RPG Maker MZ 的架构可以分为三大类：

1. 有模型对象的系统（Model-View 架构）
🟢 角色系统（Character System）
Game_CharacterBase (基类)
├── Game_Character (中间类)
    ├── Game_Player (玩家)
    ├── Game_Follower (跟随者)
    ├── Game_Vehicle (载具)
    └── Game_Event (事件)


对应显示对象: Sprite_Character

🟢 战斗系统（Battle System）
Game_BattlerBase (基类)
└── Game_Battler (中间类)
    ├── Game_Actor (角色)
    └── Game_Enemy (敌人)


对应显示对象: Sprite_Actor, Sprite_Enemy

🟢 图片系统（Picture System）
Game_Picture (图片模型)
对应显示对象: Sprite_Picture

纯显示对象系统（View-Only）
🔴 UI 窗口系统（Window System）
Window_Base
├── Window_Scrollable
│   ├── Window_Selectable
│   │   ├── Window_Command
│   │   │   ├── Window_HorzCommand
│   │   │   │   ├── Window_MenuCommand
│   │   │   │   └── Window_ItemCategory
│   │   │   ├── Window_TitleCommand
│   │   │   └── Window_PartyCommand
│   │   ├── Window_ItemList
│   │   └── Window_SavefileList
│   └── Window_Help
├── Window_Gold
├── Window_StatusBase
└── Window_Message
特点: 没有对应的模型对象，直接继承显示对象

精灵系统（Sprite System）
Sprite (PIXI.js 基类)
├── Sprite_Clickable
├── Sprite_Button
├── Sprite_Animation
├── Sprite_Damage
└── ... (20+ 精灵类)
特点: 大部分没有模型对象，直接操作显示属性

管理器系统（Manager System）
🟡 游戏数据管理
Game_System, Game_Switches, Game_Variables, 
Game_Map, Game_Party, Game_Troop, etc.
特点: 纯逻辑对象，没有直接的显示对象






Window (PIXI.Container)
├── _container (PIXI.Container)
│   ├── _backSprite (Sprite)
│   │   └── TilingSprite
│   └── _frameSprite (Sprite)
│       ├── Sprite (corner 0)
│       ├── Sprite (corner 1)
│       ├── Sprite (corner 2)
│       ├── Sprite (corner 3)
│       ├── Sprite (edge 4)
│       ├── Sprite (edge 5)
│       ├── Sprite (edge 6)
│       └── Sprite (edge 7)
├── _clientArea (Sprite)
│   ├── _contentsBackSprite (Sprite)
│   ├── _cursorSprite (Sprite)
│   │   ├── Sprite (cursor part 0)
│   │   ├── ... (cursor parts 1-7)
│   │   └── Sprite (cursor part 8)
│   └── _contentsSprite (Sprite)
├── _downArrowSprite (Sprite)
├── _upArrowSprite (Sprite)
└── _pauseSignSprite (Sprite)



绝对不能跳过的步骤
✅ Window.prototype.initialize.call(this) - 基础框架
✅ this.loadWindowskin() - 加载窗口皮肤
✅ this.createContents() - 创建内容 bitmap
✅ this._createAllParts() - 创建窗口部件
✅ 正确的属性访问器设置