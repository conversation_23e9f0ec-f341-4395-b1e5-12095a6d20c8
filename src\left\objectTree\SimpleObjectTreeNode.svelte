<script lang="ts">
  /**
   * 简化的对象树节点组件 - 接受 BaseObjectModel
   * 内置选择节点逻辑
   */

  import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';
  import {
    selectObject,
    sceneModelState,
    getCurrentState
  } from '../../stores/sceneModelStore';
  import SimpleObjectTreeNode from './SimpleObjectTreeNode.svelte';
  import { handleContextMenu } from './contextMenuLogic';
  import DragSource from '../../components/drop/DragSource.svelte';
  import { treeDragReorder, type DropPosition } from '../../logics/tool/TreeDragReorder';
  import { dragStore } from '../../stores/dragStore';

  // Props
  interface Props {
    node: BaseObjectModel;
    level?: number; // 层级深度，用于缩进
  }

  let { node, level = 0 }: Props = $props();

  // 内部处理选择状态
  let currentState = $derived($sceneModelState);
  let isSelected = $derived(currentState.selectedObjects.includes(node));

  // 展开/收起状态
  let isExpanded = $state(false);

  // 是否有子对象
  let hasChildren = $derived(node.children && node.children.length > 0);

  // 拖拽目标状态
  let dropPosition = $state<DropPosition>('none');
  let isDragOver = $state(false);
  let canDrop = $state(false);
  let nodeElement: HTMLDivElement;

  // 订阅拖拽状态
  let dragState = $state<{ isDragging: boolean; draggedObject: BaseObjectModel | null }>({
    isDragging: false,
    draggedObject: null
  });

  // 监听拖拽状态变化
  dragStore.subscribe(state => {
    dragState = state;
  });

  // 组件挂载时添加自定义事件监听器
  $effect(() => {
    if (nodeElement) {
      nodeElement.addEventListener('customdrop', handleCustomDrop);

      return () => {
        nodeElement.removeEventListener('customdrop', handleCustomDrop);
      };
    }
  });

  // 监听全局鼠标移动，用于拖拽时的实时反馈
  $effect(() => {
    if (!dragState.isDragging) return;

    function handleGlobalMouseMove(event: MouseEvent) {
      if (!nodeElement || !isDragOver) return;

      // 检查鼠标是否在当前节点上
      const rect = nodeElement.getBoundingClientRect();
      const isOver = event.clientX >= rect.left &&
                     event.clientX <= rect.right &&
                     event.clientY >= rect.top &&
                     event.clientY <= rect.bottom;

      if (isOver) {
        updateDropIndicator(event);
      }
    }

    document.addEventListener('mousemove', handleGlobalMouseMove);

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
    };
  });

  // 节点图标
  let nodeIcon = $derived(() => {
    if (node.className.includes('Scene')) {
      return '🎬';
    } else if (node.className.includes('Window')) {
      return '🪟';
    } else if (node.className.includes('Sprite')) {
      return '🖼️';
    } else if (node.className.includes('Container')) {
      return '📦';
    } else if (node.className.includes('Bitmap')) {
      return '🎨';
    } else {
      return '📄';
    }
  });

  // 处理展开/收起
  function handleToggleExpand(event: Event) {
    event.stopPropagation(); // 阻止事件冒泡，避免触发节点选择
    if (hasChildren) {
      isExpanded = !isExpanded;
    }
  }

  // 处理点击 - 选择当前节点并同步
  function handleClick() {
    console.log('选择节点:', node.className);

    // 选择对象
    selectObject(node, `node_${node.className}`);

    // 同步场景模型到原始对象
    const state = getCurrentState();
    if (state.currentScene) {
      state.currentScene.syncToOriginalScene();
      console.log('🔧 场景模型已同步到原始对象');
    }
  }

  // 处理键盘事件
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleClick();
    }
  }

  // 处理右键菜单
  function handleRightClick(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    // 先选择当前节点
    selectObject(node, `node_${node.className}`);

    // 显示右键菜单
    handleContextMenu(event, node);
  }

  // 处理拖拽开始
  function handleDragStart(draggedNode: BaseObjectModel) {
    console.log('🎯 SimpleObjectTreeNode: 开始拖拽节点', draggedNode.className);

    // 可以在这里添加拖拽开始时的特殊处理
    // 比如高亮显示可接受的拖拽目标等
  }

  // 处理鼠标进入（自定义拖拽事件）
  function handleMouseEnter(event: MouseEvent) {
    if (!dragState.isDragging) return;

    console.log('🎯 鼠标进入节点:', node.className);
    isDragOver = true;

    // 确保根场景已设置
    const currentScene = getCurrentState().currentScene;
    if (currentScene) {
      treeDragReorder.setRootScene(currentScene);
    }

    updateDropIndicator(event);
  }

  // 处理鼠标移动（自定义拖拽事件）
  function handleMouseMove(event: MouseEvent) {
    if (!dragState.isDragging || !isDragOver) return;

    updateDropIndicator(event);
  }

  // 处理鼠标离开（自定义拖拽事件）
  function handleMouseLeave() {
    if (!dragState.isDragging) return;

    console.log('🎯 鼠标离开节点:', node.className);
    isDragOver = false;
    dropPosition = 'none';
    canDrop = false;
  }

  // 处理自定义拖拽释放事件
  function handleCustomDrop(event: Event) {
    const customEvent = event as CustomEvent;
    console.log('🎯 接收到自定义拖拽事件:', customEvent.detail);

    const { object: draggedObject } = customEvent.detail;

    if (draggedObject && canDrop && dropPosition !== 'none') {
      const result = treeDragReorder.executeDrop(draggedObject, node, dropPosition);

      if (result.success) {
        console.log('✅ 拖拽操作成功:', result.message);
        // 这里会自动触发响应式更新和重新渲染
      } else {
        console.warn('❌ 拖拽操作失败:', result.message);
      }
    }

    // 重置状态
    isDragOver = false;
    dropPosition = 'none';
    canDrop = false;
  }

  // 更新拖拽指示器
  function updateDropIndicator(event: MouseEvent) {
    // 使用响应式状态中的拖拽对象
    const draggedObject = dragState.draggedObject;

    if (!draggedObject) return;

    // 计算拖拽位置和验证
    const targetInfo = treeDragReorder.getDropTargetInfo(
      draggedObject,
      nodeElement,
      node,
      event.clientY
    );

    dropPosition = targetInfo.position;
    canDrop = targetInfo.canDrop;

    console.log('🎯 更新拖拽指示器:', {
      position: dropPosition,
      canDrop: canDrop,
      reason: targetInfo.reason
    });

    if (!canDrop) {
      console.log('🚫 无效拖拽:', targetInfo.reason);
    }
  }
</script>

<div class="tree-node-container" style="padding-left: {level > 0 ? 20 : 0}px;">
  <DragSource object={node} ondragstart={handleDragStart}>
    {#snippet children()}
      <div
        bind:this={nodeElement}
        class="tree-node"
        class:selected={isSelected}
        class:drag-over={isDragOver}
        class:drop-before={dropPosition === 'before' && canDrop}
        class:drop-after={dropPosition === 'after' && canDrop}
        class:drop-on={dropPosition === 'on-node' && canDrop}
        class:can-drop={canDrop}
        data-drop-target="true"
        onclick={handleClick}
        oncontextmenu={handleRightClick}
        onkeydown={handleKeydown}
        onmouseenter={handleMouseEnter}
        onmousemove={handleMouseMove}
        onmouseleave={handleMouseLeave}
        role="button"
        tabindex="0"
      >
    <div class="node-content">
      <!-- 展开/收起图标 -->
      {#if hasChildren}
        <button
          class="expand-icon"
          class:expanded={isExpanded}
          onclick={handleToggleExpand}
          aria-label={isExpanded ? '收起' : '展开'}
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M5 3 L11 8 L5 13" stroke="currentColor" stroke-width="2" fill="none" />
          </svg>
        </button>
      {:else}
        <div class="expand-spacer"></div>
      {/if}

      <span class="node-icon">{nodeIcon()}</span>
      <span class="node-label">
        <span class="node-class">{node.className}</span>
        {#if node.name}
          <span class="node-name">({node.name})</span>
        {/if}
        {#if isDragOver}
          <small style="color: #2196F3; margin-left: 8px;">
            [{dropPosition}] {canDrop ? '✓' : '✗'}
          </small>
        {/if}
      </span>
    </div>
      </div>
    {/snippet}
  </DragSource>

  <!-- 子对象 -->
  {#if hasChildren && isExpanded}
    <div class="children-container">
      {#each node.children as child, index (child.className + index)}
        <SimpleObjectTreeNode node={child} level={level + 1} />
      {/each}
    </div>
  {/if}
</div>

<style>
  .tree-node-container {
    width: 99%;
  }

  .tree-node {
    display: flex;
    align-items: center;
    padding: 2px 4px; /* 更紧凑的内边距 */
    margin: 1px 0;
    border-radius: 3px; /* 更小的圆角 */
    cursor: pointer;
    transition: background-color 0.15s ease;
    user-select: none;
    min-height: 20px; /* 更小的最小高度 */
    position: relative; /* 关键！让 ::before 和 ::after 相对于这个元素定位 */
  }

  .tree-node:hover {
    background-color: var(--theme-surface-hover, rgba(0, 0, 0, 0.05));
  }

  .tree-node.selected {
    background-color: var(--theme-primary-light, rgba(33, 150, 243, 0.1));
    border: 1px solid var(--theme-primary, #2196F3);
  }

  .node-content {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 4px; /* 更小的间距 */
  }

  /* 展开/收起图标 */
  .expand-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25px;
    height: 25px;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 3px;
    transition: transform 0.2s ease, background-color 0.15s ease;
    color: var(--theme-text-secondary, #666);
    flex-shrink: 0;
  }

  .expand-icon:hover {
    background-color: var(--theme-surface-hover, rgba(0, 0, 0, 0.1));
  }

  .expand-icon.expanded {
    transform: rotate(90deg);
  }

  .expand-spacer {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }

  /* 拖拽目标样式 */
  .tree-node.drag-over {
    background-color: rgba(33, 150, 243, 0.05);
    transition: background-color 0.2s ease;
  }

  /* 插入线动画 */
  .tree-node.drop-before::before,
  .tree-node.drop-after::after {
    animation: insertLineGlow 1s ease-in-out infinite alternate;
  }

  @keyframes insertLineGlow {
    0% {
      box-shadow: 0 0 8px rgba(33, 150, 243, 0.6);
      opacity: 0.8;
    }
    100% {
      box-shadow: 0 0 12px rgba(33, 150, 243, 0.9);
      opacity: 1;
    }
  }

  .tree-node.drop-before::before {
    content: '';
    position: absolute !important;
    top: -4px !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    height: 6px !important;
    background: #FF5722 !important; /* 改成橙色更明显 */
    z-index: 9999 !important;
    border-radius: 3px !important;
    box-shadow: 0 0 8px rgba(255, 87, 34, 0.8) !important;
    display: block !important;
  }

  .tree-node.drop-after::after {
    content: '';
    position: absolute !important;
    bottom: -4px !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    height: 6px !important;
    background: #FF5722 !important; /* 改成橙色更明显 */
    z-index: 9999 !important;
    border-radius: 3px !important;
    box-shadow: 0 0 8px rgba(255, 87, 34, 0.8) !important;
    display: block !important;
  }

  .tree-node.drop-on.can-drop {
    background: rgba(33, 150, 243, 0.1);
    border: 1px dashed #2196F3;
    border-radius: 4px;
  }

  .tree-node.drop-on:not(.can-drop) {
    background: rgba(244, 67, 54, 0.1);
    border: 1px dashed #f44336;
    border-radius: 4px;
  }

  .node-icon {
    font-size: 14px; /* 更小的图标 */
    flex-shrink: 0;
  }

  .node-label {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 3px; /* 更小的间距 */
    min-width: 0;
  }

  .node-class {
    font-weight: 500;
    font-size: 12px; /* 更小的字体 */
    color: var(--theme-text, #333);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .node-name {
    color: var(--theme-text-secondary, #666);
    font-size: 10px; /* 更小的字体 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .node-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 9px; /* 更小的字体 */
    color: var(--theme-text-secondary, #666);
    flex-shrink: 0;
    line-height: 1.2; /* 更紧凑的行高 */
  }

  .node-position,
  .node-size {
    white-space: nowrap;
  }

  .children-container {
    margin-top: 1px;
  }

  .tree-node:focus {
    outline: 2px solid var(--theme-primary, #2196F3);
    outline-offset: -2px;
  }

  /* 展开图标的 SVG 样式 */
  .expand-icon svg {
    transition: inherit;
  }
</style>
