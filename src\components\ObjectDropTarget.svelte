<script lang="ts">
  /**
   * 简单的对象拖拽接收组件
   */
  import { dndzone, TRIGGERS } from 'svelte-dnd-action';
  import type { BaseObjectModel } from '../type/baseObjectModel.svelte';

  // Props
  interface Props {
    onDrop?: (object: BaseObjectModel) => void;
    placeholder?: string;
    acceptedTypes?: string[];
  }

  let { 
    onDrop,
    placeholder = "拖拽对象到此处",
    acceptedTypes = ['objectTree']
  }: Props = $props();

  // 状态
  let dropItems = $state([]);
  let isActive = $state(false);

  // 处理拖拽事件
  function handleDragEvent(e: CustomEvent) {
    const { detail: { items, info: { trigger } } } = e;
    
    if (trigger === TRIGGERS.DRAGGED_ENTERED) {
      isActive = true;
    } else if (trigger === TRIGGERS.DRAGGED_LEFT) {
      isActive = false;
    } else if (trigger === TRIGGERS.DROPPED_INTO_ZONE) {
      // 接收到拖拽对象
      const droppedItem = items[0];
      if (droppedItem?.node) {
        console.log('🎯 接收到对象:', droppedItem.node.className);
        onDrop?.(droppedItem.node);
      }
      
      // 清空接收区域
      dropItems = [];
      isActive = false;
    }
    
    // 更新 items
    dropItems = items;
  }
</script>

<div 
  class="drop-target"
  class:active={isActive}
  use:dndzone={{
    items: dropItems,
    type: 'objectTree',
    dragDisabled: true,
    flipDurationMs: 0
  }}
  onconsider={handleDragEvent}
  onfinalize={handleDragEvent}
>
  <div class="drop-content">
    {#if isActive}
      <div class="drop-active">
        📦 准备接收对象
      </div>
    {:else}
      <div class="drop-placeholder">
        {placeholder}
      </div>
    {/if}
  </div>
</div>

<style>
  .drop-target {
    min-height: 80px;
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .drop-target.active {
    border-color: #2196F3;
    background-color: rgba(33, 150, 243, 0.1);
    transform: scale(1.02);
  }

  .drop-content {
    text-align: center;
    color: #666;
  }

  .drop-active {
    color: #2196F3;
    font-weight: 500;
  }

  .drop-placeholder {
    color: #999;
  }
</style>
