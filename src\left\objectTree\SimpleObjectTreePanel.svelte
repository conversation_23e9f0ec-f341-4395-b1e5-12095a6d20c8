<script lang="ts">
  /**
   * 简化的对象树面板
   * 使用 SceneModelState 中的子对象遍历场景
   */

  import {
    sceneModelState,
    hasScene
  } from '../../stores/sceneModelStore';
  import SimpleObjectTreeNode from './SimpleObjectTreeNode.svelte';
  import ContextMenu from './ContextMenu.svelte';
  import { contextMenuManager } from './contextMenuLogic';
  import { handleObjectCreation } from './objectCreationLogic';
  import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';

  // 响应式数据
  let currentState = $derived($sceneModelState);
  let hasCurrentScene = $derived($hasScene);
  let currentScene = $derived(currentState.currentScene);
  let children = $derived(currentScene?.children || []);

  // 场景选择相关
  let selectedScene = $state('');

  // RPG Maker MZ 场景列表
  const sceneList = [
    { className: 'Scene_Boot', displayName: '启动场景 (Scene_Boot)' },
    { className: 'Scene_Title', displayName: '标题场景 (Scene_Title)' },
    { className: 'Scene_Map', displayName: '地图场景 (Scene_Map)' },
    { className: 'Scene_Menu', displayName: '菜单场景 (Scene_Menu)' },
    { className: 'Scene_Item', displayName: '物品场景 (Scene_Item)' },
    { className: 'Scene_Skill', displayName: '技能场景 (Scene_Skill)' },
    { className: 'Scene_Equip', displayName: '装备场景 (Scene_Equip)' },
    { className: 'Scene_Status', displayName: '状态场景 (Scene_Status)' },
    { className: 'Scene_Options', displayName: '选项场景 (Scene_Options)' },
    { className: 'Scene_File', displayName: '文件场景 (Scene_File)' },
    { className: 'Scene_Save', displayName: '保存场景 (Scene_Save)' },
    { className: 'Scene_Load', displayName: '读取场景 (Scene_Load)' },
    { className: 'Scene_GameEnd', displayName: '游戏结束场景 (Scene_GameEnd)' },
    { className: 'Scene_Shop', displayName: '商店场景 (Scene_Shop)' },
    { className: 'Scene_Name', displayName: '命名场景 (Scene_Name)' },
    { className: 'Scene_Debug', displayName: '调试场景 (Scene_Debug)' },
    { className: 'Scene_Battle', displayName: '战斗场景 (Scene_Battle)' },
    { className: 'Scene_Gameover', displayName: '游戏结束场景 (Scene_Gameover)' }
  ];

  // 右键菜单状态
  let contextMenuData = $state({
    x: 0,
    y: 0,
    visible: false,
    targetNode: null as BaseObjectModel | null
  });

  // 设置右键菜单回调
  $effect(() => {
    contextMenuManager.setCallbacks({
      onShow: (data) => {
        contextMenuData = { ...data };
      },
      onHide: () => {
        contextMenuData = {
          x: 0,
          y: 0,
          visible: false,
          targetNode: null
        };
      },
      onCreateObject: handleCreateObject,
      onDeleteObject: handleDeleteObject,
      onCopyObject: handleCopyObject
    });
  });

  // 处理场景切换
  function handleSceneChange() {
    if (selectedScene && typeof window !== 'undefined' && window.SceneManager) {
      console.log('🎬 切换到场景:', selectedScene);

      try {
        // 获取场景类
        const SceneClass = (window as any)[selectedScene];
        if (SceneClass) {
          // 使用 SceneManager 切换场景
          window.SceneManager.goto(SceneClass);
          console.log('✅ 场景切换成功:', selectedScene);
        } else {
          console.error('❌ 场景类不存在:', selectedScene);
          alert(`场景类 ${selectedScene} 不存在`);
        }
      } catch (error) {
        console.error('❌ 场景切换失败:', error);
        alert('场景切换失败：' + (error as Error).message);
      }
    }
  }

  // 处理创建对象
  function handleCreateObject(objectType: string, targetNode: BaseObjectModel) {
    console.log('面板处理创建对象:', objectType, targetNode.className);
    handleObjectCreation(objectType, targetNode);
  }

  // 🎯 处理复制对象
  function handleCopyObject(targetNode: BaseObjectModel) {
    console.log('面板处理复制对象:', targetNode.className);

    // 检查是否为根节点（Scene类型通常是根节点）
    if (targetNode.className.includes('Scene')) {
      console.warn('不能复制场景根节点');
      return;
    }

    try {
      // 🎯 使用带历史记录的复制方法
      const copiedObject = targetNode.cloneWithHistory();

      if (copiedObject) {
        console.log('✅ 对象复制成功（已记录历史）:', copiedObject.className);

        // 选中新复制的对象
        import('../../stores/sceneModelStore').then(({ selectObject }) => {
          selectObject(copiedObject);
        });
      } else {
        console.warn('❌ 对象复制失败');
      }

    } catch (error) {
      console.error('❌ 复制对象时发生错误:', error);
      alert('复制失败：' + (error as Error).message);
    }
  }

  // 处理删除对象
  function handleDeleteObject(targetNode: BaseObjectModel) {
    console.log('面板处理删除对象:', targetNode.className);

    // 检查是否为根节点（Scene类型通常是根节点）
    if (targetNode.className.includes('Scene')) {
      console.warn('不能删除场景根节点');
      return;
    }

    // // 确认删除操作
    // if (!confirm(`确定要删除 "${targetNode.className}" 吗？`)) {
    //   return;
    // }

    try {
      // 从选择列表中移除该对象（如果已选中）
      import('../../stores/sceneModelStore').then(({ removeFromSelection }) => {
        removeFromSelection(targetNode);
      });

      // 🎯 使用带历史记录的删除方法
      targetNode.destroyWithHistory();

      console.log('✅ 对象删除成功（已记录历史）:', targetNode.className);

    } catch (error) {
      console.error('❌ 删除对象时发生错误:', error);
      alert('删除失败：' + (error as Error).message);
    }
  }



  // 关闭右键菜单
  function closeContextMenu() {
    contextMenuManager.hideContextMenu();
  }
</script>

<div class="object-tree-panel">
  <!-- 头部标题和场景选择 -->
  <div class="toolbar">
    <h3 class="panel-title">响应式对象树</h3>
    <div class="scene-selector">
      <select bind:value={selectedScene} onchange={handleSceneChange} class="scene-select">
        <option value="">选择场景</option>
        {#each sceneList as scene}
          <option value={scene.className}>{scene.displayName}</option>
        {/each}
      </select>
    </div>
  </div>

  <!-- 对象树内容 -->
  <div class="tree-content">
    {#if !hasCurrentScene}
      <div class="empty-state">
        <div class="empty-icon">🌳</div>
        <p class="empty-message">暂无场景</p>
        <p class="empty-hint">点击测试按钮或按 Ctrl+T 创建测试场景</p>
        <div class="shortcut-hints">
          <p class="shortcut-title">快捷键提示：</p>
          <p class="shortcut-item">Ctrl+T - 创建测试场景</p>
          <p class="shortcut-item">🎯 Ctrl+D - 复制选中对象（支持撤销）</p>
          <p class="shortcut-item">🎯 Delete - 删除选中对象（支持撤销）</p>
          <p class="shortcut-item">Ctrl+S - 保存操作记录</p>
          <p class="shortcut-item">🎯 Ctrl+Z - 撤销操作</p>
          <p class="shortcut-item">🎯 Ctrl+Y - 重做操作</p>
          <p class="shortcut-item">🎯 右键菜单 - 复制/删除对象</p>
        </div>
      </div>
    {:else}
      <div class="tree-nodes">
        <!-- 场景根节点（自动处理子对象显示） -->
        {#if currentScene}
          <SimpleObjectTreeNode node={currentScene} level={0} />
        {/if}
      </div>
    {/if}
  </div>

  <!-- 底部信息 -->
  {#if hasCurrentScene}
    <div class="footer-info">
      <span class="scene-info">
        场景: {currentScene?.className || '未知'}
      </span>
      <span class="total-count">
        子对象: {children.length}
      </span>
    </div>
  {/if}

  <!-- 右键菜单 -->
  <ContextMenu
    {contextMenuData}
    onCreateObject={handleCreateObject}
    onDeleteObject={handleDeleteObject}
    onCopyObject={handleCopyObject}
    onClose={closeContextMenu}
  />
</div>

<style>
  .object-tree-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-background);
    border-radius: 8px;
    overflow: hidden;
  }

  .toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px; /* 减小内边距，让头部更紧凑 */
    background: var(--theme-surface);
    border-bottom: 1px solid var(--theme-border);
    flex-shrink: 0;
    gap: 12px; /* 添加间距 */
  }



  .panel-title {
    margin: 0;
    font-size: 14px; /* 减小字体大小 */
    font-weight: 600;
    color: var(--theme-text);
    flex-shrink: 0; /* 防止标题被压缩 */
  }

  .scene-selector {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .scene-select {
    background: var(--theme-background);
    color: var(--theme-text);
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    min-width: 140px;
    cursor: pointer;
    transition: border-color 0.2s ease;
  }

  .scene-select:hover {
    border-color: var(--theme-primary, #2196F3);
  }

  .scene-select:focus {
    outline: none;
    border-color: var(--theme-primary, #2196F3);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
  }

  .tree-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
  }

  .tree-nodes {
    padding: 0 8px;
  }



  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    text-align: center;
    color: var(--theme-text-secondary);
  }

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-message {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: var(--theme-text);
  }

  .empty-hint {
    font-size: 14px;
    margin: 0 0 16px 0;
    opacity: 0.7;
  }

  .shortcut-hints {
    margin-top: 16px;
    padding: 12px;
    background: var(--theme-surface-light, rgba(255, 255, 255, 0.05));
    border-radius: 6px;
    border: 1px dashed var(--theme-border, #e2e8f0);
  }

  .shortcut-title {
    font-size: 12px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--theme-text, #333);
  }

  .shortcut-item {
    font-size: 11px;
    margin: 4px 0;
    color: var(--theme-text-secondary, #666);
    font-family: 'Courier New', monospace;
  }

  .footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: var(--theme-surface);
    border-top: 1px solid var(--theme-border);
    font-size: 12px;
    color: var(--theme-text-secondary);
    flex-shrink: 0;
  }

  .scene-info {
    font-weight: 500;
    color: var(--theme-text);
  }

  .total-count {
    font-weight: 500;
  }
</style>
