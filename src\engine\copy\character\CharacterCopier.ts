/**
 * 角色对象复制器
 */

import type { CharacterCopyOptions, CopyResult } from './types';
import { DEFAULT_COPY_OPTIONS } from './types';
import { CopyUtils } from './CopyUtils';

/**
 * 角色复制器类
 */
export class CharacterCopier {
  
  /**
   * 复制角色对象
   * @param sourceCharacter 源角色对象
   * @param options 复制选项
   * @returns 复制结果
   */
  static async copy(sourceCharacter: any, options: CharacterCopyOptions = {}): Promise<CopyResult> {
    console.log('=== CharacterCopier: 开始复制角色对象 ===');
    console.log('源对象:', sourceCharacter);
    console.log('复制选项:', options);

    try {
      // 合并默认选项
      const copyOptions = { ...DEFAULT_COPY_OPTIONS, ...options };

      // 等待资源加载
      const resourcesReady = await CopyUtils.waitForResources();
      if (!resourcesReady) {
        throw new Error('资源加载超时');
      }

      // 解析源对象结构
      const structure = CopyUtils.parseObjectStructure(sourceCharacter);
      console.log('源对象结构:', structure);

      // 处理直接的 Sprite_Character 对象
      if (!structure.gameObject && sourceCharacter._character) {
        structure.gameObject = sourceCharacter._character;
        structure.displayObject = sourceCharacter;
      }

      if (!structure.gameObject) {
        throw new Error('无法找到有效的游戏对象');
      }

      const sourceGameObject = structure.gameObject;
      const sourceSprite = structure.displayObject;

      // 验证是否为角色对象
      const gameObjectType = sourceGameObject.constructor?.name;
      if (gameObjectType !== 'Game_Character') {
        throw new Error(`期望 Game_Character 对象，实际得到: ${gameObjectType}`);
      }

      console.log('源角色信息:', {
        characterName: sourceGameObject._characterName,
        characterIndex: sourceGameObject._characterIndex,
        direction: sourceGameObject._direction,
        x: sourceGameObject._x,
        y: sourceGameObject._y
      });

      // 动态导入创建器
      const { createCharacter } = await import('../../creators/character/CharacterCreator');

      // 创建新的角色对象
      const newCharacter = await createCharacter(
        options.characterName || sourceGameObject._characterName,
        options.characterIndex !== undefined ? options.characterIndex : sourceGameObject._characterIndex,
        sourceGameObject._direction
      );

      if (!newCharacter) {
        throw new Error('创建新角色对象失败');
      }

      // 应用位置偏移
      if (newCharacter._character && copyOptions.positionOffset) {
        CopyUtils.applyPositionOffset(newCharacter._character, copyOptions.positionOffset);
      }

      // 复制显示属性
      if (newCharacter && sourceSprite) {
        CopyUtils.copyDisplayProperties(sourceSprite, newCharacter);
      }

      // 更新名称
      if (copyOptions.nameSuffix) {
        CopyUtils.updateDisplayName(newCharacter, copyOptions.nameSuffix);
      }

      console.log('角色对象复制成功:', newCharacter);

      return {
        success: true,
        copiedObject: newCharacter,
        sourceType: CopyUtils.detectObjectType(sourceCharacter),
        targetType: CopyUtils.detectObjectType(newCharacter)
      };

    } catch (error) {
      console.error('CharacterCopier: 复制角色对象失败:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        sourceType: CopyUtils.detectObjectType(sourceCharacter)
      };
    }
  }

  /**
   * 批量复制角色对象
   * @param sourceCharacters 源角色对象数组
   * @param options 复制选项
   * @returns 复制结果数组
   */
  static async copyMultiple(
    sourceCharacters: any[], 
    options: CharacterCopyOptions = {}
  ): Promise<CopyResult[]> {
    console.log(`=== CharacterCopier: 批量复制 ${sourceCharacters.length} 个角色对象 ===`);

    const results: CopyResult[] = [];

    for (let i = 0; i < sourceCharacters.length; i++) {
      const sourceCharacter = sourceCharacters[i];
      
      // 为每个对象生成不同的偏移
      const characterOptions = {
        ...options,
        positionOffset: {
          x: (options.positionOffset?.x || 1) + i,
          y: (options.positionOffset?.y || 1) + i
        }
      };

      const result = await this.copy(sourceCharacter, characterOptions);
      results.push(result);

      // 如果复制失败，记录但继续处理其他对象
      if (!result.success) {
        console.warn(`第 ${i + 1} 个角色对象复制失败:`, result.error);
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`批量复制完成: ${successCount}/${sourceCharacters.length} 成功`);

    return results;
  }

  /**
   * 验证角色对象是否可以复制
   * @param sourceCharacter 源角色对象
   * @returns 是否可以复制
   */
  static canCopy(sourceCharacter: any): boolean {
    try {
      const structure = CopyUtils.parseObjectStructure(sourceCharacter);
      
      // 处理直接的 Sprite_Character 对象
      if (!structure.gameObject && sourceCharacter._character) {
        structure.gameObject = sourceCharacter._character;
      }

      if (!structure.gameObject) {
        return false;
      }

      // 检查是否为角色对象
      const gameObjectType = structure.gameObject.constructor?.name;
      if (gameObjectType !== 'Game_Character') {
        return false;
      }

      // 检查必要属性
      const gameObject = structure.gameObject;
      return !!(
        gameObject._characterName &&
        gameObject._characterIndex !== undefined &&
        gameObject._direction !== undefined
      );

    } catch (error) {
      console.warn('CharacterCopier: 验证对象时出错:', error);
      return false;
    }
  }

  /**
   * 获取角色对象信息
   * @param sourceCharacter 源角色对象
   * @returns 角色信息
   */
  static getCharacterInfo(sourceCharacter: any): any {
    try {
      const structure = CopyUtils.parseObjectStructure(sourceCharacter);
      
      // 处理直接的 Sprite_Character 对象
      if (!structure.gameObject && sourceCharacter._character) {
        structure.gameObject = sourceCharacter._character;
      }

      if (!structure.gameObject) {
        return null;
      }

      const gameObject = structure.gameObject;
      
      return {
        type: 'Game_Character',
        characterName: gameObject._characterName,
        characterIndex: gameObject._characterIndex,
        direction: gameObject._direction,
        position: { x: gameObject._x, y: gameObject._y },
        isWrapper: structure.isWrapper,
        displayName: structure.displayName
      };

    } catch (error) {
      console.warn('CharacterCopier: 获取角色信息时出错:', error);
      return null;
    }
  }
}
