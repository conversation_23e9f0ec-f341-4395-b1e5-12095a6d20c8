import { BaseObjectModel } from '../baseObjectModel.svelte';

/**
 * UIItem 模型类
 * 支持数据绑定和子组件管理
 */
export class ItemModel extends BaseObjectModel {
    // 组件类型
    public readonly componentType = 'UIItem';

    // 基础属性
    enabled = $state<boolean>(true);

    // 数据绑定相关属性
    dataBindings = $state<Map<BaseObjectModel, string>>(new Map());
    currentData = $state<any>(null);

    // UI 绑定项状态（用于属性面板显示）
    bindingItems = $state<Array<{
        id: string;
        targetObject: any;
        fieldName: string;
        dataType: string;
    }>>([]);

    // 事件代码属性
    onClickCode = $state<string>('');
    onDoubleClickCode = $state<string>('');
    onHoverCode = $state<string>('');

    constructor(originalObject: any) {
        super(originalObject);
        console.log('🎭 ItemModel: 创建 UIItem 模型', originalObject);

        // 初始化基础属性
        this.enabled = originalObject?.enabled !== false;

        // 初始化事件代码
        this.onClickCode = originalObject?.onClickCode || originalObject?._eventCodes?.onClick || '';
        this.onDoubleClickCode = originalObject?.onDoubleClickCode || originalObject?._eventCodes?.onDoubleClick || '';
        this.onHoverCode = originalObject?.onHoverCode || originalObject?._eventCodes?.onHover || '';

        // 🔑 初始化数据绑定状态（从显示对象恢复）
        if (originalObject?.dataBindings && originalObject.dataBindings instanceof Map) {
            // 从显示对象的dataBindings恢复模型的dataBindings
            this.dataBindings = new Map(originalObject.dataBindings);
        }

        // 🔑 初始化当前数据
        this.currentData = originalObject?.currentData || null;

        // 🔑 初始化绑定项状态（用于UI显示，从显示对象恢复）
        this.bindingItems = originalObject?.bindingItems || [];

        // setupSync() 已经在基类构造函数中调用了
    }

    /**
     * 设置特有属性同步（重写基类方法）
     * 🔑 注意：此方法在BaseObjectModel的$effect内部调用，不要再使用$effect
     */
    protected setupSpecificSync(): void {
        // 同步 enabled 属性
        if (this._originalObject.enabled !== this.enabled) {
            this._originalObject.enabled = this.enabled;
        }

        // 同步事件代码
        if (this._originalObject._eventCodes) {
            if (this._originalObject._eventCodes.onClick !== this.onClickCode) {
                this._originalObject._eventCodes.onClick = this.onClickCode;
            }
            if (this._originalObject._eventCodes.onDoubleClick !== this.onDoubleClickCode) {
                this._originalObject._eventCodes.onDoubleClick = this.onDoubleClickCode;
            }
            if (this._originalObject._eventCodes.onHover !== this.onHoverCode) {
                this._originalObject._eventCodes.onHover = this.onHoverCode;
            }
        }

        // 🔑 同步数据绑定状态到显示对象（保持状态）
        if (this._originalObject.dataBindings !== this.dataBindings) {
            this._originalObject.dataBindings = this.dataBindings;
        }

        // 🔑 同步当前数据
        if (this._originalObject.currentData !== this.currentData) {
            this._originalObject.currentData = this.currentData;
        }

        // 同步绑定项状态（用于UI显示）
        if (this._originalObject.bindingItems !== this.bindingItems) {
            this._originalObject.bindingItems = this.bindingItems;
        }
    }

    /**
     * 添加数据绑定
     * @param childModel 子组件模型
     * @param fieldName 字段名
     */
    addDataBinding(childModel: BaseObjectModel, fieldName: string): void {
        console.log('🔗 ItemModel: 添加数据绑定', {
            component: childModel.className,
            field: fieldName
        });

        // 🔑 在模型中保存绑定状态
        this.dataBindings.set(childModel, fieldName);

        // 🔑 同步到显示对象（通过setupSpecificSync自动触发）
        const childOriginal = childModel.getOriginalObject();
        if (this._originalObject && typeof this._originalObject.addDataBinding === 'function') {
            this._originalObject.addDataBinding(childOriginal, fieldName);
        }

        // 🔑 触发响应式更新（重新赋值以触发$state）
        this.dataBindings = new Map(this.dataBindings);
    }

    /**
     * 移除数据绑定
     * @param childModel 子组件模型
     */
    removeDataBinding(childModel: BaseObjectModel): void {
        console.log('🔗 ItemModel: 移除数据绑定', {
            component: childModel.className
        });

        // 🔑 从模型中移除绑定状态
        this.dataBindings.delete(childModel);

        // 🔑 同步到显示对象
        const childOriginal = childModel.getOriginalObject();
        if (this._originalObject && typeof this._originalObject.removeDataBinding === 'function') {
            this._originalObject.removeDataBinding(childOriginal);
        }

        // 🔑 触发响应式更新
        this.dataBindings = new Map(this.dataBindings);
    }

    /**
     * 绑定数据对象
     * @param dataObject 数据对象
     */
    bindData(dataObject: any): void {
        console.log('📊 ItemModel: 绑定数据对象', dataObject);

        // 🔑 在模型中保存当前数据
        this.currentData = dataObject;

        // 🔑 同步到显示对象（通过setupSpecificSync自动触发）
        if (this._originalObject && typeof this._originalObject.bindData === 'function') {
            this._originalObject.bindData(dataObject);
        }
    }

    /**
     * 获取所有数据绑定
     */
    getDataBindings(): Map<BaseObjectModel, string> {
        return new Map(this.dataBindings);
    }

    /**
     * 清除所有数据绑定
     */
    clearDataBindings(): void {
        console.log('🔗 ItemModel: 清除所有数据绑定');

        // 🔑 清除模型中的绑定状态
        this.dataBindings.clear();

        // 🔑 同步到显示对象
        if (this._originalObject && typeof this._originalObject.clearDataBindings === 'function') {
            this._originalObject.clearDataBindings();
        }

        // 🔑 触发响应式更新
        this.dataBindings = new Map(this.dataBindings);
    }

    /**
     * 获取绑定状态信息
     */
    getBindingInfo(): string {
        const info: string[] = [];

        if (this.currentData) info.push('已绑定数据');
        if (this.dataBindings.size > 0) info.push(`${this.dataBindings.size} 个字段绑定`);

        return info.length > 0 ? info.join(', ') : '未绑定数据';
    }

    /**
     * 克隆当前 UIItem 对象 - 调用插件的 clone 方法
     */
    clone(): ItemModel {
        console.log('🔄 ItemModel: 开始克隆 UIItem 对象（调用插件方法）');

        // 1. 调用原始 UIItem 对象的 clone 方法
        const originalUIItem = this.getOriginalObject();
        if (!originalUIItem || typeof originalUIItem.clone !== 'function') {
            console.error('❌ ItemModel: 原始对象没有 clone 方法');
            throw new Error('UIItem 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUIItem = originalUIItem.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 ItemModel 包装克隆的对象
        const clonedModel = new ItemModel(clonedUIItem);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        // 6. 复制事件代码
        clonedModel.onClickCode = this.onClickCode;
        clonedModel.onDoubleClickCode = this.onDoubleClickCode;
        clonedModel.onHoverCode = this.onHoverCode;

        // 7. 重建数据绑定关系
        this.rebuildDataBindingsForClone(clonedModel, clonedChildrenModels);

        console.log('✅ ItemModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 为克隆对象重建数据绑定关系
     * @param clonedItem 克隆的 ItemModel 对象
     * @param clonedChildren 克隆的子对象数组
     */
    private rebuildDataBindingsForClone(clonedItem: ItemModel, clonedChildren: BaseObjectModel[]): void {
        console.log('🔗 ItemModel: 重建克隆对象的数据绑定关系');

        // 找到原始绑定对象在子对象数组中的索引
        const findChildIndex = (boundObject: BaseObjectModel): number => {
            if (!boundObject) return -1;
            for (let i = 0; i < this.children.length; i++) {
                if (this.children[i] === boundObject) {
                    return i;
                }
            }
            return -1;
        };

        // 重建所有数据绑定
        this.dataBindings.forEach((fieldName, originalChild) => {
            const index = findChildIndex(originalChild);
            if (index >= 0 && index < clonedChildren.length) {
                clonedItem.addDataBinding(clonedChildren[index], fieldName);
                console.log('🔗 重新绑定数据字段:', fieldName);
            }
        });
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UIItem 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        const baseArgs = super.getConstructorArgs();

        return {
            ...baseArgs,
            // UIItem 特有属性
            enabled: this.enabled,

            // 🔑 修复：安全地处理绑定项状态，避免循环引用
            bindingItems: this.bindingItems.map(item => ({
                id: item.id,
                fieldName: item.fieldName,
                dataType: item.dataType,
                // 不保存targetObject引用，避免循环引用
                targetObject: null
            })),

            // 事件代码
            _eventCodes: {
                onClick: this.onClickCode,
                onDoubleClick: this.onDoubleClickCode,
                onHover: this.onHoverCode
            }
        };
    }

    /**
     * 从JSON反序列化
     */
    static fromJSON(data: any): ItemModel {
        return new ItemModel(data);
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const lines: string[] = [];

        // 生成构造函数调用
        lines.push(`${indent}const ${varName} = new UIItem({`);
        lines.push(`${indent}    width: ${this.width},`);
        lines.push(`${indent}    height: ${this.height},`);
        lines.push(`${indent}    enabled: ${this.enabled}`);
        lines.push(`${indent}});`);

        return lines.join('\n');
    }

    /**
     * 重写特定属性设置代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns UIItem 特定属性设置代码
     */
    protected generateSpecificProperties(varName: string, indent: string): string {
        const codes: string[] = [];

        // 事件代码设置
        if (this.onClickCode) {
            codes.push(`${indent}// 点击事件`);
            codes.push(`${indent}${varName}._eventCodes.onClick = \`${this.onClickCode}\`;`);
        }

        if (this.onDoubleClickCode) {
            codes.push(`${indent}// 双击事件`);
            codes.push(`${indent}${varName}._eventCodes.onDoubleClick = \`${this.onDoubleClickCode}\`;`);
        }

        if (this.onHoverCode) {
            codes.push(`${indent}// 悬停事件`);
            codes.push(`${indent}${varName}._eventCodes.onHover = \`${this.onHoverCode}\`;`);
        }

        return codes.join('\n');
    }

    /**
     * 生成数据绑定代码
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 数据绑定代码
     */
    generateDataBindingCode(varName: string, indent: string = ''): string {
        const codes: string[] = [];

        if (this.dataBindings.size > 0) {
            codes.push(`${indent}// 数据绑定配置`);
            this.dataBindings.forEach((fieldName, childModel) => {
                const childVarName = `${varName}_${childModel.name || 'child'}`;
                codes.push(`${indent}${varName}.addDataBinding(${childVarName}, '${fieldName}');`);
            });
        }

        return codes.join('\n');
    }

    /**
     * 获取组件状态信息
     */
    public getComponentInfo(): string {
        const info: string[] = [];

        info.push(`类型: ${this.componentType}`);
        info.push(`状态: ${this.enabled ? '启用' : '禁用'}`);

        if (this.dataBindings.size > 0) {
            info.push(`数据绑定: ${this.dataBindings.size} 个`);
        }

        const eventCount = [this.onClickCode, this.onDoubleClickCode, this.onHoverCode]
            .filter(code => code.trim()).length;
        if (eventCount > 0) {
            info.push(`事件: ${eventCount} 个`);
        }

        return info.join(', ');
    }

    /**
     * 获取事件配置信息
     */
    public getEventInfo(): string {
        const events: string[] = [];

        if (this.onClickCode.trim()) events.push('点击');
        if (this.onDoubleClickCode.trim()) events.push('双击');
        if (this.onHoverCode.trim()) events.push('悬停');

        return events.length > 0 ? `已配置: ${events.join(', ')}` : '未配置事件';
    }

    /**
     * 检查是否有数据绑定
     */
    public hasDataBindings(): boolean {
        return this.dataBindings.size > 0;
    }

    /**
     * 检查是否有事件代码
     */
    public hasEventCodes(): boolean {
        return !!(this.onClickCode.trim() || this.onDoubleClickCode.trim() || this.onHoverCode.trim());
    }
}

// 注册 ItemModel 到基类容器
BaseObjectModel.registerModel('UIItem', ItemModel);
BaseObjectModel.registerModel('Item', ItemModel);

// 导出到全局，以便在 ListModel 中使用
if (typeof window !== 'undefined') {
    (window as any).ItemModel = ItemModel;
}
