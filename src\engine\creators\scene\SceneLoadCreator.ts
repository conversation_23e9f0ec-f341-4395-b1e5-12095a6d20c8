/**
 * Scene_Load 创建器
 * 专门用于创建读取场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';
import { type FileSceneOptions } from './SceneFileCreator';

/**
 * 读取场景创建选项
 */
export interface LoadSceneOptions extends FileSceneOptions {
    /** 是否自动选择最新的保存文件 */
    autoSelectLatest?: boolean;
}

/**
 * 创建读取场景
 * @param options 创建选项
 * @returns 创建的读取场景实例
 */
export async function createSceneLoad(options: LoadSceneOptions = {}): Promise<any> {
    console.log('=== 创建读取场景 Scene_Load ===');
    
    try {
        // 预加载读取场景资源
        BaseSceneCreator.preloadSceneResources('Scene_Load');
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Load', defaultOptions);
        
        // Scene_Load 特定的设置
        if (options.autoSelectLatest && scene._listWindow) {
            // 这里可以实现自动选择最新保存文件的逻辑
            console.log('设置自动选择最新保存文件');
        }
        
        console.log('Scene_Load 创建完成');
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Load 失败:', error);
        throw error;
    }
}

/**
 * 创建并启动读取场景
 * @param options 创建选项
 * @returns 创建的读取场景实例
 */
export async function createAndStartSceneLoad(options: LoadSceneOptions = {}): Promise<any> {
    return createSceneLoad({ ...options, autoStart: true });
}

/**
 * 创建简单的读取场景（用于测试）
 * @returns 创建的读取场景实例
 */
export async function createSimpleSceneLoad(): Promise<any> {
    return createSceneLoad({
        initialFileIndex: 1,
        mode: 'load',
        autoSelectLatest: true,
        backgroundType: 1,
        autoStart: false,
        addToStage: true
    });
}
