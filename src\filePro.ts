/**
 * 文件操作处理模块
 * 统一管理操作记录文件的保存和加载
 */

import { OperationRecordsAPI } from './lib';
import { OperationManager } from './generators/operationManager';

import { getCurrentProjectState } from './stores/projectStore';

// 创建全局 OperationManager 实例
export const globalOperationManager = new OperationManager();

/**
 * 初始化时加载保存的插件代码
 */
export async function initializePluginCode(): Promise<void> {
  console.log('=== 初始化插件代码 ===');

  try {
    const projectState = getCurrentProjectState();

    if (!projectState.isLoaded || !projectState.projectPath) {
      console.log('没有加载的项目，跳过初始化');
      return;
    }

    // 尝试加载保存的插件代码
    const loadResult = await OperationRecordsAPI.loadOperationRecords(projectState.projectPath);

    if (loadResult.success && loadResult.data) {
      try {
        // 解析JSON数据
        const parsedData = JSON.parse(loadResult.data);

        // 检查是否有插件代码
        if (parsedData.pluginCode && typeof parsedData.pluginCode === 'string') {
          globalOperationManager.loadPluginCode(parsedData.pluginCode);
          console.log('已加载保存的插件代码');
        } else {
          console.log('没有找到有效的插件代码');
        }
      } catch (error) {
        console.error('解析保存的数据失败:', error);
      }
    } else {
      console.log('没有找到保存的插件代码');
    }

  } catch (error) {
    console.error('初始化插件代码时发生异常:', error);
  }
}


/**
 * 保存操作记录到当前项目 - 新架构
 */
export async function saveOperationRecords(): Promise<void> {
  console.log('=== 开始保存当前场景插件代码 ===');

  try {
    const projectState = getCurrentProjectState();

    if (!projectState.isLoaded || !projectState.projectPath) {
      console.log('没有加载的项目，跳过保存操作记录');
      return;
    }

    // 保存当前场景的插件代码
    const pluginCode = await globalOperationManager.saveCurrentScenePluginCode();

    if (!pluginCode) {
      console.warn('没有场景数据，无法生成插件代码');
      return;
    }

    console.log('准备保存的插件代码长度:', pluginCode.length);

    // 将插件代码包装成JSON格式
    const recordsData = {
      pluginCode: pluginCode,
      generatedAt: Date.now(),
      version: '2.0.0'
    };

    // 保存到.mlzh文件
    const saveResult = await OperationRecordsAPI.saveOperationRecords(
      projectState.projectPath,
      JSON.stringify(recordsData, null, 2)
    );

    if (saveResult.success) {
      console.log('=== 插件代码保存成功 ===');
      console.log('保存路径:', saveResult.data);
    } else {
      console.error('保存插件代码失败:', saveResult.error);
    }

  } catch (error) {
    console.error('保存操作记录时发生异常:', error);
  }
}





/**
 * 从指定项目路径加载操作记录
 */
export async function loadOperationRecords(projectPath: string): Promise<{ success: boolean; message?: string }> {
  console.log('=== 开始加载操作记录 ===');
  console.log('项目路径:', projectPath);

  try {
    const loadResult = await OperationRecordsAPI.loadOperationRecords(projectPath);

    if (!loadResult.success) {
      console.error('加载操作记录失败:', loadResult.error);
      return { success: false, message: loadResult.error };
    }

    if (!loadResult.data) {
      console.log('项目中没有操作记录文件');
      return { success: true, message: '项目中没有操作记录文件' };
    }

    // 解析加载的数据
    const parsedData = JSON.parse(loadResult.data);

    // 检查数据格式 - 新格式包含pluginCode
    if (parsedData.records && parsedData.records.pluginCode) {
      console.log('检测到插件代码数据，开始恢复到 OperationManager...');

      // 清空现有记录
      globalOperationManager.clearAllRecords();

      // 加载插件代码
      globalOperationManager.loadPluginCode(parsedData.records.pluginCode);

      console.log('=== 插件代码加载完成 ===');
      return { success: true, message: '插件代码加载成功' };
    } else {
      console.log('未识别的数据格式');
      return { success: false, message: '未识别的数据格式' };
    }

  } catch (error) {
    console.error('加载操作记录时发生异常:', error);
    return { success: false, message: String(error) };
  }
}

/**
 * 导出插件到当前项目 - 新架构
 * @param includePlugins 要包含的插件文件名列表
 */
export async function exportPlug(includePlugins?: string[]): Promise<void> {
  console.log('=== 开始导出插件 ===');

  try {
    const projectState = getCurrentProjectState();

    if (!projectState.isLoaded || !projectState.projectPath) {
      console.log('没有加载的项目，无法导出插件');

      if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
        new Notification('导出插件失败', {
          body: '没有加载的项目',
          icon: '/favicon.ico'
        });
      }
      return;
    }

    // 获取已保存的插件代码
    let pluginCode = globalOperationManager.getSavedPluginCode();

    // 如果没有保存的代码，先保存当前场景（包含指定插件）
    if (!pluginCode) {
      console.log('没有保存的插件代码，先保存当前场景...');
      if (includePlugins && includePlugins.length > 0) {
        console.log('包含插件:', includePlugins);
      }
      pluginCode = await globalOperationManager.saveCurrentScenePluginCode(undefined, includePlugins);
    } else if (includePlugins && includePlugins.length > 0) {
      // 如果有保存的代码但需要包含新插件，重新生成
      console.log('重新生成插件代码以包含指定插件:', includePlugins);
      pluginCode = await globalOperationManager.saveCurrentScenePluginCode(undefined, includePlugins);
    }

    if (!pluginCode) {
      console.log('没有场景数据，无法生成插件');

      if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
        new Notification('导出插件失败', {
          body: '没有场景数据',
          icon: '/favicon.ico'
        });
      }
      return;
    }

    // 导出插件文件
    const exportResult = await OperationRecordsAPI.savePluginFile(
      projectState.projectPath,
      pluginCode
    );

    if (exportResult.success) {
      console.log('=== 插件导出完成 ===');
      console.log('插件文件路径:', exportResult.data);

      if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
        new Notification('插件导出成功', {
          body: '插件已保存到项目的plugins文件夹',
          icon: '/favicon.ico'
        });
      }
    } else {
      console.error('插件导出失败:', exportResult.error);

      if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
        new Notification('插件导出失败', {
          body: exportResult.error,
          icon: '/favicon.ico'
        });
      }
    }

  } catch (error) {
    console.error('导出插件时发生异常:', error);

    if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
      new Notification('插件导出异常', {
        body: String(error),
        icon: '/favicon.ico'
      });
    }
  }
}

