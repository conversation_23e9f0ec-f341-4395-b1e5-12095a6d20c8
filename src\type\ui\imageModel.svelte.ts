import { BaseObjectModel } from '../baseObjectModel.svelte';

// 裁切区域类型定义
export interface CropRegion {
    id: string;
    label: string;
    sx: number;  // 源图片裁切X
    sy: number;  // 源图片裁切Y
    sw: number;  // 源图片裁切宽度
    sh: number;  // 源图片裁切高度
    gridIndex: number;  // 在网格中的索引
}

export class ImageModel extends BaseObjectModel {

    constructor(image: any) {
        super(image);

        // 初始化图片特有属性
        this.width = image.width || 100;
        this.height = image.height || 100;
        this.imagePath = image.imagePath || '';
        this.scaleMode = image.scaleMode || 'none';
        this.preserveAspectRatio = image.preserveAspectRatio !== false;
        this.regions = image.regions || [];
        this.currentRegionIndex = image.currentRegionIndex || 0;
        this.gridRows = image.gridRows || 1;
        this.gridCols = image.gridCols || 1;

        console.log('🔧 ImageModel: 创建图片模型', image);

        // setupSync() 已经在基类构造函数中调用了
    }



    // 图片路径属性
    imagePath = $state('');             // 项目资源路径

    // 显示属性
    scaleMode = $state('none');         // 缩放模式: stretch, fit, fill, none
    preserveAspectRatio = $state(true); // 保持宽高比

    // 裁切相关属性
    regions = $state<CropRegion[]>([]);
    currentRegionIndex = $state(0);
    gridRows = $state(1);
    gridCols = $state(1);

    // 用于触发属性面板重新渲染的计数器
    private _updateCounter = $state(0);

    // 计算属性：是否为多区域裁切
    get isMultiRegion(): boolean {
        return this.regions.length > 1;
    }

    // 计算属性：当前区域
    get currentRegion(): CropRegion | null {
        return this.regions[this.currentRegionIndex] || null;
    }

    // 获取更新计数器（用于触发属性面板重新渲染）
    get updateCounter(): number {
        return this._updateCounter;
    }

    // 触发更新
    private _triggerUpdate(): void {
        this._updateCounter++;
        console.log('🔧 ImageModel: 触发更新，计数器:', this._updateCounter);
    }

    /**
     * 设置Image特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Image特有的属性
     */
    protected setupSpecificSync(): void {
        // 设置加载完成回调
        if (this._originalObject.setOnLoadCallback && typeof this._originalObject.setOnLoadCallback === 'function') {
            this._originalObject.setOnLoadCallback((width: number, height: number) => {
                console.log('🔧 ImageModel: 图片加载完成回调，更新尺寸:', width, 'x', height);
                this.width = width;
                this.height = height;
                // 同步区域数据
                this.syncRegionsFromOriginal();

                // 🔧 关键修复：触发属性面板重新检查bitmap状态
                // 通过修改一个响应式属性来触发重新渲染
                this._triggerUpdate();
            });
        }

        // 同步图片路径（如果有变化则重新加载）
        if (this._originalObject.imagePath !== this.imagePath) {
            console.log('🔧 ImageModel: 图片路径变化', {
                old: this._originalObject.imagePath,
                new: this.imagePath,
                hasSetImagePath: !!(this._originalObject.setImagePath && typeof this._originalObject.setImagePath === 'function')
            });

            this._originalObject.imagePath = this.imagePath;
            if (this._originalObject.setImagePath && typeof this._originalObject.setImagePath === 'function') {
                this._originalObject.setImagePath(this.imagePath);
            } else {
                console.warn('🔧 ImageModel: setImagePath方法不存在或不是函数');
            }
        }

        // 同步显示属性
        if (this._originalObject.scaleMode !== this.scaleMode) {
            this._originalObject.scaleMode = this.scaleMode;
            if (this._originalObject.setScaleMode && typeof this._originalObject.setScaleMode === 'function') {
                this._originalObject.setScaleMode(this.scaleMode);
            }
        }

        this._originalObject.preserveAspectRatio = this.preserveAspectRatio;

        // 同步裁切相关属性
        this._originalObject.gridRows = this.gridRows;
        this._originalObject.gridCols = this.gridCols;

        // 初始同步区域数据
        this.syncRegionsFromOriginal();

    }

    /**
     * 设置图片路径
     */
    public setImagePath(path: string): void {
        this.imagePath = path;
    }

    /**
     * 设置缩放模式
     */
    public setScaleMode(mode: 'stretch' | 'fit' | 'fill' | 'none'): void {
        this.scaleMode = mode;
    }

    /**
     * 设置图片尺寸
     */
    public setImageSize(width: number, height: number): void {
        this.width = width;
        this.height = height;
    }

    /**
     * 生成网格区域
     */
    public generateGridRegions(): void {
        if (this._originalObject && typeof this._originalObject.generateGridRegions === 'function') {
            const regions = this._originalObject.generateGridRegions(this.gridRows, this.gridCols);
            this.syncRegionsFromOriginal();

            // 生成网格后，同步第一个区域的尺寸
            if (this.isMultiRegion && this.currentRegion) {
                console.log('🔄 ImageModel: 生成网格后同步尺寸', {
                    regionIndex: this.currentRegionIndex,
                    region: this.currentRegion,
                    newSize: `${this.currentRegion.sw}x${this.currentRegion.sh}`
                });

                this.width = this.currentRegion.sw;
                this.height = this.currentRegion.sh;
            }
        }
    }

    /**
     * 设置当前区域
     */
    public setCurrentRegion(index: number): void {
        if (this._originalObject && typeof this._originalObject.setCurrentRegion === 'function') {
            if (this._originalObject.setCurrentRegion(index)) {
                this.currentRegionIndex = index;

                // 同步当前区域的尺寸到模型
                if (this.isMultiRegion && this.currentRegion) {
                    console.log('🎯 ImageModel: 设置区域后同步尺寸', {
                        regionIndex: this.currentRegionIndex,
                        region: this.currentRegion,
                        newSize: `${this.currentRegion.sw}x${this.currentRegion.sh}`
                    });

                    this.width = this.currentRegion.sw;
                    this.height = this.currentRegion.sh;
                }
            }
        }
    }

    /**
     * 重置为默认区域（整张图片）
     */
    public resetToDefaultRegion(): void {
        if (this._originalObject && typeof this._originalObject.resetToDefaultRegion === 'function') {
            this._originalObject.resetToDefaultRegion();

            // 重置网格设置
            this.gridRows = 1;
            this.gridCols = 1;

            // 同步区域数据
            this.syncRegionsFromOriginal();

            // 重置宽高为原始图片尺寸
            if (this._originalObject.bitmap) {
                this.width = this._originalObject.bitmap.width;
                this.height = this._originalObject.bitmap.height;

                console.log('🔄 ImageModel: 重置宽高为原始尺寸', {
                    width: this.width,
                    height: this.height
                });
            }

            console.log('🔄 ImageModel: 重置为默认区域', {
                regions: this.regions.length,
                currentIndex: this.currentRegionIndex,
                gridSize: `${this.gridRows}x${this.gridCols}`,
                size: `${this.width}x${this.height}`
            });
        }
    }

    /**
     * 从原始对象同步区域数据
     */
    private syncRegionsFromOriginal(): void {
        if (this._originalObject?.regions) {
            this.regions = [...this._originalObject.regions];
            this.currentRegionIndex = this._originalObject.currentRegionIndex || 0;
        }
    }

    /**
     * 获取图片信息
     */
    public getImageInfo(): {
        hasImage: boolean;
        isFromPath: boolean;
        source: string;
        size: { width: number; height: number };
        scaleMode: string;
    } {
        const hasPath = this.imagePath && this.imagePath.trim() !== '';

        return {
            hasImage: true, // 总是有图片（要么是用户指定的，要么是默认的）
            isFromPath: !!hasPath,
            source: this.imagePath || 'ui/defaultImage.png', // 没有路径时使用默认图片
            size: { width: this.width, height: this.height },
            scaleMode: this.scaleMode
        };
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 创建UIImage对象（在构造函数中传入宽高，避免异步加载问题）
        codes.push(`${indent}const ${varName} = new UIImage({`);

        // 传入目标尺寸
        codes.push(`${indent}    width: ${this.width},`);
        codes.push(`${indent}    height: ${this.height},`);

        if (this.imagePath) {
            codes.push(`${indent}    imagePath: '${this.imagePath}',`);
        }

        // 只有在多区域时才生成裁切数据
        if (this.isMultiRegion) {
            codes.push(`${indent}    regions: ${JSON.stringify(this.regions)},`);
            codes.push(`${indent}    currentRegionIndex: ${this.currentRegionIndex},`);
            codes.push(`${indent}    gridRows: ${this.gridRows},`);
            codes.push(`${indent}    gridCols: ${this.gridCols},`);
        }

        codes.push(`${indent}    scaleMode: 'none',`);
        codes.push(`${indent}    preserveAspectRatio: ${this.preserveAspectRatio}`);
        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 克隆当前图片对象 - 调用插件的 clone 方法
     */
    clone(): ImageModel {
        console.log('🔄 ImageModel: 开始克隆图片对象（调用插件方法）');

        // 1. 调用原始 UIImage 对象的 clone 方法
        const originalUIImage = this.getOriginalObject();
        if (!originalUIImage || typeof originalUIImage.clone !== 'function') {
            console.error('❌ ImageModel: 原始对象没有 clone 方法');
            throw new Error('UIImage 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUIImage = originalUIImage.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 ImageModel 包装克隆的对象
        const clonedModel = new ImageModel(clonedUIImage);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ ImageModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UIImage 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UIImage 特有属性
            imagePath: this.imagePath,
            scaleMode: this.scaleMode,
            preserveAspectRatio: this.preserveAspectRatio,
            regions: JSON.parse(JSON.stringify(this.regions)), // 深拷贝区域数据
            currentRegionIndex: this.currentRegionIndex,
            gridRows: this.gridRows,
            gridCols: this.gridCols
        };
    }

}

// 注册ImageModel到基类容器
BaseObjectModel.registerModel('UIImage', ImageModel);
BaseObjectModel.registerModel('Image', ImageModel);
