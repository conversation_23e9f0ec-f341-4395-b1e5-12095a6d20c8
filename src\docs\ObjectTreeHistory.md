# ObjectTree 历史记录功能

## 🎯 功能概述

ObjectTree 现在支持完整的撤销/重做功能，包括：

- ✅ **拖拽重排**：支持撤销节点的拖拽移动操作
- ✅ **删除操作**：支持撤销节点删除操作
- ✅ **复制操作**：支持撤销节点复制操作
- ✅ **全局快捷键**：Ctrl+Z 撤销，Ctrl+Y 重做

## 🔧 实现原理

### 基于现有历史记录系统
- 使用现有的 `historyManager` 系统
- 不创建新的历史记录管理器
- 完全兼容现有的撤销/重做机制

### 虚拟属性机制
在 `BaseObjectModel` 中添加了虚拟属性：
- `_treeParent`：记录父节点变化
- `_treeIndex`：记录索引位置变化
- `_treeDeleted`：记录删除状态
- `_treeCreated`：记录创建状态

### 操作组支持
每个用户操作作为一个操作组：
```typescript
historyManager.startGroup('移动节点', '移动UILabel到Container');
historyManager.recordChange(object, '_treeParent', oldParent, newParent);
historyManager.recordChange(object, '_treeIndex', oldIndex, newIndex);
historyManager.endGroup();
```

## 📋 使用方法

### 1. 拖拽操作
- 在 ObjectTree 中拖拽节点到新位置
- 操作会自动记录到历史记录
- 按 `Ctrl+Z` 可撤销拖拽操作

### 2. 删除操作
- 右键点击节点 → 选择"删除对象"
- 或使用 `targetNode.destroyWithHistory()`
- 按 `Ctrl+Z` 可恢复被删除的节点

### 3. 复制操作
- 右键点击节点 → 选择"复制对象"
- 或使用快捷键 `Ctrl+D` 复制选中对象
- 或使用 `targetNode.cloneWithHistory()`
- 按 `Ctrl+Z` 可移除复制的节点

### 4. 快捷键
- `Ctrl+D`：复制选中对象（支持撤销）
- `Delete`：删除选中对象（支持撤销）
- `Ctrl+Z`：撤销上一个操作
- `Ctrl+Y`：重做上一个撤销的操作

## 🧪 测试功能

### 控制台测试
在浏览器控制台中可以使用测试工具：

```javascript
// 获取场景中的对象
const state = getCurrentState();
const objects = state.currentScene.children;

// 运行完整测试
ObjectTreeHistoryTest.runCompleteTest(objects);

// 单独测试拖拽
ObjectTreeHistoryTest.testDragHistory(objects[0], objects[1], 0);

// 测试撤销
ObjectTreeHistoryTest.testUndo();

// 测试重做
ObjectTreeHistoryTest.testRedo();
```

### 手动测试步骤
1. 创建测试场景（Ctrl+T）
2. 在 ObjectTree 中拖拽节点
3. 按 Ctrl+Z 验证撤销
4. 按 Ctrl+Y 验证重做
5. 右键复制节点
6. 按 Ctrl+Z 验证撤销复制
7. 右键删除节点
8. 按 Ctrl+Z 验证恢复删除

## 🔍 技术细节

### 历史记录流程
1. **操作开始**：调用 `historyManager.startGroup()`
2. **记录变更**：调用 `historyManager.recordChange()`
3. **执行操作**：修改对象的实际状态
4. **操作结束**：调用 `historyManager.endGroup()`

### 撤销恢复流程
1. **撤销触发**：用户按 Ctrl+Z
2. **属性恢复**：`historyManager.undo()` 恢复虚拟属性
3. **执行恢复**：虚拟属性的 setter 执行实际恢复操作
4. **UI更新**：响应式系统自动更新界面

### 关键方法

#### BaseObjectModel 新增方法
- `setParentAndIndex()`：带历史记录的拖拽方法
- `destroyWithHistory()`：带历史记录的删除方法
- `cloneWithHistory()`：带历史记录的复制方法

#### 虚拟属性处理
- `executeParentChange()`：执行父节点变更
- `executeIndexChange()`：执行索引调整
- `executeDelete()`：执行删除操作
- `executeRestore()`：执行恢复操作

## 🎯 优势特点

### 1. 完全兼容
- 与现有历史记录系统完全兼容
- 不破坏现有的撤销/重做功能
- 使用统一的快捷键系统

### 2. 性能优化
- 只记录必要的变更信息
- 不保存完整的对象状态快照
- 利用响应式系统自动更新UI

### 3. 用户体验
- 自然的撤销/重做行为
- 支持复杂的树操作
- 实时的视觉反馈

### 4. 开发友好
- 简单的API接口
- 详细的调试日志
- 完整的测试工具

## 🚀 未来扩展

可以基于这个架构继续扩展：
- 支持多选操作的批量撤销
- 添加操作历史的可视化界面
- 支持更复杂的树操作（如剪切/粘贴）
- 添加操作历史的持久化存储
