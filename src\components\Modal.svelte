<script lang="ts">
  /**
   * Modal 模态框组件
   * 基于 Skeleton UI 的通用模态框
   */

  // Props
  let {
    open = false,
    title = '',
    maxWidth = 'md',
    closable = true,
    maskClosable = true,
    showHeader = true,
    showFooter = false,
    onClose = undefined,
    children,
    footer
  }: {
    open: boolean;
    title?: string;
    maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
    closable?: boolean;
    maskClosable?: boolean;
    showHeader?: boolean;
    showFooter?: boolean;
    onClose?: () => void;
    children?: import('svelte').Snippet;
    footer?: import('svelte').Snippet;
  } = $props();

  // 内部状态
  let modalElement = $state<HTMLElement>();

  // 处理关闭
  function handleClose() {
    if (closable && onClose) {
      onClose();
    }
  }

  // 处理遮罩点击
  function handleMaskClick(event: MouseEvent) {
    if (maskClosable && event.target === modalElement) {
      handleClose();
    }
  }

  // 处理键盘事件
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape' && closable) {
      handleClose();
    }
  }

  // 获取最大宽度类名
  function getMaxWidthClass(size: string): string {
    const sizeMap = {
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      full: 'max-w-full'
    };
    return sizeMap[size as keyof typeof sizeMap] || 'max-w-md';
  }

  // 监听open状态变化，管理body滚动
  $effect(() => {
    if (open) {
      document.body.style.overflow = 'hidden';
      // 聚焦到模态框以便键盘事件生效
      if (modalElement) {
        modalElement.focus();
      }
    } else {
      document.body.style.overflow = '';
    }

    // 清理函数
    return () => {
      document.body.style.overflow = '';
    };
  });
</script>

<!-- 键盘事件监听 -->
<svelte:window on:keydown={handleKeydown} />

{#if open}
  <!-- 模态框遮罩 -->
  <!-- svelte-ignore a11y_click_events_have_key_events -->
  <!-- svelte-ignore a11y_no_static_element_interactions -->
  <div
    class="modal-backdrop"
    bind:this={modalElement}
    onclick={handleMaskClick}
    tabindex="-1"
    role="dialog"
    aria-modal="true"
    aria-labelledby={title ? 'modal-title' : undefined}
  >
    <!-- 模态框内容 -->
    <div class="modal-container {getMaxWidthClass(maxWidth)}">
      <!-- 模态框头部 -->
      {#if showHeader}
        <div class="modal-header">
          {#if title}
            <h2 id="modal-title" class="modal-title">{title}</h2>
          {/if}

          {#if closable}
            <button
              class="modal-close-btn"
              onclick={handleClose}
              aria-label="关闭"
            >
              ✕
            </button>
          {/if}
        </div>
      {/if}

      <!-- 模态框主体 -->
      <div class="modal-body">
        {#if children}
          {@render children()}
        {/if}
      </div>

      <!-- 模态框底部 -->
      {#if showFooter && footer}
        <div class="modal-footer">
          {@render footer()}
        </div>
      {/if}
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--spacing-4, 1rem);
  }

  .modal-container {
    background: var(--theme-surface, #ffffff);
    border-radius: var(--border-radius-lg, 8px);
    box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4, 1rem) var(--spacing-6, 1.5rem);
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    background: var(--theme-surface-light, #f9fafb);
  }

  .modal-title {
    margin: 0;
    font-size: var(--font-size-lg, 1.125rem);
    font-weight: 600;
    color: var(--theme-text, #111827);
  }

  .modal-close-btn {
    background: none;
    border: none;
    font-size: var(--font-size-xl, 1.25rem);
    color: var(--theme-text-secondary, #6b7280);
    cursor: pointer;
    padding: var(--spacing-1, 0.25rem);
    border-radius: var(--border-radius, 4px);
    transition: all 0.2s ease;
  }

  .modal-close-btn:hover {
    background: var(--theme-surface-dark, #f3f4f6);
    color: var(--theme-text, #111827);
  }

  .modal-body {
    padding: var(--spacing-6, 1.5rem);
    overflow-y: auto;
    flex: 1;
  }

  .modal-footer {
    padding: var(--spacing-4, 1rem) var(--spacing-6, 1.5rem);
    border-top: 1px solid var(--theme-border, #e5e7eb);
    background: var(--theme-surface-light, #f9fafb);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-2, 0.5rem);
  }

  /* 响应式设计 */
  @media (max-width: 640px) {
    .modal-backdrop {
      padding: var(--spacing-2, 0.5rem);
    }

    .modal-container {
      max-height: 95vh;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
      padding-left: var(--spacing-4, 1rem);
      padding-right: var(--spacing-4, 1rem);
    }
  }
</style>
