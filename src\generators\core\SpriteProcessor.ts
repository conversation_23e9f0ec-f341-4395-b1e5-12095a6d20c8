/**
 * Sprite 处理器
 * 处理 Sprite 对象的序列化、反序列化和代码生成
 */

import { BaseProcessor } from './BaseProcessor';
import { type SpriteProperties, type BitmapProperties, type SpriteEventsProperties } from '../types/sprite';
import { BitmapUtils } from './utils/BitmapUtils';

/**
 * Sprite 处理器
 */
export class SpriteProcessor extends BaseProcessor<SpriteProperties> {

  /**
   * 序列化 Sprite 对象
   * @param obj Sprite 对象
   * @returns 序列化后的属性
   */
  serialize(obj: any): SpriteProperties {
    const baseProperties = this.extractBaseProperties(obj);

    return {
      ...baseProperties,
      // Sprite 特有属性
      blendMode: obj.blendMode !== undefined ? obj.blendMode : 0,
      zIndex: obj.zIndex !== undefined ? obj.zIndex : 0,
      // Bitmap 处理
      bitmap: BitmapUtils.serialize(obj.bitmap),
      // 事件处理
      events: this.extractEvents(obj)
    };
  }

  /**
   * 提取事件属性
   * @param obj Sprite 对象
   * @returns 事件属性或 undefined
   */
  private extractEvents(obj: any): SpriteEventsProperties | undefined {
    const events: SpriteEventsProperties = {};
    let hasEvents = false;

    // 从对象中提取事件代码
    if (obj._eventCodes) {
      if (obj._eventCodes.onClick) {
        events.onClick = obj._eventCodes.onClick;
        hasEvents = true;
      }
      if (obj._eventCodes.onHover) {
        events.onHover = obj._eventCodes.onHover;
        hasEvents = true;
      }
      if (obj._eventCodes.onHoverOut) {
        events.onHoverOut = obj._eventCodes.onHoverOut;
        hasEvents = true;
      }
      if (obj._eventCodes.onPress) {
        events.onPress = obj._eventCodes.onPress;
        hasEvents = true;
      }
      if (obj._eventCodes.onRelease) {
        events.onRelease = obj._eventCodes.onRelease;
        hasEvents = true;
      }
      if (obj._eventCodes.onDoubleClick) {
        events.onDoubleClick = obj._eventCodes.onDoubleClick;
        hasEvents = true;
      }
    }

    return hasEvents ? events : undefined;
  }

  /**
   * 反序列化为 Sprite 对象
   * @param data Sprite 属性
   * @returns Sprite 对象
   */
  deserialize(data: SpriteProperties): any {
    const sprite = this.createObject('Sprite');

    // 设置基础属性
    this.applyBaseProperties(sprite, data);

    // 设置 Sprite 特有属性
    sprite.blendMode = data.blendMode || 0;
    sprite.zIndex = data.zIndex || 0;

    // 设置 Bitmap
    if (data.bitmap) {
      sprite.bitmap = BitmapUtils.deserialize(data.bitmap);
    }

    return sprite;
  }

  /**
   * 生成 Sprite 创建代码
   * @param varName 变量名
   * @param data Sprite 属性
   * @param indent 缩进字符串
   * @returns 生成的代码字符串
   */
  generateCode(varName: string, data: SpriteProperties, indent: string): string {
    const codes: string[] = [];

    // 1. 创建 Sprite 对象，传入组合数据对象或单纯的 bitmap 数据对象
    const hasEvents = data.events && Object.keys(data.events).length > 0;

    if (data.bitmap || hasEvents) {
      if (hasEvents) {
        // 生成组合数据对象 { bitmap: {...}, events: {...} }
        codes.push(`${indent}// 创建 Sprite（包含 bitmap 和 events）`);
        const combinedDataString = this.generateCombinedDataString(data, indent + '  ');
        codes.push(`${indent}const ${varName} = new Sprite(${combinedDataString});`);
      } else {
        // 只有 bitmap，生成单纯的 bitmap 数据对象
        codes.push(`${indent}// 创建 Sprite（包含 bitmap）`);
        const bitmapDataString = this.generateBitmapDataString(data.bitmap, indent + '  ');
        codes.push(`${indent}const ${varName} = new Sprite(${bitmapDataString});`);
      }
    } else {
      codes.push(`${indent}// 创建 Sprite`);
      codes.push(`${indent}const ${varName} = new Sprite();`);
    }

    // 2. 设置基础属性
    codes.push(this.generateBasePropertiesCode(varName, data, indent));

    // 3. 设置 Sprite 特有属性
    if (data.blendMode !== undefined && data.blendMode !== 0) {
      codes.push(`${indent}${varName}.blendMode = ${data.blendMode};`);
    }
    if (data.zIndex !== undefined && data.zIndex !== 0) {
      codes.push(`${indent}${varName}.zIndex = ${data.zIndex};`);
    }

    return codes.join('\n');
  }

  /**
   * 生成组合数据对象的字符串表示
   * @param data Sprite 属性
   * @param indent 缩进字符串
   * @returns 组合数据对象字符串
   */
  private generateCombinedDataString(data: SpriteProperties, indent: string): string {
    const parts: string[] = [];

    // 添加 bitmap 部分
    if (data.bitmap) {
      const bitmapString = this.generateBitmapDataString(data.bitmap, indent + '  ');
      parts.push(`${indent}bitmap: ${bitmapString}`);
    }

    // 添加 events 部分
    if (data.events) {
      const eventsString = this.generateEventsDataString(data.events, indent + '  ');
      parts.push(`${indent}events: ${eventsString}`);
    }

    return `{\n${parts.join(',\n')}\n${indent.slice(2)}}`;
  }

  /**
   * 生成事件数据对象的字符串表示
   * @param events 事件属性
   * @param indent 缩进字符串
   * @returns 事件数据对象字符串
   */
  private generateEventsDataString(events: SpriteEventsProperties, indent: string): string {
    const props: string[] = [];

    if (events.onClick) props.push(`${indent}onClick: "${events.onClick.replace(/"/g, '\\"')}"`);
    if (events.onHover) props.push(`${indent}onHover: "${events.onHover.replace(/"/g, '\\"')}"`);
    if (events.onHoverOut) props.push(`${indent}onHoverOut: "${events.onHoverOut.replace(/"/g, '\\"')}"`);
    if (events.onPress) props.push(`${indent}onPress: "${events.onPress.replace(/"/g, '\\"')}"`);
    if (events.onRelease) props.push(`${indent}onRelease: "${events.onRelease.replace(/"/g, '\\"')}"`);
    if (events.onDoubleClick) props.push(`${indent}onDoubleClick: "${events.onDoubleClick.replace(/"/g, '\\"')}"`);

    if (props.length === 0) {
      return '{}';
    }

    return `{\n${props.join(',\n')}\n${indent.slice(2)}}`;
  }

  /**
   * 生成 bitmap 数据对象的字符串表示
   * @param bitmap bitmap 属性
   * @param indent 缩进字符串
   * @returns bitmap 数据对象字符串
   */
  protected generateBitmapDataString(bitmap: BitmapProperties, indent: string): string {
    if (!bitmap) {
      return 'null';
    }

    const props: string[] = [];

    // 基本属性
    if (bitmap.fontBold !== undefined) props.push(`fontBold: ${bitmap.fontBold}`);
    if (bitmap.fontFace) props.push(`fontFace: '${bitmap.fontFace}'`);
    if (bitmap.fontItalic !== undefined) props.push(`fontItalic: ${bitmap.fontItalic}`);
    if (bitmap.fontSize !== undefined) props.push(`fontSize: ${bitmap.fontSize}`);
    if (bitmap.outlineColor) props.push(`outlineColor: '${bitmap.outlineColor}'`);
    if (bitmap.outlineWidth !== undefined) props.push(`outlineWidth: ${bitmap.outlineWidth}`);
    if (bitmap.textColor) props.push(`textColor: '${bitmap.textColor}'`);
    if (bitmap._paintOpacity !== undefined) props.push(`_paintOpacity: ${bitmap._paintOpacity}`);
    if (bitmap._smooth !== undefined) props.push(`_smooth: ${bitmap._smooth}`);

    // URL 属性
    if (bitmap.url) {
      props.push(`url: '${bitmap.url}'`);
    }

    // elements 数组
    if (bitmap.elements && Array.isArray(bitmap.elements)) {
      const elementsString = this.generateElementsArrayString(bitmap.elements, indent + '  ');
      props.push(`elements: ${elementsString}`);
    }

    // 如果没有任何属性，返回 null
    if (props.length === 0) {
      return 'null';
    }

    // 生成对象字符串
    const propsString = props.map(prop => `${indent}  ${prop}`).join(',\n');
    return `{\n${propsString}\n${indent}}`;
  }

  /**
   * 生成 elements 数组的字符串表示
   * @param elements elements 数组
   * @param indent 缩进字符串
   * @returns elements 数组字符串
   */
  protected generateElementsArrayString(elements: any[], indent: string): string {
    if (!elements || elements.length === 0) {
      return '[]';
    }

    const elementStrings = elements.map(element => {
      if (!element) return 'null';

      const props: string[] = [];

      // 通用属性
      if (element.type) props.push(`type: '${element.type}'`);
      if (element.id) props.push(`id: '${element.id}'`);

      // 文本元素属性
      if (element.type === 'text') {
        if (element.text) props.push(`text: '${element.text}'`);
        if (element.x !== undefined) props.push(`x: ${element.x}`);
        if (element.y !== undefined) props.push(`y: ${element.y}`);
        if (element.maxWidth !== undefined) props.push(`maxWidth: ${element.maxWidth}`);
        if (element.lineHeight !== undefined) props.push(`lineHeight: ${element.lineHeight}`);
        if (element.align) props.push(`align: '${element.align}'`);
      }

      // 图像元素属性
      if (element.type === 'image') {
        if (element.sourceUrl) props.push(`sourceUrl: '${element.sourceUrl}'`);
        if (element.sx !== undefined) props.push(`sx: ${element.sx}`);
        if (element.sy !== undefined) props.push(`sy: ${element.sy}`);
        if (element.sw !== undefined) props.push(`sw: ${element.sw}`);
        if (element.sh !== undefined) props.push(`sh: ${element.sh}`);
        if (element.dx !== undefined) props.push(`dx: ${element.dx}`);
        if (element.dy !== undefined) props.push(`dy: ${element.dy}`);
        if (element.dw !== undefined) props.push(`dw: ${element.dw}`);
        if (element.dh !== undefined) props.push(`dh: ${element.dh}`);
      }

      const propsString = props.map(prop => `${indent}    ${prop}`).join(',\n');
      return `{\n${propsString}\n${indent}  }`;
    });

    const elementsString = elementStrings.map(str => `${indent}  ${str}`).join(',\n');
    return `[\n${elementsString}\n${indent}]`;
  }

  /**
   * 应用基础属性到对象
   * @param obj 对象
   * @param data 属性数据
   */
  protected applyBaseProperties(obj: any, data: SpriteProperties): void {
    obj.x = data.x || 0;
    obj.y = data.y || 0;
    obj.width = data.width || 0;
    obj.height = data.height || 0;

    if (obj.scale) {
      obj.scale.x = data.scaleX || 1;
      obj.scale.y = data.scaleY || 1;
    }

    if (obj.anchor) {
      obj.anchor.x = data.anchorX || 0;
      obj.anchor.y = data.anchorY || 0;
    }

    obj.alpha = data.alpha !== undefined ? data.alpha : 1;
    obj.visible = data.visible !== undefined ? data.visible : true;
    obj.rotation = data.rotation || 0;
  }
}
