/**
 * RPG Maker MZ 类继承关系图 - 类型定义和工具函数
 */

// 基类型枚举
export enum BaseClassType {
  GAME_OBJECTS = 'Game Objects',      // Game_* 系列
  SCENES = 'Scenes',                  // Scene_* 系列
  WINDOWS = 'Windows',                // Window_* 系列
  MANAGERS = 'Managers',              // *Manager 系列
  CORE_CLASSES = 'Core Classes',      // 核心基础类
  INPUT_SYSTEM = 'Input System',      // 输入处理类
  GRAPHICS = 'Graphics',              // 图形渲染类
  UTILITIES = 'Utilities'             // 工具类
}

// 类信息接口
export interface ClassInfo {
  name: string;
  type: 'class' | 'static' | 'interface';
  category: BaseClassType;
  description: string;
  properties?: string[];
  methods?: string[];
  parent?: string;
  children: string[];
  isAbstract?: boolean;
  level?: number; // 在继承树中的层级
}

// 继承关系树节点
export interface ClassTreeNode {
  name: string;
  info: ClassInfo;
  children: ClassTreeNode[];
  level: number;
  x?: number; // 在 SVG 中的 x 坐标
  y?: number; // 在 SVG 中的 y 坐标
  width?: number; // 节点的实际宽度
}

// 连接线信息
export interface ConnectionLine {
  from: { x: number; y: number };
  to: { x: number; y: number };
  fromClass: string;
  toClass: string;
}

// 布局配置
export interface LayoutConfig {
  nodeWidth: number;
  nodeHeight: number;
  horizontalSpacing: number;
  verticalSpacing: number;
  marginTop: number;
  marginLeft: number;
}

// 默认布局配置
export const DEFAULT_LAYOUT: LayoutConfig = {
  nodeWidth: 120,
  nodeHeight: 40,
  horizontalSpacing: 30,
  verticalSpacing: 60,
  marginTop: 20,
  marginLeft: 20
};

/**
 * 构建继承关系树
 * @param classes 类信息映射
 * @param rootClassName 根类名称
 * @returns 继承关系树
 */
export function buildClassTree(
  classes: Map<string, ClassInfo>,
  rootClassName: string
): ClassTreeNode | null {
  const rootClass = classes.get(rootClassName);
  if (!rootClass) return null;

  function buildNode(className: string, level: number): ClassTreeNode {
    const classInfo = classes.get(className);
    if (!classInfo) {
      console.error(`类 ${className} 未在 treeData 中定义`);
      throw new Error(`类 ${className} 未在 treeData 中定义`);
    }

    const children: ClassTreeNode[] = [];

    // 递归构建子节点
    for (const childName of classInfo.children) {
      const childInfo = classes.get(childName);
      if (!childInfo) {
        console.error(`子类 ${childName} 未在 treeData 中定义，父类: ${className}`);
        continue; // 跳过缺失的子类，而不是抛出错误
      }
      children.push(buildNode(childName, level + 1));
    }

    return {
      name: className,
      info: { ...classInfo, level },
      children,
      level
    };
  }

  return buildNode(rootClassName, 0);
}

/**
 * 计算节点的动态宽度
 * @param className 类名
 * @returns 节点宽度
 */
export function calculateNodeWidth(className: string): number {
  const baseWidth = 40; // 基础宽度（图标和边距）
  const charWidth = 8; // 每个字符的平均宽度
  const padding = 20; // 左右内边距
  const minWidth = 100; // 最小宽度
  const maxWidth = 250; // 最大宽度

  const textWidth = className.length * charWidth;
  const calculatedWidth = baseWidth + textWidth + padding;

  return Math.min(Math.max(calculatedWidth, minWidth), maxWidth);
}

/**
 * 计算树形布局（支持动态节点宽度）
 * @param tree 继承关系树
 * @param config 布局配置
 * @returns 包含坐标信息的树和连接线
 */
export function calculateTreeLayout(
  tree: ClassTreeNode,
  config: LayoutConfig = DEFAULT_LAYOUT,
  containerWidth: number = 800,
  containerHeight: number = 600
): { tree: ClassTreeNode; connections: ConnectionLine[]; width: number; height: number } {
  const connections: ConnectionLine[] = [];
  let maxX = 0;
  let maxY = 0;

  // 计算每一层的节点数量和位置
  const levelNodes = new Map<number, ClassTreeNode[]>();

  // 首先为所有节点计算宽度
  function calculateWidths(node: ClassTreeNode) {
    node.width = calculateNodeWidth(node.name);
    node.children.forEach(calculateWidths);
  }

  function collectNodes(node: ClassTreeNode) {
    const level = node.level;
    if (!levelNodes.has(level)) {
      levelNodes.set(level, []);
    }
    levelNodes.get(level)!.push(node);
    node.children.forEach(collectNodes);
  }

  calculateWidths(tree);
  collectNodes(tree);

  // 计算整个树的尺寸
  const maxLevel = Math.max(...levelNodes.keys());
  const treeHeight = (maxLevel + 1) * (config.nodeHeight + config.verticalSpacing) - config.verticalSpacing;

  // 计算最宽层的宽度（考虑动态节点宽度）
  let maxLevelWidth = 0;
  for (const nodes of levelNodes.values()) {
    const levelWidth = nodes.reduce((sum, node, index) => {
      return sum + (node.width || config.nodeWidth) + (index > 0 ? config.horizontalSpacing : 0);
    }, 0);
    maxLevelWidth = Math.max(maxLevelWidth, levelWidth);
  }

  // 计算居中偏移
  const totalTreeWidth = maxLevelWidth + 2 * config.marginLeft;
  const totalTreeHeight = treeHeight + 2 * config.marginTop;

  const offsetX = Math.max(0, (containerWidth - totalTreeWidth) / 2);
  const offsetY = Math.max(0, (containerHeight - totalTreeHeight) / 2);

  // 为每个节点分配坐标
  function assignCoordinates(node: ClassTreeNode) {
    const level = node.level;
    const nodesAtLevel = levelNodes.get(level)!;
    const nodeIndex = nodesAtLevel.indexOf(node);

    // 计算 Y 坐标（基于层级）
    node.y = offsetY + config.marginTop + level * (config.nodeHeight + config.verticalSpacing);

    // 计算当前层的总宽度
    const levelWidth = nodesAtLevel.reduce((sum, n, index) => {
      return sum + (n.width || config.nodeWidth) + (index > 0 ? config.horizontalSpacing : 0);
    }, 0);

    // 计算 X 坐标 - 每层独立居中
    const levelStartX = offsetX + config.marginLeft + (maxLevelWidth - levelWidth) / 2;

    // 计算当前节点的 X 位置
    let currentX = levelStartX;
    for (let i = 0; i < nodeIndex; i++) {
      currentX += (nodesAtLevel[i].width || config.nodeWidth) + config.horizontalSpacing;
    }
    node.x = currentX;

    // 更新最大坐标
    maxX = Math.max(maxX, node.x + (node.width || config.nodeWidth));
    maxY = Math.max(maxY, node.y + config.nodeHeight);

    // 递归处理子节点并创建连接线
    node.children.forEach(child => {
      assignCoordinates(child);

      // 创建连接线（使用节点中心点）
      connections.push({
        from: {
          x: node.x! + (node.width || config.nodeWidth) / 2,
          y: node.y! + config.nodeHeight
        },
        to: {
          x: child.x! + (child.width || config.nodeWidth) / 2,
          y: child.y!
        },
        fromClass: node.name,
        toClass: child.name
      });
    });
  }

  assignCoordinates(tree);

  return {
    tree,
    connections,
    width: Math.max(containerWidth, maxX + offsetX + config.marginLeft),
    height: Math.max(containerHeight, maxY + offsetY + config.marginTop)
  };
}

/**
 * 获取指定基类型的根类列表
 * @param baseType 基类型
 * @returns 根类名称列表
 */
export function getRootClassesForType(baseType: BaseClassType): string[] {
  switch (baseType) {
    case BaseClassType.GAME_OBJECTS:
      return ['Game_Temp', 'Game_System', 'Game_BattlerBase', 'Game_Unit', 'Game_CharacterBase'];
    case BaseClassType.SCENES:
      return ['Scene_Base'];
    case BaseClassType.WINDOWS:
      return ['Window_Base'];
    case BaseClassType.MANAGERS:
      return ['DataManager', 'ConfigManager', 'StorageManager', 'ImageManager', 'AudioManager'];
    case BaseClassType.CORE_CLASSES:
      return ['Object', 'Bitmap', 'Point', 'Rectangle'];
    case BaseClassType.INPUT_SYSTEM:
      return ['Input', 'TouchInput'];
    case BaseClassType.GRAPHICS:
      return ['Graphics', 'WebAudio', 'Video'];
    case BaseClassType.UTILITIES:
      return ['Utils', 'JsonEx'];
    default:
      return [];
  }
}

/**
 * 根据类名获取节点颜色
 * @param className 类名
 * @returns CSS 颜色值
 */
export function getNodeColor(className: string): string {
  if (className.startsWith('Game_')) return '#4CAF50'; // 绿色
  if (className.startsWith('Scene_')) return '#2196F3'; // 蓝色
  if (className.startsWith('Window_')) return '#FF9800'; // 橙色
  if (className.endsWith('Manager')) return '#9C27B0'; // 紫色
  if (['Input', 'TouchInput'].includes(className)) return '#F44336'; // 红色
  if (['Graphics', 'WebAudio', 'Video'].includes(className)) return '#607D8B'; // 蓝灰色
  if (['Utils', 'JsonEx'].includes(className)) return '#795548'; // 棕色
  return '#757575'; // 默认灰色
}

/**
 * 节点点击处理函数
 * @param className 被点击的类名
 */
export function handleNodeClick(className: string): void {
  console.log(`点击了类节点: ${className}`);
}