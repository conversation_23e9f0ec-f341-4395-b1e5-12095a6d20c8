<script lang="ts">
  import type { ImageModel } from '../../../type/ui/imageModel.svelte';
  import { selectImageResourcePath } from '../../../logics/resourceLoader/resourceSelector';
  import { imageCache } from '../../../utils/imageCache';
  import { tick } from 'svelte';
  import { AccordionPanel, PropertyContainer } from '../../../components/accordionPanel';
  import Label from '../../../components/Label.svelte';
  import LabelInput from '../../../components/LabelInput.svelte';
  import Select from '../../../components/Select.svelte';

  interface Props {
    model: ImageModel;
  }

  let { model }: Props = $props();
  // 响应式状态来跟踪bitmap URL变化
  let bitmapUrl = $state('');

  // Canvas 相关
  let previewCanvas: HTMLCanvasElement;
  let hoveredRegionIndex = $state(-1);

  // 监听model变化并强制检查bitmap
  $effect(() => {
    // 监听model的变化
    model.imagePath;
    model.updateCounter; // 监听ImageModel的updateCounter

    const bitmap = (model as any)._originalObject?.bitmap;
    if (!bitmap) {
      console.log('No bitmap found, originalObject:', (model as any)._originalObject);
      bitmapUrl = '';
      return;
    }

    console.log('🔍 属性面板检查bitmap:', {
      bitmap: bitmap,
      url: bitmap.url,
      loadingState: bitmap._loadingState,
      hasUrl: !!bitmap.url,
      urlType: typeof bitmap.url,
      updateCounter: model.updateCounter
    });

    // 检查当前URL
    const currentUrl = bitmap.url || '';

    if (currentUrl.startsWith('blob:')) {
      console.log('Found blob URL:', currentUrl);
      bitmapUrl = currentUrl;
    } else {
      console.log('No blob URL yet, current url:', currentUrl);
      bitmapUrl = '';

      // 如果bitmap正在加载，添加加载监听器
      if (bitmap._loadingState === 'loading' && typeof bitmap.addLoadListener === 'function') {
        const loadListener = () => {
          console.log('Bitmap loaded, checking URL again:', bitmap.url);
          if (bitmap.url && bitmap.url.startsWith('blob:')) {
            bitmapUrl = bitmap.url;
            // updateCounter已经通过ImageModel的_triggerUpdate()触发了
          }
        };

        bitmap.addLoadListener(loadListener);

        // 清理函数
        return () => {
          if (typeof bitmap.removeLoadListener === 'function') {
            bitmap.removeLoadListener(loadListener);
          }
        };
      }
    }
  });

  // 直接使用model的响应式数据
  let imageInfo = $derived(model.getImageInfo());
  let shouldShowImage = $derived(imageInfo.hasImage && bitmapUrl && bitmapUrl.startsWith('blob:'));

  // 调试 shouldShowImage 的计算
  $effect(() => {
    console.log('🔍 shouldShowImage 计算:', {
      imageInfo,
      hasImage: imageInfo.hasImage,
      bitmapUrl,
      hasBitmapUrl: !!bitmapUrl,
      isBlob: bitmapUrl && bitmapUrl.startsWith('blob:'),
      shouldShowImage
    });
  });

  // 手风琴展开状态
  let isExpanded = $state(true);



  // 处理图片选择
  async function handleSelectImage() {
    try {
      const selectedPath = await selectImageResourcePath('选择图片文件');
      if (selectedPath && selectedPath !== '用户取消选择') {
        model.imagePath = selectedPath;
        console.log('选择的图片路径:', selectedPath);
      }
    } catch (error) {
      console.error('选择图片失败:', error);
    }
  }

  // 生成网格区域
  function generateGrid() {
    model.generateGridRegions();
    // 生成网格后立即重绘
    drawCanvas();
  }

  // 重置为默认区域
  function resetToDefault() {
    model.resetToDefaultRegion();
    // 重置后立即重绘
    drawCanvas().catch(error => {
      console.error('重置后重绘失败:', error);
    });
  }

  // Canvas 绘制相关函数
  async function drawCanvas() {
    if (!previewCanvas || !bitmapUrl) return;

    const ctx = previewCanvas.getContext('2d');
    if (!ctx) return;

    try {
      // ✅ 使用图片缓存工具，避免重复加载
      const img = await imageCache.ensureImageLoaded(bitmapUrl);

      // 计算Canvas尺寸，保持宽高比
      const maxWidth = 300;
      const maxHeight = 200;
      const aspectRatio = img.width / img.height;

      let canvasWidth, canvasHeight;
      if (aspectRatio > maxWidth / maxHeight) {
        canvasWidth = maxWidth;
        canvasHeight = maxWidth / aspectRatio;
      } else {
        canvasHeight = maxHeight;
        canvasWidth = maxHeight * aspectRatio;
      }

      previewCanvas.width = canvasWidth;
      previewCanvas.height = canvasHeight;

      // 清空画布
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // ✅ 直接绘制已加载的图片，无需等待
      ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);

      // 绘制网格线和区域
      drawGridAndRegions(ctx, canvasWidth, canvasHeight);
    } catch (error) {
      console.error('Canvas 绘制失败:', error);
    }
  }

  function drawGridAndRegions(ctx: CanvasRenderingContext2D, canvasWidth: number, canvasHeight: number) {
    const rows = model.gridRows;
    const cols = model.gridCols;

    // 当网格大于1x1时就绘制网格线（预览模式）
    if (rows <= 1 && cols <= 1) return;

    // 绘制网格线
    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 1;
    ctx.setLineDash([2, 2]);

    // 垂直线
    for (let i = 1; i < cols; i++) {
      const x = (canvasWidth / cols) * i;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvasHeight);
      ctx.stroke();
    }

    // 水平线
    for (let i = 1; i < rows; i++) {
      const y = (canvasHeight / rows) * i;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvasWidth, y);
      ctx.stroke();
    }

    // 只有在多区域模式下才绘制区域高亮
    if (model.isMultiRegion) {
      // 绘制当前选中区域
      if (model.currentRegionIndex >= 0 && model.currentRegionIndex < rows * cols) {
        const cellWidth = canvasWidth / cols;
        const cellHeight = canvasHeight / rows;
        const row = Math.floor(model.currentRegionIndex / cols);
        const col = model.currentRegionIndex % cols;

        ctx.strokeStyle = '#ff0000';
        ctx.lineWidth = 2;
        ctx.setLineDash([]);
        ctx.strokeRect(col * cellWidth, row * cellHeight, cellWidth, cellHeight);

        // 填充半透明背景
        ctx.fillStyle = 'rgba(255, 0, 0, 0.2)';
        ctx.fillRect(col * cellWidth, row * cellHeight, cellWidth, cellHeight);
      }

      // 绘制悬停区域
      if (hoveredRegionIndex >= 0 && hoveredRegionIndex < rows * cols && hoveredRegionIndex !== model.currentRegionIndex) {
        const cellWidth = canvasWidth / cols;
        const cellHeight = canvasHeight / rows;
        const row = Math.floor(hoveredRegionIndex / cols);
        const col = hoveredRegionIndex % cols;

        ctx.strokeStyle = '#ffff00';
        ctx.lineWidth = 1;
        ctx.setLineDash([]);
        ctx.strokeRect(col * cellWidth, row * cellHeight, cellWidth, cellHeight);

        // 填充半透明背景
        ctx.fillStyle = 'rgba(255, 255, 0, 0.1)';
        ctx.fillRect(col * cellWidth, row * cellHeight, cellWidth, cellHeight);
      }
    }
  }

  // Canvas 事件处理
  function onCanvasClick(event: MouseEvent) {
    if (!previewCanvas || model.gridRows <= 1 && model.gridCols <= 1) return;

    const rect = previewCanvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const cellWidth = previewCanvas.width / model.gridCols;
    const cellHeight = previewCanvas.height / model.gridRows;

    const col = Math.floor(x / cellWidth);
    const row = Math.floor(y / cellHeight);
    const regionIndex = row * model.gridCols + col;

    if (regionIndex >= 0 && regionIndex < model.gridRows * model.gridCols) {
      model.setCurrentRegion(regionIndex);
      // 立即重绘以显示选中效果
      drawCanvas().catch(error => {
        console.error('点击选择区域重绘失败:', error);
      });
    }
  }

  function onCanvasMouseMove(event: MouseEvent) {
    if (!previewCanvas || model.gridRows <= 1 && model.gridCols <= 1) return;

    const rect = previewCanvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const cellWidth = previewCanvas.width / model.gridCols;
    const cellHeight = previewCanvas.height / model.gridRows;

    const col = Math.floor(x / cellWidth);
    const row = Math.floor(y / cellHeight);
    const regionIndex = row * model.gridCols + col;

    if (regionIndex >= 0 && regionIndex < model.gridRows * model.gridCols) {
      hoveredRegionIndex = regionIndex;
      drawCanvas().catch(error => {
        console.error('鼠标悬停重绘失败:', error);
      }); // 重绘以显示悬停效果
    }
  }

  function onCanvasMouseLeave() {
    hoveredRegionIndex = -1;
    drawCanvas().catch(error => {
      console.error('鼠标离开重绘失败:', error);
    }); // 重绘以清除悬停效果
  }

  // 监听变化并重绘Canvas
  $effect(() => {
    console.log('🔍 Canvas 绘制条件检查:', {
      hasBitmapUrl: !!bitmapUrl,
      bitmapUrl: bitmapUrl,
      hasPreviewCanvas: !!previewCanvas,
      canDraw: !!(bitmapUrl && previewCanvas)
    });

    if (bitmapUrl) {
      console.log('🎨 有 bitmapUrl，等待 Canvas 绑定');
      // 使用 tick() 确保 DOM 完全更新后再检查 Canvas
      tick().then(() => {
        if (previewCanvas) {
          console.log('✅ Canvas 已绑定，开始绘制');
          drawCanvas().catch(error => {
            console.error('Canvas 重绘失败:', error);
          });
        } else {
          console.log('⏳ Canvas 还未绑定，等待下次更新');
        }
      });
    } else {
      console.log('❌ 没有 bitmapUrl');
    }
  });

  // 专门监听网格变化的 effect
  $effect(() => {
    // 显式读取这些属性以确保监听
    const gridRows = model.gridRows;
    const gridCols = model.gridCols;
    const currentRegionIndex = model.currentRegionIndex;

    console.log('🔄 网格属性变化检测:', {
      gridRows,
      gridCols,
      currentRegionIndex,
      hasCanvas: !!previewCanvas
    });

    if (previewCanvas) {
      console.log('✅ 触发Canvas重绘');
      // 使用 tick() 确保状态更新完成后再绘制
      tick().then(() => {
        drawCanvas().catch(error => {
          console.error('Canvas 重绘失败:', error);
        });
      });
    } else {
      console.log('⏳ Canvas未准备好，跳过重绘');
    }
  });

  // Canvas 元素绑定后立即尝试绘制
  $effect(() => {
    if (previewCanvas) {
      console.log('🎯 Canvas 元素已绑定，立即尝试绘制');
      if (bitmapUrl) {
        console.log('🎨 有 bitmapUrl，开始绘制');
        // 使用 tick() 确保 DOM 完全更新后再绘制
        tick().then(() => {
          drawCanvas().catch(error => {
            console.error('Canvas 初始绘制失败:', error);
          });
        });
      } else {
        console.log('⏳ 等待 bitmapUrl...');
      }
    }
  });


</script>

<AccordionPanel
  title="UI Image 属性"
  icon="🖼️"
  badge={shouldShowImage ? '有图片' : '无图片'}
  badgeVariant={shouldShowImage ? 'active' : 'inactive'}
  bind:expanded={isExpanded}
>
  {#if shouldShowImage}
    <!-- 图片预览 -->
    <div class="image-layout">
      <div class="image-preview">
        <canvas
          bind:this={previewCanvas}
          class="preview-canvas"
          onclick={onCanvasClick}
          onmousemove={onCanvasMouseMove}
          onmouseleave={onCanvasMouseLeave}
        ></canvas>
      </div>
    </div>

    <!-- 更换图片按钮 -->
    <div class="image-actions">
      <button
        type="button"
        onclick={handleSelectImage}
        class="change-image-button"
      >
        🔄 更换图片
      </button>
    </div>
  {:else}
    <!-- 无图片状态 -->
    <div class="no-image-section">
      <div class="no-image-content">
        <div class="no-image-icon">🖼️</div>
        <div class="no-image-text">
          <span class="no-image-title">无图片</span>
          <span class="no-image-subtitle">点击选择图片</span>
        </div>
        <button
          type="button"
          onclick={handleSelectImage}
          class="select-image-button"
        >
          选择图片
        </button>
      </div>
    </div>
  {/if}
</AccordionPanel>

<!-- 图片裁切面板 -->
<AccordionPanel title="图片裁切" expanded={false}>
  <div class="crop-section">
    <!-- 当前状态显示 -->
    <div class="crop-status">
      <div class="status-info">
        <span class="status-label">裁切状态:</span>
        <span class="status-value">
          {#if model.isMultiRegion}
            多区域裁切 ({model.regions.length} 个区域)
          {:else}
            单区域 (完整图片)
          {/if}
        </span>
      </div>

      {#if model.currentRegion}
        <div class="current-region">
          <span class="region-label">当前区域:</span>
          <span class="region-info">
            {model.currentRegion.label}
            ({model.currentRegion.sw}×{model.currentRegion.sh})
          </span>
        </div>
      {/if}
    </div>

    <!-- 网格设置 -->
    <div class="grid-settings">
      <div class="grid-inputs">
        <PropertyContainer>
          <Label text="网格行数:" />
          <LabelInput
            bind:value={model.gridRows}
            type="number"
            min={1}
            max={20}
            targetObject={model}
            fieldName="gridRows"
            name="网格行数"
          />
        </PropertyContainer>
        <PropertyContainer>
          <Label text="网格列数:" />
          <LabelInput
            bind:value={model.gridCols}
            type="number"
            min={1}
            max={20}
            targetObject={model}
            fieldName="gridCols"
            name="网格列数"
          />
        </PropertyContainer>
      </div>
      <div class="grid-actions">
        <button class="action-btn" onclick={() => generateGrid()}>
          🔄 生成网格 ({model.gridRows}×{model.gridCols})
        </button>
        {#if model.isMultiRegion}
          <button class="action-btn secondary" onclick={() => resetToDefault()}>
            ↩️ 重置为完整图片
          </button>
        {/if}
      </div>
    </div>
  </div>
</AccordionPanel>

<style>
  /* 图片布局容器 */
  .image-layout {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16px;
  }

  /* 图片预览区域 */
  .image-preview {
    width: 100%;
    max-width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--theme-surface-dark, #1a202c);
    border-radius: var(--border-radius, 4px);
    border: 1px solid var(--theme-border, rgba(255, 255, 255, 0.2));
    overflow: hidden;
    min-height: 150px;
  }

  /* 无图片状态 */
  .no-image-section {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-6, 1.5rem);
    margin-bottom: 16px;
  }

  .no-image-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2, 0.5rem);
    text-align: center;
  }

  .no-image-icon {
    font-size: var(--font-size-xl, 1.25rem);
    opacity: 0.5;
  }

  .no-image-text {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1, 0.25rem);
  }

  .no-image-title {
    font-size: var(--font-size-sm, 0.875rem);
    font-weight: 500;
    color: var(--theme-text, #ffffff);
  }

  .no-image-subtitle {
    font-size: var(--font-size-xs, 0.75rem);
    color: var(--theme-text-secondary, rgba(255, 255, 255, 0.8));
  }



  /* 属性设置区域 */
  .property-section {
    margin-bottom: 20px;
    padding: 12px;
    background: #333333;
    border-radius: 6px;
    border-left: 3px solid #4CAF50;
  }

  .property-section h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: bold;
    color: #81C784;
  }

  .property-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 8px;
  }

  .property-row label {
    min-width: 80px;
    font-size: 12px;
    color: #cccccc;
  }

  .property-row select {
    flex: 1;
    padding: 4px 8px;
    background: #444444;
    border: 1px solid #666666;
    border-radius: 4px;
    color: #ffffff;
    font-size: 12px;
  }

  .input-field {
    flex: 1;
    padding: 4px 8px;
    background: #444444;
    border: 1px solid #666666;
    border-radius: 4px;
    color: #ffffff;
    font-size: 12px;
  }

  .input-field:focus {
    border-color: #4CAF50;
    outline: none;
  }

  .select-button {
    padding: 4px 8px;
    background: #4CAF50;
    border: none;
    border-radius: 4px;
    color: #ffffff;
    cursor: pointer;
    font-size: 14px;
    margin-left: 4px;
    transition: background-color 0.2s;
  }

  .select-button:hover {
    background: #45a049;
  }

  .select-button:active {
    background: #3d8b40;
  }

  /* Canvas 预览样式 */
  .preview-canvas {
    max-width: 100%;
    max-height: 200px;
    cursor: pointer;
    border-radius: 4px;
    border: 1px solid var(--border-color);
  }

  /* 图片操作按钮 */
  .image-actions {
    display: flex;
    justify-content: center;
    margin-top: 8px;
  }

  .change-image-button {
    padding: 6px 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .change-image-button:hover {
    background: var(--primary-color-dark);
  }

  /* 裁切相关样式 */
  .crop-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .grid-settings {
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
  }

  .grid-inputs {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
  }

  .grid-actions {
    display: flex;
    gap: 8px;
  }

  .crop-status {
    padding: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    font-size: 12px;
  }

  .status-info, .current-region {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
  }

  .status-label, .region-label {
    color: var(--text-secondary);
  }

  .status-value, .region-info {
    color: var(--text-primary);
    font-weight: 500;
  }

  .crop-actions {
    display: flex;
    gap: 8px;
  }

  .action-btn {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .action-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-hover);
  }

  .action-btn.secondary {
    background: var(--bg-secondary);
  }

  .regions-section h4 {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: var(--text-primary);
  }

  .regions-grid {
    display: grid;
    gap: 6px;
    max-height: 150px;
    overflow-y: auto;
  }

  .region-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 6px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    cursor: pointer;
    transition: all 0.2s;
  }

  .region-item:hover {
    border-color: var(--primary-color);
  }

  .region-item.selected {
    border-color: var(--primary-color);
    background: var(--primary-color-light);
  }

  .region-preview {
    width: 24px;
    height: 24px;
    border: 1px solid var(--border-color);
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
  }

  .region-placeholder {
    font-size: 10px;
    font-weight: bold;
    color: var(--text-secondary);
  }

  .region-info {
    text-align: center;
  }

  .region-index {
    font-size: 9px;
    font-weight: bold;
    color: var(--text-primary);
  }

  .region-size {
    font-size: 8px;
    color: var(--text-secondary);
  }

  /* 弹窗样式 */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    min-width: 300px;
    max-width: 90vw;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
  }

  .modal-header h3 {
    margin: 0;
    font-size: 14px;
    color: var(--text-primary);
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-btn:hover {
    color: var(--text-primary);
  }

  .modal-body {
    padding: 16px;
  }

  .grid-settings {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .input-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .input-group label {
    min-width: 80px;
    font-size: 12px;
    color: var(--text-secondary);
  }

  .input-group input {
    flex: 1;
    padding: 4px 8px;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 12px;
  }

  .preview-info {
    font-size: 11px;
    color: var(--text-secondary);
    text-align: center;
    padding: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
  }

  .modal-footer {
    display: flex;
    gap: 8px;
    padding: 16px;
    border-top: 1px solid var(--border-color);
  }

  .btn {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }

  .btn.primary:hover {
    background: var(--primary-color-dark);
  }

  .btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
  }

  .btn.secondary:hover {
    background: var(--bg-hover);
  }

  .select-image-button {
    padding: 8px 16px;
    background: #4CAF50;
    border: none;
    border-radius: 4px;
    color: #ffffff;
    cursor: pointer;
    font-size: 12px;
    margin-top: 8px;
    transition: background-color 0.2s;
  }

  .select-image-button:hover {
    background: #45a049;
  }

  .select-image-button:active {
    background: #3d8b40;
  }
</style>
