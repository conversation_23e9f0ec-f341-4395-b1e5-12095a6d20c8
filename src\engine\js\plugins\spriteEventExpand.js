/*:
----------------------->编辑器本地插件
 * @target MZ
 * @plugindesc Sprite Expand v2.0.0
 * <AUTHOR>
 * @version 2.0.0
 * @description 扩展 Sprite 功能，支持 bitmap 数据对象和事件处理
 *
 * @help spriteExpand.js
 *
 * 这个插件扩展了 Sprite 类的功能：
 * 1. 支持传入 bitmap 数据对象自动创建 Bitmap
 * 2. 支持传入 events 数据对象自动设置事件处理
 * 3. 支持组合数据对象 { bitmap: {...}, events: {...} }
 *
 * 使用方法：
 * // 方式1：只传入 bitmap 数据对象
 * const sprite1 = new Sprite(bitmapData);
 *
 * // 方式2：传入组合数据对象
 * const sprite2 = new Sprite({
 *   bitmap: bitmapData,
 *   events: {
 *     onClick: "SceneManager.goto(Scene_Map);",
 *     onHover: "this.tint = 0xcccccc;"
 *   }
 * });
 *
 * 版本历史：
 * 2.0.0 - 添加事件处理支持
 * 1.0.0 - 初始版本
 */

(() => {
    'use strict';
    //=============================================================================
    // 重写 Sprite 初始化，处理 bitmap 数据对象
    //=============================================================================
    const _Sprite_initialize = Sprite.prototype.initialize;

    Sprite.prototype.initialize = function (data) {
               //     // 初始化组件系统
            this._components = new Map();           // 组件映射表
            this._componentList = [];               // 组件列表（用于更新）
            this._componentUpdateEnabled = true;    // 是否启用组件更新
            this._componentDebugMode = false;       // 调试模式
        // 如果是真正的 Bitmap 对象（有 canvas 属性），直接调用原始方法
        console.log('🔧 Sprite.initialize 被调用，参数:', data);
        let bitmapData = null;
        let eventsData = null;

        // 检查是否是组合数据对象 { bitmap: {...}, events: {...} }
        if (data && typeof data === 'object' && !data._canvas) {
            if (data.bitmap || data.events) {
                console.log('✅ 检测到组合数据对象 (bitmap + events)');
                bitmapData = data.bitmap;
                eventsData = data.events;
            } else {
                // 单纯的 bitmap 数据对象
                console.log('✅ 检测到 bitmap 数据对象', data);
                bitmapData = data;
            }
        }

        // 处理 bitmap 数据对象（能到这里的都是数据对象，不是真正的 Bitmap）
        let processedBitmap = bitmapData;

        if (bitmapData) {
            console.log('🔄 转换 bitmap 数据对象:', bitmapData);
            if (bitmapData.url && typeof bitmapData.url === 'string') {
                processedBitmap = ImageManager.loadBitmapFromUrl(bitmapData.url);
                //   console.log("============================================================.1  utl",processedBitmap)
            } else if (bitmapData && bitmapData.elements && Array.isArray(bitmapData.elements)) {
                console.log('从 elements 创建 bitmap');
                const newBitmap = new Bitmap(Graphics.width, Graphics.height);
                // 设置其他属性
                Object.assign(newBitmap, {
                    fontBold: bitmapData.fontBold || false,
                    fontFace: bitmapData.fontFace || 'GameFont',
                    fontItalic: bitmapData.fontItalic || false,
                    fontSize: bitmapData.fontSize || 28,
                    outlineColor: bitmapData.outlineColor || 'rgba(0, 0, 0, 0.5)',
                    outlineWidth: bitmapData.outlineWidth || 4,
                    textColor: bitmapData.textColor || '#ffffff',
                    _paintOpacity: bitmapData._paintOpacity || 255,
                    _smooth: bitmapData._smooth !== undefined ? bitmapData._smooth : true
                });
                // 处理 elements 数组，收集需要加载的图片
                const imagesToLoad = [];
                const processedElements = bitmapData.elements.map(element => {
                    if (element.type === 'image' && element.sourceUrl) {
                        const processedElement = { ...element };
                        const sourceBitmap = ImageManager.loadBitmapFromUrl(element.sourceUrl);
                        processedElement.source = sourceBitmap;

                        // 收集需要等待加载的图片
                        imagesToLoad.push(sourceBitmap);
                        return processedElement;
                    }
                    return element;
                });

                newBitmap.elements = processedElements;

                // 等待所有图片加载完成后重新绘制
                if (imagesToLoad.length > 0) {
                    let loadedCount = 0;
                    const checkAllLoaded = () => {
                        loadedCount++;
                        if (loadedCount >= imagesToLoad.length) {
                            // 所有图片都加载完成，重新绘制
                            console.log('所有图片加载完成，重新绘制 bitmap');
                            if (newBitmap.redrawing) {
                                newBitmap.redrawing();
                            }
                        }
                    };

                    // 为每个图片添加加载完成监听器
                    imagesToLoad.forEach(sourceBitmap => {
                        if (sourceBitmap.isReady && sourceBitmap.isReady()) {
                            // 图片已经加载完成
                            checkAllLoaded();
                        } else {
                            // 图片还在加载中，添加监听器
                            sourceBitmap.addLoadListener(checkAllLoaded);
                        }
                    });
                } else {
                    // 没有图片需要加载，直接重新绘制
                    if (newBitmap.redrawing) {
                        newBitmap.redrawing();
                    }
                }



                processedBitmap = newBitmap;
            } else {
                // 处理只有字体属性的 bitmap 数据对象
                console.log('处理字体属性 bitmap 数据对象:', bitmapData);
                const newBitmap = new Bitmap(Graphics.width, Graphics.height);

                // 应用字体相关属性
                if (bitmapData.fontBold !== undefined) newBitmap.fontBold = bitmapData.fontBold;
                if (bitmapData.fontFace) newBitmap.fontFace = bitmapData.fontFace;
                if (bitmapData.fontItalic !== undefined) newBitmap.fontItalic = bitmapData.fontItalic;
                if (bitmapData.fontSize !== undefined) newBitmap.fontSize = bitmapData.fontSize;
                if (bitmapData.outlineColor) newBitmap.outlineColor = bitmapData.outlineColor;
                if (bitmapData.outlineWidth !== undefined) newBitmap.outlineWidth = bitmapData.outlineWidth;
                if (bitmapData.textColor) newBitmap.textColor = bitmapData.textColor;
                if (bitmapData._paintOpacity !== undefined) newBitmap._paintOpacity = bitmapData._paintOpacity;
                if (bitmapData._smooth !== undefined) newBitmap._smooth = bitmapData._smooth;

                console.log('创建的新 bitmap 对象:', newBitmap);
                processedBitmap = newBitmap;
            }
        } else { processedBitmap = data }



        //         // 处理事件数据对象
                if (eventsData) {
                    console.log('🎯 设置 Sprite 事件:', eventsData);
                    this.setupEvents(eventsData);
                }
        console.log("✅ 检测到 Bitmap 对象，直接使用:", processedBitmap);
        _Sprite_initialize.call(this, processedBitmap);
    };

    //=============================================================================
    // 为 Sprite 添加事件处理系统
    //=============================================================================

    /**
     * 设置 Sprite 事件处理（使用 RPG Maker MZ 的事件系统）
     * @param {Object} eventsData - 事件数据对象
     */
    Sprite.prototype.setupEvents = function (eventsData) {
        console.log('🎯 Sprite.setupEvents 被调用:', eventsData);

        // 清理现有的事件处理器和代码
        this._eventHandlers = {};
        this._eventCodes = {};

        // 检查是否有任何事件
        const hasAnyEvents = eventsData && Object.keys(eventsData).length > 0;

        if (hasAnyEvents) {
            console.log('🎯 设置事件处理器');
            this._isEventEnabled = true;

            // 设置各种事件处理器
            if (eventsData.onClick) {
                console.log('设置点击事件:', eventsData.onClick);
                this._eventCodes.onClick = eventsData.onClick;
                this._eventHandlers.onClick = new Function(eventsData.onClick);
            }

            if (eventsData.onHover) {
                console.log('设置悬停进入事件:', eventsData.onHover);
                this._eventCodes.onHover = eventsData.onHover;
                this._eventHandlers.onHover = new Function(eventsData.onHover);
            }

            if (eventsData.onHoverOut) {
                console.log('设置悬停离开事件:', eventsData.onHoverOut);
                this._eventCodes.onHoverOut = eventsData.onHoverOut;
                this._eventHandlers.onHoverOut = new Function(eventsData.onHoverOut);
            }

            if (eventsData.onPress) {
                console.log('设置按下事件:', eventsData.onPress);
                this._eventCodes.onPress = eventsData.onPress;
                this._eventHandlers.onPress = new Function(eventsData.onPress);
            }

            if (eventsData.onRelease) {
                console.log('设置释放事件:', eventsData.onRelease);
                this._eventCodes.onRelease = eventsData.onRelease;
                this._eventHandlers.onRelease = new Function(eventsData.onRelease);
            }

            if (eventsData.onDoubleClick) {
                console.log('设置双击事件:', eventsData.onDoubleClick);
                this._eventCodes.onDoubleClick = eventsData.onDoubleClick;
                this._eventHandlers.onDoubleClick = new Function(eventsData.onDoubleClick);
                this._doubleClickCount = 0;
                this._doubleClickTimer = null;
            }

            // 添加 RPG Maker MZ 风格的事件处理方法
            this.makeClickable();

            console.log('✅ Sprite 事件设置完成，启用事件处理');
        } else {
            console.log('🎯 没有事件，禁用事件处理');
            this._isEventEnabled = false;

            // 清理双击相关属性
            this._doubleClickCount = 0;
            this._doubleClickTimer = null;

            // 重置状态
            this._pressed = false;
            this._hovered = false;

            console.log('✅ Sprite 事件已清理');
        }
    };

    /**
     * 使 Sprite 可点击（使用 RPG Maker MZ 的事件系统）
     */
    Sprite.prototype.makeClickable = function () {
        console.log('🎯 Sprite.makeClickable 被调用');

        // 添加 Sprite_Clickable 的属性
        this._pressed = false;
        this._hovered = false;
        this._isEventEnabled = true;

        // 保存原始的 update 方法
        if (!this._originalUpdate) {
            this._originalUpdate = this.update;
        }

        // 重写 update 方法来处理事件
        this.update = function () {
            // 调用原始的 update 方法
            if (this._originalUpdate) {
                this._originalUpdate.call(this);
            }

            // 处理触摸事件
            this.processTouch();
        };

        console.log('✅ Sprite 已设置为可点击');
    };

    /**
     * 处理触摸事件（模仿 Sprite_Clickable.prototype.processTouch）
     */
    Sprite.prototype.processTouch = function () {
        if (this.isClickEnabled()) {
            if (this.isBeingTouched()) {
                if (!this._hovered && TouchInput.isHovered()) {
                    this._hovered = true;
                    this.onMouseEnter();
                }
                if (TouchInput.isTriggered()) {
                    this._pressed = true;
                    this.onPress();
                }
            } else {
                if (this._hovered) {
                    this.onMouseExit();
                }
                this._pressed = false;
                this._hovered = false;
            }
            if (this._pressed && TouchInput.isReleased()) {
                this._pressed = false;
                this.onClick();
            }
        } else {
            this._pressed = false;
            this._hovered = false;
        }
    };

    /**
     * 检查是否启用点击
     */
    Sprite.prototype.isClickEnabled = function () {
        return this._isEventEnabled && this.worldVisible;
    };

    /**
     * 检查是否被触摸
     */
    Sprite.prototype.isBeingTouched = function () {
        const touchPos = new Point(TouchInput.x, TouchInput.y);
        const localPos = this.worldTransform.applyInverse(touchPos);
        return this.hitTest(localPos.x, localPos.y);
    };

    /**
     * 碰撞检测
     */
    Sprite.prototype.hitTest = function (x, y) {
        const rect = new Rectangle(
            -this.anchor.x * this.width,
            -this.anchor.y * this.height,
            this.width,
            this.height
        );
        return rect.contains(x, y);
    };

    /**
     * 鼠标进入事件
     */
    Sprite.prototype.onMouseEnter = function () {
        console.log('🎯 Sprite: 鼠标进入');
        if (this._eventHandlers && this._eventHandlers.onHover) {
            this._eventHandlers.onHover.call(this);
        }
    };

    /**
     * 鼠标离开事件
     */
    Sprite.prototype.onMouseExit = function () {
        console.log('🎯 Sprite: 鼠标离开');
        if (this._eventHandlers && this._eventHandlers.onHoverOut) {
            this._eventHandlers.onHoverOut.call(this);
        }
    };

    /**
     * 按下事件
     */
    Sprite.prototype.onPress = function () {
        console.log('🎯 Sprite: 按下事件');
        if (this._eventHandlers && this._eventHandlers.onPress) {
            this._eventHandlers.onPress.call(this);
        }
    };

    /**
     * 点击事件
     */
    Sprite.prototype.onClick = function () {
        console.log('🎯 Sprite: 点击事件');

        // 处理双击事件
        if (this._eventHandlers && this._eventHandlers.onDoubleClick) {
            this._doubleClickCount++;
            if (this._doubleClickCount === 1) {
                this._doubleClickTimer = setTimeout(() => {
                    this._doubleClickCount = 0;
                    // 单击事件
                    if (this._eventHandlers.onClick) {
                        this._eventHandlers.onClick.call(this);
                    }
                }, 300); // 300ms 内的第二次点击算作双击
            } else if (this._doubleClickCount === 2) {
                clearTimeout(this._doubleClickTimer);
                this._doubleClickCount = 0;
                // 双击事件
                this._eventHandlers.onDoubleClick.call(this);
                return; // 不执行单击事件
            }
        } else {
            // 只有单击事件
            if (this._eventHandlers && this._eventHandlers.onClick) {
                this._eventHandlers.onClick.call(this);
            }
        }

        // 释放事件
        if (this._eventHandlers && this._eventHandlers.onRelease) {
            this._eventHandlers.onRelease.call(this);
        }
    };

    /**
     * 动态添加事件处理器
     * @param {string} eventType - 事件类型
     * @param {string} code - 事件代码
     */
    Sprite.prototype.addEventHandler = function (eventType, code) {
        if (!this._eventHandlers) {
            this._eventHandlers = {};
            this._eventCodes = {};
        }

        console.log(`添加 ${eventType} 事件:`, code);

        this._eventCodes[eventType] = code;
        this._eventHandlers[eventType] = new Function(code);

        // 启用交互
        this.interactive = true;
        this.buttonMode = true;

        // 根据事件类型绑定相应的监听器
        switch (eventType) {
            case 'onClick':
                this.on('pointerup', this._eventHandlers[eventType].bind(this));
                break;
            case 'onHover':
                this.on('pointerover', this._eventHandlers[eventType].bind(this));
                break;
            case 'onHoverOut':
                this.on('pointerout', this._eventHandlers[eventType].bind(this));
                break;
            case 'onPress':
                this.on('pointerdown', this._eventHandlers[eventType].bind(this));
                break;
            case 'onRelease':
                this.on('pointerup', this._eventHandlers[eventType].bind(this));
                break;
        }
    };

    /**
     * 移除事件处理器
     * @param {string} eventType - 事件类型
     */
    Sprite.prototype.removeEventHandler = function (eventType) {
        if (this._eventHandlers && this._eventHandlers[eventType]) {
            console.log(`移除 ${eventType} 事件`);

            // 移除监听器
            switch (eventType) {
                case 'onClick':
                case 'onRelease':
                    this.off('pointerup', this._eventHandlers[eventType]);
                    break;
                case 'onHover':
                    this.off('pointerover', this._eventHandlers[eventType]);
                    break;
                case 'onHoverOut':
                    this.off('pointerout', this._eventHandlers[eventType]);
                    break;
                case 'onPress':
                    this.off('pointerdown', this._eventHandlers[eventType]);
                    break;
            }

            // 清理存储
            delete this._eventHandlers[eventType];
            delete this._eventCodes[eventType];
        }
    };

})();