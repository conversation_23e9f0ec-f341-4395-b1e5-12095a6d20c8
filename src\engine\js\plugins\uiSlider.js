//=============================================================================
// ui.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc UI Components Plugin v1.0.0
 * <AUTHOR>
 * @version 1.0.0
 * @description 提供各种UI组件，包括滑动条、开关等
 *
 * @help ui.js
 *
 * 这个插件提供了各种UI组件：
 * - Slider: 滑动条组件
 * - Switch: 开关组件
 *
 * 使用方法：
 * const slider = new Slider(properties);
 *
 */

(() => {
    'use strict';

    //=============================================================================
    // Slider Class
    //=============================================================================

    /**
     * 滑动条类 - 继承自 Container
     * Slider 作为容器管理轨道、进度条、滑块和标签等子组件
     * 通过属性面板绑定轨道精灵、滑块精灵、进度精灵、文本精灵
     * 所有子组件都是外部绑定，Slider 只负责逻辑控制和位置计算
     */
    class UISlider extends PIXI.Container {
        constructor(properties = {}) {
            super();

            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'Slider';

            this.initializeSlider(properties);
        }

        /**
         * 初始化滑动条
         * @param {Object} properties 滑动条属性
         */
        initializeSlider(properties) {
            // 设置默认属性
            this.setupDefaultProperties(properties);

            // 初始化容器
            this.initializeContainer();

            // 绑定事件
            this.setupEventListeners();

            // 初始更新
            this.updateSlider();

            console.log('Slider created:', this.sliderWidth, 'x', this.sliderHeight);
        }

        /**
         * 设置默认属性
         */
        setupDefaultProperties(properties) {
            // 基础属性
            this.sliderWidth = properties.width || 200;
            this.sliderHeight = properties.height || 20;

            // 数值属性
            this.value = properties.value || 0;
            this.minValue = properties.minValue || 0;
            this.maxValue = properties.maxValue || 100;
            this.step = properties.step || 1;

            // 🔑 子组件绑定属性（通过属性面板设置）
            this.boundRangeSprite = properties.boundRangeSprite || null;       // 绑定的范围精灵 (UIImage) - 定义有效范围 ⭐
            this.boundProgressSprite = properties.boundProgressSprite || null; // 绑定的进度精灵 (UIImage) - 显示当前进度
            this.boundThumbSprite = properties.boundThumbSprite || null;       // 绑定的滑块精灵 (UIImage) - 可拖拽控制
            this.boundLabelSprite = properties.boundLabelSprite || null;       // 绑定的文本精灵 (UILabel) - 显示数值

            // 默认样式设置
            this.defaultProgressColor = properties.defaultProgressColor || '#00ff00';
            this.defaultTrackColor = properties.defaultTrackColor || '#404040';

            // 行为属性
            this.enabled = properties.enabled !== false;

            // 回调函数
            this.onChange = properties.onChange || null;
            this.onDragStart = properties.onDragStart || null;
            this.onDragEnd = properties.onDragEnd || null;

            // 事件代码属性（用于代码生成）
            this._eventCodes = properties._eventCodes || {};

            // 内部状态
            this.isDragging = false;
            this.dragStartX = 0;
            this.dragStartValue = 0;
        }

        /**
         * 初始化容器（Container 不需要轨道位图，轨道由外部绑定）
         */
        initializeContainer() {
            // 设置容器的交互属性
            this.interactive = true;
            this.interactiveChildren = true;

            // 设置容器的基础尺寸（用于碰撞检测等）
            this.containerWidth = this.sliderWidth;
            this.containerHeight = this.sliderHeight;

            console.log('✅ Slider 容器初始化完成:', this.containerWidth, 'x', this.containerHeight);
        }

        /**
         * 🔑 核心方法：更新滑动条
         */
        updateSlider() {
            this.updateProgress();
            this.updateThumb();
            this.updateLabel();
        }

        /**
         * 更新进度显示
         */
        updateProgress() {
            const progress = (this.value - this.minValue) / (this.maxValue - this.minValue);

            if (this.boundProgressSprite) {
                // 🔑 如果有范围精灵，使用新的布局方法
                if (this.boundRangeSprite) {
                    this.layoutProgressOnRange();
                } else {
                    // 使用旧的绑定进度精灵方法（兼容性）
                    this.updateBoundProgressSprite(progress);
                }
            }
            // 🔑 删除默认绘制 - 如果没有绑定进度精灵，就不显示进度
        }

        /**
         * 更新绑定的进度精灵 - 使用遮罩实现
         */
        updateBoundProgressSprite(progress) {
            if (!this.boundProgressSprite) return;

            // 🎯 使用遮罩方式实现进度显示
            this.applyProgressMask(this.boundProgressSprite, progress);
        }



        /**
         * 更新滑块位置
         */
        updateThumb() {
            if (!this.boundThumbSprite) return;

            // 🔑 如果有范围精灵，使用新的布局方法
            if (this.boundRangeSprite) {
                this.layoutThumbOnRange();
                return;
            }

            // 🔑 旧的布局方法（兼容性）
            const progress = (this.value - this.minValue) / (this.maxValue - this.minValue);
            const thumbWidth = this.boundThumbSprite.width || this.boundThumbSprite.texture?.width || 20;

            // 计算滑块位置（使用完整的滑动条宽度）
            this.boundThumbSprite.x = progress * this.sliderWidth - thumbWidth / 2;
            this.boundThumbSprite.y = (this.sliderHeight - (this.boundThumbSprite.height || this.boundThumbSprite.texture?.height || 20)) / 2;
        }

        /**
         * 更新文本显示
         */
        updateLabel() {
            if (!this.boundLabelSprite) return;

            // 🔑 更新 UILabel 的文本内容，让 UILabel 自己处理前缀、后缀和重绘
            this.boundLabelSprite.text = this.value.toString();

            // 🔑 调用 UILabel 的重绘方法，这样会正确处理前缀和后缀
            if (this.boundLabelSprite.redrawText && typeof this.boundLabelSprite.redrawText === 'function') {
                this.boundLabelSprite.redrawText();
            } else if (this.boundLabelSprite.setText && typeof this.boundLabelSprite.setText === 'function') {
                // 备用方法：调用 setText，它内部会调用 redrawText
                this.boundLabelSprite.setText(this.value.toString());
            }
        }

        /**
         * 检查对象是否为 UIImage 类型
         */
        isUIImageType(obj) {
            if (!obj) return false;

            // 方法1: 检查 UI 组件标识
            if (obj.isUIComponent === true && obj.uiComponentType === 'UIImage') {
                return true;
            }

            // 方法2: 检查构造函数名称
            if (obj.constructor && obj.constructor.name === 'UIImage') {
                return true;
            }

            // 方法3: 检查特征属性（UIImage 特有的属性）
            if (obj.imagePath !== undefined || obj.regions !== undefined ||
                obj.scaleMode !== undefined || obj.preserveAspectRatio !== undefined) {
                return true;
            }

            return false;
        }

        /**
         * 检查对象是否为 UILabel 类型
         */
        isUILabelType(obj) {
            if (!obj) return false;

            // 方法1: 检查 UI 组件标识
            if (obj.isUIComponent === true && obj.uiComponentType === 'UILabel') {
                return true;
            }

            // 方法2: 检查构造函数名称
            if (obj.constructor && obj.constructor.name === 'UILabel') {
                return true;
            }

            // 方法3: 检查特征属性（UILabel 特有的属性）
            if (obj.text !== undefined || obj.fontSize !== undefined ||
                obj.textColor !== undefined || obj.fontFace !== undefined) {
                return true;
            }

            return false;
        }

        /**
         * 根据轨道精灵的有效长度更新 UISlider 的尺寸 - 已删除boundTrackSprite
         */
        updateSizeFromTrack() {
            console.log('🔧 UISlider: boundTrackSprite已删除，保持当前尺寸');
            return;
        }

        /**
         * 根据范围精灵的长度更新 UISlider 的总长度
         */
        updateSizeFromRange() {
            console.log('🔍 UISlider: updateSizeFromRange() 被调用');

            if (!this.boundRangeSprite) {
                console.log('🔧 UISlider: 没有范围精灵，保持当前尺寸');
                return;
            }

            console.log('🔍 UISlider: 范围精灵对象详情', {
                sprite: this.boundRangeSprite,
                spriteType: this.boundRangeSprite.constructor.name,
                width: this.boundRangeSprite.width,
                height: this.boundRangeSprite.height,
                textureWidth: this.boundRangeSprite.texture?.width,
                textureHeight: this.boundRangeSprite.texture?.height,
                bitmap: this.boundRangeSprite.bitmap,
                bitmapWidth: this.boundRangeSprite.bitmap?.width,
                bitmapHeight: this.boundRangeSprite.bitmap?.height
            });

            // 🔑 获取范围精灵的尺寸作为滑动条的总长度
            const rangeWidth = this.boundRangeSprite.width ||
                             this.boundRangeSprite.texture?.width ||
                             this.boundRangeSprite.bitmap?.width || 0;
            const rangeHeight = this.boundRangeSprite.height ||
                              this.boundRangeSprite.texture?.height ||
                              this.boundRangeSprite.bitmap?.height || 0;

            console.log('🔍 UISlider: 计算出的范围精灵尺寸', {
                rangeWidth: rangeWidth,
                rangeHeight: rangeHeight,
                currentSliderWidth: this.sliderWidth,
                currentSliderHeight: this.sliderHeight
            });

            if (rangeWidth <= 0 || rangeHeight <= 0) {
                console.warn('⚠️ UISlider: 范围精灵尺寸无效', {
                    width: rangeWidth,
                    height: rangeHeight
                });
                return;
            }

            // 🔑 范围精灵的长度就是滑动条的总长度
            const oldSliderWidth = this.sliderWidth;
            this.sliderWidth = rangeWidth;
            this.sliderHeight = rangeHeight;

            // 容器尺寸也使用范围精灵的尺寸
            this.containerWidth = rangeWidth;
            this.containerHeight = rangeHeight;

            console.log('🔧 UISlider: 根据范围精灵更新总长度', {
                oldSliderWidth: oldSliderWidth,
                newSliderWidth: this.sliderWidth,
                rangeSpriteSize: `${rangeWidth}x${rangeHeight}`,
                sliderTotalLength: rangeWidth,
                containerSize: `${this.containerWidth}x${this.containerHeight}`
            });

            // 🔑 立刻进行布局设置
            this.layoutComponents();

            console.log('🔧 UISlider: updateSizeFromRange() 完成，当前 sliderWidth =', this.sliderWidth);
        }

        /**
         * 布局所有组件
         */
        layoutComponents() {
            console.log('🎯 UISlider: 开始布局组件');

            // 1. 布局轨道精灵（背景装饰，居中对齐）- boundTrackSprite已删除

            // 2. 布局范围精灵（定义有效范围）
            if (this.boundRangeSprite) {
                this.boundRangeSprite.x = 0;
                this.boundRangeSprite.y = 0;
                console.log('📍 范围精灵布局: (0, 0) - 定义有效范围');
            }

            // 3. 布局进度精灵（显示当前进度）
            if (this.boundProgressSprite && this.boundRangeSprite) {
                this.layoutProgressOnRange();
            }

            // 4. 布局滑块精灵（可拖拽控制）
            if (this.boundThumbSprite && this.boundRangeSprite) {
                this.layoutThumbOnRange();
            }

            // 5. 文字除外，不参与自动布局
            console.log('📍 文字精灵不参与自动布局');
        }

        /**
         * 将进度精灵布局到范围精灵上 - 使用遮罩实现
         */
        layoutProgressOnRange() {
            if (!this.boundProgressSprite || !this.boundRangeSprite) return;

            // 进度精灵与范围精灵对齐
            this.boundProgressSprite.x = this.boundRangeSprite.x;
            this.boundProgressSprite.y = this.boundRangeSprite.y;

            // 根据当前值计算进度比例
            const progress = (this.value - this.minValue) / (this.maxValue - this.minValue);

            // 🎯 使用遮罩方式实现进度显示
            this.applyProgressMask(this.boundProgressSprite, progress);

            console.log('📍 进度精灵布局到范围精灵上（遮罩方式）:', {
                progress: progress,
                value: this.value
            });
        }

        /**
         * 应用进度显示 - 使用裁剪区域方法
         * @param {Sprite} targetSprite - 目标精灵
         * @param {number} progress - 进度比例 (0-1)
         */
        applyProgressMask(targetSprite, progress) {
            if (!targetSprite || !targetSprite.bitmap) return;

            // 获取原始图片尺寸
            const originalWidth = targetSprite.bitmap.width;
            const originalHeight = targetSprite.bitmap.height;

            // 计算显示宽度
            const displayWidth = Math.max(0, Math.round(progress * originalWidth));

            // 使用setFrame方法裁剪图片显示区域
            if (targetSprite.setFrame) {
                // RPG Maker MZ的Sprite有setFrame方法
                targetSprite.setFrame(0, 0, displayWidth, originalHeight);
            } else {
                // 如果没有setFrame，直接设置texture的frame
                if (targetSprite.texture && targetSprite.texture.frame) {
                    targetSprite.texture.frame.width = displayWidth;
                    targetSprite.texture.frame.height = originalHeight;
                    targetSprite.texture._updateUvs();
                }
            }

            console.log('进度更新（裁剪区域）:', {
                progress: progress,
                displayWidth: displayWidth,
                originalSize: `${originalWidth}x${originalHeight}`,
                targetSprite: targetSprite.constructor.name
            });
        }

        /**
         * 将滑块精灵布局到范围精灵上
         */
        layoutThumbOnRange() {
            if (!this.boundThumbSprite || !this.boundRangeSprite) return;

            // 根据当前值计算滑块位置
            const progress = (this.value - this.minValue) / (this.maxValue - this.minValue);
            const thumbX = this.boundRangeSprite.x + progress * this.sliderWidth;

            // 将滑块居中对齐到范围精灵上
            const thumbWidth = this.boundThumbSprite.width || this.boundThumbSprite.texture?.width || 20;
            const thumbHeight = this.boundThumbSprite.height || this.boundThumbSprite.texture?.height || 20;

            this.boundThumbSprite.x = thumbX - thumbWidth / 2;
            this.boundThumbSprite.y = this.boundRangeSprite.y + (this.sliderHeight - thumbHeight) / 2;

            console.log('📍 滑块精灵布局到范围精灵上:', {
                progress: progress,
                thumbX: this.boundThumbSprite.x,
                thumbY: this.boundThumbSprite.y,
                value: this.value
            });
        }

        /**
         * 🔑 绑定子组件的方法
         */
        // bindTrackSprite方法已删除 - boundTrackSprite属性不再需要

        bindRangeSprite(sprite) {
            console.log('🔍 UISlider: bindRangeSprite() 被调用', {
                sprite: sprite,
                spriteType: sprite?.constructor?.name
            });

            // 检查是否为 UIImage 类型
            if (!this.isUIImageType(sprite)) {
                console.error('🚨 UISlider: boundRangeSprite 必须是 UIImage 类型', {
                    provided: sprite.constructor.name,
                    isUIComponent: sprite.isUIComponent,
                    uiComponentType: sprite.uiComponentType,
                    expected: 'UIImage'
                });
                return;
            }

            console.log('🔍 UISlider: 设置 boundRangeSprite');
            this.boundRangeSprite = sprite;

            console.log('🔍 UISlider: 调用 updateSizeFromRange()');
            // 🔑 范围精灵的长度决定滑动条的总长度
            this.updateSizeFromRange();

            console.log('✅ UISlider: 成功绑定 RangeSprite (UIImage) - 定义有效范围');
        }

        bindThumbSprite(sprite) {
            // 检查是否为 UIImage 类型
            if (!this.isUIImageType(sprite)) {
                console.error('🚨 UISlider: boundThumbSprite 必须是 UIImage 类型', {
                    provided: sprite.constructor.name,
                    isUIComponent: sprite.isUIComponent,
                    uiComponentType: sprite.uiComponentType,
                    expected: 'UIImage'
                });
                return;
            }

            this.boundThumbSprite = sprite;

            // 🔑 绑定滑块后，重新计算轨道有效长度 - boundTrackSprite已删除

            this.updateThumb();
            console.log('✅ UISlider: 成功绑定 ThumbSprite (UIImage)');
        }

        bindProgressSprite(sprite) {
            // 检查是否为 UIImage 类型
            if (!this.isUIImageType(sprite)) {
                console.error('🚨 UISlider: boundProgressSprite 必须是 UIImage 类型', {
                    provided: sprite.constructor.name,
                    isUIComponent: sprite.isUIComponent,
                    uiComponentType: sprite.uiComponentType,
                    expected: 'UIImage'
                });
                return;
            }

            this.boundProgressSprite = sprite;
            this.updateProgress();
            console.log('✅ UISlider: 成功绑定 ProgressSprite (UIImage)');
        }

        bindLabelSprite(sprite) {
            // 检查是否为 UILabel 类型
            if (!this.isUILabelType(sprite)) {
                console.error('🚨 UISlider: boundLabelSprite 必须是 UILabel 类型', {
                    provided: sprite.constructor.name,
                    isUIComponent: sprite.isUIComponent,
                    uiComponentType: sprite.uiComponentType,
                    expected: 'UILabel'
                });
                return;
            }

            this.boundLabelSprite = sprite;
            this.updateLabel();
            console.log('✅ UISlider: 成功绑定 LabelSprite (UILabel)');
        }

        /**
         * 创建进度填充精灵 (sl_2)
         */
        createProgressSprite() {
            this.progressSprite = new Sprite();
            this.progressSprite.bitmap = ImageManager.loadBitmap('ui/slider/', 'sl_2');

            // 等待进度图片加载完成
            this.progressSprite.bitmap.addLoadListener(() => {
                this.updateProgressDisplay();
                console.log('✅ Slider 进度图片加载完成');
            });

            // 添加到轨道上方
            this.addChild(this.progressSprite);
        }

        /**
         * 重绘轨道 - 图片模式下不需要重绘
         */
        redrawTrack() {
            // 使用图片时不需要重绘轨道
            // 如果需要显示进度，可以考虑使用遮罩或者叠加图片
            console.log('Slider 使用图片模式，无需重绘轨道');
        }

        /**
         * 创建滑动按钮 - 使用图片 (sl_3)
         */
        createThumbSprite() {
            this.thumbSprite = new Sprite();

            // 加载滑动按钮图片 (sl_3)
            this.thumbSprite.bitmap = ImageManager.loadBitmap('ui/slider/', 'sl_3');

            // 等待图片加载完成后设置尺寸
            this.thumbSprite.bitmap.addLoadListener(() => {
                this.thumbWidth = this.thumbSprite.bitmap.width;
                this.thumbHeight = this.thumbSprite.bitmap.height;
                this.updateThumbPosition();
                console.log('✅ Slider 滑块图片加载完成:', this.thumbWidth, 'x', this.thumbHeight);
            });

            this.addChild(this.thumbSprite);
        }





        /**
         * 更新进度显示 - 使用统一的遮罩系统
         */
        updateProgressDisplay() {
            if (!this.progressSprite || !this.progressSprite.bitmap) {
                return;
            }

            // 计算进度比例
            const progress = (this.value - this.minValue) / (this.maxValue - this.minValue);

            // 🎯 使用统一的遮罩方法
            this.applyProgressMask(this.progressSprite, progress);

            console.log('🎨 更新进度显示（统一遮罩）:', {
                progress: progress,
                value: this.value
            });
        }

        /**
         * 重绘滑动按钮 - 图片模式下不需要重绘
         */
        redrawThumb() {
            // 使用图片时不需要重绘按钮
            console.log('Slider 使用图片模式，无需重绘按钮');
        }

        /**
         * 设置事件监听器 - 使用 RPG Maker MZ 的 TouchInput 系统
         */
        setupEventListeners() {
            // 初始化拖拽状态
            this.isDragging = false;
            this.dragStartX = 0;
            this.dragStartValue = 0;

            // 保存原始的 update 方法
            if (!this._originalUpdate) {
                this._originalUpdate = this.update;
            }

            // 重写 update 方法来处理触摸事件
            this.update = function() {
                // 调用原始的 update 方法
                if (this._originalUpdate) {
                    this._originalUpdate.call(this);
                }

                // 🔑 确保子元素的update方法被调用（预防性修复）
                if (this.children && this.children.length > 0) {
                    this.children.forEach(child => {
                        if (child && typeof child.update === 'function') {
                            try {
                                child.update();
                            } catch (error) {
                                console.warn('UISlider子元素update失败:', error);
                            }
                        }
                    });
                }

                // 处理滑动条触摸事件
                this.processSliderTouch();
            };

            console.log('✅ Slider 事件监听器设置完成');
        }

        /**
         * 处理滑动条触摸事件 - 使用 RPG Maker MZ 的 TouchInput 系统
         */
        processSliderTouch() {
            if (!this.enabled || !this.visible) return;

            if (typeof TouchInput === 'undefined') return;

            // 如果正在拖拽，优先处理拖拽逻辑（不管鼠标是否在滑块范围内）
            if (this.isDragging) {
                if (TouchInput.isPressed()) {
                    // 继续拖拽
                    this.onSliderDrag();
                } else if (TouchInput.isReleased()) {
                    // 释放拖拽
                    this.onSliderRelease();
                }
                return; // 拖拽中时不处理其他逻辑
            }

            // 只有在没有拖拽时才检查是否开始新的触摸
            if (this.isBeingTouched() && TouchInput.isTriggered()) {
                this.onSliderPress();
            }
        }

        /**
         * 检查是否被触摸
         */
        isBeingTouched() {
            if (typeof TouchInput === 'undefined') return false;

            // 获取滑动条在屏幕上的位置
            const bounds = this.getBounds();
            const touchX = TouchInput.x;
            const touchY = TouchInput.y;

            return touchX >= bounds.x && touchX <= bounds.x + bounds.width &&
                   touchY >= bounds.y && touchY <= bounds.y + bounds.height;
        }

        /**
         * 碰撞检测
         */
        hitTest(x, y) {
            return x >= 0 && x <= this.sliderWidth && y >= 0 && y <= this.sliderHeight;
        }

        /**
         * 滑动条按下事件
         */
        onSliderPress() {
            if (typeof TouchInput === 'undefined') return;

            // 获取滑动条的边界
            const bounds = this.getBounds ? this.getBounds() : { x: this.x, y: this.y, width: this.sliderWidth, height: this.sliderHeight };

            // 计算相对于滑动条的本地坐标
            const localX = TouchInput.x - bounds.x;
            const localY = TouchInput.y - bounds.y;

            // 检查是否点击在滑动按钮上（如果有绑定的滑块精灵）
            if (this.boundThumbSprite) {
                const thumbX = this.boundThumbSprite.x;
                const thumbY = this.boundThumbSprite.y;
                const thumbWidth = this.boundThumbSprite.width || 16;
                const thumbHeight = this.boundThumbSprite.height || 20;

                if (localX >= thumbX && localX <= thumbX + thumbWidth &&
                    localY >= thumbY && localY <= thumbY + thumbHeight) {
                    // 点击在滑动按钮上，开始拖拽
                    this.isDragging = true;
                    this.dragStartX = TouchInput.x;
                    this.dragStartValue = this.value;

                    // 执行回调函数
                    if (this.onDragStart) {
                        this.onDragStart(this.value);
                    }

                    // 🔑 执行事件代码
                    this.executeEvent('onDragStart');

                    console.log('🎯 Slider: 开始拖拽');
                    return; // 退出，不执行轨道点击逻辑
                }
            }

            // 点击在轨道上，跳转到该位置
            const progress = Math.max(0, Math.min(1, localX / this.sliderWidth));
            const newValue = this.minValue + progress * (this.maxValue - this.minValue);
            this.setValue(newValue);

            console.log('🎯 Slider: 轨道点击');

            // 播放音效
            if (typeof SoundManager !== 'undefined') {
                SoundManager.playCursor();
            }
        }

        /**
         * 滑动条拖拽事件
         */
        onSliderDrag() {
            if (!this.isDragging) return;

            const deltaX = TouchInput.x - this.dragStartX;
            const deltaValue = (deltaX / this.sliderWidth) * (this.maxValue - this.minValue);
            const newValue = this.dragStartValue + deltaValue;

            this.setValue(newValue);
        }

        /**
         * 滑动条释放事件
         */
        onSliderRelease() {
            if (!this.isDragging) return;

            this.isDragging = false;

            // 执行回调函数
            if (this.onDragEnd) {
                this.onDragEnd(this.value);
            }

            // 🔑 执行事件代码
            this.executeEvent('onDragEnd');

            console.log('🎯 Slider: 拖拽结束');
        }

        /**
         * 设置值
         */
        setValue(value) {
            const oldValue = this.value;

            // 限制范围
            value = Math.max(this.minValue, Math.min(this.maxValue, value));

            // 应用步长
            if (this.step > 0) {
                value = Math.round((value - this.minValue) / this.step) * this.step + this.minValue;
            }

            this.value = value;

            if (this.value !== oldValue) {
                // 🔑 使用新的更新方法
                this.updateSlider();

                // 执行回调函数
                if (this.onChange) {
                    this.onChange(this.value);
                }

                // 🔑 执行事件代码
                this.executeEvent('onChange');
            }
        }

        /**
         * 获取值
         */
        getValue() {
            return this.value;
        }

        /**
         * 更新按钮位置（旧方法，保持兼容性）
         */
        updateThumbPosition() {
            // 新设计中使用 updateThumb 方法
            this.updateThumb();
        }

        /**
         * 设置启用状态
         */
        setEnabled(enabled) {
            this.enabled = enabled;
            this.alpha = enabled ? 1.0 : 0.5;
            this.interactive = enabled;

            // 检查 thumbSprite 是否已经创建
            if (this.thumbSprite) {
                this.thumbSprite.interactive = enabled;
            }
        }

        /**
         * 设置范围
         */
        setRange(minValue, maxValue) {
            this.minValue = minValue;
            this.maxValue = maxValue;
            this.setValue(this.value); // 重新验证当前值
        }

        /**
         * 设置图片路径
         */
        setTrackImage(imagePath) {
            this.trackImagePath = imagePath;
            if (this.bitmap && typeof ImageManager !== 'undefined') {
                this.bitmap = ImageManager.loadBitmap('', imagePath);
            }
        }

        setProgressImage(imagePath) {
            this.progressImagePath = imagePath;
            if (this.progressSprite && typeof ImageManager !== 'undefined') {
                this.progressSprite.bitmap = ImageManager.loadBitmap('', imagePath);
            }
        }

        setThumbImage(imagePath) {
            this.thumbImagePath = imagePath;
            if (this.thumbSprite && typeof ImageManager !== 'undefined') {
                this.thumbSprite.bitmap = ImageManager.loadBitmap('', imagePath);
            }
        }



        /**
         * 获取所有属性（用于模型同步）
         */
        getProperties() {
            return {
                // 基础属性
                x: this.x,
                y: this.y,
                width: this.sliderWidth,
                height: this.sliderHeight,

                // 数值属性
                value: this.value,
                minValue: this.minValue,
                maxValue: this.maxValue,
                step: this.step,

                // 图片路径
                trackImagePath: this.trackImagePath,
                progressImagePath: this.progressImagePath,
                thumbImagePath: this.thumbImagePath,

                // 外观属性
                trackColor: this.trackColor,
                fillColor: this.fillColor,
                thumbColor: this.thumbColor,
                thumbWidth: this.thumbWidth,
                thumbHeight: this.thumbHeight,

                // 🔑 文本功能已删除 - 文本由外部绑定对象提供

                // 行为属性
                enabled: this.enabled,

                // 事件代码
                _eventCodes: this._eventCodes
            };
        }

        /**
         * 执行指定类型的事件 - 参考 UIButton 实现
         */
        executeEvent(eventType) {
            // 从 _eventCodes 对象中获取事件代码
            const eventCode = this._eventCodes && this._eventCodes[eventType];
            if (!eventCode || typeof eventCode !== 'string') return;

            console.log('⚡ UISlider: 执行事件', eventType, eventCode);

            try {
                // 创建函数并执行，this指向当前滑动条，同时传入当前值
                const eventFunction = new Function('value', eventCode);
                eventFunction.call(this, this.value);
            } catch (error) {
                console.error(`UISlider ${eventType} 事件执行失败:`, error);
                console.error('事件代码:', eventCode);
            }
        }

        /**
         * 克隆当前 UISlider 对象
         * @param {Object} options 克隆选项
         * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
         * @param {number} options.offsetX 水平偏移量 (默认: 20)
         * @param {number} options.offsetY 垂直偏移量 (默认: 20)
         * @returns {UISlider} 克隆的 UISlider 对象
         */
        clone(options = {}) {
            console.log('🔄 UISlider: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = {
                // 基础属性
                width: this.width,
                height: this.height,
                visible: this.visible,

                // UISlider 特有属性
                minValue: this.minValue,
                maxValue: this.maxValue,
                value: this.value,
                step: this.step,
                sliderWidth: this.sliderWidth,
                sliderHeight: this.sliderHeight,

                // 事件代码
                _eventCodes: {
                    onChange: this._eventCodes?.onChange || ''
                }
            };

            // 2. 创建克隆对象
            const clonedSlider = new UISlider(cloneProperties);

            // 3. 设置位置和变换属性
            clonedSlider.x = this.x + (offsetPosition ? offsetX : 0);
            clonedSlider.y = this.y + (offsetPosition ? offsetY : 0);
            clonedSlider.scale.x = this.scale.x;
            clonedSlider.scale.y = this.scale.y;
            clonedSlider.rotation = this.rotation;
            clonedSlider.alpha = this.alpha;
            clonedSlider.zIndex = this.zIndex;

            // 4. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                    clonedSlider.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                }
            }

            // 5. 重建绑定关系
            this.rebuildBindingsForClone(clonedSlider, clonedChildren);

            console.log('✅ UISlider: 克隆完成，包含', clonedChildren.length, '个子对象');
            return clonedSlider;
        }

        /**
         * 为克隆对象重建绑定关系
         * @param {UISlider} clonedSlider 克隆的滑块对象
         * @param {Array} clonedChildren 克隆的子对象数组
         */
        rebuildBindingsForClone(clonedSlider, clonedChildren) {
            console.log('🔗 UISlider: 重建克隆对象的绑定关系');

            // 找到原始绑定对象在子对象数组中的索引
            const findChildIndex = (boundObject) => {
                if (!boundObject) return -1;
                for (let i = 0; i < this.children.length; i++) {
                    if (this.children[i] === boundObject) {
                        return i;
                    }
                }
                return -1;
            };

            // 重新绑定范围精灵
            if (this.boundRangeSprite) {
                const index = findChildIndex(this.boundRangeSprite);
                if (index >= 0 && index < clonedChildren.length) {
                    clonedSlider.bindRangeSprite(clonedChildren[index]);
                    console.log('🔗 重新绑定范围精灵');
                }
            }

            // 重新绑定滑块精灵
            if (this.boundThumbSprite) {
                const index = findChildIndex(this.boundThumbSprite);
                if (index >= 0 && index < clonedChildren.length) {
                    clonedSlider.bindThumbSprite(clonedChildren[index]);
                    console.log('🔗 重新绑定滑块精灵');
                }
            }

            // 重新绑定文本精灵
            if (this.boundLabelSprite) {
                const index = findChildIndex(this.boundLabelSprite);
                if (index >= 0 && index < clonedChildren.length) {
                    clonedSlider.bindLabelSprite(clonedChildren[index]);
                    console.log('🔗 重新绑定文本精灵');
                }
            }
        }

        /**
         * 销毁滑动条
         */
        destroy() {
            if (this.thumbSprite) {
                this.thumbSprite.destroy();
            }
            if (this.progressSprite) {
                this.progressSprite.destroy();
            }
            if (this.progressMask) {
                this.progressMask.destroy();
            }
            super.destroy();
        }
    }

    // 将 UISlider 类添加到全局
    window.UISlider = UISlider;

    // 🔧 调试：检查方法是否正确定义
    console.log('🔧 调试：UISlider 类方法检查', {
        hasBindRangeSprite: typeof UISlider.prototype.bindRangeSprite,
        hasBindThumbSprite: typeof UISlider.prototype.bindThumbSprite,
        allMethods: Object.getOwnPropertyNames(UISlider.prototype).filter(name => typeof UISlider.prototype[name] === 'function')
    });

    console.log('UI Components Plugin loaded - UISlider class available');
     console.log('--------------------------------------UI Components Plugin loaded - Slider class available----------------------------------------');

})();