/*:
 * @target MZ
 * @plugindesc RPGEditor_GeneratedPlugin v1.0.0
 * <AUTHOR> Editor
 * @version 1.0.0
 * @description Auto-generated plugin from RPG Editor
 *
 * @help RPGEditor_GeneratedPlugin.js
 *
 * This plugin was automatically generated by RPG Editor.
 * It recreates the scene objects and UI elements based on saved data.
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 * Free for commercial and non-commercial use.
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 * Version 1.0.0: Initial release
 */

(() => {
  "use strict";

  // 插件初始化
  console.log("RPG Editor Generated Plugin loaded");

  // ===== Scene_Title 场景处理 =====
  Scene_Title.prototype.create = function() {
    Scene_Base.prototype.create.call(this);
    console.log('RPG Editor: 开始创建 Scene_Title 的自定义对象');

    // 创建UI对象
    const obj_0 = new Sprite();
    obj_0.x = 408;
    obj_0.y = 312;
    obj_0.width = 816;
    obj_0.height = 624;
    obj_0.anchor.x = 0.5;
    obj_0.anchor.y = 0.5;
    obj_0.scale.x = 1;
    obj_0.scale.y = 1;
    obj_0.alpha = 1;
    obj_0.visible = true;
    obj_0.rotation = 0;
    obj_0.blendMode = 0;
    obj_0.zIndex = 0;
    obj_0.bitmap = ImageManager.loadBitmap('img/titles1/', 'Universe');

    this.addChild(obj_0);
    const obj_1 = new Sprite();
    obj_1.x = 408;
    obj_1.y = 312;
    obj_1.width = 1;
    obj_1.height = 1;
    obj_1.anchor.x = 0.5;
    obj_1.anchor.y = 0.5;
    obj_1.scale.x = 816;
    obj_1.scale.y = 816;
    obj_1.alpha = 1;
    obj_1.visible = true;
    obj_1.rotation = 0;
    obj_1.blendMode = 0;
    obj_1.zIndex = 0;
    this.addChild(obj_1);
    const obj_2 = new Sprite();
    obj_2.x = 34;
    obj_2.y = 0;
    obj_2.width = 816;
    obj_2.height = 624;
    obj_2.anchor.x = 0;
    obj_2.anchor.y = 0;
    obj_2.scale.x = 1;
    obj_2.scale.y = 1;
    obj_2.alpha = 1;
    obj_2.visible = true;
    obj_2.rotation = 0.1;
    obj_2.blendMode = 0;
    obj_2.zIndex = 0;
    // 确保 bitmap 存在（RPGEditor_BitmapTracker 插件）
    if (!obj_2.bitmap) {
        obj_2.bitmap = new Bitmap(Graphics.width, Graphics.height);
    }
    // 设置 bitmap 文字属性
    obj_2.bitmap.fontBold = false;
    obj_2.bitmap.fontFace = "rmmz-mainfont, Microsoft Yahei, PingFang SC, sans-serif";
    obj_2.bitmap.fontItalic = false;
    obj_2.bitmap.fontSize = 72;
    obj_2.bitmap.outlineColor = "#d27979";
    obj_2.bitmap.outlineWidth = 8;
    obj_2.bitmap.textColor = "#ffffff";
    obj_2.bitmap._paintOpacity = 255;
    obj_2.bitmap._smooth = true;
    // 重建 elements 数组
    obj_2.bitmap.elements = [
        {
            "type": "text",
            "text": "Project6",
            "x": 20,
            "y": 156,
            "maxWidth": 776,
            "lineHeight": 48,
            "align": "center"
        }
    ];
    // 调用重绘方法
    obj_2.bitmap.redrawing();

    this.addChild(obj_2);

    console.log('RPG Editor: Scene_Title 自定义对象创建完成');
  };

  // ===== Scene_Title 方法重写 =====
  // 重写 createBackground - 跳过原生背景创建
  Scene_Title.prototype.createBackground = function() {
    console.log('RPG Editor: 跳过原生背景创建，使用编辑器对象');
  };

  // 重写 createForeground - 跳过原生前景创建
  Scene_Title.prototype.createForeground = function() {
    console.log('RPG Editor: 跳过原生前景创建，使用编辑器对象');
  };

  // 重写 adjustBackground - 避免访问不存在的背景精灵
  Scene_Title.prototype.adjustBackground = function() {
    console.log('RPG Editor: 跳过背景调整，使用编辑器设置的属性');
  };

  // 重写 createCommandWindow - 跳过原生命令窗口创建
  Scene_Title.prototype.createCommandWindow = function() {
    console.log('RPG Editor: 跳过原生命令窗口创建，使用编辑器对象');
  };

  // 重写 isBusy - 安全检查命令窗口状态
  Scene_Title.prototype.isBusy = function() {
    if (this._commandWindow && this._commandWindow.isClosing) {
      return this._commandWindow.isClosing();
    }
    return Scene_Base.prototype.isBusy.call(this);
  };

  // 重写 update - 安全检查命令窗口
  Scene_Title.prototype.update = function() {
    if (!this.isBusy() && this._commandWindow && this._commandWindow.open) {
      this._commandWindow.open();
    }
    Scene_Base.prototype.update.call(this);
  };

  // 重写 terminate - 安全检查游戏标题精灵
  Scene_Title.prototype.terminate = function() {
    Scene_Base.prototype.terminate.call(this);
    SceneManager.snapForBackground();
    if (this._gameTitleSprite && this._gameTitleSprite.bitmap) {
      this._gameTitleSprite.bitmap.destroy();
    }
  };

  // 重写 scaleSprite - 安全检查精灵对象
  Scene_Title.prototype.scaleSprite = function(sprite) {
    if (sprite && sprite.bitmap) {
      Scene_Base.prototype.scaleSprite.call(this, sprite);
    }
  };

  // 重写 centerSprite - 安全检查精灵对象
  Scene_Title.prototype.centerSprite = function(sprite) {
    if (sprite && sprite.bitmap) {
      Scene_Base.prototype.centerSprite.call(this, sprite);
    }
  };

})();