<script lang="ts">
  /**
   * 组件历史记录功能测试
   * 测试所有支持历史记录的组件
   */
  
  import ColorPicker from './ColorPicker.svelte';
  import Checkbox from './Checkbox.svelte';
  import Slider from './Slider.svelte';
  import Select from './Select.svelte';
  import Switch from './Switch.svelte';
  import { historyManager } from '../historyManager';
  
  // 模拟一个测试对象
  let testObject = {
    className: 'TestObject',
    color: '#ff0000',
    isVisible: true,
    opacity: 50,
    category: 'option1',
    isEnabled: false
  };
  
  // 响应式状态
  let objectColor = $state(testObject.color);
  let objectVisible = $state(testObject.isVisible);
  let objectOpacity = $state(testObject.opacity);
  let objectCategory = $state(testObject.category);
  let objectEnabled = $state(testObject.isEnabled);
  
  // 下拉选择选项
  const categoryOptions = [
    { value: 'option1', label: '选项1' },
    { value: 'option2', label: '选项2' },
    { value: 'option3', label: '选项3' },
    { value: 'option4', label: '选项4' }
  ];
  
  // 处理变更的函数
  function handleColorChange(newValue: string) {
    testObject.color = newValue;
    objectColor = newValue;
    console.log('🎨 颜色变更:', newValue);
  }
  
  function handleVisibleChange(newValue: boolean) {
    testObject.isVisible = newValue;
    objectVisible = newValue;
    console.log('👁️ 可见性变更:', newValue);
  }
  
  function handleOpacityChange(newValue: number) {
    testObject.opacity = newValue;
    objectOpacity = newValue;
    console.log('🔍 透明度变更:', newValue);
  }
  
  function handleCategoryChange(newValue: string | number) {
    testObject.category = newValue as string;
    objectCategory = newValue as string;
    console.log('📋 分类变更:', newValue);
  }
  
  function handleEnabledChange(newValue: boolean) {
    testObject.isEnabled = newValue;
    objectEnabled = newValue;
    console.log('🔄 启用状态变更:', newValue);
  }
  
  // 历史记录操作
  function undo() {
    const success = historyManager.undo();
    if (success) {
      // 同步UI状态
      objectColor = testObject.color;
      objectVisible = testObject.isVisible;
      objectOpacity = testObject.opacity;
      objectCategory = testObject.category;
      objectEnabled = testObject.isEnabled;
      console.log('↶ 撤销成功');
    } else {
      console.log('↶ 撤销失败');
    }
  }
  
  function redo() {
    const success = historyManager.redo();
    if (success) {
      // 同步UI状态
      objectColor = testObject.color;
      objectVisible = testObject.isVisible;
      objectOpacity = testObject.opacity;
      objectCategory = testObject.category;
      objectEnabled = testObject.isEnabled;
      console.log('↷ 重做成功');
    } else {
      console.log('↷ 重做失败');
    }
  }
  
  function clearHistory() {
    historyManager.clear();
    console.log('🗑️ 历史记录已清空');
  }
  
  function getHistoryState() {
    const state = historyManager.getState();
    console.log('📊 历史记录状态:', state);
  }
</script>

<div class="test-container">
  <h2>🧪 组件历史记录功能测试</h2>
  
  <div class="test-section">
    <h3>🎨 ColorPicker - 颜色选择器</h3>
    <div class="component-row">
      <ColorPicker
        bind:value={objectColor}
        label="测试颜色"
        targetObject={testObject}
        fieldName="color"
        name="测试颜色"
        onChange={handleColorChange}
      />
      <span class="current-value">当前值: {objectColor}</span>
    </div>
  </div>
  
  <div class="test-section">
    <h3>✅ Checkbox - 复选框</h3>
    <div class="component-row">
      <Checkbox
        bind:checked={objectVisible}
        label="可见性"
        targetObject={testObject}
        fieldName="isVisible"
        name="可见性"
        onChange={handleVisibleChange}
      />
      <span class="current-value">当前值: {objectVisible}</span>
    </div>
  </div>
  
  <div class="test-section">
    <h3>🎚️ Slider - 滑块</h3>
    <div class="component-row">
      <div class="slider-container">
        <label>透明度: {objectOpacity}</label>
        <Slider
          bind:value={objectOpacity}
          min={0}
          max={100}
          step={1}
          targetObject={testObject}
          fieldName="opacity"
          name="透明度"
          onChange={handleOpacityChange}
        />
      </div>
      <span class="current-value">当前值: {objectOpacity}</span>
    </div>
  </div>
  
  <div class="test-section">
    <h3>📋 Select - 下拉选择</h3>
    <div class="component-row">
      <Select
        bind:value={objectCategory}
        options={categoryOptions}
        placeholder="选择分类"
        targetObject={testObject}
        fieldName="category"
        name="分类"
        onChange={handleCategoryChange}
      />
      <span class="current-value">当前值: {objectCategory}</span>
    </div>
  </div>
  
  <div class="test-section">
    <h3>🔄 Switch - 开关</h3>
    <div class="component-row">
      <Switch
        bind:checked={objectEnabled}
        label="启用状态"
        targetObject={testObject}
        fieldName="isEnabled"
        name="启用状态"
        onChange={handleEnabledChange}
      />
      <span class="current-value">当前值: {objectEnabled}</span>
    </div>
  </div>
  
  <div class="test-section">
    <h3>🎮 历史记录控制</h3>
    <div class="button-row">
      <button onclick={undo}>↶ 撤销 (Ctrl+Z)</button>
      <button onclick={redo}>↷ 重做 (Ctrl+Y)</button>
      <button onclick={getHistoryState}>📊 查看状态</button>
      <button onclick={clearHistory}>🗑️ 清空历史</button>
    </div>
  </div>
  
  <div class="test-section">
    <h3>📊 当前对象状态</h3>
    <div class="object-state">
      <pre>{JSON.stringify(testObject, null, 2)}</pre>
    </div>
  </div>
  
  <div class="test-section">
    <h3>📝 测试说明</h3>
    <div class="test-instructions">
      <h4>🧪 测试步骤：</h4>
      <ol>
        <li><strong>ColorPicker</strong>：点击颜色选择器，选择新颜色，然后按Ctrl+Z撤销</li>
        <li><strong>Checkbox</strong>：点击复选框切换状态，然后按Ctrl+Z撤销</li>
        <li><strong>Slider</strong>：拖拽滑块改变透明度，然后按Ctrl+Z撤销</li>
        <li><strong>Select</strong>：选择不同的分类选项，然后按Ctrl+Z撤销</li>
        <li><strong>Switch</strong>：切换开关状态，然后按Ctrl+Z撤销</li>
      </ol>
      
      <h4>✅ 预期结果：</h4>
      <ul>
        <li>每个组件的操作都应该只产生一条历史记录</li>
        <li>撤销操作应该正确恢复到之前的状态</li>
        <li>重做操作应该正确恢复到撤销前的状态</li>
        <li>控制台应该显示详细的历史记录日志</li>
      </ul>
    </div>
  </div>
</div>

<style>
  .test-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
    font-family: system-ui, sans-serif;
  }
  
  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
  }
  
  .test-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
  }
  
  .component-row {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 10px;
  }
  
  .slider-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 200px;
  }
  
  .current-value {
    font-family: monospace;
    background: #e8f4fd;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #1976d2;
  }
  
  .button-row {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }
  
  button {
    padding: 10px 16px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }
  
  button:hover {
    background: #f0f0f0;
    border-color: #999;
  }
  
  button:active {
    background: #e0e0e0;
  }
  
  .object-state {
    background: #2d2d2d;
    color: #f8f8f2;
    padding: 15px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    overflow-x: auto;
  }
  
  .test-instructions {
    background: #fff;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #4caf50;
  }
  
  .test-instructions h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #2e7d32;
  }
  
  .test-instructions ol,
  .test-instructions ul {
    margin: 0;
    padding-left: 20px;
  }
  
  .test-instructions li {
    margin-bottom: 8px;
    line-height: 1.4;
  }
</style>
