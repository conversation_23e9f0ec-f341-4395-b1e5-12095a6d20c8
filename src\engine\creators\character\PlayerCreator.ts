/**
 * Game_Player 创建器
 * 专门用于创建RPG Maker MZ玩家角色
 */

import { objManage } from '../../objManage';

declare global {
    interface Window {
        Game_Player: any;
        Sprite_Character: any;
        Graphics: any;
        ImageManager: any;
        $gamePlayer: any;
        $dataActors: any[];
        $gameParty: any;
    }
}

/**
 * 创建玩家角色精灵
 * @param options 创建选项
 * @returns 创建的玩家精灵对象
 */
export async function createPlayer(options: {
    actorId?: number;
    characterName?: string;
    characterIndex?: number;
    direction?: number;
    x?: number;
    y?: number;
} = {}): Promise<any> {
    console.log('=== 创建 Game_Player 精灵 ===');
    console.log('创建选项:', options);

    try {
        // 1. 检查资源是否准备就绪
        const resourceStatus = objManage.getResourceStatus();
        if (!resourceStatus.all) {
            console.log('等待资源加载完成...', resourceStatus);
            const loaded = await objManage.waitForResources();
            if (!loaded) {
                throw new Error('资源加载超时');
            }
        }

        // 2. 检查必要的类是否存在
        if (!window.Game_Player || !window.Sprite_Character) {
            throw new Error('RPG Maker MZ Player 类未加载');
        }

        // 3. 创建或获取 Game_Player 实例
        let player: any;

        if (window.$gamePlayer) {
            // 使用现有的全局玩家实例
            player = window.$gamePlayer;
            console.log('使用现有的 $gamePlayer 实例');
        } else {
            // 创建新的 Game_Player 实例
            player = new window.Game_Player();
            console.log('创建新的 Game_Player 实例');
        }

        // 4. 设置玩家角色图像
        if (options.characterName && options.characterIndex !== undefined) {
            // 使用指定的角色图像
            player.setImage(options.characterName, options.characterIndex);
            console.log(`设置玩家图像: ${options.characterName}, 索引: ${options.characterIndex}`);
        } else if (options.actorId && window.$dataActors && window.$dataActors[options.actorId]) {
            // 使用指定角色ID的图像
            const actorData = window.$dataActors[options.actorId];
            player.setImage(actorData.characterName || 'Actor1', actorData.characterIndex || 0);
            console.log(`使用角色ID ${options.actorId} 的图像: ${actorData.characterName}`);
        } else if (window.$gameParty && window.$gameParty.leader && window.$gameParty.leader()) {
            // 使用队伍领队的图像
            const leader = window.$gameParty.leader();
            const actorData = window.$dataActors[leader.actorId()];
            if (actorData) {
                player.setImage(actorData.characterName || 'Actor1', actorData.characterIndex || 0);
                console.log(`使用队伍领队的图像: ${actorData.characterName}`);
            }
        } else {
            // 使用默认图像
            player.setImage('Actor1', 0);
            console.log('使用默认玩家图像: Actor1, 索引: 0');
        }

        // 5. 设置玩家位置
        const x = options.x !== undefined ? options.x : Math.floor(window.Graphics.width / 2 / 48);
        const y = options.y !== undefined ? options.y : Math.floor(window.Graphics.height / 2 / 48);
        player.setPosition(x, y);

        // 6. 设置玩家方向
        const direction = options.direction || 2; // 默认向下
        player.setDirection(direction);

        // 7. 预加载角色图像
        if (window.ImageManager && player._characterName) {
            const bitmap = window.ImageManager.loadCharacter(player._characterName);
            console.log('预加载玩家图像:', player._characterName, bitmap);

            // 等待图像加载完成
            if (bitmap && typeof bitmap.isReady === 'function' && !bitmap.isReady()) {
                console.log('等待玩家图像加载完成...');
                await new Promise<void>((resolve) => {
                    if (typeof bitmap.addLoadListener === 'function') {
                        bitmap.addLoadListener(() => {
                            console.log('玩家图像加载完成');
                            resolve();
                        });
                    } else {
                        const checkReady = () => {
                            if (bitmap.isReady && bitmap.isReady()) {
                                console.log('玩家图像加载完成（轮询检测）');
                                resolve();
                            } else {
                                setTimeout(checkReady, 100);
                            }
                        };
                        checkReady();
                    }
                });
            }
        }

        console.log('Game_Player 创建完成:', {
            characterName: player._characterName,
            characterIndex: player._characterIndex,
            direction: player._direction,
            x: player._x,
            y: player._y
        });

        // 8. 创建精灵对象
        const sprite = new window.Sprite_Character(player);

        console.log('Sprite_Character (Player) 创建完成:', sprite);

        // 验证并修正锚点设置
        if (sprite.anchor) {
            const expectedAnchorX = 0.5;
            const expectedAnchorY = 1;

            console.log('Player 锚点验证（修正前）:', {
                actualAnchorX: sprite.anchor.x,
                expectedAnchorX: expectedAnchorX,
                anchorXCorrect: sprite.anchor.x === expectedAnchorX,
                actualAnchorY: sprite.anchor.y,
                expectedAnchorY: expectedAnchorY,
                anchorYCorrect: sprite.anchor.y === expectedAnchorY,
                bothAnchorsCorrect: sprite.anchor.x === expectedAnchorX && sprite.anchor.y === expectedAnchorY
            });

            // 强制设置正确的锚点（符合 RPG Maker MZ 源码标准）
            sprite.anchor.x = expectedAnchorX;
            sprite.anchor.y = expectedAnchorY;

            console.log('Player 锚点已修正为 RPG Maker MZ 标准:', {
                newAnchorX: sprite.anchor.x,
                newAnchorY: sprite.anchor.y,
                correctionApplied: true
            });
        } else {
            console.warn('Player Sprite 没有 anchor 属性！这不符合 RPG Maker MZ 标准');
        }

        // 强制更新精灵
        if (sprite.update) {
            sprite.update();
            console.log('玩家精灵已强制更新');
        }

        // 9. 设置显示属性
        sprite.scale.x = 2; // 放大2倍
        sprite.scale.y = 2; // 放大2倍

        // 10. 通过objManage添加到舞台
        objManage.addToStage(sprite);

        // 11. 设置调试属性
        sprite.tint = 0xFFFFFF; // 确保没有色调变化
        console.log('玩家精灵创建完成（已放大2倍）');

        // 12. 返回包含显示对象和游戏对象的结构
        // 主要对象是 Sprite（显示对象），游戏对象作为关联数据
        const result = {
            displayObject: sprite,           // 主要对象：Sprite_Character
            gameObject: player,             // 关联的游戏逻辑对象
            type: 'Sprite_Character',       // 对象树中显示的类型
            gameType: 'Game_Player',        // 关联的游戏对象类型
            displayName: `Sprite_Character (Player: ${player._characterName || 'Actor1'})`
        };

        console.log('Player 创建结果:', result);
        return result;

    } catch (error) {
        console.error('创建 Game_Player 精灵失败:', error);
        throw error;
    }
}

/**
 * 创建测试玩家精灵
 * @returns 创建的玩家精灵对象
 */
export async function createTestPlayer(): Promise<any> {
    console.log('=== 创建测试玩家精灵 ===');

    try {
        return await createPlayer({
            characterName: 'Actor1',
            characterIndex: 0,
            direction: 2
        });
    } catch (error) {
        console.error('创建测试玩家失败:', error);
        throw error;
    }
}

/**
 * 创建指定角色ID的玩家精灵
 * @param actorId 角色ID
 * @returns 创建的玩家精灵对象
 */
export async function createPlayerWithActor(actorId: number): Promise<any> {
    console.log(`=== 创建角色ID ${actorId} 的玩家精灵 ===`);

    try {
        return await createPlayer({
            actorId: actorId
        });
    } catch (error) {
        console.error(`创建角色ID ${actorId} 的玩家失败:`, error);
        throw error;
    }
}
