# TextElementPanel 数据绑定功能

TextElementPanel 组件现在集成了 RPG Maker MZ 数据管理系统，支持将游戏数据字段绑定到文本元素。

## 功能特性

### 🎯 数据绑定选择器
- 集成了 `SimpleDataSelector` 组件
- 支持选择 RPG Maker MZ 的所有静态数据类型
- 智能字段解析和中文显示名称

### ⚡ 快速绑定按钮
- 预设常用数据绑定的快捷按钮
- 一键插入角色名称、等级、物品名称等
- 支持自定义快速绑定选项

### 🔄 实时预览
- 选择数据字段后实时显示绑定信息
- 支持应用绑定到文本内容
- 显示完整的字段路径信息

## 使用方法

### 1. 基本数据绑定

1. 在 TextElementPanel 中找到"数据绑定"部分
2. 点击下拉框选择数据类型（如：角色数据、技能数据等）
3. 选择具体的字段（如：名称、等级等）
4. 点击"应用"按钮将绑定应用到文本内容

### 2. 快速绑定

使用预设的快速绑定按钮：
- **角色名称**: 插入 `{{actors.name}}`
- **角色等级**: 插入 `{{actors.initialLevel}}`
- **物品名称**: 插入 `{{items.name}}`
- **游戏标题**: 插入 `{{system.gameTitle}}`

### 3. 绑定表达式格式

数据绑定使用双花括号语法：
```
{{dataType.fieldPath}}
```

示例：
- `{{actors.name}}` - 角色名称
- `{{skills.mpCost}}` - 技能MP消耗
- `{{items.price}}` - 物品价格
- `{{system.gameTitle}}` - 游戏标题

## 支持的数据类型

| 数据类型 | 说明 | 常用字段 |
|---------|------|---------|
| actors | 角色数据 | name, initialLevel, classId |
| classes | 职业数据 | name, params |
| skills | 技能数据 | name, mpCost, description |
| items | 物品数据 | name, price, description |
| weapons | 武器数据 | name, params, price |
| armors | 防具数据 | name, params, price |
| enemies | 敌人数据 | name, params, exp |
| states | 状态数据 | name, message1, message2 |
| system | 系统数据 | gameTitle, currencyUnit |

## 技术实现

### 组件结构
```svelte
<script>
  import { SimpleDataSelector } from '../../dataManage';
  import type { DataSelection } from '../../dataManage';
  
  let dataSelection = $state<DataSelection | null>(null);
  
  function handleDataSelect(selection: DataSelection | null) {
    dataSelection = selection;
  }
  
  function applyDataBinding() {
    if (dataSelection) {
      const bindingExpression = `{{${dataSelection.dataType}.${dataSelection.fieldPath}}}`;
      handleChange('text', bindingExpression);
    }
  }
</script>
```

### 数据流
1. 用户选择数据字段 → `handleDataSelect`
2. 更新 `dataSelection` 状态
3. 用户点击应用 → `applyDataBinding`
4. 生成绑定表达式 → 更新文本内容

## 自定义扩展

### 添加新的快速绑定

在 `insertQuickBinding` 函数中添加新的绑定类型：

```typescript
function insertQuickBinding(bindingType: string) {
  let bindingText = '';
  
  switch (bindingType) {
    case 'custom_binding':
      bindingText = '{{custom.field}}';
      break;
    // ... 其他绑定
  }
  
  handleChange('text', bindingText);
}
```

然后在模板中添加对应的按钮：

```svelte
<button 
  class="quick-button"
  onclick={() => insertQuickBinding('custom_binding')}
  title="自定义绑定"
>
  自定义
</button>
```

### 自定义绑定表达式格式

可以修改 `applyDataBinding` 函数来使用不同的绑定格式：

```typescript
function applyDataBinding() {
  if (!dataSelection) return;
  
  // 使用不同的格式
  const bindingExpression = `[${dataSelection.dataType}:${dataSelection.fieldPath}]`;
  // 或者
  const bindingExpression = `$${dataSelection.dataType}.${dataSelection.fieldPath}$`;
  
  handleChange('text', bindingExpression);
}
```

## 测试

使用测试组件验证功能：

```svelte
<script>
  import TextElementPanelTest from '../test/TextElementPanelTest.svelte';
</script>

<TextElementPanelTest />
```

测试组件提供：
- 实时状态显示
- JSON 数据预览
- 功能使用说明

## 注意事项

1. **数据可用性**: 确保 RPG Maker MZ 的数据已加载
2. **绑定格式**: 使用统一的绑定表达式格式
3. **性能**: 大量绑定时注意性能影响
4. **错误处理**: 处理数据不存在的情况

## 未来改进

- [ ] 支持复杂表达式（如数组索引）
- [ ] 添加数据预览功能
- [ ] 支持条件绑定
- [ ] 添加绑定验证
- [ ] 支持多语言绑定
