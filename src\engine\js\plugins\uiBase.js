//=============================================================================
// uiBase.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc UI Base Components Plugin v1.0.0
 * <AUTHOR>
 * @version 1.0.0
 * @description 提供基础UI组件，包括Image和Label组件
 *
 * @help uiBase.js
 *
 * 这个插件提供了基础UI组件：
 * - UIImage: 图片组件，专门用于显示图片
 * - UILabel: 文本组件，专门用于显示文本
 *
 * 使用方法：
 * const image = new UIImage(properties);
 * const label = new UILabel(properties);
 *
 * 1. UIImage组件 - 专门的图片组件
功能：专门用于显示图片，不混合其他功能
特性：
支持从项目资源路径加载图片 (imagePath)
支持从URL加载图片 (imageUrl)
多种缩放模式：stretch、fit、fill、none
自动创建占位图片（当没有指定图片时）
已删除所有事件相关代码，保持组件纯净
2. UILabel组件 - 专门的文本组件
功能：专门用于显示文本，不混合其他功能
特性：
完整的字体控制（字体、大小、粗体、斜体）
文本颜色和描边设置
水平和垂直对齐选项
背景颜色支持
动态文本更新和重绘
 */

(() => {
    'use strict';
    //=============================================================================
    // UILabel Class - 专门的文本组件
    //=============================================================================

    /**
     * 文本组件类 - 继承自 Sprite
     * 专门用于显示文本，不混合其他功能
     */
    class UILabel extends Sprite {
        constructor(properties = {}) {
            super();

            // 标识为 UI 组件
            this.isUIComponent = true;
            this.uiComponentType = 'UILabel';

            this.initializeLabel(properties);
        }

        /**
         * 初始化文本组件
         * @param {Object} properties 文本属性
         */
        initializeLabel(properties) {
            // 🔑 设置为非交互式，避免拦截父容器的事件
            this.interactive = false;
            this.interactiveChildren = false;

            // 设置默认属性
            this.setupDefaultProperties(properties);

            // 设置文本位图
            this.setupTextBitmap();

            // 绘制文本
            this.redrawText();

            console.log('UILabel created:', this.labelWidth, 'x', this.labelHeight);
        }

        /**
         * 设置默认属性
         */
        setupDefaultProperties(properties) {
            // 基础属性 - 如果没有传入宽高，使用默认值
            this.labelWidth = properties.width || 200;
            this.labelHeight = properties.height || 40;

            // 文本属性
            this.text = properties.text || 'Label Text';
            this.prefix = properties.prefix || '';  // 前缀
            this.suffix = properties.suffix || '';  // 后缀
            this.fontSize = properties.fontSize || 16;
            this.fontFace = properties.fontFace || 'GameFont';
            this.fontBold = properties.fontBold || false;
            this.fontItalic = properties.fontItalic || false;

            // 颜色属性
            this.textColor = properties.textColor || '#ffffff';
            this.outlineColor = properties.outlineColor || '#000000';
            this.outlineWidth = properties.outlineWidth || 4;

            // 对齐属性
            this.textAlign = properties.textAlign || 'center'; // left, center, right
            this.verticalAlign = properties.verticalAlign || 'middle'; // top, middle, bottom

            // 背景属性
            this.backgroundColor = properties.backgroundColor || 'transparent';
            this.backgroundOpacity = properties.backgroundOpacity || 1;
        }

        /**
         * 获取标签宽度
         */
        get width() {
            return this.labelWidth;
        }

        /**
         * 设置标签宽度
         */
        set width(value) {
            this.labelWidth = value;
            this.setupTextBitmap();
            this.redrawText();
        }

        /**
         * 获取标签高度
         */
        get height() {
            return this.labelHeight;
        }

        /**
         * 设置标签高度
         */
        set height(value) {
            this.labelHeight = value;
            this.setupTextBitmap();
            this.redrawText();
        }

        /**
         * 设置文本位图
         */
        setupTextBitmap() {
            this.bitmap = new Bitmap(this.labelWidth, this.labelHeight);

            // 设置字体属性
            this.bitmap.fontSize = this.fontSize;
            this.bitmap.fontFace = this.fontFace;
            this.bitmap.fontBold = this.fontBold;
            this.bitmap.fontItalic = this.fontItalic;
            this.bitmap.textColor = this.textColor;
            this.bitmap.outlineColor = this.outlineColor;
            this.bitmap.outlineWidth = this.outlineWidth;
        }

        /**
         * 重绘文本
         */
        redrawText() {
            if (!this.bitmap) return;

            // 清除画布
            this.bitmap.clear();

            // 重新设置bitmap的所有属性（确保颜色正确）
            this.bitmap.fontSize = this.fontSize;
            this.bitmap.fontFace = this.fontFace;
            this.bitmap.fontBold = this.fontBold;
            this.bitmap.fontItalic = this.fontItalic;
            this.bitmap.textColor = this.textColor;
            this.bitmap.outlineColor = this.outlineColor;
            this.bitmap.outlineWidth = this.outlineWidth;

            console.log('🔧 UILabel: redrawText - textColor:', this.textColor, 'outlineColor:', this.outlineColor);

            // 绘制背景（如果需要）
            if (this.backgroundColor !== 'transparent') {
                this.bitmap.fillRect(0, 0, this.labelWidth, this.labelHeight, this.backgroundColor);
            }

            // 计算文本位置
            const textY = this.calculateTextY();

            // 处理文本内容（检查是否为表达式）
            let displayText = this.text;
            if (window.EDITOR_MODE && this.text && this.text.startsWith('{{') && this.text.endsWith('}}')) {
                // 编辑器中用eval()预览表达式
                try {
                    const expression = this.text.slice(2, -2); // 去掉{{}}
                    displayText = String(eval(expression)) || this.text;
                } catch (error) {
                    console.warn('表达式预览失败:', this.text, error);
                    displayText = this.text; // 出错就显示原字符串
                }
            }

            // 添加前缀和后缀
            const finalText = this.prefix + displayText + this.suffix;

            // 绘制文本
            this.bitmap.drawText(
                finalText,
                0, textY,
                this.labelWidth, this.labelHeight,
                this.textAlign
            );
        }

        /**
         * 计算文本Y位置（垂直对齐）
         */
        calculateTextY() {
            switch (this.verticalAlign) {
                case 'top':
                    return 0;
                case 'bottom':
                    return this.labelHeight - this.fontSize;
                case 'middle':
                default:
                    return (this.labelHeight - this.fontSize) / 2;
            }
        }

        /**
         * 设置文本内容
         */
        setText(text) {
            this.text = text;
            this.redrawText();
        }

        /**
         * 设置前缀
         */
        setPrefix(prefix) {
            this.prefix = prefix;
            this.redrawText();
        }

        /**
         * 设置后缀
         */
        setSuffix(suffix) {
            this.suffix = suffix;
            this.redrawText();
        }

        /**
         * 设置字体大小
         */
        setFontSize(size) {
            this.fontSize = size;
            this.bitmap.fontSize = size;
            this.redrawText();
        }

        /**
         * 设置文本颜色
         */
        setTextColor(color) {
            console.log('🔧 UILabel: setTextColor调用', color);
            this.textColor = color;
            this.bitmap.textColor = color;
            console.log('🔧 UILabel: bitmap.textColor设置为', this.bitmap.textColor);
            this.redrawText();
        }

        /**
         * 设置描边颜色
         */
        setOutlineColor(color) {
            console.log('🔧 UILabel: setOutlineColor调用', color);
            this.outlineColor = color;
            this.bitmap.outlineColor = color;
            console.log('🔧 UILabel: bitmap.outlineColor设置为', this.bitmap.outlineColor);
            this.redrawText();
        }

        /**
         * 设置描边宽度
         */
        setOutlineWidth(width) {
            this.outlineWidth = width;
            this.bitmap.outlineWidth = width;
            this.redrawText();
        }

        /**
         * 设置文本对齐
         */
        setTextAlign(align) {
            this.textAlign = align;
            this.redrawText();
        }

        /**
         * 设置垂直对齐
         */
        setVerticalAlign(align) {
            this.verticalAlign = align;
            this.redrawText();
        }

        /**
         * 设置尺寸
         */
        setSize(width, height) {
            this.labelWidth = width;
            this.labelHeight = height;
            this.setupTextBitmap();
            this.redrawText();
        }

        /**
         * 设置背景颜色
         */
        setBackgroundColor(color) {
            this.backgroundColor = color;
            this.redrawText();
        }

        /**
         * 获取所有属性（用于模型同步）
         */
        getProperties() {
            return {
                // 基础属性
                x: this.x,
                y: this.y,
                width: this.labelWidth,
                height: this.labelHeight,

                // 文本属性
                text: this.text,
                fontSize: this.fontSize,
                fontFace: this.fontFace,
                fontBold: this.fontBold,
                fontItalic: this.fontItalic,

                // 颜色属性
                textColor: this.textColor,
                outlineColor: this.outlineColor,
                outlineWidth: this.outlineWidth,

                // 对齐属性
                textAlign: this.textAlign,
                verticalAlign: this.verticalAlign,

                // 背景属性
                backgroundColor: this.backgroundColor,
                backgroundOpacity: this.backgroundOpacity
            };
        }

        /**
         * 克隆当前 UILabel 对象
         * @param {Object} options 克隆选项
         * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
         * @param {number} options.offsetX 水平偏移量 (默认: 20)
         * @param {number} options.offsetY 垂直偏移量 (默认: 20)
         * @returns {UILabel} 克隆的 UILabel 对象
         */
        clone(options = {}) {
            console.log('🔄 UILabel: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = {
                // 基础属性
                width: this.labelWidth,
                height: this.labelHeight,
                visible: this.visible,

                // UILabel 特有属性
                text: this.text,
                prefix: this.prefix,
                suffix: this.suffix,
                fontSize: this.fontSize,
                fontFace: this.fontFace,
                fontBold: this.fontBold,
                fontItalic: this.fontItalic,
                textColor: this.textColor,
                outlineColor: this.outlineColor,
                outlineWidth: this.outlineWidth,
                textAlign: this.textAlign,
                verticalAlign: this.verticalAlign,
                backgroundColor: this.backgroundColor,
                backgroundOpacity: this.backgroundOpacity
            };

            // 2. 创建克隆对象
            const clonedLabel = new UILabel(cloneProperties);

            // 3. 设置位置和变换属性
            clonedLabel.x = this.x + (offsetPosition ? offsetX : 0);
            clonedLabel.y = this.y + (offsetPosition ? offsetY : 0);
            clonedLabel.scale.x = this.scale.x;
            clonedLabel.scale.y = this.scale.y;
            clonedLabel.rotation = this.rotation;
            clonedLabel.alpha = this.alpha;
            clonedLabel.anchor.x = this.anchor.x;
            clonedLabel.anchor.y = this.anchor.y;
            clonedLabel.pivot.x = this.pivot.x;
            clonedLabel.pivot.y = this.pivot.y;
            clonedLabel.skew.x = this.skew.x;
            clonedLabel.skew.y = this.skew.y;
            clonedLabel.zIndex = this.zIndex;

            // 4. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                    clonedLabel.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                }
            }

            console.log('✅ UILabel: 克隆完成，包含', clonedChildren.length, '个子对象');
            return clonedLabel;
        }

        /**
         * 销毁文本组件
         */
        destroy() {
            if (this.bitmap) {
                this.bitmap.destroy();
            }
            super.destroy();
        }
    }
    // 将类添加到全局
    window.UILabel = UILabel;

    console.log('UI Base Components Plugin loaded - UIImage and UILabel classes available');

})();