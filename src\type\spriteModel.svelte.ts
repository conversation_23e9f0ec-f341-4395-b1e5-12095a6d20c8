import { BaseObjectModel } from './baseObjectModel.svelte';

export class SpriteModel extends BaseObjectModel {

    constructor(sprite: any) {
        super(sprite);
        this.bitmap = sprite.bitmap;
        this.blendMode = sprite.blendMode;
        this.mask = sprite.mask;
        this.filters = sprite.filters;

        // 初始化事件属性
        this.onClickCode = sprite._eventCodes?.onClick || '';
        this.onHoverCode = sprite._eventCodes?.onHover || '';
        this.onHoverOutCode = sprite._eventCodes?.onHoverOut || '';
        this.onPressCode = sprite._eventCodes?.onPress || '';
        this.onReleaseCode = sprite._eventCodes?.onRelease || '';
        this.onDoubleClickCode = sprite._eventCodes?.onDoubleClick || '';
        console.log('🔧 SpriteModel: 创建 sprite 模型', sprite);
        // setupSync() 已经在基类构造函数中调用了
    }
    blendMode = $state(0)
     mask=$state(null) // 复杂对象，暂时直接保存
    filters=$state(null) // 复杂对象，暂时直接保存
    //   实际bitmap值
    bitmap = $state(null)

    // 事件属性
    onClickCode = $state('');         // 点击事件代码
    onHoverCode = $state('');         // 悬停进入事件代码
    onHoverOutCode = $state('');      // 悬停离开事件代码
    onPressCode = $state('');         // 按下事件代码
    onReleaseCode = $state('');       // 释放事件代码
    onDoubleClickCode = $state('');   // 双击事件代码
    /**
     * 重写设置同步：模型变化 → sprite
     * 使用 $effect.root 创建独立的响应式上下文
     */
    /**
     * 设置Sprite特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Sprite特有的属性
     */
    protected setupSpecificSync(): void {
        // 同步Sprite特有属性
        this._originalObject.bitmap = this.bitmap;
        this._originalObject.blendMode = this.blendMode;
        this._originalObject.mask = this.mask;
        this._originalObject.filters = this.filters;

        // 同步事件属性
        if (!this._originalObject._eventCodes) {
            this._originalObject._eventCodes = {};
        }
        this._originalObject._eventCodes.onClick = this.onClickCode;
        this._originalObject._eventCodes.onHover = this.onHoverCode;
        this._originalObject._eventCodes.onHoverOut = this.onHoverOutCode;
        this._originalObject._eventCodes.onPress = this.onPressCode;
        this._originalObject._eventCodes.onRelease = this.onReleaseCode;
        this._originalObject._eventCodes.onDoubleClick = this.onDoubleClickCode;

        // 重新设置事件处理（包括清理已删除的事件）
        if (this._originalObject.setupEvents) {
            const eventsData = {
                onClick: this.onClickCode || undefined,
                onHover: this.onHoverCode || undefined,
                onHoverOut: this.onHoverOutCode || undefined,
                onPress: this.onPressCode || undefined,
                onRelease: this.onReleaseCode || undefined,
                onDoubleClick: this.onDoubleClickCode || undefined
            };

            // 移除空值
            Object.keys(eventsData).forEach(key => {
                if (!eventsData[key as keyof typeof eventsData]) {
                    delete eventsData[key as keyof typeof eventsData];
                }
            });

            // 总是调用 setupEvents，即使没有事件（用于清理）
            this._originalObject.setupEvents(eventsData);
            console.log('🔧 SpriteModel: 重新设置事件处理', eventsData);

            // 如果没有任何事件，清理事件相关属性
            if (Object.keys(eventsData).length === 0) {
                console.log('🔧 SpriteModel: 清理所有事件处理器');
                // 清理事件处理器
                if (this._originalObject._eventHandlers) {
                    this._originalObject._eventHandlers = {};
                }
                if (this._originalObject._eventCodes) {
                    this._originalObject._eventCodes = {};
                }
                // 禁用事件
                this._originalObject._isEventEnabled = false;
            }
        }

        // 触发重绘
        if (this._originalObject.bitmap && this._originalObject.elements && typeof this._originalObject.bitmap.redrawing === 'function') {
            this._originalObject.bitmap.redrawing();
            console.log('🔧 SpriteModel: 触发 bitmap 重绘');
        }
    }



    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 创建Sprite对象，参考SpriteProcessor的实现
        const hasEvents = this.hasEvents();

        if (this.bitmap || hasEvents) {
            if (hasEvents) {
                // 生成组合数据对象 { bitmap: {...}, events: {...} }
                codes.push(`${indent}// 创建 Sprite（包含 bitmap 和 events）`);
                const combinedDataString = this.generateCombinedDataString(indent + '  ');
                codes.push(`${indent}const ${varName} = new Sprite(${combinedDataString});`);
            } else {
                // 只有 bitmap，生成单纯的 bitmap 数据对象
                codes.push(`${indent}// 创建 Sprite（包含 bitmap）`);
                const bitmapDataString = this.generateBitmapDataString(indent + '  ');
                codes.push(`${indent}const ${varName} = new Sprite(${bitmapDataString});`);
            }
        } else {
            codes.push(`${indent}// 创建 Sprite`);
            codes.push(`${indent}const ${varName} = new Sprite();`);
        }

        return codes.join('\n');
    }

    /**
     * 重写特定属性设置代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns Sprite特定属性设置代码
     */
    protected generateSpecificProperties(varName: string, indent: string): string {
        const codes: string[] = [];

        // 设置Sprite特有属性
        if (this.blendMode !== undefined && this.blendMode !== 0) {
            codes.push(`${indent}${varName}.blendMode = ${this.blendMode};`);
        }

        return codes.join('\n');
    }



    /**
     * 生成组合数据对象字符串（包含bitmap和events）
     */
    private generateCombinedDataString(indent: string): string {
        const parts: string[] = [];

        // 添加 bitmap 部分
        if (this.bitmap) {
            const bitmapString = this.generateBitmapDataString(indent + '  ');
            parts.push(`${indent}bitmap: ${bitmapString}`);
        }

        // 添加 events 部分
        if (this.hasEvents()) {
            const eventsString = this.generateEventsDataString(indent + '  ');
            parts.push(`${indent}events: ${eventsString}`);
        }

        return `{\n${parts.join(',\n')}\n${indent.slice(2)}}`;
    }

    /**
     * 生成bitmap数据对象字符串
     */
    private generateBitmapDataString(indent: string): string {
        if (!this.bitmap) {
            return 'null';
        }

        const bitmap = this.bitmap as any; // 类型断言避免TypeScript错误
        const props: string[] = [];

        // 基本属性（参考SpriteProcessor的顺序）
        if (bitmap.fontBold !== undefined) props.push(`fontBold: ${bitmap.fontBold}`);
        if (bitmap.fontFace) props.push(`fontFace: '${bitmap.fontFace}'`);
        if (bitmap.fontItalic !== undefined) props.push(`fontItalic: ${bitmap.fontItalic}`);
        if (bitmap.fontSize !== undefined) props.push(`fontSize: ${bitmap.fontSize}`);
        if (bitmap.outlineColor) props.push(`outlineColor: '${bitmap.outlineColor}'`);
        if (bitmap.outlineWidth !== undefined) props.push(`outlineWidth: ${bitmap.outlineWidth}`);
        if (bitmap.textColor) props.push(`textColor: '${bitmap.textColor}'`);
        if (bitmap._paintOpacity !== undefined) props.push(`_paintOpacity: ${bitmap._paintOpacity}`);
        if (bitmap._smooth !== undefined) props.push(`_smooth: ${bitmap._smooth}`);

        // URL 属性 - 使用相对路径，参考BitmapUtils的处理
        const url = this.extractBitmapUrl(bitmap);
        if (url) {
            props.push(`url: '${url}'`);
        }

        // elements 数组
        if (bitmap.elements && Array.isArray(bitmap.elements)) {
            const elementsString = this.generateElementsArrayString(bitmap.elements, indent + '  ');
            props.push(`elements: ${elementsString}`);
        }

        // 如果没有任何属性，返回 null
        if (props.length === 0) {
            return 'null';
        }

        // 生成对象字符串
        const propsString = props.map(prop => `${indent}  ${prop}`).join(',\n');
        return `{\n${propsString}\n${indent}}`;
    }

    /**
     * 生成events数据对象字符串
     */
    private generateEventsDataString(indent: string): string {
        const props: string[] = [];

        // 使用双引号包裹，参考SpriteProcessor的实现
        if (this.onClickCode) props.push(`${indent}onClick: "${this.onClickCode.replace(/"/g, '\\"')}"`);
        if (this.onHoverCode) props.push(`${indent}onHover: "${this.onHoverCode.replace(/"/g, '\\"')}"`);
        if (this.onHoverOutCode) props.push(`${indent}onHoverOut: "${this.onHoverOutCode.replace(/"/g, '\\"')}"`);
        if (this.onPressCode) props.push(`${indent}onPress: "${this.onPressCode.replace(/"/g, '\\"')}"`);
        if (this.onReleaseCode) props.push(`${indent}onRelease: "${this.onReleaseCode.replace(/"/g, '\\"')}"`);
        if (this.onDoubleClickCode) props.push(`${indent}onDoubleClick: "${this.onDoubleClickCode.replace(/"/g, '\\"')}"`);

        if (props.length === 0) {
            return '{}';
        }

        return `{\n${props.join(',\n')}\n${indent.slice(2)}}`;
    }

    /**
     * 生成 elements 数组的字符串表示
     */
    private generateElementsArrayString(elements: any[], indent: string): string {
        if (!elements || elements.length === 0) {
            return '[]';
        }

        const elementStrings = elements.map(element => {
            if (!element) return 'null';

            const props: string[] = [];

            // 通用属性
            if (element.type) props.push(`type: '${element.type}'`);
            if (element.id) props.push(`id: '${element.id}'`);

            // 文本元素属性
            if (element.type === 'text') {
                if (element.text) props.push(`text: '${element.text}'`);
                if (element.x !== undefined) props.push(`x: ${element.x}`);
                if (element.y !== undefined) props.push(`y: ${element.y}`);
                if (element.maxWidth !== undefined) props.push(`maxWidth: ${element.maxWidth}`);
                if (element.lineHeight !== undefined) props.push(`lineHeight: ${element.lineHeight}`);
                if (element.align) props.push(`align: '${element.align}'`);
            }

            // 图像元素属性
            if (element.type === 'image') {
                if (element.sourceUrl) props.push(`sourceUrl: '${element.sourceUrl}'`);
                if (element.sx !== undefined) props.push(`sx: ${element.sx}`);
                if (element.sy !== undefined) props.push(`sy: ${element.sy}`);
                if (element.sw !== undefined) props.push(`sw: ${element.sw}`);
                if (element.sh !== undefined) props.push(`sh: ${element.sh}`);
                if (element.dx !== undefined) props.push(`dx: ${element.dx}`);
                if (element.dy !== undefined) props.push(`dy: ${element.dy}`);
                if (element.dw !== undefined) props.push(`dw: ${element.dw}`);
                if (element.dh !== undefined) props.push(`dh: ${element.dh}`);
            }

            const propsString = props.map(prop => `${indent}    ${prop}`).join(',\n');
            return `{\n${propsString}\n${indent}  }`;
        });

        return `[\n${indent}  ${elementStrings.join(`,\n${indent}  `)}\n${indent}]`;
    }

    /**
     * 检查是否有事件代码
     */
    private hasEvents(): boolean {
        return !!(this.onClickCode || this.onHoverCode || this.onHoverOutCode ||
                 this.onPressCode || this.onReleaseCode || this.onDoubleClickCode);
    }

    /**
     * 提取 Bitmap 的 URL（参考 BitmapUtils.extractUrl）
     */
    private extractBitmapUrl(bitmap: any): string | undefined {
        // 优先使用原始路径（CustomResourcePath 插件保存的相对路径）
        if (bitmap._originalPath) {
            return bitmap._originalPath;
        }

        // 如果是 Bitmap 对象，尝试获取 URL
        if (bitmap._url) {
            // 如果是 blob URL，尝试从其他属性获取原始路径
            if (bitmap._url.startsWith('blob:')) {
                return bitmap._filePath || undefined;
            } else if (bitmap._url.trim() !== '') {
                // 如果不是blob URL，需要转换为相对路径
                return this.convertToRelativePath(bitmap._url);
            }
        }

        // 如果是字符串路径
        if (typeof bitmap === 'string') {
            return this.convertToRelativePath(bitmap);
        }

        return undefined;
    }

    /**
     * 转换为相对路径格式 (img/xxx/xxx.png)
     */
    private convertToRelativePath(absolutePath: string): string {
        if (!absolutePath) {
            return '';
        }

        // 如果已经是相对路径格式（以 img/ 开头），直接返回
        if (absolutePath.startsWith('img/')) {
            return absolutePath;
        }

        // 处理绝对路径，提取 img/ 后面的部分
        if (absolutePath.includes('img/')) {
            const imgIndex = absolutePath.lastIndexOf('img/');
            return absolutePath.substring(imgIndex);
        }

        // 处理 Windows 路径分隔符
        const normalizedPath = absolutePath.replace(/\\/g, '/');
        if (normalizedPath.includes('img/')) {
            const imgIndex = normalizedPath.lastIndexOf('img/');
            return normalizedPath.substring(imgIndex);
        }

        // 如果路径中包含项目目录结构，尝试智能提取
        const pathParts = normalizedPath.split('/');
        const imgIndex = pathParts.findIndex(part => part === 'img');

        if (imgIndex !== -1) {
            return pathParts.slice(imgIndex).join('/');
        }

        // 最后的回退：返回原路径
        return absolutePath;
    }

    /**
     * 生成示例代码（用于测试）
     */
    generateExampleCode(): string {
        // 设置一些示例数据
        this.name = 'ExampleSprite';
        this.x = 100;
        this.y = 200;
        this.width = 64;
        this.height = 64;
        this.alpha = 0.8;
        this.onClickCode = 'console.log("Sprite clicked!");';

        return this.generateCreationCode('exampleSprite', '    ');
    }

    /**
     * 克隆当前Sprite对象
     */
    clone(): SpriteModel {
        // 创建新的Sprite数据对象
        const clonedData = {
            // 基础属性
            className: this.className,
            x: this.x + 20, // 稍微偏移位置
            y: this.y + 20,
            width: this.width,
            height: this.height,
            scaleX: this.scaleX,
            scaleY: this.scaleY,
            rotation: this.rotation,
            alpha: this.alpha,
            visible: this.visible,
            anchorX: this.anchorX,
            anchorY: this.anchorY,
            pivotX: this.pivotX,
            pivotY: this.pivotY,
            skewX: this.skewX,
            skewY: this.skewY,
            zIndex: this.zIndex,

            // Sprite特有属性
            bitmap: this.bitmap ? { ...this.bitmap } : null, // 浅拷贝bitmap
            onClickCode: this.onClickCode,

            // 深拷贝子对象
            children: this.children.map(child => {
                if (typeof child.clone === 'function') {
                    return child.clone();
                } else {
                    console.warn('🔄 SpriteModel: 子对象没有clone方法', child.className);
                    return child;
                }
            })
        };

        console.log('🔄 SpriteModel: 克隆Sprite对象', clonedData);
        return new SpriteModel(clonedData);
    }
}

// 注册SpriteModel到基类容器
BaseObjectModel.registerModel('Sprite', SpriteModel);
BaseObjectModel.registerModel('Sprite_Button', SpriteModel); // Sprite_Button也使用SpriteModel