<script lang="ts">
  import SimpleDataSelector from '../components/SimpleDataSelector.svelte';
  import type { DataSelection } from '../types/dataTypes';

  let selection = $state<DataSelection | null>(null);

  function handleSelect(newSelection: DataSelection | null) {
    selection = newSelection;
    console.log('选择结果:', newSelection);
  }

  // 测试displayText的计算
  const testDisplayText = $derived.by(() => {
    if (selection) {
      const parts = selection.displayText.split(' → ');
      if (parts.length > 1) {
        return parts.slice(1).join(' → ');
      }
      return selection.fieldInfo.displayName || selection.fieldPath;
    }
    return "请选择数据字段";
  });
</script>

<div class="test-container">
  <h2>显示文本测试</h2>
  
  <div class="test-section">
    <h3>SimpleDataSelector</h3>
    <SimpleDataSelector 
      value={selection}
      onSelect={handleSelect}
      placeholder="选择数据字段"
    />
  </div>

  <div class="debug-section">
    <h3>调试信息</h3>
    <div class="debug-item">
      <strong>计算的显示文本:</strong> {testDisplayText}
    </div>
    {#if selection}
      <div class="debug-item">
        <strong>原始displayText:</strong> {selection.displayText}
      </div>
      <div class="debug-item">
        <strong>字段名称:</strong> {selection.fieldInfo.displayName}
      </div>
      <div class="debug-item">
        <strong>字段路径:</strong> {selection.fieldPath}
      </div>
    {/if}
  </div>
</div>

<style>
  .test-container {
    max-width: 600px;
    margin: 20px auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .test-section, .debug-section {
    margin-bottom: 20px;
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
  }

  .test-section h3, .debug-section h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #333;
  }

  .debug-item {
    margin-bottom: 8px;
    padding: 4px 0;
    font-family: monospace;
    font-size: 14px;
  }

  .debug-item strong {
    color: #495057;
    margin-right: 8px;
  }
</style>
