<script lang="ts">
  import ColorPicker from '../../components/ColorPicker.svelte';
  import { bitmapModel } from '../stores/bitmapStore';

  // 字体选项
  const fontFaceOptions = [
    { value: 'GameFont', label: '<PERSON>Font (默认)' },
    { value: 'Arial', label: 'Arial' },
    { value: 'sans-serif', label: 'Sans-serif' },
    { value: 'serif', label: 'Serif' },
    { value: 'monospace', label: 'Monospace' }
  ];

  // 🔧 简化的颜色处理函数
  function handleTextColorChange(color: string) {
    $bitmapModel.textColor = color;
  }

  function handleOutlineColorChange(color: string) {
    $bitmapModel.outlineColor = color;
  }
</script>

<div class="font-properties-panel">
  <div class="panel-header">
    <h3>Bitmap 公共属性</h3>
  </div>

  {#if bitmapModel}
    <div class="properties-grid">
      <!-- 字体系列 -->
      <div class="property-group">
        <label>字体:</label>
        <select bind:value={$bitmapModel.fontFace}>
          {#each fontFaceOptions as option}
            <option value={option.value}>{option.label}</option>
          {/each}
        </select>
      </div>

      <!-- 字体大小 -->
      <div class="property-group">
        <label>大小:</label>
        <input
          type="number"
          bind:value={$bitmapModel.fontSize}
          min="8"
          max="72"
          class="size-input"
        />
      </div>

      <!-- 字体样式 -->
      <div class="property-group">
        <label>样式:</label>
        <div class="style-buttons">
          <button
            class="style-btn"
            class:active={$bitmapModel.fontBold}
            on:click={() => $bitmapModel.fontBold = !$bitmapModel.fontBold}
            title="粗体"
          >
            <strong>B</strong>
          </button>
          <button
            class="style-btn"
            class:active={$bitmapModel.fontItalic}
            on:click={() => $bitmapModel.fontItalic = !$bitmapModel.fontItalic}
            title="斜体"
          >
            <em>I</em>
          </button>
        </div>
      </div>

      <!-- 文字颜色 -->
      <div class="property-group">
        <label>文字颜色:</label>
        <ColorPicker
          value={$bitmapModel.textColor}
          targetObject={$bitmapModel}
          fieldName="textColor"
          name="文字颜色"
          onChange={handleTextColorChange}
        />
      </div>

      <!-- 轮廓颜色 -->
      <div class="property-group">
        <label>轮廓颜色:</label>
        <ColorPicker
          value={$bitmapModel.outlineColor}
          targetObject={$bitmapModel}
          fieldName="outlineColor"
          name="轮廓颜色"
          onChange={handleOutlineColorChange}
        />
      </div>

      <!-- 轮廓宽度 -->
      <div class="property-group">
        <label>轮廓宽度:</label>
        <input
          type="number"
          bind:value={$bitmapModel.outlineWidth}
          min="0"
          max="10"
          class="size-input"
        />
      </div>

      <!-- 透明度 -->
      <div class="property-group">
        <label>透明度: {$bitmapModel._paintOpacity}</label>
        <div class="slider-container">
          <input
            type="range"
            min="0"
            max="255"
            step="1"
            bind:value={$bitmapModel._paintOpacity}
            class="opacity-slider"
          />
          <div class="slider-labels">
            <span>透明</span>
            <span>不透明</span>
          </div>
        </div>
      </div>

      <!-- 平滑 -->
      <div class="property-group">
        <label>平滑:</label>
        <input
          type="checkbox"
          bind:checked={$bitmapModel._smooth}
        />
      </div>
    </div>
  {:else}
    <div class="no-bitmap">
      <p>BitmapModel 未初始化</p>
    </div>
  {/if}
</div>

<style>
  .font-properties-panel {
    height: 100%;
    background: var(--theme-surface, #ffffff);
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    padding: var(--spacing-2, 0.5rem) var(--spacing-3, 0.75rem);
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    background: var(--theme-surface-light, #f9fafb);
  }

  .panel-header h3 {
    margin: 0;
    font-size: var(--font-size-sm, 0.875rem);
    font-weight: 600;
    color: var(--theme-text, #111827);
  }

  .properties-grid {
    flex: 1;
    padding: var(--spacing-3, 0.75rem);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-3, 0.75rem);
    align-content: start;
  }

  .property-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1, 0.25rem);
  }

  .property-group label {
    font-size: var(--font-size-xs, 0.75rem);
    font-weight: 500;
    color: var(--theme-text, #111827);
  }

  .property-group select {
    padding: var(--spacing-1, 0.25rem);
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: var(--border-radius, 4px);
    background: var(--theme-surface, #ffffff);
    color: var(--theme-text, #111827);
    font-size: var(--font-size-sm, 0.875rem);
  }

  .style-buttons {
    display: flex;
    gap: var(--spacing-1, 0.25rem);
  }

  .style-btn {
    width: 28px;
    height: 28px;
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: var(--border-radius, 4px);
    background: var(--theme-surface, #ffffff);
    color: var(--theme-text, #111827);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm, 0.875rem);
  }

  .style-btn:hover {
    background: var(--theme-surface-light, #f9fafb);
    border-color: var(--theme-primary-light, #93c5fd);
  }

  .style-btn.active {
    background: var(--theme-primary, #3b82f6);
    color: white;
    border-color: var(--theme-primary, #3b82f6);
  }

  .property-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
  }

  /* 滑动条样式 */
  .slider-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1, 0.25rem);
  }

  .opacity-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(to right,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 0.8) 100%);
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
  }

  .opacity-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--theme-primary, #3b82f6);
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .opacity-slider::-webkit-slider-thumb:hover {
    background: var(--theme-primary-hover, #2563eb);
    transform: scale(1.1);
  }

  .opacity-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--theme-primary, #3b82f6);
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .opacity-slider::-moz-range-thumb:hover {
    background: var(--theme-primary-hover, #2563eb);
    transform: scale(1.1);
  }

  .slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-xs, 0.75rem);
    color: var(--theme-text-secondary, #6b7280);
    margin-top: 2px;
  }

  .no-bitmap {
    padding: var(--spacing-4, 1rem);
    text-align: center;
    color: var(--theme-text-secondary, #6b7280);
    font-style: italic;
  }
</style>
