<script lang="ts">
  /**
   * Button 按钮组件
   * 基于 Skeleton UI 和全局主题色彩
   */
  
  // Props
  export let type: 'button' | 'submit' | 'reset' = 'button';
  export let variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'success' | 'warning' | 'error' = 'primary';
  export let size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  export let disabled: boolean = false;
  export let loading: boolean = false;
  export let fullWidth: boolean = false;
  export let leftIcon: string = '';
  export let rightIcon: string = '';
  export let href: string = '';
  export let target: string = '';
  export let rel: string = '';
  export let id: string = '';
  export let name: string = '';
  export let value: string = '';
  export let ariaLabel: string = '';
  export let ariaDescribedBy: string = '';
  
  // Events
  export let onClick: (event: MouseEvent) => void = () => {};
  export let onFocus: (event: FocusEvent) => void = () => {};
  export let onBlur: (event: FocusEvent) => void = () => {};
  export let onKeydown: (event: KeyboardEvent) => void = () => {};
  
  // 内部状态
  let buttonElement: HTMLButtonElement | HTMLAnchorElement;
  
  // 处理点击事件
  function handleClick(event: MouseEvent) {
    if (disabled || loading) {
      event.preventDefault();
      return;
    }
    onClick(event);
  }
  
  // 获取按钮样式类
  function getButtonClass() {
    const baseClass = 'btn';
    const variantClass = `btn-${variant}`;
    const sizeClass = `btn-${size}`;
    const disabledClass = disabled ? 'btn-disabled' : '';
    const loadingClass = loading ? 'btn-loading' : '';
    const fullWidthClass = fullWidth ? 'btn-full-width' : '';
    
    return [baseClass, variantClass, sizeClass, disabledClass, loadingClass, fullWidthClass]
      .filter(Boolean)
      .join(' ');
  }
  
  // 公开方法
  export function focus() {
    buttonElement?.focus();
  }
  
  export function blur() {
    buttonElement?.blur();
  }
  
  // 判断是否为链接
  $derived isLink = !!href;
</script>

{#if isLink}
  <a
    bind:this={buttonElement}
    {href}
    {target}
    {rel}
    {id}
    aria-label={ariaLabel}
    aria-describedby={ariaDescribedBy}
    aria-disabled={disabled || loading}
    class={getButtonClass()}
    onclick={handleClick}
    onfocus={onFocus}
    onblur={onBlur}
    onkeydown={onKeydown}
  >
    {#if loading}
      <div class="btn-spinner">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="37.7" stroke-dashoffset="37.7">
            <animateTransform attributeName="transform" type="rotate" values="0 8 8;360 8 8" dur="1s" repeatCount="indefinite"/>
          </circle>
        </svg>
      </div>
    {:else if leftIcon}
      <span class="btn-icon btn-icon-left">{leftIcon}</span>
    {/if}
    
    <span class="btn-content">
      <slot />
    </span>
    
    {#if !loading && rightIcon}
      <span class="btn-icon btn-icon-right">{rightIcon}</span>
    {/if}
  </a>
{:else}
  <button
    bind:this={buttonElement}
    {type}
    {disabled}
    {id}
    {name}
    {value}
    aria-label={ariaLabel}
    aria-describedby={ariaDescribedBy}
    class={getButtonClass()}
    onclick={handleClick}
    onfocus={onFocus}
    onblur={onBlur}
    onkeydown={onKeydown}
  >
    {#if loading}
      <div class="btn-spinner">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="37.7" stroke-dashoffset="37.7">
            <animateTransform attributeName="transform" type="rotate" values="0 8 8;360 8 8" dur="1s" repeatCount="indefinite"/>
          </circle>
        </svg>
      </div>
    {:else if leftIcon}
      <span class="btn-icon btn-icon-left">{leftIcon}</span>
    {/if}
    
    <span class="btn-content">
      <slot />
    </span>
    
    {#if !loading && rightIcon}
      <span class="btn-icon btn-icon-right">{rightIcon}</span>
    {/if}
  </button>
{/if}

<style>
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    font-family: var(--font-family-base);
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
  }
  
  .btn:focus-visible {
    outline: 2px solid var(--theme-primary);
    outline-offset: 2px;
  }
  
  .btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  .btn-loading {
    cursor: wait;
  }
  
  .btn-full-width {
    width: 100%;
  }
  
  .btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .btn-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  /* 尺寸变体 */
  .btn-xs {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-xs);
    min-height: 24px;
  }
  
  .btn-sm {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    min-height: 32px;
  }
  
  .btn-md {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-base);
    min-height: 40px;
  }
  
  .btn-lg {
    padding: var(--spacing-4) var(--spacing-5);
    font-size: var(--font-size-lg);
    min-height: 48px;
  }
  
  .btn-xl {
    padding: var(--spacing-5) var(--spacing-6);
    font-size: var(--font-size-xl);
    min-height: 56px;
  }
  
  /* 主要变体 */
  .btn-primary {
    background-color: var(--theme-primary);
    color: var(--theme-text-inverse);
    border-color: var(--theme-primary);
  }
  
  .btn-primary:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: var(--theme-primary-dark);
    border-color: var(--theme-primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--theme-shadow-light);
  }
  
  .btn-primary:active:not(.btn-disabled):not(.btn-loading) {
    transform: translateY(0);
    box-shadow: 0 2px 4px var(--theme-shadow-light);
  }
  
  /* 次要变体 */
  .btn-secondary {
    background-color: var(--theme-secondary);
    color: var(--theme-text-inverse);
    border-color: var(--theme-secondary);
  }
  
  .btn-secondary:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: var(--theme-secondary-dark);
    border-color: var(--theme-secondary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--theme-shadow-light);
  }
  
  /* 轮廓变体 */
  .btn-outline {
    background-color: transparent;
    color: var(--theme-primary);
    border-color: var(--theme-primary);
  }
  
  .btn-outline:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: var(--theme-primary);
    color: var(--theme-text-inverse);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--theme-shadow-light);
  }
  
  /* 幽灵变体 */
  .btn-ghost {
    background-color: transparent;
    color: var(--theme-text);
    border-color: transparent;
  }
  
  .btn-ghost:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: var(--theme-surface-light);
    color: var(--theme-primary);
  }
  
  /* 状态变体 */
  .btn-success {
    background-color: var(--theme-success);
    color: var(--theme-text-inverse);
    border-color: var(--theme-success);
  }
  
  .btn-success:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: #16a34a;
    border-color: #16a34a;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--theme-shadow-light);
  }
  
  .btn-warning {
    background-color: var(--theme-warning);
    color: var(--theme-text-inverse);
    border-color: var(--theme-warning);
  }
  
  .btn-warning:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: #d97706;
    border-color: #d97706;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--theme-shadow-light);
  }
  
  .btn-error {
    background-color: var(--theme-error);
    color: var(--theme-text-inverse);
    border-color: var(--theme-error);
  }
  
  .btn-error:hover:not(.btn-disabled):not(.btn-loading) {
    background-color: #dc2626;
    border-color: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--theme-shadow-light);
  }
  
  /* 加载状态动画 */
  .btn-loading .btn-content {
    opacity: 0.7;
  }
  
  /* 波纹效果 */
  .btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }
  
  .btn:active:not(.btn-disabled):not(.btn-loading)::before {
    width: 300px;
    height: 300px;
  }
</style>
