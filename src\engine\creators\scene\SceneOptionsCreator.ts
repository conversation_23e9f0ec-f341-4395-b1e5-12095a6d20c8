/**
 * Scene_Options 创建器
 * 专门用于创建选项场景
 */

import { BaseSceneCreator, type SceneCreationOptions } from './SceneCreator';
import { type MenuBaseSceneOptions } from './SceneMenuBaseCreator';

/**
 * 选项场景创建选项
 */
export interface OptionsSceneOptions extends MenuBaseSceneOptions {
    /** 初始选中的选项索引 */
    initialOptionIndex?: number;
}

/**
 * 创建选项场景
 * @param options 创建选项
 * @returns 创建的选项场景实例
 */
export async function createSceneOptions(options: OptionsSceneOptions = {}): Promise<any> {
    console.log('=== 创建选项场景 Scene_Options ===');
    
    try {
        // 预加载选项场景资源
        BaseSceneCreator.preloadSceneResources('Scene_Options');
        
        // 设置默认选项
        const defaultOptions: SceneCreationOptions = {
            autoStart: false,
            addToStage: true,
            initParams: [],
            ...options
        };
        
        // 创建场景实例
        const scene = await BaseSceneCreator.createSceneInstance('Scene_Options', defaultOptions);
        
        // Scene_Options 特定的设置
        if (options.initialOptionIndex !== undefined && scene._optionsWindow) {
            scene._optionsWindow.select(options.initialOptionIndex);
            console.log('设置初始选项索引:', options.initialOptionIndex);
        }
        
        console.log('Scene_Options 创建完成');
        return scene;
        
    } catch (error) {
        console.error('创建 Scene_Options 失败:', error);
        throw error;
    }
}

/**
 * 创建并启动选项场景
 * @param options 创建选项
 * @returns 创建的选项场景实例
 */
export async function createAndStartSceneOptions(options: OptionsSceneOptions = {}): Promise<any> {
    return createSceneOptions({ ...options, autoStart: true });
}

/**
 * 创建简单的选项场景（用于测试）
 * @returns 创建的选项场景实例
 */
export async function createSimpleSceneOptions(): Promise<any> {
    return createSceneOptions({
        initialOptionIndex: 0,
        backgroundType: 1,
        autoStart: false,
        addToStage: true
    });
}
