<script lang="ts">
  import TextElementPanel from '../components/TextElementPanel.svelte';
  import type { TextElement } from '../../type/bitmap.svelte';

  // 创建一个测试用的文本元素
  let testElement: TextElement = $state({
    type: 'text',
    text: '测试文本',
    x: 10,
    y: 20,
    maxWidth: 200,
    lineHeight: 24,
    align: 'left',
    fontFamily: 'Arial',
    fontSize: 16,
    fontColor: '#000000',
    fontBold: false,
    fontItalic: false,
    outlineColor: '#ffffff',
    outlineWidth: 0
  });

  // 监听元素变化
  $effect(() => {
    console.log('测试元素更新:', testElement);
  });
</script>

<div class="test-container">
  <h2>TextElementPanel 数据绑定测试</h2>
  
  <div class="test-layout">
    <!-- 左侧：TextElementPanel -->
    <div class="panel-section">
      <h3>文本元素面板</h3>
      <div class="panel-wrapper">
        <TextElementPanel element={testElement} />
      </div>
    </div>

    <!-- 右侧：当前元素状态 -->
    <div class="status-section">
      <h3>当前元素状态</h3>
      <div class="status-content">
        <div class="status-item">
          <strong>文本内容:</strong>
          <span class="text-content">{testElement.text}</span>
        </div>
        <div class="status-item">
          <strong>位置:</strong>
          <span>X: {testElement.x}, Y: {testElement.y}</span>
        </div>
        <div class="status-item">
          <strong>尺寸:</strong>
          <span>宽度: {testElement.maxWidth}, 行高: {testElement.lineHeight}</span>
        </div>
        <div class="status-item">
          <strong>对齐:</strong>
          <span>{testElement.align}</span>
        </div>
      </div>

      <!-- JSON 显示 -->
      <div class="json-display">
        <h4>完整JSON:</h4>
        <pre>{JSON.stringify(testElement, null, 2)}</pre>
      </div>
    </div>
  </div>

  <!-- 使用说明 -->
  <div class="instructions">
    <h3>测试说明</h3>
    <ul>
      <li>使用"数据绑定"下拉框选择RPG Maker MZ的数据字段</li>
      <li>点击"应用"按钮将选择的字段绑定到文本内容</li>
      <li>使用"快速绑定"按钮快速插入常用的数据绑定</li>
      <li>右侧会实时显示当前元素的状态变化</li>
      <li>数据绑定格式: <code>{{dataType.fieldPath}}</code></li>
    </ul>
  </div>
</div>

<style>
  .test-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .test-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
  }

  .panel-section, .status-section {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
  }

  .panel-section h3, .status-section h3 {
    margin: 0;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    font-size: 14px;
    font-weight: 600;
  }

  .panel-wrapper {
    padding: 16px;
    background: white;
  }

  .status-content {
    padding: 16px;
    background: white;
  }

  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .status-item:last-child {
    border-bottom: none;
  }

  .status-item strong {
    color: #333;
    font-weight: 500;
  }

  .text-content {
    font-family: monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .json-display {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
  }

  .json-display h4 {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: #666;
  }

  .json-display pre {
    margin: 0;
    padding: 12px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 11px;
    overflow-x: auto;
    max-height: 300px;
    overflow-y: auto;
  }

  .instructions {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
  }

  .instructions h3 {
    margin-top: 0;
    color: #333;
  }

  .instructions ul {
    margin: 0;
    padding-left: 20px;
  }

  .instructions li {
    margin-bottom: 8px;
    line-height: 1.5;
  }

  .instructions code {
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
  }

  @media (max-width: 768px) {
    .test-layout {
      grid-template-columns: 1fr;
    }
  }
</style>
