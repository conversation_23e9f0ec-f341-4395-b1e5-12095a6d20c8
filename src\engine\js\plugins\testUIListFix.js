/**
 * 测试UIList修复后的功能
 * 验证重构后的UIList是否能正常工作
 */

(() => {
    'use strict';

    console.log('🧪 开始加载UIList修复测试脚本');

    // 等待所有插件加载完成
    setTimeout(() => {
        console.log('🧪 开始UIList修复测试');

        // 添加测试函数到全局
        window.testUIListFix = function() {
            console.log('🧪 执行UIList修复测试');

            // 检查必要的类是否存在
            if (typeof UIList === 'undefined') {
                console.error('❌ UIList类未找到');
                return;
            }

            if (typeof UILayout === 'undefined') {
                console.error('❌ UILayout类未找到');
                return;
            }

            // 获取当前场景
            const scene = SceneManager._scene;
            if (!scene) {
                console.error('❌ 无法获取当前场景');
                return;
            }

            console.log('✅ 开始创建UIList修复测试');

            try {
                // 创建简单的测试UIList
                const testList = new UIList({
                    x: 100,
                    y: 100,
                    width: 250,
                    height: 300,
                    layoutType: 'vertical',
                    itemSpacing: 8
                });
                testList.name = 'TestFixedList';

                // 设置简单的测试数据
                const testData = [
                    { id: 1, title: '测试项目1' },
                    { id: 2, title: '测试项目2' },
                    { id: 3, title: '测试项目3' }
                ];

                console.log('📊 设置测试数据:', testData);
                testList.setDataSource(testData);

                // 添加到场景
                scene.addChild(testList);

                console.log('✅ UIList修复测试创建成功');
                console.log('📍 测试列表位置: (100, 100)');
                console.log('🔍 检查UIList结构:');
                console.log('  - UIList子对象:', testList.children.map(child => child.constructor.name));
                console.log('  - Layout对象:', testList.layout?.constructor.name);
                console.log('  - Layout子对象数量:', testList.layout?.children.length);

                // 返回测试对象供进一步调试
                return {
                    testList,
                    testData
                };

            } catch (error) {
                console.error('❌ UIList修复测试失败:', error);
                console.error('错误堆栈:', error.stack);
                return null;
            }
        };

        // 添加布局切换测试
        window.testLayoutSwitchFix = function() {
            console.log('🧪 测试修复后的布局切换');
            
            const testObjects = window.testUIListFix();
            if (testObjects) {
                const { testList } = testObjects;
                
                const layoutTypes = ['vertical', 'horizontal', 'grid'];
                let currentIndex = 0;
                
                console.log('🔄 开始布局切换测试');
                
                // 每2秒切换一次布局
                const switchInterval = setInterval(() => {
                    currentIndex = (currentIndex + 1) % layoutTypes.length;
                    const newLayoutType = layoutTypes[currentIndex];
                    
                    console.log(`🔄 切换到 ${newLayoutType} 布局`);
                    
                    try {
                        testList.setLayoutType(newLayoutType);
                        console.log('✅ 布局切换成功');
                    } catch (error) {
                        console.error('❌ 布局切换失败:', error);
                    }
                    
                    if (currentIndex === 0) { // 回到起始状态后停止
                        clearInterval(switchInterval);
                        console.log('✅ 布局切换测试完成');
                    }
                }, 2000);
            }
        };

        // 添加错误检查函数
        window.checkUIListErrors = function() {
            console.log('🔍 检查UIList可能的错误');
            
            const testObjects = window.testUIListFix();
            if (testObjects) {
                const { testList } = testObjects;
                
                console.log('🔍 UIList错误检查:');
                
                // 检查关键方法是否存在
                const methods = [
                    'updateItemContainerMask',
                    'setLayoutType',
                    'setItemSpacing',
                    'refreshItems',
                    'updateLayout'
                ];
                
                methods.forEach(methodName => {
                    const hasMethod = typeof testList[methodName] === 'function';
                    console.log(`  - ${methodName}: ${hasMethod ? '✅' : '❌'}`);
                });
                
                // 检查关键属性
                const properties = [
                    'layout',
                    'layoutType',
                    'itemSpacing',
                    'itemInstances'
                ];
                
                properties.forEach(propName => {
                    const hasProperty = testList[propName] !== undefined;
                    const value = testList[propName];
                    console.log(`  - ${propName}: ${hasProperty ? '✅' : '❌'} (${typeof value})`);
                });
                
                // 检查layout对象
                if (testList.layout) {
                    console.log('🔍 Layout对象检查:');
                    console.log('  - 类型:', testList.layout.constructor.name);
                    console.log('  - 子对象数量:', testList.layout.children.length);
                    console.log('  - 布局类型:', testList.layout.layoutType);
                    console.log('  - 有updateLayout方法:', typeof testList.layout.updateLayout === 'function');
                    console.log('  - 有setLayoutType方法:', typeof testList.layout.setLayoutType === 'function');
                }
                
                console.log('✅ UIList错误检查完成');
            }
        };

        console.log('✅ UIList修复测试脚本加载完成');
        console.log('💡 使用方法:');
        console.log('  - testUIListFix() - 基础修复测试');
        console.log('  - testLayoutSwitchFix() - 布局切换测试');
        console.log('  - checkUIListErrors() - 错误检查');

    }, 4000); // 延迟4秒确保所有插件加载完成

})();
