import { BaseObjectModel } from '../baseObjectModel.svelte';

export class LabelModel extends BaseObjectModel {

    constructor(label: any) {
        super(label);

        // 初始化文本特有属性
        this.width = label.width || 200;
        this.height = label.height || 40;
        this.text = label.text || 'Label Text';
        this.prefix = label.prefix || '';
        this.suffix = label.suffix || '';
        this.fontSize = label.fontSize || 16;
        this.fontFace = label.fontFace || 'GameFont';
        this.fontBold = label.fontBold || false;
        this.fontItalic = label.fontItalic || false;
        this.textColor = label.textColor || '#ffffff';
        this.outlineColor = label.outlineColor || '#000000';
        this.outlineWidth = label.outlineWidth || 4;
        this.textAlign = label.textAlign || 'center';
        this.verticalAlign = label.verticalAlign || 'middle';
        this.backgroundColor = label.backgroundColor || 'transparent';
        this.backgroundOpacity = label.backgroundOpacity || 1;

        console.log('🔧 LabelModel: 创建文本模型', label);

        // setupSync() 已经在基类构造函数中调用了
    }



    // 文本内容属性
    text = $state('Label Text');        // 文本内容
    prefix = $state('');                // 前缀
    suffix = $state('');                // 后缀

    // 字体属性
    fontSize = $state(16);              // 字体大小
    fontFace = $state('GameFont');      // 字体名称
    fontBold = $state(false);           // 是否粗体
    fontItalic = $state(false);         // 是否斜体

    // 颜色属性
    textColor = $state('#ffffff');      // 文本颜色
    outlineColor = $state('#000000'); // 描边颜色
    outlineWidth = $state(4);           // 描边宽度

    // 对齐属性
    textAlign = $state('center');       // 水平对齐: left, center, right
    verticalAlign = $state('middle');   // 垂直对齐: top, middle, bottom

    // 背景属性
    backgroundColor = $state('transparent'); // 背景颜色
    backgroundOpacity = $state(1);      // 背景透明度

    /**
     * 设置Label特有属性同步（重写基类方法）
     * 基类已经处理了所有基础属性，这里只处理Label特有的属性
     */
    protected setupSpecificSync(): void {
        // 同步文本特有属性
        this._originalObject.labelWidth = this.width;
        this._originalObject.labelHeight = this.height;

        // 同步文本内容（如果有变化则重新绘制）
        if (this._originalObject.text !== this.text) {
            this._originalObject.text = this.text;
            if (this._originalObject.setText && typeof this._originalObject.setText === 'function') {
                this._originalObject.setText(this.text);
            }
        }

        // 同步前缀和后缀
        if (this._originalObject.prefix !== this.prefix) {
            this._originalObject.prefix = this.prefix;
            if (this._originalObject.setPrefix && typeof this._originalObject.setPrefix === 'function') {
                this._originalObject.setPrefix(this.prefix);
            }
        }

        if (this._originalObject.suffix !== this.suffix) {
            this._originalObject.suffix = this.suffix;
            if (this._originalObject.setSuffix && typeof this._originalObject.setSuffix === 'function') {
                this._originalObject.setSuffix(this.suffix);
            }
        }

        // 同步字体属性
        if (this._originalObject.fontSize !== this.fontSize) {
            this._originalObject.fontSize = this.fontSize;
            if (this._originalObject.setFontSize && typeof this._originalObject.setFontSize === 'function') {
                this._originalObject.setFontSize(this.fontSize);
            }
        }

        this._originalObject.fontFace = this.fontFace;
        this._originalObject.fontBold = this.fontBold;
        this._originalObject.fontItalic = this.fontItalic;

        // 同步颜色属性
        if (this._originalObject.textColor !== this.textColor) {
            this._originalObject.textColor = this.textColor;
            if (this._originalObject.setTextColor && typeof this._originalObject.setTextColor === 'function') {
                this._originalObject.setTextColor(this.textColor);
            }
        }

        if (this._originalObject.outlineColor !== this.outlineColor) {
            this._originalObject.outlineColor = this.outlineColor;
            if (this._originalObject.setOutlineColor && typeof this._originalObject.setOutlineColor === 'function') {
                this._originalObject.setOutlineColor(this.outlineColor);
            }
        }

        if (this._originalObject.outlineWidth !== this.outlineWidth) {
            this._originalObject.outlineWidth = this.outlineWidth;
            if (this._originalObject.bitmap) {
                this._originalObject.bitmap.outlineWidth = this.outlineWidth;
            }
            if (this._originalObject.setOutlineWidth && typeof this._originalObject.setOutlineWidth === 'function') {
                this._originalObject.setOutlineWidth(this.outlineWidth);
            }
        }

        // 同步对齐属性
        if (this._originalObject.textAlign !== this.textAlign) {
            this._originalObject.textAlign = this.textAlign;
            if (this._originalObject.setTextAlign && typeof this._originalObject.setTextAlign === 'function') {
                this._originalObject.setTextAlign(this.textAlign);
            }
        }

        if (this._originalObject.verticalAlign !== this.verticalAlign) {
            this._originalObject.verticalAlign = this.verticalAlign;
            if (this._originalObject.setVerticalAlign && typeof this._originalObject.setVerticalAlign === 'function') {
                this._originalObject.setVerticalAlign(this.verticalAlign);
            }
        }

        // 同步背景属性
        if (this._originalObject.backgroundColor !== this.backgroundColor) {
            this._originalObject.backgroundColor = this.backgroundColor;
            if (this._originalObject.setBackgroundColor && typeof this._originalObject.setBackgroundColor === 'function') {
                this._originalObject.setBackgroundColor(this.backgroundColor);
            }
        }

        this._originalObject.backgroundOpacity = this.backgroundOpacity;

        // 同步尺寸（如果有变化则重新设置）
        if (this._originalObject.setSize && typeof this._originalObject.setSize === 'function') {
            this._originalObject.setSize(this.width, this.height);
        }
    }

    /**
     * 设置文本内容
     */
    public setText(text: string): void {
        this.text = text;
    }

    /**
     * 设置字体大小
     */
    public setFontSize(size: number): void {
        this.fontSize = size;
    }

    /**
     * 设置文本颜色
     */
    public setTextColor(color: string): void {
        this.textColor = color;
    }

    /**
     * 设置描边
     */
    public setOutline(color: string, width: number): void {
        this.outlineColor = color;
        this.outlineWidth = width;
    }

    /**
     * 设置对齐方式
     */
    public setAlignment(horizontal: string, vertical: string): void {
        this.textAlign = horizontal;
        this.verticalAlign = vertical;
    }

    /**
     * 设置文本区域尺寸
     */
    public setLabelSize(width: number, height: number): void {
        this.width = width;
        this.height = height;
    }

    /**
     * 设置背景
     */
    public setBackground(color: string, opacity: number = 1): void {
        this.backgroundColor = color;
        this.backgroundOpacity = opacity;
    }

    /**
     * 获取文本信息
     */
    public getTextInfo(): {
        text: string;
        prefix: string;
        suffix: string;
        font: { face: string; size: number; bold: boolean; italic: boolean };
        colors: { text: string; outline: string; background: string };
        alignment: { horizontal: string; vertical: string };
        size: { width: number; height: number };
    } {
        return {
            text: this.text,
            prefix: this.prefix,
            suffix: this.suffix,
            font: {
                face: this.fontFace,
                size: this.fontSize,
                bold: this.fontBold,
                italic: this.fontItalic
            },
            colors: {
                text: this.textColor,
                outline: this.outlineColor,
                background: this.backgroundColor
            },
            alignment: {
                horizontal: this.textAlign,
                vertical: this.verticalAlign
            },
            size: {
                width: this.width,
                height: this.height
            }
        };
    }

    /**
     * 重写对象创建代码生成（模板方法模式）
     * @param varName 变量名
     * @param indent 缩进字符串
     * @returns 对象创建代码
     */
    protected generateObjectCreation(varName: string, indent: string): string {
        const codes: string[] = [];

        // 创建UILabel对象（不包含宽高，统一在外面设置）
        codes.push(`${indent}const ${varName} = new UILabel({`);

        // 处理文本内容 - 如果是表达式，去掉{{}}直接作为代码
        if (this.text && this.text.startsWith('{{') && this.text.endsWith('}}')) {
            // 是表达式，去掉{{}}，不加引号
            const expression = this.text.slice(2, -2);
            codes.push(`${indent}    text: ${expression},`);
        } else {
            // 普通文本，加引号
            codes.push(`${indent}    text: '${this.text.replace(/'/g, "\\'")}',`);
        }

        codes.push(`${indent}    prefix: '${this.prefix.replace(/'/g, "\\'")}',`);
        codes.push(`${indent}    suffix: '${this.suffix.replace(/'/g, "\\'")}',`);
        codes.push(`${indent}    fontSize: ${this.fontSize},`);
        codes.push(`${indent}    fontFace: '${this.fontFace}',`);
        codes.push(`${indent}    fontBold: ${this.fontBold},`);
        codes.push(`${indent}    fontItalic: ${this.fontItalic},`);
        codes.push(`${indent}    textColor: '${this.textColor}',`);
        codes.push(`${indent}    outlineColor: '${this.outlineColor}',`);
        codes.push(`${indent}    outlineWidth: ${this.outlineWidth},`);
        codes.push(`${indent}    textAlign: '${this.textAlign}',`);
        codes.push(`${indent}    verticalAlign: '${this.verticalAlign}',`);

        if (this.backgroundColor !== 'transparent') {
            codes.push(`${indent}    backgroundColor: '${this.backgroundColor}',`);
            codes.push(`${indent}    backgroundOpacity: ${this.backgroundOpacity}`);
        }

        codes.push(`${indent}});`);

        return codes.join('\n');
    }

    /**
     * 克隆当前标签对象 - 调用插件的 clone 方法
     */
    clone(): LabelModel {
        console.log('🔄 LabelModel: 开始克隆标签对象（调用插件方法）');

        // 1. 调用原始 UILabel 对象的 clone 方法
        const originalUILabel = this.getOriginalObject();
        if (!originalUILabel || typeof originalUILabel.clone !== 'function') {
            console.error('❌ LabelModel: 原始对象没有 clone 方法');
            throw new Error('UILabel 对象缺少 clone 方法');
        }

        // 2. 使用插件的 clone 方法克隆原始对象
        const clonedUILabel = originalUILabel.clone({
            offsetPosition: true,
            offsetX: 20,
            offsetY: 20
        });

        // 3. 创建新的 LabelModel 包装克隆的对象
        const clonedModel = new LabelModel(clonedUILabel);

        // 4. 克隆子对象的模型
        const clonedChildrenModels: any[] = [];
        for (let i = 0; i < this.children.length; i++) {
            const childModel = this.children[i];
            if (typeof childModel.clone === 'function') {
                const clonedChildModel = childModel.clone();
                clonedChildrenModels.push(clonedChildModel);
                clonedChildModel.parent = clonedModel;
            }
        }

        // 5. 设置克隆模型的子对象
        clonedModel.children = clonedChildrenModels;

        console.log('✅ LabelModel: 克隆完成，包含', clonedChildrenModels.length, '个子对象');
        return clonedModel;
    }

    /**
     * 重写获取构造函数参数方法
     * 保存 UILabel 特有的属性用于快照恢复
     */
    protected getConstructorArgs(): any {
        return {
            // 基础属性
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            visible: this.visible,
            alpha: this.alpha,

            // UILabel 特有属性
            text: this.text,
            prefix: this.prefix,
            suffix: this.suffix,
            fontSize: this.fontSize,
            fontFace: this.fontFace,
            fontBold: this.fontBold,
            fontItalic: this.fontItalic,
            textColor: this.textColor,
            outlineColor: this.outlineColor,
            outlineWidth: this.outlineWidth,
            textAlign: this.textAlign,
            verticalAlign: this.verticalAlign,
            backgroundColor: this.backgroundColor,
            backgroundOpacity: this.backgroundOpacity
        };
    }

}

// 注册LabelModel到基类容器
BaseObjectModel.registerModel('UILabel', LabelModel);
BaseObjectModel.registerModel('Label', LabelModel);
