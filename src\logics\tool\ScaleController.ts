/**
 * 缩放控制器 - 处理对象缩放逻辑
 */

import type {
  ArrowType,
  DragState,
  ToolEventCallbacks,
  ToolConfig,
  RenderContext
} from './types';
import type { BaseObjectModel } from '../../type/baseObjectModel.svelte';
import { CoordinateTransform } from './CoordinateTransform';
import { historyManager } from '../../historyManager';

export class ScaleController {
  private dragState: DragState = {
    isDragging: false,
    dragType: null,
    startX: 0,
    startY: 0,
    startObjectX: 0,
    startObjectY: 0,
    currentX: 0,
    currentY: 0
  };

  // 缩放相关的初始状态
  private startScaleX: number = 1;
  private startScaleY: number = 1;

  private config: ToolConfig;
  private callbacks: ToolEventCallbacks;
  private renderContext: RenderContext | null = null;

  // 历史记录相关
  private historyOperationStarted: boolean = false;
  private enableHistory: boolean = true;

  constructor(config: ToolConfig, callbacks: ToolEventCallbacks = {}) {
    this.config = config;
    this.callbacks = callbacks;
  }

  /**
   * 设置渲染上下文
   */
  setRenderContext(context: RenderContext): void {
    this.renderContext = context;
  }

  /**
   * 开始缩放
   */
  startScale(
    object: BaseObjectModel,
    scaleType: ArrowType,
    mouseX: number,
    mouseY: number
  ): void {
    this.dragState = {
      isDragging: true,
      dragType: scaleType,
      startX: mouseX,
      startY: mouseY,
      startObjectX: object.x,
      startObjectY: object.y,
      currentX: mouseX,
      currentY: mouseY
    };

    // 保存初始缩放值
    this.startScaleX = object.scaleX || 1;
    this.startScaleY = object.scaleY || 1;

    console.log('📏 开始缩放:', {
      object: object.className,
      scaleType,
      startScale: { x: this.startScaleX, y: this.startScaleY }
    });

    // 🎯 开始历史记录操作组
    if (this.enableHistory && historyManager.isRecording()) {
      const operationName = `缩放${object.className}`;
      historyManager.startGroup(operationName);
      this.historyOperationStarted = true;

      console.log("📝 ScaleController: 开始缩放历史记录操作组:", operationName);
    }

    this.callbacks.onDragStart?.(object, scaleType);
  }

  /**
   * 更新缩放 - 优化版本，使用坐标转换和模型驱动更新
   */
  updateScale(
    object: BaseObjectModel,
    mouseX: number,
    mouseY: number
  ): boolean {
    if (!this.dragState.isDragging || !this.dragState.dragType) {
      return false;
    }

    this.dragState.currentX = mouseX;
    this.dragState.currentY = mouseY;

    // 1. 将鼠标坐标转换为模型坐标（如果有渲染上下文）
    let deltaX: number, deltaY: number;

    if (this.renderContext) {
      const currentModelPos = CoordinateTransform.mouseToModel(
        mouseX,
        mouseY,
        this.renderContext
      );

      const startModelPos = CoordinateTransform.mouseToModel(
        this.dragState.startX,
        this.dragState.startY,
        this.renderContext
      );

      deltaX = currentModelPos.x - startModelPos.x;
      deltaY = currentModelPos.y - startModelPos.y;
    } else {
      // 回退到原始计算方式
      deltaX = mouseX - this.dragState.startX;
      deltaY = mouseY - this.dragState.startY;
    }

    // 2. 计算缩放因子（基于移动距离）
    const scaleFactor = 0.01; // 缩放灵敏度
    const scaleChangeX = deltaX * scaleFactor;
    const scaleChangeY = -deltaY * scaleFactor; // Y轴反向

    let newScaleX = this.startScaleX;
    let newScaleY = this.startScaleY;

    // 3. 根据缩放类型计算新的缩放值
    switch (this.dragState.dragType) {
      case 'up':
        // Y轴缩放
        newScaleY = Math.max(0.1, this.startScaleY + scaleChangeY);
        break;
      case 'right':
        // X轴缩放
        newScaleX = Math.max(0.1, this.startScaleX + scaleChangeX);
        break;
      case 'center':
        // 等比缩放
        const uniformScaleChange = (scaleChangeX + scaleChangeY) / 2;
        newScaleX = Math.max(0.1, this.startScaleX + uniformScaleChange);
        newScaleY = Math.max(0.1, this.startScaleY + uniformScaleChange);
        break;
    }

    // 4. 应用缩放步长
    if (this.config.snapToGrid) {
      const step = 0.1; // 缩放步长
      newScaleX = Math.round(newScaleX / step) * step;
      newScaleY = Math.round(newScaleY / step) * step;
    }

    // 5. 只修改模型属性，让响应式系统处理渲染更新
    object.scaleX = newScaleX;
    object.scaleY = newScaleY;

    console.log('📏 缩放更新 (模型驱动):', {
      object: object.className,
      mouseDelta: { x: deltaX, y: deltaY },
      scaleChange: { x: scaleChangeX, y: scaleChangeY },
      newScale: { x: newScaleX, y: newScaleY },
      useCoordinateTransform: !!this.renderContext
    });

    // 6. 通知回调（传递缩放变化量）
    this.callbacks.onDragMove?.(object, scaleChangeX, scaleChangeY);
    return true;
  }

  /**
   * 结束缩放
   */
  endScale(object: BaseObjectModel): void {
    if (!this.dragState.isDragging) {
      return;
    }

    console.log('📏 结束缩放:', {
      object: object.className,
      finalScale: { x: object.scaleX, y: object.scaleY },
      startScale: { x: this.startScaleX, y: this.startScaleY }
    });

    // 🎯 处理历史记录
    if (this.historyOperationStarted && this.enableHistory && historyManager.isRecording()) {
      const startScaleX = this.startScaleX;
      const startScaleY = this.startScaleY;
      const finalScaleX = object.scaleX || 1;
      const finalScaleY = object.scaleY || 1;

      // 检查缩放是否真的发生了变化
      const hasScaleChanged = (startScaleX !== finalScaleX) || (startScaleY !== finalScaleY);

      if (hasScaleChanged) {
        // 记录X轴缩放变化
        if (startScaleX !== finalScaleX) {
          historyManager.recordChange(object, 'scaleX', startScaleX, finalScaleX);
          console.log("📝 ScaleController: 已记录X轴缩放变更", { from: startScaleX, to: finalScaleX });
        }

        // 记录Y轴缩放变化
        if (startScaleY !== finalScaleY) {
          historyManager.recordChange(object, 'scaleY', startScaleY, finalScaleY);
          console.log("📝 ScaleController: 已记录Y轴缩放变更", { from: startScaleY, to: finalScaleY });
        }
      } else {
        console.log("📝 ScaleController: 缩放未变化，跳过历史记录");
      }

      // 结束操作组
      historyManager.endGroup();
      console.log("📝 ScaleController: 缩放历史记录操作组已结束");
    }

    this.callbacks.onDragEnd?.(object, object.scaleX || 1, object.scaleY || 1);

    // 重置状态
    this.dragState = {
      isDragging: false,
      dragType: null,
      startX: 0,
      startY: 0,
      startObjectX: 0,
      startObjectY: 0,
      currentX: 0,
      currentY: 0
    };

    // 重置历史记录状态
    this.historyOperationStarted = false;
  }

  /**
   * 取消缩放（恢复到原始缩放）
   */
  cancelScale(object: BaseObjectModel): void {
    if (!this.dragState.isDragging) {
      return;
    }

    // 恢复到开始缩放时的值
    object.scaleX = this.startScaleX;
    object.scaleY = this.startScaleY;

    console.log('📏 取消缩放:', {
      object: object.className,
      restoredScale: { x: this.startScaleX, y: this.startScaleY }
    });

    // 🎯 取消缩放时不记录历史，直接结束操作组
    if (this.historyOperationStarted && this.enableHistory && historyManager.isRecording()) {
      historyManager.endGroup();
      console.log("📝 ScaleController: 取消缩放，结束历史记录操作组（不记录变更）");
    }

    // 重置历史记录状态
    this.historyOperationStarted = false;

    // 调用回调
    this.callbacks.onDragEnd?.(object, object.scaleX || 1, object.scaleY || 1);

    // 重置拖动状态
    this.dragState = {
      isDragging: false,
      dragType: null,
      startX: 0,
      startY: 0,
      startObjectX: 0,
      startObjectY: 0,
      currentX: 0,
      currentY: 0
    };
  }

  /**
   * 获取当前拖动状态
   */
  getDragState(): DragState {
    return { ...this.dragState };
  }

  /**
   * 是否正在缩放
   */
  isScaling(): boolean {
    return this.dragState.isDragging;
  }

  /**
   * 获取当前缩放类型
   */
  getCurrentScaleType(): ArrowType | null {
    return this.dragState.dragType;
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ToolConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 更新回调
   */
  updateCallbacks(callbacks: Partial<ToolEventCallbacks>): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * 启用/禁用历史记录
   */
  setHistoryEnabled(enabled: boolean): void {
    this.enableHistory = enabled;
    console.log(`📏 ScaleController: 历史记录${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 获取历史记录启用状态
   */
  isHistoryEnabled(): boolean {
    return this.enableHistory;
  }

  /**
   * 获取当前是否有进行中的历史记录操作
   */
  isHistoryOperationActive(): boolean {
    return this.historyOperationStarted;
  }

  /**
   * 计算缩放预览（不实际修改对象）
   */
  calculateScalePreview(
    object: BaseObjectModel,
    mouseX: number,
    mouseY: number
  ): { scaleX: number; scaleY: number } | null {
    if (!this.dragState.isDragging || !this.dragState.dragType) {
      return null;
    }

    const deltaX = mouseX - this.dragState.startX;
    const deltaY = mouseY - this.dragState.startY;

    const scaleFactor = 0.01;
    const scaleChangeX = deltaX * scaleFactor;
    const scaleChangeY = -deltaY * scaleFactor;

    let newScaleX = this.startScaleX;
    let newScaleY = this.startScaleY;

    switch (this.dragState.dragType) {
      case 'up':
        newScaleY = Math.max(0.1, this.startScaleY + scaleChangeY);
        break;
      case 'right':
        newScaleX = Math.max(0.1, this.startScaleX + scaleChangeX);
        break;
      case 'center':
        const uniformScaleChange = (scaleChangeX + scaleChangeY) / 2;
        newScaleX = Math.max(0.1, this.startScaleX + uniformScaleChange);
        newScaleY = Math.max(0.1, this.startScaleY + uniformScaleChange);
        break;
    }

    if (this.config.snapToGrid) {
      const step = 0.1;
      newScaleX = Math.round(newScaleX / step) * step;
      newScaleY = Math.round(newScaleY / step) * step;
    }

    return { scaleX: newScaleX, scaleY: newScaleY };
  }

  /**
   * 重置缩放到默认值
   */
  resetScale(object: BaseObjectModel): void {
    if (this.enableHistory && historyManager.isRecording()) {
      const oldScaleX = object.scaleX || 1;
      const oldScaleY = object.scaleY || 1;

      // 只有当缩放值不是默认值时才记录历史
      if (oldScaleX !== 1 || oldScaleY !== 1) {
        historyManager.startGroup(`重置${object.className}缩放`);

        if (oldScaleX !== 1) {
          historyManager.recordChange(object, 'scaleX', oldScaleX, 1);
        }
        if (oldScaleY !== 1) {
          historyManager.recordChange(object, 'scaleY', oldScaleY, 1);
        }

        historyManager.endGroup();
        console.log("📝 ScaleController: 已记录重置缩放操作");
      }
    }

    object.scaleX = 1;
    object.scaleY = 1;
    console.log('📏 重置缩放:', object.className);
  }
}
