/**
 * RPG Maker引擎加载器
 * 基于LabelSpriteEditorDemo的引擎加载逻辑
 */

// 引擎初始化状态
let isEngineInitialized = false;
let initializationPromise: Promise<void> | null = null;

// 获取项目配置
function getProjectConfig() {
  return (window as any).PROJECT_CONFIG || null;
}

// 需要加载的核心脚本文件（从项目中加载）
const coreScriptPaths = [
  'js/libs/pixi.js',
  'js/rmmz_core.js',
  'js/rmmz_sprites.js',
  'js/rmmz_managers.js'
];

// 本地插件文件（从engine/js/plugins加载）
const localPluginPath = "src/engine/js/plugins/RPGEditor_BitmapTracker.js";

/**
 * 动态加载脚本文件
 */
function loadScript(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = url;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`Failed to load script: ${url}`));
    document.head.appendChild(script);
  });
}

/**
 * 使用Tauri API加载项目脚本文件
 */
async function loadProjectScript(scriptPath: string): Promise<void> {
  const projectConfig = getProjectConfig();
  if (!projectConfig || !projectConfig.scripts) {
    throw new Error('没有找到项目配置或脚本列表');
  }

  // 查找对应的脚本
  const script = projectConfig.scripts.find((s: any) =>
    s.local_path.replace(/\\/g, '/') === scriptPath
  );

  if (!script) {
    throw new Error(`脚本未找到: ${scriptPath}`);
  }

  console.log('加载项目脚本:', scriptPath);

  // 动态导入 Tauri API
  const { TauriAPI } = await import('../../../lib/tauriAPI');

  // 从 file:// URL 提取文件路径
  const filePath = script.url.replace('file://', '');

  // 使用 Tauri API 读取脚本内容
  const result = await TauriAPI.Project.readProjectFile(filePath);

  if (result.success && result.data) {
    // 创建脚本元素并设置内容
    const scriptElement = document.createElement("script");
    scriptElement.type = "text/javascript";
    scriptElement.textContent = result.data;

    // 添加到页面并执行
    document.body.appendChild(scriptElement);

    console.log('✓ 项目脚本加载成功:', scriptPath);
  } else {
    console.error('✗ 项目脚本加载失败:', scriptPath, result.error);
    throw new Error(`项目脚本加载失败: ${scriptPath}`);
  }
}

/**
 * 加载所有必需的脚本文件
 */
async function loadAllScripts(): Promise<void> {
  console.log('开始加载RPG Maker引擎脚本...');

  // 1. 加载项目中的核心脚本
  for (const scriptPath of coreScriptPaths) {
    try {
      await loadProjectScript(scriptPath);
      console.log(`已加载项目脚本: ${scriptPath}`);
    } catch (error) {
      console.error(`加载项目脚本失败: ${scriptPath}`, error);
      throw error;
    }
  }

  // 2. 加载本地插件
  try {
    await loadScript(localPluginPath);
    console.log(`已加载本地插件: ${localPluginPath}`);
  } catch (error) {
    console.error(`加载本地插件失败: ${localPluginPath}`, error);
    throw error;
  }

  console.log('所有脚本加载完成');
}

/**
 * 初始化RPG Maker引擎
 */
function initializeRpgMaker(): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      // 检查必要的对象是否都已定义
      if (typeof window.Graphics === 'undefined' ||
          typeof window.Bitmap === 'undefined' ||
          typeof window.Sprite === 'undefined') {
        reject(new Error('RPG Maker引擎对象未正确加载'));
        return;
      }

      // 初始化Graphics
      if (!window.Graphics._app) {
        // 创建一个隐藏的canvas用于引擎初始化
        const canvas = document.createElement('canvas');
        canvas.width = 816;
        canvas.height = 624;
        canvas.style.display = 'none';
        document.body.appendChild(canvas);

        // 初始化Graphics
        window.Graphics.initialize(canvas.width, canvas.height);
        window.Graphics._canvas = canvas;
      }

      console.log('RPG Maker引擎初始化完成');
      resolve();
    } catch (error) {
      console.error('RPG Maker引擎初始化失败:', error);
      reject(error);
    }
  });
}

/**
 * 确保引擎已初始化
 */
export async function ensureEngineInitialized(): Promise<void> {
  if (isEngineInitialized) {
    return;
  }

  if (initializationPromise) {
    return initializationPromise;
  }

  initializationPromise = (async () => {
    try {
      // 加载脚本
      await loadAllScripts();

      // 等待一小段时间确保脚本执行完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 初始化引擎
      await initializeRpgMaker();

      isEngineInitialized = true;
      console.log('RPG Maker引擎完全初始化完成');
    } catch (error) {
      console.error('引擎初始化过程失败:', error);
      initializationPromise = null;
      throw error;
    }
  })();

  return initializationPromise;
}

/**
 * 检查引擎是否已初始化
 */
export function isEngineReady(): boolean {
  return isEngineInitialized &&
         typeof window.Graphics !== 'undefined' &&
         typeof window.Bitmap !== 'undefined' &&
         typeof window.Sprite !== 'undefined';
}

/**
 * 获取引擎对象
 */
export function getEngineObjects() {
  if (!isEngineReady()) {
    throw new Error('引擎尚未初始化');
  }

  return {
    Graphics: window.Graphics,
    Bitmap: window.Bitmap,
    Sprite: window.Sprite,
    ImageManager: window.ImageManager
  };
}

/**
 * 重置引擎状态（用于测试或重新初始化）
 */
export function resetEngine(): void {
  isEngineInitialized = false;
  initializationPromise = null;

  // 清理全局对象（如果需要）
  if (typeof window.SpriteEditor !== 'undefined') {
    delete window.SpriteEditor;
  }
}

// 扩展window类型以支持RPG Maker对象
declare global {
  interface Window {
    Graphics: any;
    Bitmap: any;
    Sprite: any;
    ImageManager: any;
    SpriteEditor: any;
  }
}
