/*:
 * @target MZ
 * @plugindesc UIAtlas v2.0.0
 * <AUTHOR> Editor
 * @version 2.0.0
 * @description 智能图集容器 - 支持子对象编辑和图集合并
 *
 * @help UIAtlas.js
 *
 * 这个插件提供了UIAtlas类，作为智能容器支持：
 * 1. 编辑模式：直接编辑子对象（UIImage、UILabel）
 * 2. 合并模式：将子对象合并到单一纹理，优化性能
 * 3. 解开合并：可以随时从合并模式切换回编辑模式
 *
 * 限制：
 * - 只接受UIImage和UILabel类型的子对象
 * - 只支持一层嵌套：子对象不能再包含子对象
 * - 添加到UIAtlas的子对象会被禁用嵌套功能
 *
 * 使用方法：
 * const atlas = new UIAtlas({
 *     width: 512,
 *     height: 512,
 *     backgroundColor: 'transparent'
 * });
 *
 * // 添加子对象（只支持UIImage和UILabel，且不能有嵌套子对象）
 * const image = new UIImage(...);
 * const label = new UILabel(...);
 * atlas.addChild(image);  // ✅ 支持
 * atlas.addChild(label);  // ✅ 支持
 *
 * // ❌ 不支持的操作：
 * // atlas.addChild(sprite);           // 不支持其他类型
 * // atlas.addChild(containerWithChildren); // 不支持有嵌套子对象的容器
 * // image.addChild(nestedSprite);     // 添加到UIAtlas后，子对象被禁用嵌套功能
 *
 * // 合并到单一纹理（性能优化）
 * await atlas.switchToAtlasMode();
 *
 * // 解开合并，恢复编辑
 * atlas.switchToEditMode();
 *
 * // 完全解开合并，移除合并纹理
 * atlas.unmerge();
 *
 * // 便捷方法
 * await atlas.toggleMode();        // 切换模式
 * console.log(atlas.getStatus());  // 获取状态信息
 */

(() => {
    'use strict';

    // 确保PIXI可用
    if (typeof PIXI === 'undefined') {
        console.error('🎨 UIAtlas: PIXI未定义，插件加载失败');
        return;
    }

    /**
     * UIAtlas类 - 智能图集容器
     * 继承自Container，可以在编辑模式（显示子对象）和合并模式（单一纹理）之间切换
     */
    class UIAtlas extends PIXI.Container {
        constructor() {
            // 初始化为Container
            super();

            // 设置默认尺寸（使用自定义属性，避免影响子对象缩放）
            this._atlasWidth = 512;
            this._atlasHeight = 512;

            // 合并模式下的Sprite（用于显示合并纹理）
            this._atlasSprite = null;

            // 模式控制
            this._isAtlasMode = false;  // false: 编辑模式, true: 合并模式

            // 子对象管理（内部数组，不使用PIXI的children）
            this._children = [];        // 内部子对象数组
            this._boundingBox = null;   // 编辑模式的边界框

            // 渲染相关
            this._canvas = null;
            this._context = null;

            // 创建边界框（用于编辑时的视觉提示）
             this._createBoundingBox();

            console.log('🎨 UIAtlas: 创建智能图集容器', {
                width: this._atlasWidth,
                height: this._atlasHeight,
                mode: 'edit'
            });
        }

        /**
         * 获取图集宽度
         */
        get width() {
            return this._atlasWidth;
        }

        /**
         * 设置图集宽度
         */
        set width(value) {
            this._atlasWidth = value;
            this._updateBoundingBox();
        }

        /**
         * 获取图集高度
         */
        get height() {
            return this._atlasHeight;
        }

        /**
         * 设置图集高度
         */
        set height(value) {
            this._atlasHeight = value;
            this._updateBoundingBox();
        }

        /**
         * 更新边界框尺寸
         * @private
         */
        _updateBoundingBox() {
            if (this._boundingBox) {
                this._boundingBox.clear();
                this._boundingBox.lineStyle(2, 0x00ff00, 0.8);
                this._boundingBox.drawRect(0, 0, this._atlasWidth, this._atlasHeight);
                this._boundingBox.alpha = 0.6;
            }
        }

        /**
         * 创建边界框（编辑模式下的视觉提示）
         * @private
         */
        _createBoundingBox() {
            this._boundingBox = new PIXI.Graphics();
            this._boundingBox.lineStyle(2, 0x00ff00, 0.8); // 绿色边框
            // 使用实际的图集尺寸绘制边界框
            this._boundingBox.drawRect(0, 0, this._atlasWidth, this._atlasHeight);
            this._boundingBox.alpha = 0.6;
            super.addChild(this._boundingBox);
        }

        /**
         * 检查子对象类型是否被允许
         * @param {object} child 子对象
         * @returns {boolean} 是否允许添加
         */
        _isAllowedChildType(child) {
            // 只允许UIImage和UILabel
            const allowedTypes = ['UIImage', 'UILabel'];
            const childType = child.constructor.name;
            const isAllowed = allowedTypes.includes(childType) ||
                             (child.className && allowedTypes.includes(child.className));

            if (!isAllowed) {
                console.warn('🎨 UIAtlas: 不支持的子对象类型', childType, '只支持 UIImage 和 UILabel');
            }

            return isAllowed;
        }

        /**
         * 检查子对象是否有嵌套子对象（UIAtlas只支持一层嵌套）
         * @param {object} child 子对象
         * @returns {boolean} 是否有嵌套子对象
         */
        _hasNestedChildren(child) {
            // 检查子对象是否有children属性且不为空
            if (child.children && child.children.length > 0) {
                console.warn('🎨 UIAtlas: 子对象包含嵌套子对象，UIAtlas只支持一层嵌套', {
                    childType: child.constructor.name,
                    nestedChildrenCount: child.children.length
                });
                return true;
            }
            return false;
        }

        /**
         * 禁用子对象的addChild方法，防止嵌套
         * @param {object} child 子对象
         * @private
         */
        _preventNesting(child) {
            // 保存原始的addChild方法
            if (!child._originalAddChild) {
                child._originalAddChild = child.addChild;

                // 重写addChild方法，禁止添加子对象
                child.addChild = function(nestedChild) {
                    console.warn('🎨 UIAtlas: 禁止在UIAtlas的子对象中添加嵌套子对象', {
                        parentType: this.constructor.name,
                        childType: nestedChild.constructor.name,
                        message: 'UIAtlas只支持一层嵌套，请将子对象直接添加到UIAtlas'
                    });

                    // 可选：抛出错误以强制阻止
                    throw new Error('UIAtlas的子对象不允许添加嵌套子对象，请直接添加到UIAtlas');

                    // 或者返回null表示添加失败
                    // return null;
                };

                console.log('🎨 UIAtlas: 已禁用子对象的嵌套功能', child.constructor.name);
            }
        }

        /**
         * 恢复子对象的addChild方法
         * @param {object} child 子对象
         * @private
         */
        _restoreNesting(child) {
            if (child._originalAddChild) {
                child.addChild = child._originalAddChild;
                delete child._originalAddChild;
                console.log('🎨 UIAtlas: 已恢复子对象的嵌套功能', child.constructor.name);
            }
        }

        /**
         * 自定义addChild方法，管理内部子对象数组
         * @param {object} child 要添加的子对象
         * @returns {object} 添加的子对象
         */
        addChild(child) {
            // 内部对象（边界框）直接添加到显示列表
            if (child === this._boundingBox) {
                return super.addChild(child);
            }

            // 检查子对象类型
            if (!this._isAllowedChildType(child)) {
                throw new Error(`UIAtlas只支持UIImage和UILabel类型的子对象，不支持: ${child.constructor.name}`);
            }

            // 检查子对象是否已有嵌套子对象
            if (this._hasNestedChildren(child)) {
                throw new Error(`UIAtlas不支持嵌套子对象，请将子对象直接添加到UIAtlas。当前子对象 ${child.constructor.name} 包含 ${child.children ? child.children.length : 0} 个嵌套子对象。`);
            }

            // 检查是否已经在内部数组中
            if (this._children.includes(child)) {
                console.warn('🎨 UIAtlas: 子对象已存在，跳过添加', child.name || 'unnamed');
                return child;
            }

            // 禁用子对象的嵌套功能
            this._preventNesting(child);

            // 添加到内部子对象数组
            this._children.push(child);

            // 如果是编辑模式，添加到显示列表
            if (!this._isAtlasMode) {
                super.addChild(child);
                // 确保子对象可见
                if (child.visible !== undefined) {
                    child.visible = true;
                }
            }

            console.log('🎨 UIAtlas: 添加子对象', {
                type: child.constructor.name,
                name: child.name || 'unnamed',
                mode: this._isAtlasMode ? 'atlas' : 'edit',
                totalChildren: this._children.length,
                nestingPrevented: true
            });

            return child;
        }

        /**
         * 自定义removeChild方法
         * @param {object} child 要移除的子对象
         * @returns {object} 移除的子对象
         */
        removeChild(child) {
            // 内部对象直接从显示列表移除
            if (child === this._boundingBox) {
                return super.removeChild(child);
            }

            // 从内部数组移除
            const index = this._children.indexOf(child);
            if (index !== -1) {
                this._children.splice(index, 1);

                // 恢复子对象的嵌套功能
                this._restoreNesting(child);
            }

            // 尝试从显示列表移除
            try {
                super.removeChild(child);
            } catch (e) {
                // 忽略移除失败的错误
            }

            console.log('🎨 UIAtlas: 移除子对象', {
                type: child.constructor.name,
                totalChildren: this._children.length,
                nestingRestored: index !== -1
            });

            return child;
        }



        /**
         * 检查是否处于合并模式
         * @returns {boolean} 是否为合并模式
         */
        isAtlasMode() {
            return this._isAtlasMode;
        }



        /**
         * 切换到编辑模式
         * 恢复空纹理，重新显示所有子对象
         * @returns {UIAtlas} 返回自身，支持链式调用
         */
        switchToEditMode() {
            if (!this._isAtlasMode) {
                console.log('🎨 UIAtlas: 已经处于编辑模式');
                return this;
            }

            console.log('🎨 UIAtlas: 解开合并，切换到编辑模式');

            // 移除合并纹理Sprite
            if (this._atlasSprite) {
                super.removeChild(this._atlasSprite);
                this._atlasSprite.destroy();
                this._atlasSprite = null;
            }

            // 重新添加所有子对象到显示列表
            this._children.forEach(child => {
                try {
                    super.addChild(child);
                } catch (e) {
                    // 忽略添加失败的错误（可能已经在列表中）
                }
                child.visible = true;
            });

            // 显示边界框
            if (this._boundingBox) {
                this._boundingBox.visible = true;
            }

            this._isAtlasMode = false;

            console.log('🎨 UIAtlas: 编辑模式已激活，可以编辑子对象', {
                visibleChildren: this._children.length
            });

            return this;
        }



        /**
         * 切换到合并模式
         * 将所有子对象渲染到单一纹理，隐藏原始子对象，显示合并纹理
         * @returns {Promise<UIAtlas>} 返回Promise，支持异步操作
         */
        async switchToAtlasMode() {
            if (this._isAtlasMode) {
                console.log('🎨 UIAtlas: 已经处于合并模式');
                return this;
            }

            console.log('🎨 UIAtlas: 切换到合并模式', {
                childrenCount: this._children.length,
                size: `${this._atlasWidth}x${this._atlasHeight}`
            });

            try {
                // 等待所有 UIImage 加载完成
                await this._waitForImagesLoaded();

                // 创建离屏Canvas
                this._canvas = document.createElement('canvas');
                this._canvas.width = this._atlasWidth;
                this._canvas.height = this._atlasHeight;
                this._context = this._canvas.getContext('2d');

                // 渲染所有子对象到Canvas
                await this._renderChildrenToCanvas();

                // 创建合并纹理并创建Sprite显示
                const dataURL = this._canvas.toDataURL('image/png');
                const texture = PIXI.Texture.from(dataURL);

                // 创建显示合并纹理的Sprite
                this._atlasSprite = new PIXI.Sprite(texture);
                super.addChild(this._atlasSprite);

                // 从显示列表移除所有子对象（但保留在内部数组中）
                this._children.forEach(child => {
                    // 直接尝试移除，如果不在列表中removeChild会忽略
                    try {
                        super.removeChild(child);
                    } catch (e) {
                        // 忽略移除失败的错误
                    }
                    // 标记为不可见，但不销毁
                    child.visible = false;
                });

                // 隐藏边界框
                if (this._boundingBox) {
                    this._boundingBox.visible = false;
                }

                this._isAtlasMode = true;

                console.log('🎨 UIAtlas: 合并模式切换完成，当前Sprite已包含合并纹理');
                return this;

            } catch (error) {
                console.error('🎨 UIAtlas: 切换到合并模式失败', error);
                throw error;
            }
        }

        /**
         * 等待所有 UIImage 加载完成
         * @returns {Promise<void>}
         * @private
         */
        async _waitForImagesLoaded() {
            const imagePromises = [];

            this._children.forEach(child => {
                if (child.constructor.name === 'UIImage' || child.uiComponentType === 'UIImage') {
                    // 检查图片是否已经加载完成
                    if (child.bitmap && child.bitmap.isReady && child.bitmap.isReady()) {
                        console.log('🎨 UIAtlas: UIImage 已加载完成', child.imagePath);
                        return;
                    }

                    // 创建等待图片加载的 Promise
                    const imagePromise = new Promise((resolve) => {
                        if (child.bitmap && typeof child.bitmap.addLoadListener === 'function') {
                            console.log('🎨 UIAtlas: 等待 UIImage 加载', child.imagePath);
                            child.bitmap.addLoadListener(() => {
                                console.log('🎨 UIAtlas: UIImage 加载完成', child.imagePath);
                                resolve();
                            });
                        } else {
                            // 如果没有 bitmap 或 addLoadListener，直接 resolve
                            console.log('🎨 UIAtlas: UIImage 无需等待', child.imagePath);
                            resolve();
                        }
                    });

                    imagePromises.push(imagePromise);
                }
            });

            if (imagePromises.length > 0) {
                console.log(`🎨 UIAtlas: 等待 ${imagePromises.length} 个图片加载完成`);
                await Promise.all(imagePromises);
                console.log('🎨 UIAtlas: 所有图片加载完成');
            } else {
                console.log('🎨 UIAtlas: 没有需要等待的图片');
            }
        }

        /**
         * 渲染所有子对象到Canvas（内部方法）
         * @private
         */
        async _renderChildrenToCanvas() {
            for (const child of this._children) {
                if (child.visible) {
                    await this._renderChildToCanvas(child);
                }
            }
        }

        /**
         * 渲染单个子对象到Canvas（内部方法）
         * @private
         */
        async _renderChildToCanvas(child) {
            if (!child.visible) return;

            try {
                // 根据子对象类型进行不同的渲染处理
                if (child.texture && child.texture.baseTexture) {
                    // 处理有纹理的对象（如Sprite、UIImage等）
                    await this._renderTextureToCanvas(child);
                } else if (child.text !== undefined) {
                    // 处理文字对象（如UILabel等）
                    this._renderTextToCanvas(child);
                } else if (child.children && child.children.length > 0) {
                    // 处理容器对象，递归渲染子对象
                    for (const grandChild of child.children) {
                        await this._renderChildToCanvas(grandChild);
                    }
                }
            } catch (error) {
                console.warn('🎨 UIAtlas: 渲染子对象失败，跳过', child, error);
            }
        }

        /**
         * 渲染纹理对象到Canvas（内部方法）
         * @private
         */
        async _renderTextureToCanvas(child) {
            return new Promise((resolve) => {
                try {
                    // 检查纹理是否有效
                    if (!child.texture || !child.texture.baseTexture) {
                        console.warn('🎨 UIAtlas: 子对象纹理无效', child.constructor.name);
                        resolve();
                        return;
                    }

                    const baseTexture = child.texture.baseTexture;

                    // 如果纹理还没有加载完成，等待加载
                    if (!baseTexture.valid) {
                        console.log('🎨 UIAtlas: 等待纹理加载完成', child.constructor.name);
                        baseTexture.once('loaded', () => {
                            this._renderTextureToCanvas(child).then(resolve);
                        });
                        return;
                    }

                    // 获取纹理源
                    const source = baseTexture.resource?.source;
                    if (!source) {
                        console.warn('🎨 UIAtlas: 纹理源为空', child.constructor.name);
                        resolve();
                        return;
                    }

                    // 检查是否是 UIImage 且有裁切信息
                    if (child.constructor.name === 'UIImage' && child.regions && child.regions.length > 0) {
                        // UIImage 有裁切信息，使用裁切渲染
                        const currentRegion = child.regions[child.currentRegionIndex || 0];
                        if (currentRegion) {
                            // 使用 9 参数的 drawImage 进行裁切渲染
                            this._context.drawImage(
                                source,
                                currentRegion.sx, currentRegion.sy, currentRegion.sw, currentRegion.sh, // 源图片裁切区域
                                child.x, child.y, child.width || currentRegion.sw, child.height || currentRegion.sh // 目标位置和尺寸
                            );

                            console.log('🎨 UIAtlas: UIImage 裁切渲染成功', {
                                type: child.constructor.name,
                                region: currentRegion,
                                sourceRect: `${currentRegion.sx},${currentRegion.sy},${currentRegion.sw},${currentRegion.sh}`,
                                destRect: `${child.x},${child.y},${child.width || currentRegion.sw},${child.height || currentRegion.sh}`
                            });
                        } else {
                            console.warn('🎨 UIAtlas: UIImage 当前区域无效，使用完整图片');
                            // 回退到完整图片渲染
                            this._context.drawImage(
                                source,
                                child.x,
                                child.y,
                                child.width || source.width,
                                child.height || source.height
                            );
                        }
                    } else {
                        // 普通纹理渲染（非 UIImage 或无裁切信息）
                        this._context.drawImage(
                            source,
                            child.x,
                            child.y,
                            child.width || source.width,
                            child.height || source.height
                        );

                        console.log('🎨 UIAtlas: 普通纹理渲染成功', {
                            type: child.constructor.name,
                            x: child.x,
                            y: child.y,
                            width: child.width || source.width,
                            height: child.height || source.height
                        });
                    }

                } catch (error) {
                    console.warn('🎨 UIAtlas: 纹理渲染失败', error);
                }
                resolve();
            });
        }

        /**
         * 渲染文字对象到Canvas（内部方法）
         * @private
         */
        _renderTextToCanvas(child) {
            try {
                // 设置字体样式
                const fontSize = child.fontSize || child.style?.fontSize || 16;
                const fontFamily = child.fontFamily || child.style?.fontFamily || 'GameFont';
                const fontWeight = child.fontWeight || child.style?.fontWeight || 'normal';
                const fillColor = child.textColor || child.style?.fill || '#000000';

                this._context.font = `${fontWeight} ${fontSize}px ${fontFamily}`;
                this._context.fillStyle = fillColor;
                this._context.textAlign = 'left';
                this._context.textBaseline = 'top';

                // 绘制文字
                this._context.fillText(child.text, child.x, child.y);

                console.log('🎨 UIAtlas: 文字对象渲染完成', {
                    text: child.text,
                    position: `${child.x},${child.y}`,
                    fontSize: fontSize
                });

            } catch (error) {
                console.error('🎨 UIAtlas: 文字对象渲染失败', error);
            }
        }

        /**
         * 获取有效子对象数量
         * @returns {number} 有效子对象数量
         */
        getChildrenCount() {
            return this._children.length;
        }



        /**
         * 检查是否可以合并（至少有一个有效子对象）
         * @returns {boolean} 是否可以合并
         */
        canMerge() {
            return this.getChildrenCount() > 0;
        }

        /**
         * 切换模式（编辑模式 ↔ 合并模式）
         * @returns {Promise<UIAtlas>} 返回Promise
         */
        async toggleMode() {
            if (this._isAtlasMode) {
                return this.switchToEditMode();
            } else {
                if (this.canMerge()) {
                    return await this.switchToAtlasMode();
                } else {
                    console.warn('🎨 UIAtlas: 没有子对象可以合并');
                    return this;
                }
            }
        }

        /**
         * 获取当前状态信息
         * @returns {object} 状态信息
         */
        getStatus() {
            return {
                mode: this._isAtlasMode ? 'atlas' : 'edit',
                childrenCount: this.getChildrenCount(),
                canMerge: this.canMerge(),
                hasTexture: this._isAtlasMode && this._atlasSprite !== null,
                size: {
                    width: this._atlasWidth,
                    height: this._atlasHeight
                }
            };
        }

        /**
         * 克隆当前 UIAtlas 对象
         * @param {Object} options 克隆选项
         * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
         * @param {number} options.offsetX 水平偏移量 (默认: 20)
         * @param {number} options.offsetY 垂直偏移量 (默认: 20)
         * @returns {UIAtlas} 克隆的 UIAtlas 对象
         */
        clone(options = {}) {
            console.log('🔄 UIAtlas: 开始克隆对象');

            const {
                offsetPosition = true,
                offsetX = 20,
                offsetY = 20
            } = options;

            // 1. 准备克隆的属性
            const cloneProperties = {
                // 基础属性
                width: this.atlasWidth,
                height: this.atlasHeight,
                visible: this.visible,

                // UIAtlas 特有属性
                backgroundColor: this.backgroundColor,
                backgroundOpacity: this.backgroundOpacity
            };

            // 2. 创建克隆对象
            const clonedAtlas = new UIAtlas(cloneProperties);

            // 3. 设置位置和变换属性
            clonedAtlas.x = this.x + (offsetPosition ? offsetX : 0);
            clonedAtlas.y = this.y + (offsetPosition ? offsetY : 0);
            clonedAtlas.scale.x = this.scale.x;
            clonedAtlas.scale.y = this.scale.y;
            clonedAtlas.rotation = this.rotation;
            clonedAtlas.alpha = this.alpha;
            clonedAtlas.zIndex = this.zIndex;

            // 4. 克隆所有子对象
            const clonedChildren = [];
            for (let i = 0; i < this.children.length; i++) {
                const child = this.children[i];
                if (child && typeof child.clone === 'function') {
                    const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                    clonedAtlas.addChild(clonedChild);
                    clonedChildren.push(clonedChild);
                }
            }

            // 5. 如果原始对象处于图集模式，克隆对象也进入图集模式
            if (this._isAtlasMode) {
                clonedAtlas.mergeToAtlas().then(() => {
                    console.log('✅ UIAtlas: 克隆对象已自动合并到图集模式');
                });
            }

            console.log('✅ UIAtlas: 克隆完成，包含', clonedChildren.length, '个子对象');
            return clonedAtlas;
        }

        /**
         * 销毁图集对象
         */
        destroy() {
            console.log('🎨 UIAtlas: 销毁智能图集Sprite');

            // 清理Canvas
            if (this._canvas) {
                this._canvas = null;
                this._context = null;
            }

            // 清理子对象数组
            this._children = [];

            // 重置状态
            this._isAtlasMode = false;

            // 调用父类销毁方法
            super.destroy();
        }
    }

    // 将UIAtlas类添加到全局作用域
    window.UIAtlas = UIAtlas;

    console.log('🎨 UIAtlas插件已加载');

})();