<script lang="ts">
  /**
   * Select 下拉框组件
   * 基于 Skeleton UI 和全局主题色彩，支持历史记录
   */

  import { historyManager } from '../historyManager';

  export interface SelectOption {
    value: string | number;
    label: string;
    disabled?: boolean;
    group?: string;
  }

  // Props using $props()
  let {
    options = [],
    value = $bindable(''),
    placeholder = '请选择...',
    disabled = false,
    required = false,
    size = 'md',
    variant = 'default',
    fullWidth = false,
    searchable = false,
    clearable = false,
    multiple = false,
    id = '',
    name = '',
    ariaLabel = '',
    // Events
    onChange = () => {},
    onFocus = () => {},
    onBlur = () => {},
    // 历史记录相关
    targetObject = undefined,
    fieldName = undefined,
    enableHistory = true
  }: {
    options?: SelectOption[];
    value?: string | number | (string | number)[];
    placeholder?: string;
    disabled?: boolean;
    required?: boolean;
    size?: 'sm' | 'md' | 'lg';
    variant?: 'default' | 'success' | 'warning' | 'error';
    fullWidth?: boolean;
    searchable?: boolean;
    clearable?: boolean;
    multiple?: boolean;
    id?: string;
    name?: string;
    ariaLabel?: string;
    // Events
    onChange?: (value: string | number | (string | number)[], event: Event) => void;
    onFocus?: (event: FocusEvent) => void;
    onBlur?: (event: FocusEvent) => void;
    // 历史记录相关
    targetObject?: any;
    fieldName?: string;
    enableHistory?: boolean;
  } = $props();

  // 内部状态
  let isOpen = $state(false);
  let searchTerm = $state('');
  let selectElement: HTMLElement;
  let selectedValues = $state<(string | number)[]>(multiple ? (Array.isArray(value) ? value : []) : []);

  // 过滤选项
  const filteredOptions = $derived(searchable && searchTerm
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options);





  // 获取选中的选项
  const selectedOption = $derived(options.find(option => option.value === value));
  const selectedLabel = $derived(selectedOption?.label || placeholder);

  // 分组选项
  const groupedOptions = $derived(filteredOptions.reduce((groups, option) => {
    const group = option.group || 'default';
    if (!groups[group]) groups[group] = [];
    groups[group].push(option);
    return groups;
  }, {} as Record<string, SelectOption[]>));

  // 调试分组选项
  $effect(() => {
    console.log('🔍 Select组件分组选项:', {
      groupedOptions,
      groupKeys: Object.keys(groupedOptions),
      groupEntries: Object.entries(groupedOptions)
    });
  });



  // 处理选项选择
  function handleOptionSelect(option: SelectOption) {
    if (option.disabled) return;

    if (multiple) {
      const oldValues = [...selectedValues];
      const index = selectedValues.indexOf(option.value);
      if (index > -1) {
        selectedValues = selectedValues.filter(v => v !== option.value);
      } else {
        selectedValues = [...selectedValues, option.value];
      }

      // 🎯 处理多选历史记录
      if (enableHistory && historyManager.isRecording()) {
        if (targetObject && fieldName) {
          historyManager.recordChange(targetObject, fieldName, oldValues, selectedValues);
          console.log("📝 HistoryManager: 已记录多选下拉框变更");
        } else {
          const virtualObject = {
            className: `Select_${name || id || 'unnamed'}`,
            [fieldName || 'selectedValues']: selectedValues
          };
          historyManager.recordChange(virtualObject, fieldName || 'selectedValues', oldValues, selectedValues);
          console.log("📝 HistoryManager: 已记录多选下拉框虚拟对象变更");
        }
      }

      onChange(selectedValues, new Event('change'));
    } else {
      const oldValue = value;
      const newValue = option.value;

      console.log("📋 Select: 选项变化:", {
        from: oldValue,
        to: newValue,
        enableHistory,
        targetObject: targetObject?.className || 'none',
        fieldName: fieldName || 'none'
      });

      value = newValue;

      // 🎯 处理单选历史记录
      if (enableHistory && oldValue !== newValue && historyManager.isRecording()) {
        if (targetObject && fieldName) {
          historyManager.recordChange(targetObject, fieldName, oldValue, newValue);
          console.log("📝 HistoryManager: 已记录下拉框变更");
        } else {
          const virtualObject = {
            className: `Select_${name || id || 'unnamed'}`,
            [fieldName || 'value']: newValue
          };
          historyManager.recordChange(virtualObject, fieldName || 'value', oldValue, newValue);
          console.log("📝 HistoryManager: 已记录下拉框虚拟对象变更");
        }
      }

      onChange(value, new Event('change'));
      isOpen = false;
    }
  }

  // 处理清除
  function handleClear(event: Event) {
    event.stopPropagation();
    if (multiple) {
      selectedValues = [];
      onChange(selectedValues, event);
    } else {
      value = '';
      onChange('', event);
    }
  }

  // 切换下拉状态
  function toggleDropdown() {
    if (disabled) return;
    isOpen = !isOpen;
  }

  // 关闭下拉框
  function closeDropdown() {
    isOpen = false;
    searchTerm = '';
  }

  // 获取容器样式类
  function getContainerClass() {
    const baseClass = 'select-container';
    const sizeClass = size === 'sm' ? 'component-sm' : size === 'lg' ? 'component-lg' : '';
    const variantClass = variant === 'success' ? 'component-success' :
                        variant === 'warning' ? 'component-warning' :
                        variant === 'error' ? 'component-error' : '';
    const openClass = isOpen ? 'select-open' : '';
    const disabledClass = disabled ? 'select-disabled' : '';
    const fullWidthClass = fullWidth ? 'component-full-width' : '';

    return [baseClass, sizeClass, variantClass, openClass, disabledClass, fullWidthClass]
      .filter(Boolean)
      .join(' ');
  }

  // 点击外部关闭下拉框
  function handleClickOutside(event: MouseEvent) {
    if (selectElement && !selectElement.contains(event.target as Node)) {
      closeDropdown();
    }
  }

  // 键盘导航
  function handleKeydown(event: KeyboardEvent) {
    if (disabled) return;

    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        toggleDropdown();
        break;
      case 'Escape':
        closeDropdown();
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) toggleDropdown();
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (!isOpen) toggleDropdown();
        break;
    }
  }
</script>

<svelte:window onclick={handleClickOutside} />

<div
  bind:this={selectElement}
  class={getContainerClass()}
  {id}
  role="combobox"
  aria-expanded={isOpen}
  aria-haspopup="listbox"
  aria-label={ariaLabel}
  tabindex={disabled ? -1 : 0}
  onkeydown={handleKeydown}
  onfocus={onFocus}
  onblur={onBlur}
>
  <div class="select-trigger" onclick={toggleDropdown}>
    <div class="select-value">
      {#if multiple && selectedValues.length > 0}
        <div class="select-tags">
          {#each selectedValues as val}
            {@const option = options.find(opt => opt.value === val)}
            {#if option}
              <span class="select-tag">
                {option.label}
                <button
                  type="button"
                  class="select-tag-remove"
                  onclick={(e) => { e.stopPropagation(); handleOptionSelect(option); }}
                >
                  ×
                </button>
              </span>
            {/if}
          {/each}
        </div>
      {:else if selectedLabel !== placeholder}
        {selectedLabel}
      {:else}
        <span class="select-placeholder">{placeholder}</span>
      {/if}
    </div>

    <div class="select-actions">
      {#if clearable && (value || selectedValues.length > 0)}
        <button
          type="button"
          class="select-clear"
          onclick={handleClear}
        >
          ×
        </button>
      {/if}
      <div class="select-arrow" class:rotated={isOpen}>
        ▼
      </div>
    </div>
  </div>

  {#if isOpen}
    <div class="select-dropdown">
      {#if searchable}
        <div class="select-search">
          <input
            type="text"
            bind:value={searchTerm}
            placeholder="搜索选项..."
            class="select-search-input"
          />
        </div>
      {/if}

      <div class="select-options" role="listbox">
        {#each Object.entries(groupedOptions) as [groupName, groupOptions]}
          {#if groupName !== 'default'}
            <div class="select-group-label">{groupName}</div>
          {/if}

          {#each groupOptions as option}
            <div
              class="select-option"
              class:selected={multiple ? selectedValues.includes(option.value) : value === option.value}
              class:disabled={option.disabled}
              role="option"
              aria-selected={multiple ? selectedValues.includes(option.value) : value === option.value}
              onclick={() => handleOptionSelect(option)}
            >
              {#if multiple}
                <input
                  type="checkbox"
                  checked={selectedValues.includes(option.value)}
                  disabled={option.disabled}
                  readonly
                />
              {/if}
              {option.label}
            </div>
          {/each}
        {/each}

        {#if filteredOptions.length === 0}
          <div class="select-no-options">没有找到选项</div>
        {/if}
      </div>
    </div>
  {/if}

  {#if name}
    <input type="hidden" {name} value={multiple ? selectedValues.join(',') : value} />
  {/if}
</div>

<style>
  .select-container {
    position: relative;
    display: inline-block;
    min-width: 90px;
    z-index: 1;
  }

  .select-container.select-full-width {
    width: 100%;
  }

  .select-container.select-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .select-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #252436 !important;
    border: var(--component-border);
    border-radius: var(--component-border-radius);
    cursor: pointer;
    transition: var(--component-transition);
    padding: var(--component-padding);
    font-size: var(--component-font-size);
    font-family: var(--component-font-family);
    line-height: var(--component-line-height);
    color: var(--component-text);
    box-sizing: border-box;
  }

  .select-trigger:hover:not(.select-disabled .select-trigger) {
    background-color: var(--component-bg-hover);
  }

  .select-container.select-open .select-trigger {
    border-color: var(--component-border-focus);
    box-shadow: var(--component-shadow-focus);
  }

  .select-value {
    flex: 1;
    color: var(--theme-text);
    font-family: var(--font-family-base);
  }

  .select-placeholder {
    color: var(--theme-text-secondary);
    opacity: 0.7;
  }

  .select-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-1);
  }

  .select-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    background-color: var(--theme-primary);
    color: var(--theme-text-inverse);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-small);
    font-size: var(--font-size-sm);
  }

  .select-tag-remove {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    font-size: 1.2em;
    line-height: 1;
  }

  .select-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding-right: var(--spacing-2);
  }

  .select-clear {
    background: none;
    border: none;
    color: var(--theme-text-secondary);
    cursor: pointer;
    padding: 0;
    font-size: 1.2em;
    line-height: 1;
  }

  .select-clear:hover {
    color: var(--theme-text);
  }

  .select-arrow {
    color: var(--theme-text-secondary);
    font-size: 0.8em;
    transition: var(--transition-base);
  }

  .select-arrow.rotated {
    transform: rotate(180deg);
  }

  .select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 99999;
    background-color: #252436 !important;
    border: var(--component-border);
    border-radius: var(--component-border-radius);
    box-shadow: var(--component-shadow-dropdown);
    margin-top: 2px;
    max-height: 200px;
    overflow: visible;
  }

  .select-search {
    padding: var(--spacing-2);
    border-bottom: 1px solid var(--theme-border);
  }

  .select-search-input {
    width: 100%;
    padding: var(--spacing-2);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    background-color: var(--theme-background);
    color: var(--theme-text);
    font-family: var(--font-family-base);
  }

  .select-options {
    max-height: 180px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .select-group-label {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--theme-text-secondary);
    background-color: var(--theme-surface-light);
    border-bottom: 1px solid var(--theme-border);
  }

  .select-option {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: var(--component-padding);
    cursor: pointer;
    transition: background-color 0.15s ease;
    color: var(--component-text);
    font-size: var(--component-font-size);
    font-family: var(--component-font-family);
    line-height: var(--component-line-height);
  }

  .select-option:hover:not(.disabled) {
    background-color: var(--component-bg-hover);
  }

  .select-option.selected {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--component-border-focus);
  }

  .select-option.disabled {
    color: var(--component-text-disabled);
    cursor: not-allowed;
  }

  .select-no-options {
    padding: var(--spacing-4);
    text-align: center;
    color: var(--theme-text-secondary);
    font-style: italic;
  }

  /* 导入统一样式 */
  @import './shared-styles.css';

  .select-sm .select-arrow {
    font-size: 8px;
  }

  .select-md .select-trigger {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-base);
  }

  .select-lg .select-trigger {
    padding: var(--spacing-4) var(--spacing-5);
    font-size: var(--font-size-lg);
  }

  /* 状态变体 */
  .select-success .select-trigger {
    border-color: var(--theme-success);
  }

  .select-warning .select-trigger {
    border-color: var(--theme-warning);
  }

  .select-error .select-trigger {
    border-color: var(--theme-error);
  }
</style>
