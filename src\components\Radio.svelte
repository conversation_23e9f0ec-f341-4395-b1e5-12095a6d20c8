<script lang="ts">
  /**
   * Radio 单选框组件
   * 基于 Skeleton UI 和全局主题色彩
   */

  export interface RadioOption {
    value: string | number;
    label: string;
    disabled?: boolean;
    description?: string;
  }

  // Props
  export let options: RadioOption[] = [];
  export let value: string | number = '';
  export let name: string = '';
  export let disabled: boolean = false;
  export let required: boolean = false;
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let variant: 'default' | 'success' | 'warning' | 'error' = 'default';
  export let direction: 'horizontal' | 'vertical' = 'vertical';
  export let ariaLabel: string = '';

  // Events
  export let onChange: (value: string | number, event: Event) => void = () => {};

  // 处理选择变化
  function handleChange(optionValue: string | number, event: Event) {
    if (disabled) return;
    value = optionValue;
    onChange(value, event);
  }

  // 获取容器样式类
  function getContainerClass() {
    const baseClass = 'radio-group';
    const sizeClass = size === 'sm' ? 'component-sm' : size === 'lg' ? 'component-lg' : '';
    const variantClass = variant === 'success' ? 'component-success' :
                        variant === 'warning' ? 'component-warning' :
                        variant === 'error' ? 'component-error' : '';
    const directionClass = `radio-${direction}`;
    const disabledClass = disabled ? 'radio-disabled' : '';

    return [baseClass, sizeClass, variantClass, directionClass, disabledClass]
      .filter(Boolean)
      .join(' ');
  }

  // 获取选项样式类
  function getOptionClass(option: RadioOption) {
    const baseClass = 'radio-option';
    const checkedClass = value === option.value ? 'radio-checked' : '';
    const disabledClass = (disabled || option.disabled) ? 'radio-option-disabled' : '';

    return [baseClass, checkedClass, disabledClass]
      .filter(Boolean)
      .join(' ');
  }
</script>

<div
  class={getContainerClass()}
  role="radiogroup"
  aria-label={ariaLabel}
  aria-required={required}
>
  {#each options as option, index}
    <label class={getOptionClass(option)}>
      <input
        type="radio"
        {name}
        value={option.value}
        checked={value === option.value}
        disabled={disabled || option.disabled}
        {required}
        class="radio-input"
        onchange={(e) => handleChange(option.value, e)}
      />

      <div class="radio-indicator">
        <div class="radio-dot"></div>
      </div>

      <div class="radio-content">
        <div class="radio-label">{option.label}</div>
        {#if option.description}
          <div class="radio-description">{option.description}</div>
        {/if}
      </div>
    </label>
  {/each}
</div>

<style>
  @import './shared-styles.css';

  .radio-group {
    display: flex;
    gap: 8px;
    font-size: var(--component-font-size);
    font-family: var(--component-font-family);
  }

  .radio-group.radio-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .radio-vertical {
    flex-direction: column;
  }

  .radio-horizontal {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .radio-option {
    display: flex;
    align-items: flex-start;
    gap: 6px;
    cursor: pointer;
    transition: var(--component-transition);
    padding: 4px;
    border-radius: var(--component-border-radius);
  }

  .radio-option:hover:not(.radio-option-disabled) {
    background-color: var(--component-bg-hover);
  }

  .radio-option.radio-option-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .radio-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }

  .radio-indicator {
    position: relative;
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    border: 2px solid var(--theme-border);
    border-radius: 50%;
    background-color: var(--theme-surface);
    transition: var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .radio-option:hover:not(.radio-option-disabled) .radio-indicator {
    border-color: var(--theme-primary);
  }

  .radio-checked .radio-indicator {
    border-color: var(--theme-primary);
    background-color: var(--theme-primary);
  }

  .radio-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--theme-text-inverse);
    opacity: 0;
    transform: scale(0);
    transition: var(--transition-base);
  }

  .radio-checked .radio-dot {
    opacity: 1;
    transform: scale(1);
  }

  .radio-content {
    flex: 1;
    min-width: 0;
  }

  .radio-label {
    color: var(--theme-text);
    font-family: var(--font-family-base);
    font-weight: 500;
    line-height: 1.4;
  }

  .radio-description {
    color: var(--theme-text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
    margin-top: var(--spacing-1);
  }

  /* 尺寸变体 */
  .radio-sm .radio-indicator {
    width: 16px;
    height: 16px;
  }

  .radio-sm .radio-dot {
    width: 6px;
    height: 6px;
  }

  .radio-sm .radio-label {
    font-size: var(--font-size-sm);
  }

  .radio-md .radio-indicator {
    width: 20px;
    height: 20px;
  }

  .radio-md .radio-dot {
    width: 8px;
    height: 8px;
  }

  .radio-md .radio-label {
    font-size: var(--font-size-base);
  }

  .radio-lg .radio-indicator {
    width: 24px;
    height: 24px;
  }

  .radio-lg .radio-dot {
    width: 10px;
    height: 10px;
  }

  .radio-lg .radio-label {
    font-size: var(--font-size-lg);
  }

  /* 状态变体 */
  .radio-success .radio-checked .radio-indicator {
    border-color: var(--theme-success);
    background-color: var(--theme-success);
  }

  .radio-warning .radio-checked .radio-indicator {
    border-color: var(--theme-warning);
    background-color: var(--theme-warning);
  }

  .radio-error .radio-checked .radio-indicator {
    border-color: var(--theme-error);
    background-color: var(--theme-error);
  }

  /* 焦点样式 */
  .radio-input:focus-visible + .radio-indicator {
    outline: 2px solid var(--theme-primary);
    outline-offset: 2px;
  }
</style>
