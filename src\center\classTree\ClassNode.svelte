<script lang="ts">
  /**
   * 类节点组件 - 显示单个类的节点
   */
  
  import type { ClassTreeNode } from './tree';
  import { getNodeColor, handleNodeClick } from './tree';

  // Props using $props()
  interface Props {
    node: ClassTreeNode;
    nodeHeight?: number;
    onnodeclick?: (event: { className: string; node: ClassTreeNode }) => void;
    onnodehover?: (event: { className: string; node: ClassTreeNode; isEnter: boolean }) => void;
  }

  let {
    node,
    nodeHeight = 40,
    onnodeclick,
    onnodehover
  }: Props = $props();

  // 使用节点的实际宽度，如果没有则计算
  let nodeWidth = $derived(node.width || (() => {
    const baseWidth = 40;
    const charWidth = 8;
    const padding = 20;
    const minWidth = 100;
    const maxWidth = 250;

    const textWidth = node.name.length * charWidth;
    const calculatedWidth = baseWidth + textWidth + padding;

    return Math.min(Math.max(calculatedWidth, minWidth), maxWidth);
  })());

  // 计算节点样式
  let nodeColor = $derived(getNodeColor(node.name));
  let textColor = '#ffffff';
  let borderColor = $derived(nodeColor);

  // 处理节点点击
  function onNodeClick() {
    handleNodeClick(node.name);
    onnodeclick?.({ className: node.name, node });
  }

  // 处理鼠标悬停
  function onMouseEnter() {
    onnodehover?.({ className: node.name, node, isEnter: true });
  }

  function onMouseLeave() {
    onnodehover?.({ className: node.name, node, isEnter: false });
  }

  // 获取节点类型图标
  function getTypeIcon(type: string): string {
    switch (type) {
      case 'class': return '🏛️';
      case 'static': return '⚙️';
      case 'interface': return '📋';
      default: return '📦';
    }
  }

  // 智能换行：如果类名太长，分成多行显示
  function formatClassName(name: string): { lines: string[]; fontSize: number } {
    const maxCharsPerLine = Math.floor((nodeWidth - 40) / 7); // 根据节点宽度计算每行字符数

    if (name.length <= maxCharsPerLine) {
      return { lines: [name], fontSize: 11 };
    }

    // 尝试在下划线处分割
    const parts = name.split('_');
    if (parts.length > 1) {
      const lines: string[] = [];
      let currentLine = '';

      for (const part of parts) {
        const testLine = currentLine ? `${currentLine}_${part}` : part;
        if (testLine.length <= maxCharsPerLine) {
          currentLine = testLine;
        } else {
          if (currentLine) lines.push(currentLine);
          currentLine = part;
        }
      }
      if (currentLine) lines.push(currentLine);

      return {
        lines: lines.slice(0, 2), // 最多两行
        fontSize: lines.length > 1 ? 10 : 11
      };
    }

    // 如果无法智能分割，直接显示完整名称，调整字体大小
    return {
      lines: [name],
      fontSize: Math.max(8, Math.min(11, Math.floor(maxCharsPerLine * 11 / name.length)))
    };
  }

  let formattedText = $derived(formatClassName(node.name));
</script>

<!-- 类节点 -->
<g
  class="class-node"
  transform="translate({node.x || 0}, {node.y || 0})"
  onclick={onNodeClick}
  onmouseenter={onMouseEnter}
  onmouseleave={onMouseLeave}
  role="button"
  tabindex="0"
  onkeydown={(e) => e.key === 'Enter' && onNodeClick()}
>
  <!-- 节点背景 -->
  <rect
    width={nodeWidth}
    height={nodeHeight}
    rx="6"
    ry="6"
    fill={nodeColor}
    stroke={borderColor}
    stroke-width="2"
    class="node-background"
  />
  
  <!-- 节点文本 -->
  {#each formattedText.lines as line, index}
    <text
      x={nodeWidth / 2}
      y={formattedText.lines.length === 1
        ? nodeHeight / 2
        : nodeHeight / 2 - 6 + index * 12}
      text-anchor="middle"
      dominant-baseline="middle"
      fill={textColor}
      font-size={formattedText.fontSize}
      font-weight="500"
      class="node-text"
    >
      {line}
    </text>
  {/each}
  
  <!-- 类型图标 -->
  <text
    x={nodeWidth - 8}
    y={12}
    text-anchor="middle"
    dominant-baseline="middle"
    font-size="8"
    class="type-icon"
  >
    {getTypeIcon(node.info.type)}
  </text>
  
  <!-- 子类数量指示器 -->
  {#if node.children.length > 0}
    <circle
      cx={nodeWidth - 8}
      cy={nodeHeight - 8}
      r="6"
      fill="rgba(255, 255, 255, 0.8)"
      stroke={nodeColor}
      stroke-width="1"
      class="children-indicator"
    />
    <text
      x={nodeWidth - 8}
      y={nodeHeight - 8}
      text-anchor="middle"
      dominant-baseline="middle"
      fill={nodeColor}
      font-size="8"
      font-weight="bold"
      class="children-count"
    >
      {node.children.length}
    </text>
  {/if}
</g>

<style>
  .class-node {
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .class-node:hover .node-background {
    filter: brightness(1.1);
    stroke-width: 3;
  }

  .class-node:hover .node-text {
    font-weight: 600;
  }

  .node-background {
    transition: all 0.2s ease;
  }

  .node-text {
    pointer-events: none;
    user-select: none;
    transition: all 0.2s ease;
  }

  .type-icon {
    pointer-events: none;
    user-select: none;
    opacity: 0.8;
  }

  .children-indicator {
    transition: all 0.2s ease;
  }

  .children-count {
    pointer-events: none;
    user-select: none;
  }

  .class-node:focus {
    outline: none;
  }

  .class-node:focus .node-background {
    stroke: #fff;
    stroke-width: 3;
    filter: brightness(1.2);
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .node-text {
      font-size: 10px;
    }
    
    .type-icon {
      font-size: 7px;
    }
    
    .children-count {
      font-size: 7px;
    }
  }
</style>
