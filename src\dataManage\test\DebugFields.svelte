<script lang="ts">
  import { dataStore } from '../stores/dataStore';
  import type { StaticDataType } from '../types/dataTypes';

  let selectedDataType = $state<StaticDataType | null>(null);
  let fieldsData = $state<any>(null);

  // 获取可用的数据类型
  const availableDataTypes = dataStore.getAvailableDataTypes();

  // 处理数据类型选择
  async function handleDataTypeSelect(dataType: StaticDataType) {
    selectedDataType = dataType;
    console.log('选择数据类型:', dataType);
    
    try {
      await dataStore.selectDataType(dataType);
      fieldsData = {
        availableFields: dataStore.availableFields,
        currentDataTypeInfo: dataStore.currentDataTypeInfo,
        error: dataStore.error,
        isLoading: dataStore.isLoading
      };
      console.log('字段数据:', fieldsData);
    } catch (error) {
      console.error('加载字段失败:', error);
    }
  }

  // 递归显示字段结构
  function renderFieldStructure(fields: any[], depth = 0): string {
    let result = '';
    for (const field of fields) {
      const indent = '  '.repeat(depth);
      result += `${indent}- ${field.displayName} (${field.name}) [${field.type}]\n`;
      if (field.children && field.children.length > 0) {
        result += renderFieldStructure(field.children, depth + 1);
      }
    }
    return result;
  }
</script>

<div class="debug-container">
  <h2>字段结构调试</h2>
  
  <div class="data-types-section">
    <h3>选择数据类型</h3>
    <div class="data-types-grid">
      {#each availableDataTypes as { type, displayName }}
        <button
          class="data-type-button"
          class:selected={selectedDataType === type}
          onclick={() => handleDataTypeSelect(type)}
        >
          {displayName}
        </button>
      {/each}
    </div>
  </div>

  {#if selectedDataType}
    <div class="fields-section">
      <h3>字段信息 - {selectedDataType}</h3>
      
      {#if fieldsData}
        <div class="debug-info">
          <div class="info-item">
            <strong>加载状态:</strong> {fieldsData.isLoading ? '加载中' : '已完成'}
          </div>
          <div class="info-item">
            <strong>错误信息:</strong> {fieldsData.error || '无'}
          </div>
          <div class="info-item">
            <strong>字段数量:</strong> {fieldsData.availableFields?.length || 0}
          </div>
        </div>

        {#if fieldsData.availableFields && fieldsData.availableFields.length > 0}
          <div class="fields-structure">
            <h4>字段结构:</h4>
            <pre>{renderFieldStructure(fieldsData.availableFields)}</pre>
          </div>

          <div class="fields-json">
            <h4>完整JSON:</h4>
            <pre>{JSON.stringify(fieldsData.availableFields, null, 2)}</pre>
          </div>
        {:else}
          <div class="no-fields">没有找到字段数据</div>
        {/if}
      {:else}
        <div class="loading">正在加载字段数据...</div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .debug-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .data-types-section, .fields-section {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
  }

  .data-types-section h3, .fields-section h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #333;
  }

  .data-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
  }

  .data-type-button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }

  .data-type-button:hover {
    background: #f0f8ff;
    border-color: #007acc;
  }

  .data-type-button.selected {
    background: #007acc;
    color: white;
    border-color: #007acc;
  }

  .debug-info {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }

  .info-item {
    margin-bottom: 8px;
  }

  .info-item strong {
    color: #495057;
    margin-right: 8px;
  }

  .fields-structure, .fields-json {
    margin-bottom: 16px;
  }

  .fields-structure h4, .fields-json h4 {
    margin-bottom: 8px;
    color: #333;
  }

  .fields-structure pre, .fields-json pre {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
    max-height: 300px;
    overflow-y: auto;
  }

  .no-fields, .loading {
    padding: 16px;
    text-align: center;
    color: #666;
    font-style: italic;
  }
</style>
